.SearchHistory {
  .hos-tabs__active-bar {
    width: 56px !important;
  }
  // &.page {
  //   background-color: #fff;
  //   // margin-left: 10px;
  //   border-radius: 5px;
  //   padding: 10px;
  //   box-sizing: border-box;
  //   min-height: calc(100vh - 90px);
  // }
  // .pageItemManage {
  //   min-height: calc(100vh - 160px);
  //   width: 100px;
  //   overflow: visible;
  // }
  // .getBackButton {
  //   position: absolute;
  //   top: 15px;
  //   right: 25px;
  //   cursor: pointer;
  //   z-index: 1000;
  // }
}
