import store from '@/store/index'
/**
 * 权限
 * @param {*} key
 */
export function hasPermission(key, checkProjectPermissions) {
  // 超级管理员拥有所有权限
  if (store.state.d2admin.user.info.superAdmin) {
    return true
  }
  if (checkProjectPermissions) {
    if (!window.SITE_CONFIG['projectPermissions']) {
      window.SITE_CONFIG['projectPermissions'] = []
    }
    // return window.SITE_CONFIG['projectPermissions'].indexOf(key) !== -1 ? true : (window.SITE_CONFIG['permissions'].indexOf(key) !== -1)
    return window.SITE_CONFIG['projectPermissions'].indexOf(key) !== -1 || false
  } else {
    // 会一直发送请求，先注释掉
    // if (!window.SITE_CONFIG['permissions'] || window.SITE_CONFIG['permissions'].length === 0) {
    //   sysMenuService
    //   .getPermissions()
    //   .then(res => {
    //     window.SITE_CONFIG['permissions'] = res
    //   })
    //   .catch(() => {})
    // }
    return window.SITE_CONFIG['permissions'].indexOf(key) !== -1 || false
  }
}
/**
 * 获取uuid
 */
export function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
  })
}

/**
 * 获取svg图标(id)列表
 */
export function getIconList() {
  var res = []
  var list = document.querySelectorAll('svg symbol')
  for (var i = 0; i < list.length; i++) {
    res.push(list[i].id)
  }
  return res
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = 'id', pid = 'pid') {
  var res = []
  var temp = {}
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i]
  }
  for (var k = 0; k < data.length; k++) {
    if (!temp[data[k][pid]] || data[k][id] === data[k][pid]) {
      res.push(data[k])
      continue
    }
    if (!temp[data[k][pid]]['children']) {
      temp[data[k][pid]]['children'] = []
    }
    temp[data[k][pid]]['children'].push(data[k])
    data[k]['_level'] = (temp[data[k][pid]]._level || 0) + 1
  }
  return res
}

// 判断项目配置
export function getProjectSettingConfig(configName) {
  if (!store.state.d2admin.project.projectInfo) {
    return
  }
  const projectInfo = store.state.d2admin.project.projectInfo.projectInfo
  return projectInfo[configName]
}