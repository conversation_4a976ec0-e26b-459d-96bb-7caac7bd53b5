// 自动入组任务执行记录
export const pageTaskListApi = (params) => {
    return {
        url: `edc/auto-join-record/page`,
        method: 'GET',
        params
    }
}

export const detailApi = (id) => {
    return {
        url: `edc/auto-join-record/detail/` + id,
        method: 'GET'
    }
}

export const deleteApi = (data) => {
    return {
        url: `edc/auto-join-record/deletion`,
        method: 'POST',
        data
    }
}

export const addApi = (data) => {
    return {
        url: `edc/auto-join-record/insert`,
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: `edc/auto-join-record/update`,
        method: 'POST',
        data
    }
}