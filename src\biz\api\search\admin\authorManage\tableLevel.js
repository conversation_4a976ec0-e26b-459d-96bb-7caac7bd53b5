export const queryThemeListByRole = (params) => {
  return {
    url: 'search/theme-permission/page',
    method: 'GET',
    params
  }
}

export const insertOrUpdate = (data) => {
  return {
    url: 'search/theme-permission/save-or-update',
    method: 'POST',
    data
  }
}
export const oneKeyAuth = (data) => {
  return {
    url: 'search/theme-permission/save-batch',
    method: 'POST',
    data
  }
}
export const oneKeyCancel = (data) => {
  return {
    url: 'search/theme-permission/del-batch',
    method: 'POST',
    data
  }
}

export const authAllTable = (params) => {
  return {
    url: `search/theme-permission/save-all/${params.roleCode}/${params.indexId}`,
    method: "GET"
  }
}

export const cancelAllTable = (params) => {
  return {
    url: `search/theme-permission/del-all/${params.roleCode}/${params.indexId}`,
    method: "GET"
  }
}

