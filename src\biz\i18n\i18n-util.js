import i18n from "@base/i18n/index.js"
/**
 * 获取国际化 (i18n) 设置的内容,没有则返回默认值
 * 一般用于长文本在国际化配置时的key用了一个短名称的时候使用
 * @example i18n_t_or_default('xxx描述文字','描述文字长文本内容')
 * @example i18n_t_or_default('适用角色描述文字','为空时，默认所有角色都生效；若不为空，则指定的角色单独生效')
 */
export function i18n_t_or_default(i18n_key_str,default_str){
  if(i18n.t(i18n_key_str) === i18n_key_str){
    // 未设置短名称的i18n
    if(!default_str){
      // 未设置默认值
      return i18n_key_str
    }else{
      // 判断是否给默认值(长文本)设置了i18n
      if(i18n.t(default_str) !== default_str){
        // 给长文本设置了i18n,返回对应的长文本的翻译
        return i18n.t(default_str)
      }else{
        // 没有给长文本设置i18n,返回传参的默认值
        return default_str
      }
    }
  }else{
    // 设置了短名称的i18n,返回对应的短名称i18n的翻译
    return i18n.t(i18n_key_str)
  }
}