.right-detail {
      flex-grow: 1;

      .center{
        box-sizing: border-box;
        padding: 0 10px;
        overflow-y: scroll;
        background-color: #fff;

        .hos-collapse {
          height: 100% !important;
        }

        .hos-card {
          position: relative;

          .content-grade-01 {
            margin-bottom: 10px;
          }

          .content-grade-23 {
            .item {
              margin-bottom: 20px;
            }
          }

          .content-grade-01,
          .content-grade-23 {
            .form-name {
              border-bottom: 1px solid #ebebeb;
              position: relative;
              display: flex;
              justify-content: space-between;

              .name-span {
                .left-name {
                  width: fit-content;
                  height: 100%;
                  display: inline-block;
                  border-bottom: 1px solid #409eff;
                }
                .binded-template {
                  margin-left: 20px;
                  font-size: 12px;
                }

                .not-bind-template {
                  margin-left: 20px;
                  font-size: 12px;
                  span {
                    color: red;
                  }
                }
              }

              

              .btn-box {
                .el-button--text {
                  color: #888;
                  &:hover{
                    color: #409eff;
                  }
                }
                span {
                  margin-right: 15px;
                  cursor: pointer;
                }

                .delete-span {
                  color: red;
                }

                .edit-span,
                .save-span,
                .exit-span {
                  // color: #409eff;
                }
              }
            }

            .tag-container {
              margin-top: 10px;

              .hos-tag {
                margin-right: 10px;
                margin-bottom: 5px;
              }
            }
          }
        }

        // FIXME:在原来的基础上自定义了很多样式(不想换掉el-collapse组件重新写一套样式)
        .hos-collapse {
          border: none !important;
          margin: 0;
        }

        .hos-card {
          margin-bottom: 10px;

          .hos-card__header {

            span {
              width: fit-content !important;
              text-align: left !important;
              .delete-icon {
                margin-left: 5px;
              }
            }

            i {
              // display: none;
            }
          }

          .hos-card__content {
            padding-left: 10px;
          }
        }

        .hos-empty {
          .tips {
            .click-blue {
              display: inline-block;
              color: #409eff;
              border-bottom: 1px solid #409eff;
              cursor: pointer;
            }
          }
        }
      }
    }