@charset "utf-8";
.panel {
  overflow: hidden;
  text-align: left;
  margin: 0;
  border: 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.panel-header,
.panel-body {
  border-width: 1px;
  border-style: solid;
}
.panel-header {
  padding: 5px;
  position: relative;
}
.panel-title {
  background: url('images/blank.gif') no-repeat;
}
.panel-header-noborder {
  border-width: 0 0 1px 0;
}
.panel-body {
  overflow: auto;
  border-top-width: 0;
  padding: 0;
}
.panel-body-noheader {
  border-top-width: 1px;
}
.panel-body-noborder {
  border-width: 0px;
}
.panel-with-icon {
  padding-left: 18px;
}
.panel-icon,
.panel-tool {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  height: 16px;
  overflow: hidden;
}
.panel-icon {
  left: 5px;
  width: 16px;
}
.panel-tool {
  right: 5px;
  width: auto;
}
.panel-tool a {
  display: inline-block;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  filter: alpha(opacity=60);
  margin: 0 0 0 2px;
  vertical-align: top;
}
.panel-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #eaf2ff;
  -moz-border-radius: 3px 3px 3px 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}
.panel-loading {
  padding: 11px 0px 10px 30px;
}
.panel-noscroll {
  overflow: hidden;
}
.panel-fit,
.panel-fit body {
  height: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  overflow: hidden;
}
.panel-loading {
  background: url('images/loading.gif') no-repeat 10px 10px;
}
.panel-tool-close {
  background: url('images/panel_tools.png') no-repeat -16px 0px;
}
.panel-tool-min {
  background: url('images/panel_tools.png') no-repeat 0px 0px;
}
.panel-tool-max {
  background: url('images/panel_tools.png') no-repeat 0px -16px;
}
.panel-tool-restore {
  background: url('images/panel_tools.png') no-repeat -16px -16px;
}
.panel-tool-collapse {
  background: url('images/panel_tools.png') no-repeat -32px 0;
}
.panel-tool-expand {
  background: url('images/panel_tools.png') no-repeat -32px -16px;
}
.panel-header,
.panel-body {
  border-color: #95B8E7;
}
.panel-header {
  background-color: #E0ECFF;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -o-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: linear-gradient(to bottom, #EFF5FF 0, #E0ECFF 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#E0ECFF, GradientType=0);
}
.panel-body {
  background-color: #ffffff;
  color: #000000;
  font-size: 12px;
}
.panel-title {
  font-size: 12px;
  font-weight: bold;
  color: #0E2D5F;
  height: 16px;
  line-height: 16px;
}
.accordion {
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.accordion .accordion-header {
  border-width: 0 0 1px;
  cursor: pointer;
}
.accordion .accordion-body {
  border-width: 0 0 1px;
}
.accordion-noborder {
  border-width: 0;
}
.accordion-noborder .accordion-header {
  border-width: 0 0 1px;
}
.accordion-noborder .accordion-body {
  border-width: 0 0 1px;
}
.accordion-collapse {
  background: url('images/accordion_arrows.png') no-repeat 0 0;
}
.accordion-expand {
  background: url('images/accordion_arrows.png') no-repeat -16px 0;
}
.accordion {
  background: #ffffff;
  border-color: #95B8E7;
}
.accordion .accordion-header {
  background: #E0ECFF;
  filter: none;
}
.accordion .accordion-header-selected {
  background: #ffe48d;
}
.accordion .accordion-header-selected .panel-title {
  color: #000000;
}
.window {
  overflow: hidden;
  padding: 5px;
  border-width: 1px;
  border-style: solid;
}
.window .window-header {
  background: transparent;
  padding: 0px 0px 6px 0px;
}
.window .window-body {
  border-width: 1px;
  border-style: solid;
  border-top-width: 0px;
}
.window .window-body-noheader {
  border-top-width: 1px;
}
.window .window-header .panel-icon,
.window .window-header .panel-tool {
  top: 50%;
  margin-top: -11px;
}
.window .window-header .panel-icon {
  left: 1px;
}
.window .window-header .panel-tool {
  right: 1px;
}
.window .window-header .panel-with-icon {
  padding-left: 18px;
}
.window-proxy {
  position: absolute;
  overflow: hidden;
}
.window-proxy-mask {
  position: absolute;
  filter: alpha(opacity=5);
  opacity: 0.05;
}
.window-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  filter: alpha(opacity=40);
  opacity: 0.40;
  font-size: 1px;
  *zoom: 1;
  overflow: hidden;
}
.window,
.window-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.window-shadow {
  background: #ccc;
  -moz-box-shadow: 2px 2px 3px #cccccc;
  -webkit-box-shadow: 2px 2px 3px #cccccc;
  box-shadow: 2px 2px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2, MakeShadow=false, ShadowOpacity=0.2);
}
.window,
.window .window-body {
  border-color: #95B8E7;
}
.window {
  background-color: #E0ECFF;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #E0ECFF 20%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #E0ECFF 20%);
  background: -o-linear-gradient(top, #EFF5FF 0, #E0ECFF 20%);
  background: linear-gradient(to bottom, #EFF5FF 0, #E0ECFF 20%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#E0ECFF, GradientType=0);
}
.window-proxy {
  border: 1px dashed #95B8E7;
}
.window-proxy-mask,
.window-mask {
  background: #ccc;
}
.dialog-content {
  overflow: auto;
}
.dialog-toolbar {
  padding: 2px 5px;
}
.dialog-tool-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.dialog-button {
  padding: 5px;
  text-align: right;
}
.dialog-button .l-btn {
  margin-left: 5px;
}
.dialog-toolbar,
.dialog-button {
  background: #F4F4F4;
}
.dialog-toolbar {
  border-bottom: 1px solid #dddddd;
}
.dialog-button {
  border-top: 1px solid #dddddd;
}
.textbox[type=text] {
  border: 1px solid #95B8E7;
  vertical-align: middle;
}
.combo {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.combo .combo-text {
  font-size: 12px;
  border: 0px;
  line-height: 20px;
  height: 20px;
  margin: 0;
  padding: 0px 2px;
  *margin-top: -1px;
  *height: 18px;
  *line-height: 18px;
  _height: 18px;
  _line-height: 18px;
  vertical-align: baseline;
}
.combo-arrow {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.combo-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.combo-panel {
  overflow: auto;
}
.combo-arrow {
  background: url('images/combo_arrow.png') no-repeat center center;
}
.combo,
.combo-panel {
  background-color: #ffffff;
}
.combo {
  border-color: #95B8E7;
  background-color: #ffffff;
}
.combo-arrow {
  background-color: #E0ECFF;
}
.combo-arrow-hover {
  background-color: #eaf2ff;
}
.combobox-item,
.combobox-group {
  font-size: 12px;
  padding: 3px;
  padding-right: 0px;
}
.combobox-item-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.combobox-gitem {
  padding-left: 10px;
}
.combobox-group {
  font-weight: bold;
}
.combobox-item-hover {
  background-color: #eaf2ff;
  color: #000000;
}
.combobox-item-selected {
  background-color: #ffe48d;
  color: #000000;
}
.layout {
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 0;
}
.layout-panel {
  position: absolute;
  overflow: hidden;
}
.layout-panel-east,
.layout-panel-west {
  z-index: 2;
}
.layout-panel-north,
.layout-panel-south {
  z-index: 3;
}
.layout-expand {
  position: absolute;
  padding: 0px;
  font-size: 1px;
  cursor: pointer;
  z-index: 1;
}
.layout-expand .panel-header,
.layout-expand .panel-body {
  background: transparent;
  filter: none;
  overflow: hidden;
}
.layout-expand .panel-header {
  border-bottom-width: 0px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  position: absolute;
  font-size: 1px;
  display: none;
  z-index: 5;
}
.layout-split-proxy-h {
  width: 5px;
  cursor: e-resize;
}
.layout-split-proxy-v {
  height: 5px;
  cursor: n-resize;
}
.layout-mask {
  position: absolute;
  background: #fafafa;
  filter: alpha(opacity=10);
  opacity: 0.10;
  z-index: 4;
}
.layout-button-up {
  background: url('images/layout_arrows.png') no-repeat -16px -16px;
}
.layout-button-down {
  background: url('images/layout_arrows.png') no-repeat -16px 0;
}
.layout-button-left {
  background: url('images/layout_arrows.png') no-repeat 0 0;
}
.layout-button-right {
  background: url('images/layout_arrows.png') no-repeat 0 -16px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  background-color: #aac5e7;
}
.layout-split-north {
  border-bottom: 5px solid #E6EEF8;
}
.layout-split-south {
  border-top: 5px solid #E6EEF8;
}
.layout-split-east {
  border-left: 5px solid #E6EEF8;
}
.layout-split-west {
  border-right: 5px solid #E6EEF8;
}
.layout-expand {
  background-color: #E0ECFF;
}
.layout-expand-over {
  background-color: #E0ECFF;
}
.tabs-container {
  overflow: hidden;
}
.tabs-header {
  border-width: 1px;
  border-style: solid;
  border-bottom-width: 0;
  position: relative;
  padding: 0;
  padding-top: 2px;
  overflow: hidden;
}
.tabs-header-plain {
  border: 0;
  background: transparent;
}
.tabs-scroller-left,
.tabs-scroller-right {
  position: absolute;
  top: auto;
  bottom: 0;
  width: 18px;
  font-size: 1px;
  display: none;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.tabs-scroller-left {
  left: 0;
}
.tabs-scroller-right {
  right: 0;
}
.tabs-tool {
  position: absolute;
  bottom: 0;
  padding: 1px;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.tabs-header-plain .tabs-tool {
  padding: 0 1px;
}
.tabs-wrap {
  position: relative;
  left: 0;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}
.tabs-scrolling {
  margin-left: 18px;
  margin-right: 18px;
}
.tabs-disabled {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.tabs {
  list-style-type: none;
  height: 26px;
  margin: 0px;
  padding: 0px;
  padding-left: 4px;
  width: 50000px;
  border-style: solid;
  border-width: 0 0 1px 0;
}
.tabs li {
  float: left;
  display: inline-block;
  margin: 0 4px -1px 0;
  padding: 0;
  position: relative;
  border: 0;
}
.tabs li a.tabs-inner {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0 10px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  white-space: nowrap;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 0 0;
  -webkit-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.tabs li.tabs-selected a.tabs-inner {
  font-weight: bold;
  outline: none;
}
.tabs li.tabs-selected a:hover.tabs-inner {
  cursor: default;
  pointer: default;
}
.tabs li a.tabs-close,
.tabs-p-tool {
  position: absolute;
  font-size: 1px;
  display: block;
  height: 12px;
  padding: 0;
  top: 50%;
  margin-top: -6px;
  overflow: hidden;
}
.tabs li a.tabs-close {
  width: 12px;
  right: 5px;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs-p-tool {
  right: 16px;
}
.tabs-p-tool a {
  display: inline-block;
  font-size: 1px;
  width: 12px;
  height: 12px;
  margin: 0;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs li a:hover.tabs-close,
.tabs-p-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  cursor: hand;
  cursor: pointer;
}
.tabs-with-icon {
  padding-left: 18px;
}
.tabs-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 10px;
  top: 50%;
  margin-top: -8px;
}
.tabs-title {
  font-size: 12px;
}
.tabs-closable {
  padding-right: 8px;
}
.tabs-panels {
  margin: 0px;
  padding: 0px;
  border-width: 1px;
  border-style: solid;
  border-top-width: 0;
  overflow: hidden;
}
.tabs-header-bottom {
  border-width: 0 1px 1px 1px;
  padding: 0 0 2px 0;
}
.tabs-header-bottom .tabs {
  border-width: 1px 0 0 0;
}
.tabs-header-bottom .tabs li {
  margin: -1px 4px 0 0;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  -moz-border-radius: 0 0 5px 5px;
  -webkit-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
}
.tabs-header-bottom .tabs-tool {
  top: 0;
}
.tabs-header-bottom .tabs-scroller-left,
.tabs-header-bottom .tabs-scroller-right {
  top: 0;
  bottom: auto;
}
.tabs-panels-top {
  border-width: 1px 1px 0 1px;
}
.tabs-header-left {
  float: left;
  border-width: 1px 0 1px 1px;
  padding: 0;
}
.tabs-header-right {
  float: right;
  border-width: 1px 1px 1px 0;
  padding: 0;
}
.tabs-header-left .tabs-wrap,
.tabs-header-right .tabs-wrap {
  height: 100%;
}
.tabs-header-left .tabs {
  height: 100%;
  padding: 4px 0 0 4px;
  border-width: 0 1px 0 0;
}
.tabs-header-right .tabs {
  height: 100%;
  padding: 4px 4px 0 0;
  border-width: 0 0 0 1px;
}
.tabs-header-left .tabs li,
.tabs-header-right .tabs li {
  display: block;
  width: 100%;
  position: relative;
}
.tabs-header-left .tabs li {
  left: auto;
  right: 0;
  margin: 0 -1px 4px 0;
  float: right;
}
.tabs-header-right .tabs li {
  left: 0;
  right: auto;
  margin: 0 0 4px -1px;
  float: left;
}
.tabs-header-left .tabs li a.tabs-inner {
  display: block;
  text-align: left;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.tabs-header-right .tabs li a.tabs-inner {
  display: block;
  text-align: left;
  -moz-border-radius: 0 5px 5px 0;
  -webkit-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
}
.tabs-panels-right {
  float: right;
  border-width: 1px 1px 1px 0;
}
.tabs-panels-left {
  float: left;
  border-width: 1px 0 1px 1px;
}
.tabs-header-noborder,
.tabs-panels-noborder {
  border: 0px;
}
.tabs-header-plain {
  border: 0px;
  background: transparent;
}
.tabs-scroller-left {
  background: #E0ECFF url('images/tabs_icons.png') no-repeat 1px center;
}
.tabs-scroller-right {
  background: #E0ECFF url('images/tabs_icons.png') no-repeat -15px center;
}
.tabs li a.tabs-close {
  background: url('images/tabs_icons.png') no-repeat -34px center;
}
.tabs li a.tabs-inner:hover {
  background: #eaf2ff;
  color: #000000;
  filter: none;
}
.tabs li.tabs-selected a.tabs-inner {
  background-color: #ffffff;
  color: #0E2D5F;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #ffffff 100%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #ffffff 100%);
  background: -o-linear-gradient(top, #EFF5FF 0, #ffffff 100%);
  background: linear-gradient(to bottom, #EFF5FF 0, #ffffff 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#ffffff, GradientType=0);
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(top, #ffffff 0, #EFF5FF 100%);
  background: -moz-linear-gradient(top, #ffffff 0, #EFF5FF 100%);
  background: -o-linear-gradient(top, #ffffff 0, #EFF5FF 100%);
  background: linear-gradient(to bottom, #ffffff 0, #EFF5FF 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#EFF5FF, GradientType=0);
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(left, #EFF5FF 0, #ffffff 100%);
  background: -moz-linear-gradient(left, #EFF5FF 0, #ffffff 100%);
  background: -o-linear-gradient(left, #EFF5FF 0, #ffffff 100%);
  background: linear-gradient(to right, #EFF5FF 0, #ffffff 100%);
  background-repeat: repeat-y;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#ffffff, GradientType=1);
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(left, #ffffff 0, #EFF5FF 100%);
  background: -moz-linear-gradient(left, #ffffff 0, #EFF5FF 100%);
  background: -o-linear-gradient(left, #ffffff 0, #EFF5FF 100%);
  background: linear-gradient(to right, #ffffff 0, #EFF5FF 100%);
  background-repeat: repeat-y;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#EFF5FF, GradientType=1);
}
.tabs li a.tabs-inner {
  color: #0E2D5F;
  background-color: #E0ECFF;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -o-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: linear-gradient(to bottom, #EFF5FF 0, #E0ECFF 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#E0ECFF, GradientType=0);
}
.tabs-header,
.tabs-tool {
  background-color: #E0ECFF;
}
.tabs-header-plain {
  background: transparent;
}
.tabs-header,
.tabs-scroller-left,
.tabs-scroller-right,
.tabs-tool,
.tabs,
.tabs-panels,
.tabs li a.tabs-inner,
.tabs li.tabs-selected a.tabs-inner,
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner,
.tabs-header-left .tabs li.tabs-selected a.tabs-inner,
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-color: #95B8E7;
}
.tabs-p-tool a:hover,
.tabs li a:hover.tabs-close,
.tabs-scroller-over {
  background-color: #eaf2ff;
}
.tabs li.tabs-selected a.tabs-inner {
  border-bottom: 1px solid #ffffff;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  border-top: 1px solid #ffffff;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  border-right: 1px solid #ffffff;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-left: 1px solid #ffffff;
}
.l-btn {
  text-decoration: none;
  display: inline-block;
  margin: 0;
  padding: 0;
  cursor: pointer;
  outline: none;
  text-align: center;
  vertical-align: middle;
}
.l-btn-plain {
  border: 0;
  padding: 1px;
}
.l-btn-disabled {
  color: #ccc;
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
.l-btn-left {
  display: inline-block;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  vertical-align: top;
}
.l-btn-text {
  display: inline-block;
  vertical-align: top;
  width: auto;
  line-height: 24px;
  font-size: 12px;
  padding: 0;
  margin: 0 4px;
}
.l-btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  font-size: 1px;
}
.l-btn span span .l-btn-empty {
  display: inline-block;
  margin: 0;
  width: 16px;
  height: 24px;
  font-size: 1px;
  vertical-align: top;
}
.l-btn span .l-btn-icon-left {
  padding: 0 0 0 20px;
  background-position: left center;
}
.l-btn span .l-btn-icon-right {
  padding: 0 20px 0 0;
  background-position: right center;
}
.l-btn-icon-left .l-btn-text {
  margin: 0 4px 0 24px;
}
.l-btn-icon-left .l-btn-icon {
  left: 4px;
}
.l-btn-icon-right .l-btn-text {
  margin: 0 24px 0 4px;
}
.l-btn-icon-right .l-btn-icon {
  right: 4px;
}
.l-btn-icon-top .l-btn-text {
  margin: 20px 4px 0 4px;
}
.l-btn-icon-top .l-btn-icon {
  top: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-icon-bottom .l-btn-text {
  margin: 0 4px 20px 4px;
}
.l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-left .l-btn-empty {
  margin: 0 4px;
  width: 16px;
}
.l-btn-plain:hover {
  padding: 0;
}
.l-btn-focus {
  outline: #0000FF dotted thin;
}
.l-btn-large .l-btn-text {
  line-height: 40px;
}
.l-btn-large .l-btn-icon {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-top: -16px;
}
.l-btn-large .l-btn-icon-left .l-btn-text {
  margin-left: 40px;
}
.l-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.l-btn-large .l-btn-icon-top .l-btn-text {
  margin-top: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-top .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-left .l-btn-empty {
  margin: 0 4px;
  width: 32px;
}
.l-btn {
  color: #444;
  background: #fafafa;
  border: 1px solid #bbb;
  background: -webkit-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -moz-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -o-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: linear-gradient(to bottom, #ffffff 0, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#eeeeee, GradientType=0);
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn:hover {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  filter: none;
}
.l-btn-plain {
  background: transparent;
  border: 0;
  filter: none;
}
.l-btn-plain:hover {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn-disabled,
.l-btn-disabled:hover {
  background: #fafafa;
  color: #444;
  background: -webkit-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -moz-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -o-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: linear-gradient(to bottom, #ffffff 0, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#eeeeee, GradientType=0);
  filter: alpha(opacity=50) progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#eeeeee, GradientType=0);
}
.l-btn-plain-disabled,
.l-btn-plain-disabled:hover {
  background: transparent;
  filter: alpha(opacity=50);
}
.l-btn-selected,
.l-btn-selected:hover {
  background: #ddd;
  filter: none;
}
.l-btn-plain-selected,
.l-btn-plain-selected:hover {
  background: #ddd;
}
.datagrid .panel-body {
  overflow: hidden;
  position: relative;
}
.datagrid-view {
  position: relative;
  overflow: hidden;
}
.datagrid-view1,
.datagrid-view2 {
  position: absolute;
  overflow: hidden;
  top: 0;
}
.datagrid-view1 {
  left: 0;
}
.datagrid-view2 {
  right: 0;
}
.datagrid-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  filter: alpha(opacity=30);
  display: none;
}
.datagrid-mask-msg {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  padding: 10px 5px 10px 30px;
  width: auto;
  height: 16px;
  border-width: 2px;
  border-style: solid;
  display: none;
}
.datagrid-sort-icon {
  padding: 0;
}
.datagrid-toolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.datagrid .datagrid-pager {
  display: block;
  margin: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.datagrid .datagrid-pager-top {
  border-width: 0 0 1px 0;
}
.datagrid-header {
  overflow: hidden;
  cursor: default;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-header-inner {
  float: left;
  width: 10000px;
}
.datagrid-header-row,
.datagrid-row {
  height: 25px;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-width: 0 1px 1px 0;
  border-style: dotted;
  margin: 0;
  padding: 0;
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  margin: 0;
  padding: 0 4px;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
}
.datagrid-header .datagrid-cell {
  height: auto;
}
.datagrid-header .datagrid-cell span {
  font-size: 12px;
}
.datagrid-cell-group {
  text-align: center;
}
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  width: 25px;
  text-align: center;
  margin: 0;
  padding: 0;
}
.datagrid-body {
  margin: 0;
  padding: 0;
  overflow: auto;
  zoom: 1;
}
.datagrid-view1 .datagrid-body-inner {
  padding-bottom: 20px;
}
.datagrid-view1 .datagrid-body {
  overflow: hidden;
}
.datagrid-footer {
  overflow: hidden;
}
.datagrid-footer-inner {
  border-width: 1px 0 0 0;
  border-style: solid;
  width: 10000px;
  float: left;
}
.datagrid-row-editing .datagrid-cell {
  height: auto;
}
.datagrid-header-check,
.datagrid-cell-check {
  padding: 0;
  width: 27px;
  height: 18px;
  font-size: 1px;
  text-align: center;
  overflow: hidden;
}
.datagrid-header-check input,
.datagrid-cell-check input {
  margin: 0;
  padding: 0;
  width: 15px;
  height: 18px;
}
.datagrid-resize-proxy {
  position: absolute;
  width: 1px;
  height: 10000px;
  top: 0;
  cursor: e-resize;
  display: none;
}
.datagrid-body .datagrid-editable {
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable table {
  width: 100%;
  height: 100%;
}
.datagrid-body .datagrid-editable td {
  border: 0;
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable .datagrid-editable-input {
  margin: 0;
  padding: 2px;
  border-width: 1px;
  border-style: solid;
}
.datagrid-sort .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/lite/datagrid_icons.png') no-repeat -64px center;
}
.datagrid-sort-desc .datagrid-sort-icon {
  padding: 0 13px 0 0;
  background: url('images/lite/datagrid_icons.png') no-repeat -16px center;
}
.datagrid-sort-asc .datagrid-sort-icon {
  padding: 0 13px 0 0;
  background: url('images/lite/datagrid_icons.png') no-repeat 0px center;
}
.datagrid-row-collapse {
  background: url('images/lite/datagrid_icons.png') no-repeat -48px center;
}
.datagrid-row-expand {
  background: url('images/lite/datagrid_icons.png') no-repeat -32px center;
}
.datagrid-mask-msg {
  background: #ffffff url('images/lite/loading.gif') no-repeat scroll 5px center;
}
.datagrid-header,
.datagrid-td-rownumber {
  background-color: #efefef;
  background: -webkit-linear-gradient(top, #F9F9F9 0, #efefef 100%);
  background: -moz-linear-gradient(top, #F9F9F9 0, #efefef 100%);
  background: -o-linear-gradient(top, #F9F9F9 0, #efefef 100%);
  background: linear-gradient(to bottom, #F9F9F9 0, #efefef 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F9F9F9, endColorstr=#efefef, GradientType=0);
}
.datagrid-cell-rownumber {
  color: #000000;
}
.datagrid-resize-proxy {
  background: #aac5e7;
}
.datagrid-mask {
  background: #ccc;
}
.datagrid-mask-msg {
  border-color: #95B8E7;
}
.datagrid-toolbar,
.datagrid-pager {
  background: #F4F4F4;
}
.datagrid-header,
.datagrid-toolbar,
.datagrid-pager,
.datagrid-footer-inner {
  border-color: #dddddd;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #ccc;
}
.datagrid-htable,
.datagrid-btable,
.datagrid-ftable {
  color: #000000;
  border-collapse: separate;
}
.datagrid-row-alt {
  background: #fafafa;
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #eaf2ff;
  color: #000000;
  cursor: default;
}
.datagrid-row-selected {
  background: #ffe48d;
  color: #000000;
}
.datagrid-body .datagrid-editable .datagrid-editable-input {
  border-color: #95B8E7;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  padding-bottom: 1px;
  border-width: 0 1px 0 0;
}
.propertygrid .datagrid-group {
  height: 21px;
  overflow: hidden;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.propertygrid .datagrid-group span {
  font-weight: bold;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  border-color: #dddddd;
}
.propertygrid .datagrid-view1 .datagrid-group {
  border-color: #E0ECFF;
}
.propertygrid .datagrid-view2 .datagrid-group {
  border-color: #dddddd;
}
.propertygrid .datagrid-group,
.propertygrid .datagrid-view1 .datagrid-body,
.propertygrid .datagrid-view1 .datagrid-row-over,
.propertygrid .datagrid-view1 .datagrid-row-selected {
  background: #E0ECFF;
}
.pagination {
  zoom: 1;
}
.pagination table {
  float: left;
  height: 30px;
}
.pagination td {
  border: 0;
}
.pagination-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 3px 1px;
}
.pagination .pagination-num {
  border-width: 1px;
  border-style: solid;
  margin: 0 2px;
  padding: 2px;
  width: 2em;
  height: auto;
}
.pagination-page-list {
  margin: 0px 6px;
  padding: 1px 2px;
  width: auto;
  height: auto;
  border-width: 1px;
  border-style: solid;
}
.pagination-info {
  float: right;
  margin: 0 6px 0 0;
  padding: 0;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
.pagination span {
  font-size: 12px;
}
.pagination-link .l-btn-text {
  width: 24px;
  text-align: center;
  margin: 0;
}
.pagination-first {
  background: url('images/lite/pagination_icons.png') no-repeat 0 center;
}
.pagination-prev {
  background: url('images/lite/pagination_icons.png') no-repeat -16px center;
}
.pagination-next {
  background: url('images/lite/pagination_icons.png') no-repeat -32px center;
}
.pagination-last {
  background: url('images/lite/pagination_icons.png') no-repeat -48px center;
}
.pagination-load {
  background: url('images/lite/pagination_icons.png') no-repeat -64px center;
}
.pagination-loading {
  background: url('images/lite/loading.gif') no-repeat center center;
}
.pagination-page-list,
.pagination .pagination-num {
  border-color: #95B8E7;
}
.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 12px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 12px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 22px;
}
.calendar-title {
  text-align: center;
  height: 22px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 2px;
  padding: 0 3px;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  width: 14px;
  height: 14px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth {
  left: 20px;
  background: url('images/calendar_arrows.png') no-repeat -18px -2px;
}
.calendar-nextmonth {
  right: 20px;
  background: url('images/calendar_arrows.png') no-repeat -34px -2px;
}
.calendar-prevyear {
  left: 3px;
  background: url('images/calendar_arrows.png') no-repeat -1px -2px;
}
.calendar-nextyear {
  right: 3px;
  background: url('images/calendar_arrows.png') no-repeat -49px -2px;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.calendar-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 12px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 40px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  margin: 0;
  padding: 2px;
  font-weight: bold;
  font-size: 12px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 21px;
  height: 21px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 2px 2px;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -45px 2px;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-body th,
.calendar-menu-month {
  color: #4d4d4d;
}
.calendar-day {
  color: #000000;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #95B8E7;
}
.calendar {
  border-color: #95B8E7;
}
.calendar-header {
  background: #E0ECFF;
}
.calendar-body,
.calendar-menu {
  background: #ffffff;
}
.calendar-body th {
  background: #F4F4F4;
  padding: 2px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #eaf2ff;
  color: #000000;
}
.calendar-hover {
  border: 1px solid #b7d2ff;
  padding: 0;
}
.calendar-selected {
  background-color: #ffe48d;
  color: #000000;
  border: 1px solid #ffab3f;
  padding: 0;
}
.datebox-calendar-inner {
  height: 180px;
}
.datebox-button {
  height: 18px;
  padding: 2px 5px;
  text-align: center;
}
.datebox-button a {
  font-size: 12px;
  font-weight: bold;
  text-decoration: none;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.datebox-button a:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.datebox-current,
.datebox-close {
  float: left;
}
.datebox-close {
  float: right;
}
.datebox .combo-arrow {
  background-image: url('images/datebox_arrow.png');
  background-position: center center;
}
.datebox-button {
  background-color: #F4F4F4;
}
.datebox-button a {
  color: #444;
}
.numberbox {
  border: 1px solid #95B8E7;
  margin: 0;
  padding: 0 2px;
  vertical-align: middle;
}
.spinner {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.spinner .spinner-text {
  font-size: 12px;
  border: 0px;
  line-height: 20px;
  height: 20px;
  margin: 0;
  padding: 0 2px;
  *margin-top: -1px;
  *height: 18px;
  *line-height: 18px;
  _height: 18px;
  _line-height: 18px;
  vertical-align: baseline;
}
.spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 0.6;
  filter: alpha(opacity=60);
  display: block;
  font-size: 1px;
  width: 18px;
  height: 10px;
}
.spinner-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-arrow-up {
  background: url('images/spinner_arrows.png') no-repeat 1px center;
}
.spinner-arrow-down {
  background: url('images/spinner_arrows.png') no-repeat -15px center;
}
.spinner {
  border-color: #95B8E7;
}
.spinner-arrow {
  background-color: #E0ECFF;
}
.spinner-arrow-hover {
  background-color: #eaf2ff;
}
.progressbar {
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  overflow: hidden;
  position: relative;
}
.progressbar-text {
  text-align: center;
  position: absolute;
}
.progressbar-value {
  position: relative;
  overflow: hidden;
  width: 0;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.progressbar {
  border-color: #95B8E7;
}
.progressbar-text {
  color: #000000;
  font-size: 12px;
}
.progressbar-value .progressbar-text {
  background-color: #ffe48d;
  color: #000000;
}
.searchbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.searchbox .searchbox-text {
  font-size: 12px;
  border: 0;
  margin: 0;
  padding: 0 2px;
  *margin-top: -1px;
  vertical-align: top;
}
.searchbox .searchbox-prompt {
  font-size: 12px;
  color: #ccc;
}
.searchbox-button {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.searchbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.searchbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox-button {
  background: url('images/searchbox_button.png') no-repeat center center;
}
.searchbox {
  border-color: #95B8E7;
  background-color: #fff;
}
.searchbox .l-btn-plain {
  background: #E0ECFF;
}
.searchbox .l-btn-plain-disabled,
.searchbox .l-btn-plain-disabled:hover {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-h {
  height: 22px;
}
.slider-v {
  width: 22px;
}
.slider-inner {
  position: relative;
  height: 6px;
  top: 7px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
}
.slider-handle {
  position: absolute;
  display: block;
  outline: none;
  width: 20px;
  height: 20px;
  top: -7px;
  margin-left: -10px;
}
.slider-tip {
  position: absolute;
  display: inline-block;
  line-height: 12px;
  font-size: 12px;
  white-space: nowrap;
  top: -22px;
}
.slider-rule {
  position: relative;
  top: 15px;
}
.slider-rule span {
  position: absolute;
  display: inline-block;
  font-size: 0;
  height: 5px;
  border-width: 0 0 0 1px;
  border-style: solid;
}
.slider-rulelabel {
  position: relative;
  top: 20px;
}
.slider-rulelabel span {
  position: absolute;
  display: inline-block;
  font-size: 12px;
}
.slider-v .slider-inner {
  width: 6px;
  left: 7px;
  top: 0;
  float: left;
}
.slider-v .slider-handle {
  left: 3px;
  margin-top: -10px;
}
.slider-v .slider-tip {
  left: -10px;
  margin-top: -6px;
}
.slider-v .slider-rule {
  float: left;
  top: 0;
  left: 16px;
}
.slider-v .slider-rule span {
  width: 5px;
  height: 'auto';
  border-left: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.slider-v .slider-rulelabel {
  float: left;
  top: 0;
  left: 23px;
}
.slider-handle {
  background: url('images/slider_handle.png') no-repeat;
}
.slider-inner {
  border-color: #95B8E7;
  background: #E0ECFF;
}
.slider-rule span {
  border-color: #95B8E7;
}
.slider-rulelabel span {
  color: #000000;
}
.menu {
  position: absolute;
  margin: 0;
  padding: 2px;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
}
.menu-item {
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.menu-text {
  height: 20px;
  line-height: 20px;
  float: left;
  padding-left: 28px;
}
.menu-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 2px;
  top: 50%;
  margin-top: -8px;
}
.menu-rightarrow {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 0;
  top: 50%;
  margin-top: -8px;
}
.menu-line {
  position: absolute;
  left: 26px;
  top: 0;
  height: 2000px;
  font-size: 1px;
}
.menu-sep {
  margin: 3px 0px 3px 25px;
  font-size: 1px;
}
.menu-active {
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.menu-item-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
.menu-text,
.menu-text span {
  font-size: 12px;
}
.menu-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #ccc;
  -moz-box-shadow: 2px 2px 3px #cccccc;
  -webkit-box-shadow: 2px 2px 3px #cccccc;
  box-shadow: 2px 2px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2, MakeShadow=false, ShadowOpacity=0.2);
}
.menu-rightarrow {
  background: url('images/lite/menu_arrows.png') no-repeat -32px center;
}
.menu-line {
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
}
.menu-sep {
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #fff;
}
.menu {
  background-color: #fafafa;
  border-color: #ddd;
  color: #444;
}
.menu-content {
  background: #ffffff;
}
.menu-item {
  border-color: transparent;
  _border-color: #fafafa;
}
.menu-active {
  border-color: #b7d2ff;
  color: #000000;
  background: #eaf2ff;
}
.menu-active-disabled {
  border-color: transparent;
  background: transparent;
  color: #444;
}
.m-btn-downarrow,
.s-btn-downarrow {
  display: inline-block;
  position: absolute;
  width: 16px;
  height: 16px;
  font-size: 1px;
  right: 0;
  top: 50%;
  margin-top: -8px;
}
.m-btn-active,
.s-btn-active {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  filter: none;
}
.m-btn-plain-active,
.s-btn-plain-active {
  background: transparent;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.m-btn .l-btn-left .l-btn-text {
  margin-right: 20px;
}
.m-btn .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.m-btn .l-btn-icon-right .l-btn-icon {
  right: 20px;
}
.m-btn .l-btn-icon-top .l-btn-text {
  margin-right: 4px;
  margin-bottom: 14px;
}
.m-btn .l-btn-icon-bottom .l-btn-text {
  margin-right: 4px;
  margin-bottom: 34px;
}
.m-btn .l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 20px;
}
.m-btn .l-btn-icon-top .m-btn-downarrow,
.m-btn .l-btn-icon-bottom .m-btn-downarrow {
  top: auto;
  bottom: 0px;
  left: 50%;
  margin-left: -8px;
}
.m-btn-line {
  display: inline-block;
  position: absolute;
  font-size: 1px;
  display: none;
}
.m-btn .l-btn-left .m-btn-line {
  right: 0;
  width: 16px;
  height: 500px;
  border-style: solid;
  border-color: #aac5e7;
  border-width: 0 0 0 1px;
}
.m-btn .l-btn-icon-top .m-btn-line,
.m-btn .l-btn-icon-bottom .m-btn-line {
  left: 0;
  bottom: 0;
  width: 500px;
  height: 16px;
  border-width: 1px 0 0 0;
}
.m-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 56px;
}
.m-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 50px;
}
.m-btn-downarrow,
.s-btn-downarrow {
  background: url('images/lite/menu_arrows.png') no-repeat 0 center;
}
.m-btn-plain-active,
.s-btn-plain-active {
  border-color: #b7d2ff;
  background-color: #eaf2ff;
  color: #000000;
}
.s-btn:hover .m-btn-line,
.s-btn-active .m-btn-line,
.s-btn-plain-active .m-btn-line {
  display: inline-block;
}
.l-btn:hover .s-btn-downarrow,
.s-btn-active .s-btn-downarrow,
.s-btn-plain-active .s-btn-downarrow {
  border-style: solid;
  border-color: #aac5e7;
  border-width: 0 0 0 1px;
}
.messager-body {
  padding: 10px;
  overflow: hidden;
}
.messager-button {
  text-align: center;
  padding-top: 10px;
}
.messager-button .l-btn {
  width: 70px;
}
.messager-icon {
  float: left;
  width: 32px;
  height: 32px;
  margin: 0 10px 10px 0;
}
.messager-error {
  background: url('images/messager_icons.png') no-repeat scroll -64px 0;
}
.messager-info {
  background: url('images/messager_icons.png') no-repeat scroll 0 0;
}
.messager-question {
  background: url('images/messager_icons.png') no-repeat scroll -32px 0;
}
.messager-warning {
  background: url('images/messager_icons.png') no-repeat scroll -96px 0;
}
.messager-progress {
  padding: 10px;
}
.messager-p-msg {
  margin-bottom: 5px;
}
.messager-body .messager-input {
  width: 100%;
  padding: 1px 0;
  border: 1px solid #95B8E7;
}
.tree {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.tree li {
  white-space: nowrap;
}
.tree li ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.tree-node {
  height: 18px;
  white-space: nowrap;
  cursor: pointer;
}
.tree-hit {
  cursor: pointer;
}
.tree-expanded,
.tree-collapsed,
.tree-folder,
.tree-file,
.tree-checkbox,
.tree-indent {
  display: inline-block;
  width: 16px;
  height: 18px;
  vertical-align: top;
  overflow: hidden;
}
.tree-expanded {
  background: url('images/lite/tree_icons.png') no-repeat -18px 0px;
}
.tree-expanded-hover {
  background: url('images/lite/tree_icons.png') no-repeat -50px 0px;
}
.tree-collapsed {
  background: url('images/lite/tree_icons.png') no-repeat 0px 0px;
}
.tree-collapsed-hover {
  background: url('images/lite/tree_icons.png') no-repeat -32px 0px;
}
.tree-lines .tree-expanded,
.tree-lines .tree-root-first .tree-expanded {
  background: url('images/lite/tree_icons.png') no-repeat -144px 0;
}
.tree-lines .tree-collapsed,
.tree-lines .tree-root-first .tree-collapsed {
  background: url('images/lite/tree_icons.png') no-repeat -128px 0;
}
.tree-lines .tree-node-last .tree-expanded,
.tree-lines .tree-root-one .tree-expanded {
  background: url('images/lite/tree_icons.png') no-repeat -80px 0;
}
.tree-lines .tree-node-last .tree-collapsed,
.tree-lines .tree-root-one .tree-collapsed {
  background: url('images/lite/tree_icons.png') no-repeat -64px 0;
}
.tree-line {
  background: url('images/lite/tree_icons.png') no-repeat -176px 0;
}
.tree-join {
  background: url('images/lite/tree_icons.png') no-repeat -192px 0;
}
.tree-joinbottom {
  background: url('images/lite/tree_icons.png') no-repeat -160px 0;
}
.tree-folder {
  background: url('images/lite/tree_icons.png') no-repeat -208px 0;
}
.tree-folder-open {
  background: url('images/lite/tree_icons.png') no-repeat -224px 0;
}
.tree-file {
  background: url('images/lite/tree_icons.png') no-repeat -240px 0;
}
.tree-loading {
  background: url('images/loading.gif') no-repeat center center;
}
.tree-checkbox0 {
  background: url('images/lite/tree_icons.png') no-repeat -208px -18px;
}
.tree-checkbox1 {
  background: url('images/lite/tree_icons.png') no-repeat -224px -18px;
}
.tree-checkbox2 {
  background: url('images/lite/tree_icons.png') no-repeat -240px -18px;
}
.tree-title {
  font-size: 12px;
  display: inline-block;
  text-decoration: none;
  vertical-align: top;
  white-space: nowrap;
  padding: 0 2px;
  height: 18px;
  line-height: 18px;
}
.tree-node-proxy {
  font-size: 12px;
  line-height: 20px;
  padding: 0 2px 0 20px;
  border-width: 1px;
  border-style: solid;
  z-index: 9900000;
}
.tree-dnd-icon {
  display: inline-block;
  position: absolute;
  width: 16px;
  height: 18px;
  left: 2px;
  top: 50%;
  margin-top: -9px;
}
.tree-dnd-yes {
  background: url('images/tree_icons.png') no-repeat -256px 0;
}
.tree-dnd-no {
  background: url('images/tree_icons.png') no-repeat -256px -18px;
}
.tree-node-top {
  border-top: 1px dotted red;
}
.tree-node-bottom {
  border-bottom: 1px dotted red;
}
.tree-node-append .tree-title {
  border: 1px dotted red;
}
.tree-editor {
  border: 1px solid #ccc;
  font-size: 12px;
  height: 14px !important;
  height: 18px;
  line-height: 14px;
  padding: 1px 2px;
  width: 80px;
  position: absolute;
  top: 0;
}
.tree-node-proxy {
  background-color: #ffffff;
  color: #000000;
  border-color: #95B8E7;
}
.tree-node-hover {
  background: #eaf2ff;
  color: #000000;
}
.tree-node-selected {
  background: #ffe48d;
  color: #000000;
}
.validatebox-invalid {
  background-image: url('images/validatebox_warning.png');
  background-repeat: no-repeat;
  background-position: right center;
  border-color: #ffa8a8;
  background-color: #fff3f3;
  color: #000;
}
.tooltip {
  position: absolute;
  display: none;
  z-index: 9900000;
  outline: none;
  opacity: 1;
  filter: alpha(opacity=100);
  padding: 5px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.tooltip-content {
  font-size: 12px;
}
.tooltip-arrow-outer,
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 0;
  border-style: solid;
  border-width: 6px;
  border-color: transparent;
  _border-color: tomato;
  _filter: chroma(color=tomato);
}
.tooltip-right .tooltip-arrow-outer {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -13px;
}
.tooltip-right .tooltip-arrow {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -12px;
}
.tooltip-left .tooltip-arrow-outer {
  right: 0;
  top: 50%;
  margin: -6px -13px 0 0;
}
.tooltip-left .tooltip-arrow {
  right: 0;
  top: 50%;
  margin: -6px -12px 0 0;
}
.tooltip-top .tooltip-arrow-outer {
  bottom: 0;
  left: 50%;
  margin: 0 0 -13px -6px;
}
.tooltip-top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin: 0 0 -12px -6px;
}
.tooltip-bottom .tooltip-arrow-outer {
  top: 0;
  left: 50%;
  margin: -13px 0 0 -6px;
}
.tooltip-bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin: -12px 0 0 -6px;
}
.tooltip {
  background-color: #ffffff;
  border-color: #95B8E7;
  color: #000000;
}
.tooltip-right .tooltip-arrow-outer {
  border-right-color: #95B8E7;
}
.tooltip-right .tooltip-arrow {
  border-right-color: #ffffff;
}
.tooltip-left .tooltip-arrow-outer {
  border-left-color: #95B8E7;
}
.tooltip-left .tooltip-arrow {
  border-left-color: #ffffff;
}
.tooltip-top .tooltip-arrow-outer {
  border-top-color: #95B8E7;
}
.tooltip-top .tooltip-arrow {
  border-top-color: #ffffff;
}
.tooltip-bottom .tooltip-arrow-outer {
  border-bottom-color: #95B8E7;
}
.tooltip-bottom .tooltip-arrow {
  border-bottom-color: #ffffff;
}
@font-face {
  font-family: 'Mw_mifonts';
  src: url('../fonts/Mw_mifonts.eot');
  /* IE9 Compat Modes*/
  src: url('../fonts/Mw_mifonts.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */ url('../fonts/Mw_mifonts.woff') format('woff'), /* Modern Browsers */ url('../fonts/Mw_mifonts.woff2') format('woff2'), url('../fonts/Mw_mifonts.ttf') format('truetype');
  /* Safari, Android, iOS */
}
.icon {
  font: normal normal normal 16px/1 Mw_mifonts;
}
.icon:before {
  display: inline-block;
  font-size: 16px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-right-arrow,
.icon-arrow-blue,
.icon-run-red {
  font-size: 14px;
}
/* 其他颜色图标定义 */
.icon-gray-edit:before {
  color: #C7C7CC;
}
.icon-gear-gray:before {
  color: #C7C7CC;
}
.icon-refresh-gray:before {
  color: #C7C7CC;
}
.icon-triangle-gray-right:before {
  color: #C7C7CC;
}
.icon-paper-pay-gray:before {
  color: #C7C7CC;
}
.icon-two-recta-gear-gray:before {
  color: #C7C7CC;
}
.icon-paper-blue-add-gray:before {
  color: #C7C7CC;
}
.icon-box-red-add-gray:before {
  color: #C7C7CC;
}
.icon-report-eye-gray:before {
  color: #C7C7CC;
}
.icon-clock-pen-gray:before {
  color: #C7C7CC;
}
.icon-arrow-le-bo-gray:before {
  color: #C7C7CC;
}
.icon-blue-edit:before,
.icon-tip-blue:before,
.icon-help:before,
.icon-date:before {
  color: #339EFF;
}
.icon-star-yellow:before,
.icon-star-light-yellow:before {
  color: #FFB300;
}
.icon-star-orange-body:before {
  color: #F68300;
}
.icon-star:before,
.icon-star-half:before,
.icon-star-empty:before {
  color: #FF3D3D;
}
.icon-alert-red:before,
.icon-dustbin-red:before {
  color: #FF6356;
}
.icon-ca-green:before {
  color: #12AA2C;
}
/*
tree-icon tree-file icon-drug时显示drug图标
tree-icon tree-file显示默认的文件图标
以下代码为file设置成字体图片,导致tree线条不对，最终在icon-drug上增加background:none;来实现树节点图片显示问题 [3127716]
*/
/*create white icon*/
/*white icon*/
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-add,.icon-w-add,.icon-w-new,.icon-w-plus {
  text-align: center;
  background: none;
}
.icon-add:before,.icon-w-add:before,.icon-w-new:before,.icon-w-plus:before {
  font-family: Mw_mifonts;
  content: "\f001";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-edit,.icon-write-order,.icon-w-edit,.icon-gray-edit,.icon-paper-pen-blue {
  text-align: center;
  background: none;
}
.icon-edit:before,.icon-write-order:before,.icon-w-edit:before,.icon-gray-edit:before,.icon-paper-pen-blue:before {
  font-family: Mw_mifonts;
  content: "\f002";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-blue-edit {
  text-align: center;
  background: none;
}
.icon-blue-edit:before {
  font-family: Mw_mifonts;
  content: "\f002";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel,.icon-w-cancel,.icon-w-close {
  text-align: center;
  background: none;
}
.icon-cancel:before,.icon-w-cancel:before,.icon-w-close:before {
  font-family: Mw_mifonts;
  content: "\f003";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-dustbin-red {
  text-align: center;
  background: none;
}
.icon-dustbin-red:before {
  font-family: Mw_mifonts;
  content: "\f003";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cut,.icon-cut-blue {
  text-align: center;
  background: none;
}
.icon-cut:before,.icon-cut-blue:before {
  font-family: Mw_mifonts;
  content: "\f004";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-copy,.icon-w-copy,.icon-copy-blue {
  text-align: center;
  background: none;
}
.icon-copy:before,.icon-w-copy:before,.icon-copy-blue:before {
  font-family: Mw_mifonts;
  content: "\f005";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paste {
  text-align: center;
  background: none;
}
.icon-paste:before {
  font-family: Mw_mifonts;
  content: "\f006";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-save,.icon-w-save,.icon-big-save {
  text-align: center;
  background: none;
}
.icon-save:before,.icon-w-save:before,.icon-big-save:before {
  font-family: Mw_mifonts;
  content: "\f007";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-print,.icon-w-print,.icon-big-print {
  text-align: center;
  background: none;
}
.icon-print:before,.icon-w-print:before,.icon-big-print:before {
  font-family: Mw_mifonts;
  content: "\f008";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-arrow-top,.icon-w-arrow-up,.icon-top-green,.icon-up {
  text-align: center;
  background: none;
}
.icon-arrow-top:before,.icon-w-arrow-up:before,.icon-top-green:before,.icon-up:before {
  font-family: Mw_mifonts;
  content: "\f009";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-arrow-bottom,.icon-w-arrow-down,.icon-down-blue,.icon-down {
  text-align: center;
  background: none;
}
.icon-arrow-bottom:before,.icon-w-arrow-down:before,.icon-down-blue:before,.icon-down:before {
  font-family: Mw_mifonts;
  content: "\f010";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-arrow-left,.icon-w-arrow-left {
  text-align: center;
  background: none;
}
.icon-arrow-left:before,.icon-w-arrow-left:before {
  font-family: Mw_mifonts;
  content: "\f011";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-arrow-right,.icon-w-arrow-right {
  text-align: center;
  background: none;
}
.icon-arrow-right:before,.icon-w-arrow-right:before {
  font-family: Mw_mifonts;
  content: "\f012";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-remove,.icon-big-del {
  text-align: center;
  background: none;
}
.icon-remove:before,.icon-big-del:before {
  font-family: Mw_mifonts;
  content: "\f013";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-reset,.icon-w-reset {
  text-align: center;
  background: none;
}
.icon-reset:before,.icon-w-reset:before {
  font-family: Mw_mifonts;
  content: "\f014";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-omega,.icon-big-omega {
  text-align: center;
  background: none;
}
.icon-omega:before,.icon-big-omega:before {
  font-family: Mw_mifonts;
  content: "\f015";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-epr {
  text-align: center;
  background: none;
}
.icon-w-epr:before {
  font-family: Mw_mifonts;
  content: "\f016";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-clean {
  text-align: center;
  background: none;
}
.icon-w-clean:before {
  font-family: Mw_mifonts;
  content: "\f017";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-find,.icon-find,.icon-search {
  text-align: center;
  background: none;
}
.icon-w-find:before,.icon-find:before,.icon-search:before {
  font-family: Mw_mifonts;
  content: "\f018";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-other {
  text-align: center;
  background: none;
}
.icon-w-other:before {
  font-family: Mw_mifonts;
  content: "\f019";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-back {
  text-align: center;
  background: none;
}
.icon-w-back:before {
  font-family: Mw_mifonts;
  content: "\f020";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-card,.icon-w-card {
  text-align: center;
  background: none;
}
.icon-card:before,.icon-w-card:before {
  font-family: Mw_mifonts;
  content: "\f021";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-list {
  text-align: center;
  background: none;
}
.icon-w-list:before {
  font-family: Mw_mifonts;
  content: "\f022";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-switch,.icon-big-switch,.icon-transfer {
  text-align: center;
  background: none;
}
.icon-w-switch:before,.icon-big-switch:before,.icon-transfer:before {
  font-family: Mw_mifonts;
  content: "\f023";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-cal {
  text-align: center;
  background: none;
}
.icon-w-cal:before {
  font-family: Mw_mifonts;
  content: "\f024";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-date {
  text-align: center;
  background: none;
}
.icon-date:before {
  font-family: Mw_mifonts;
  content: "\f024";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-config,.icon-gear-gray {
  text-align: center;
  background: none;
}
.icon-w-config:before,.icon-gear-gray:before {
  font-family: Mw_mifonts;
  content: "\f025";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-home,.icon-house,.icon-big-home,.icon-home-black,.icon-home-gray {
  text-align: center;
  background: none;
}
.icon-w-home:before,.icon-house:before,.icon-big-home:before,.icon-home-black:before,.icon-home-gray:before {
  font-family: Mw_mifonts;
  content: "\f026";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-export,.icon-export {
  text-align: center;
  background: none;
}
.icon-w-export:before,.icon-export:before {
  font-family: Mw_mifonts;
  content: "\f027";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-import,.icon-import {
  text-align: center;
  background: none;
}
.icon-w-import:before,.icon-import:before {
  font-family: Mw_mifonts;
  content: "\f028";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-update,.icon-refresh-gray,.icon-reload,.icon-big-refresh {
  text-align: center;
  background: none;
}
.icon-w-update:before,.icon-refresh-gray:before,.icon-reload:before,.icon-big-refresh:before {
  font-family: Mw_mifonts;
  content: "\f029";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-file,.icon-folder,.icon-file {
  text-align: center;
  background: none;
}
.icon-w-file:before,.icon-folder:before,.icon-file:before {
  font-family: Mw_mifonts;
  content: "\f030";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-file-open,.icon-file-open {
  text-align: center;
  background: none;
}
.icon-w-file-open:before,.icon-file-open:before {
  font-family: Mw_mifonts;
  content: "\f031";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-star-empty {
  text-align: center;
  background: none;
}
.icon-star-empty:before {
  font-family: Mw_mifonts;
  content: "\f032";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-star,.icon-star-orange-border,.icon-big-favorite {
  text-align: center;
  background: none;
}
.icon-w-star:before,.icon-star-orange-border:before,.icon-big-favorite:before {
  font-family: Mw_mifonts;
  content: "\f032";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-paper,.icon-paper-info {
  text-align: center;
  background: none;
}
.icon-w-paper:before,.icon-paper-info:before {
  font-family: Mw_mifonts;
  content: "\f033";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-star-yellow,.icon-star-light-yellow,.icon-star,.icon-star-orange-body {
  text-align: center;
  background: none;
}
.icon-star-yellow:before,.icon-star-light-yellow:before,.icon-star:before,.icon-star-orange-body:before {
  font-family: Mw_mifonts;
  content: "\f034";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-star-half {
  text-align: center;
  background: none;
}
.icon-star-half:before {
  font-family: Mw_mifonts;
  content: "\f035";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-msg,.icon-msg-unread {
  text-align: center;
  background: none;
}
.icon-w-msg:before,.icon-msg-unread:before {
  font-family: Mw_mifonts;
  content: "\f036";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-stamp,.icon-stamp,.icon-big-stamp {
  text-align: center;
  background: none;
}
.icon-w-stamp:before,.icon-stamp:before,.icon-big-stamp:before {
  font-family: Mw_mifonts;
  content: "\f037";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-batch-cfg,.icon-batch-cfg {
  text-align: center;
  background: none;
}
.icon-w-batch-cfg:before,.icon-batch-cfg:before {
  font-family: Mw_mifonts;
  content: "\f038";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-inv {
  text-align: center;
  background: none;
}
.icon-w-inv:before {
  font-family: Mw_mifonts;
  content: "\f039";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-batch-add,.icon-batch-add {
  text-align: center;
  background: none;
}
.icon-w-batch-add:before,.icon-batch-add:before {
  font-family: Mw_mifonts;
  content: "\f040";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-eye,.icon-eye,.icon-green-line-eye,.icon-big-open-eye {
  text-align: center;
  background: none;
}
.icon-w-eye:before,.icon-eye:before,.icon-green-line-eye:before,.icon-big-open-eye:before {
  font-family: Mw_mifonts;
  content: "\f041";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-calc {
  text-align: center;
  background: none;
}
.icon-w-calc:before {
  font-family: Mw_mifonts;
  content: "\f042";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-submit,.icon-submit {
  text-align: center;
  background: none;
}
.icon-w-submit:before,.icon-submit:before {
  font-family: Mw_mifonts;
  content: "\f043";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-clock,.icon-clock,.icon-clock-blod,.icon-clock-black,.icon-w-clock-ora,.icon-clock-orange {
  text-align: center;
  background: none;
}
.icon-w-clock:before,.icon-clock:before,.icon-clock-blod:before,.icon-clock-black:before,.icon-w-clock-ora:before,.icon-clock-orange:before {
  font-family: Mw_mifonts;
  content: "\f044";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-rent {
  text-align: center;
  background: none;
}
.icon-w-rent:before {
  font-family: Mw_mifonts;
  content: "\f045";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-run,.icon-run,.icon-big-start,.icon-arrow-right-top {
  text-align: center;
  background: none;
}
.icon-w-run:before,.icon-run:before,.icon-big-start:before,.icon-arrow-right-top:before {
  font-family: Mw_mifonts;
  content: "\f046";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-line-key {
  text-align: center;
  background: none;
}
.icon-w-line-key:before {
  font-family: Mw_mifonts;
  content: "\f047";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-takes {
  text-align: center;
  background: none;
}
.icon-w-takes:before {
  font-family: Mw_mifonts;
  content: "\f048";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-key,.icon-key {
  text-align: center;
  background: none;
}
.icon-w-key:before,.icon-key:before {
  font-family: Mw_mifonts;
  content: "\f049";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-img,.icon-img,.icon-miss-img,.icon-big-img,.icon-big-miss-img,.icon-img-blue {
  text-align: center;
  background: none;
}
.icon-w-img:before,.icon-img:before,.icon-miss-img:before,.icon-big-img:before,.icon-big-miss-img:before,.icon-img-blue:before {
  font-family: Mw_mifonts;
  content: "\f050";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-setting {
  text-align: center;
  background: none;
}
.icon-w-setting:before {
  font-family: Mw_mifonts;
  content: "\f051";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-ok,.icon-ok,.icon-done,.icon-accept {
  text-align: center;
  background: none;
}
.icon-w-ok:before,.icon-ok:before,.icon-done:before,.icon-accept:before {
  font-family: Mw_mifonts;
  content: "\f052";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-filter,.icon-filter {
  text-align: center;
  background: none;
}
.icon-w-filter:before,.icon-filter:before {
  font-family: Mw_mifonts;
  content: "\f053";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-predrug,.icon-drug {
  text-align: center;
  background: none;
}
.icon-w-predrug:before,.icon-drug:before {
  font-family: Mw_mifonts;
  content: "\f054";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-trigger-box {
  text-align: center;
  background: none;
}
.icon-w-trigger-box:before {
  font-family: Mw_mifonts;
  content: "\f055";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-paid,.icon-fee {
  text-align: center;
  background: none;
}
.icon-w-paid:before,.icon-fee:before {
  font-family: Mw_mifonts;
  content: "\f056";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-zoom,.icon-arrow-zoom {
  text-align: center;
  background: none;
}
.icon-w-zoom:before,.icon-arrow-zoom:before {
  font-family: Mw_mifonts;
  content: "\f057";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-book {
  text-align: center;
  background: none;
}
.icon-w-book:before {
  font-family: Mw_mifonts;
  content: "\f058";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-upload,.icon-upload {
  text-align: center;
  background: none;
}
.icon-w-upload:before,.icon-upload:before {
  font-family: Mw_mifonts;
  content: "\f059";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-pen-paper {
  text-align: center;
  background: none;
}
.icon-w-pen-paper:before {
  font-family: Mw_mifonts;
  content: "\f060";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-volume-up {
  text-align: center;
  background: none;
}
.icon-w-volume-up:before {
  font-family: Mw_mifonts;
  content: "\f061";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-download,.icon-download {
  text-align: center;
  background: none;
}
.icon-w-download:before,.icon-download:before {
  font-family: Mw_mifonts;
  content: "\f062";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-pause-circle,.icon-pause {
  text-align: center;
  background: none;
}
.icon-w-pause-circle:before,.icon-pause:before {
  font-family: Mw_mifonts;
  content: "\f063";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-add-item {
  text-align: center;
  background: none;
}
.icon-add-item:before {
  font-family: Mw_mifonts;
  content: "\f064";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-help {
  text-align: center;
  background: none;
}
.icon-help:before {
  font-family: Mw_mifonts;
  content: "\f065";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-sum {
  text-align: center;
  background: none;
}
.icon-sum:before {
  font-family: Mw_mifonts;
  content: "\f066";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tip,.icon-big-tip {
  text-align: center;
  background: none;
}
.icon-tip:before,.icon-big-tip:before {
  font-family: Mw_mifonts;
  content: "\f067";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tip-blue {
  text-align: center;
  background: none;
}
.icon-tip-blue:before {
  font-family: Mw_mifonts;
  content: "\f067";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bed {
  text-align: center;
  background: none;
}
.icon-bed:before {
  font-family: Mw_mifonts;
  content: "\f068";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-house-maint {
  text-align: center;
  background: none;
}
.icon-house-maint:before {
  font-family: Mw_mifonts;
  content: "\f069";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-house-posi-maint {
  text-align: center;
  background: none;
}
.icon-house-posi-maint:before {
  font-family: Mw_mifonts;
  content: "\f070";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-person,.icon-user {
  text-align: center;
  background: none;
}
.icon-person:before,.icon-user:before {
  font-family: Mw_mifonts;
  content: "\f071";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-person-key-yel {
  text-align: center;
  background: none;
}
.icon-person-key-yel:before {
  font-family: Mw_mifonts;
  content: "\f072";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-key-switch {
  text-align: center;
  background: none;
}
.icon-key-switch:before {
  font-family: Mw_mifonts;
  content: "\f073";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-report-switch {
  text-align: center;
  background: none;
}
.icon-report-switch:before {
  font-family: Mw_mifonts;
  content: "\f074";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-report-blue-shie-key {
  text-align: center;
  background: none;
}
.icon-report-blue-shie-key:before {
  font-family: Mw_mifonts;
  content: "\f075";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-patient,.icon-outhosp-patient {
  text-align: center;
  background: none;
}
.icon-patient:before,.icon-outhosp-patient:before {
  font-family: Mw_mifonts;
  content: "\f076";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stethoscope,.icon-big-med-equi {
  text-align: center;
  background: none;
}
.icon-stethoscope:before,.icon-big-med-equi:before {
  font-family: Mw_mifonts;
  content: "\f077";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-uncheckin {
  text-align: center;
  background: none;
}
.icon-uncheckin:before {
  font-family: Mw_mifonts;
  content: "\f078";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-checkin,.icon-finish-report {
  text-align: center;
  background: none;
}
.icon-checkin:before,.icon-finish-report:before {
  font-family: Mw_mifonts;
  content: "\f079";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-end-adm {
  text-align: center;
  background: none;
}
.icon-end-adm:before {
  font-family: Mw_mifonts;
  content: "\f080";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-change-loc {
  text-align: center;
  background: none;
}
.icon-change-loc:before {
  font-family: Mw_mifonts;
  content: "\f081";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-doctor,.icon-big-doctor-adm {
  text-align: center;
  background: none;
}
.icon-doctor:before,.icon-big-doctor-adm:before {
  font-family: Mw_mifonts;
  content: "\f082";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-clear-screen,.icon-big-clear {
  text-align: center;
  background: none;
}
.icon-clear-screen:before,.icon-big-clear:before {
  font-family: Mw_mifonts;
  content: "\f083";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-read-card,.icon-big-card-reader,.icon-big-read-card {
  text-align: center;
  background: none;
}
.icon-read-card:before,.icon-big-card-reader:before,.icon-big-read-card:before {
  font-family: Mw_mifonts;
  content: "\f084";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-update {
  text-align: center;
  background: none;
}
.icon-update:before {
  font-family: Mw_mifonts;
  content: "\f085";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-upload-cloud {
  text-align: center;
  background: none;
}
.icon-upload-cloud:before {
  font-family: Mw_mifonts;
  content: "\f086";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-unload-cloud {
  text-align: center;
  background: none;
}
.icon-unload-cloud:before {
  font-family: Mw_mifonts;
  content: "\f087";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-other,.icon-other-yellow {
  text-align: center;
  background: none;
}
.icon-other:before,.icon-other-yellow:before {
  font-family: Mw_mifonts;
  content: "\f088";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-patient-info,.icon-pat-info {
  text-align: center;
  background: none;
}
.icon-patient-info:before,.icon-pat-info:before {
  font-family: Mw_mifonts;
  content: "\f089";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-del-diag {
  text-align: center;
  background: none;
}
.icon-del-diag:before {
  font-family: Mw_mifonts;
  content: "\f090";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-add-diag,.icon-adm-add {
  text-align: center;
  background: none;
}
.icon-add-diag:before,.icon-adm-add:before {
  font-family: Mw_mifonts;
  content: "\f091";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-emr-cri,.icon-book {
  text-align: center;
  background: none;
}
.icon-emr-cri:before,.icon-book:before {
  font-family: Mw_mifonts;
  content: "\f092";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-funnel-eye {
  text-align: center;
  background: none;
}
.icon-funnel-eye:before {
  font-family: Mw_mifonts;
  content: "\f093";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-funnel-half {
  text-align: center;
  background: none;
}
.icon-funnel-half:before {
  font-family: Mw_mifonts;
  content: "\f094";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-funnel-empty {
  text-align: center;
  background: none;
}
.icon-funnel-empty:before {
  font-family: Mw_mifonts;
  content: "\f095";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-funnel-on {
  text-align: center;
  background: none;
}
.icon-funnel-on:before {
  font-family: Mw_mifonts;
  content: "\f096";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-omega_1 {
  text-align: center;
  background: none;
}
.icon-omega_1:before {
  font-family: Mw_mifonts;
  content: "\f097";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-eye-deepgrade,.icon-big-eye-deepgrade {
  text-align: center;
  background: none;
}
.icon-eye-deepgrade:before,.icon-big-eye-deepgrade:before {
  font-family: Mw_mifonts;
  content: "\f098";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-fee-arrow,.icon-big-fee-arrow {
  text-align: center;
  background: none;
}
.icon-fee-arrow:before,.icon-big-fee-arrow:before {
  font-family: Mw_mifonts;
  content: "\f099";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-all-select {
  text-align: center;
  background: none;
}
.icon-all-select:before {
  font-family: Mw_mifonts;
  content: "\f100";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-all-unselect {
  text-align: center;
  background: none;
}
.icon-all-unselect:before {
  font-family: Mw_mifonts;
  content: "\f101";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-select-grant {
  text-align: center;
  background: none;
}
.icon-select-grant:before {
  font-family: Mw_mifonts;
  content: "\f102";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel-select-grant {
  text-align: center;
  background: none;
}
.icon-cancel-select-grant:before {
  font-family: Mw_mifonts;
  content: "\f103";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-refuse-select-grant {
  text-align: center;
  background: none;
}
.icon-refuse-select-grant:before {
  font-family: Mw_mifonts;
  content: "\f104";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-double-quotes {
  text-align: center;
  background: none;
}
.icon-double-quotes:before {
  font-family: Mw_mifonts;
  content: "\f105";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-import-xls {
  text-align: center;
  background: none;
}
.icon-import-xls:before {
  font-family: Mw_mifonts;
  content: "\f106";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-init {
  text-align: center;
  background: none;
}
.icon-init:before {
  font-family: Mw_mifonts;
  content: "\f107";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-inv-search {
  text-align: center;
  background: none;
}
.icon-inv-search:before {
  font-family: Mw_mifonts;
  content: "\f108";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-chart-sum {
  text-align: center;
  background: none;
}
.icon-chart-sum:before {
  font-family: Mw_mifonts;
  content: "\f109";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-chart-year {
  text-align: center;
  background: none;
}
.icon-chart-year:before {
  font-family: Mw_mifonts;
  content: "\f110";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-attachment {
  text-align: center;
  background: none;
}
.icon-attachment:before {
  font-family: Mw_mifonts;
  content: "\f111";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-import-reset {
  text-align: center;
  background: none;
}
.icon-import-reset:before {
  font-family: Mw_mifonts;
  content: "\f112";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mnypaper-cfg {
  text-align: center;
  background: none;
}
.icon-mnypaper-cfg:before {
  font-family: Mw_mifonts;
  content: "\f113";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mnypaper-run {
  text-align: center;
  background: none;
}
.icon-mnypaper-run:before {
  font-family: Mw_mifonts;
  content: "\f114";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mnypaper-ok {
  text-align: center;
  background: none;
}
.icon-mnypaper-ok:before {
  font-family: Mw_mifonts;
  content: "\f115";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mnypaper-no {
  text-align: center;
  background: none;
}
.icon-mnypaper-no:before {
  font-family: Mw_mifonts;
  content: "\f116";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mnypaper-down,.icon-mnypaper-down2 {
  text-align: center;
  background: none;
}
.icon-mnypaper-down:before,.icon-mnypaper-down2:before {
  font-family: Mw_mifonts;
  content: "\f117";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stamp-cancel {
  text-align: center;
  background: none;
}
.icon-stamp-cancel:before {
  font-family: Mw_mifonts;
  content: "\f118";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stamp-pass {
  text-align: center;
  background: none;
}
.icon-stamp-pass:before {
  font-family: Mw_mifonts;
  content: "\f119";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-line-paid {
  text-align: center;
  background: none;
}
.icon-line-paid:before {
  font-family: Mw_mifonts;
  content: "\f120";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-split {
  text-align: center;
  background: none;
}
.icon-split:before {
  font-family: Mw_mifonts;
  content: "\f121";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-alert-red {
  text-align: center;
  background: none;
}
.icon-alert-red:before {
  font-family: Mw_mifonts;
  content: "\f122";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-alert,.icon-big-alert {
  text-align: center;
  background: none;
}
.icon-alert:before,.icon-big-alert:before {
  font-family: Mw_mifonts;
  content: "\f122";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-money-down {
  text-align: center;
  background: none;
}
.icon-money-down:before {
  font-family: Mw_mifonts;
  content: "\f123";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-max-no {
  text-align: center;
  background: none;
}
.icon-max-no:before {
  font-family: Mw_mifonts;
  content: "\f124";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-excel {
  text-align: center;
  background: none;
}
.icon-excel:before {
  font-family: Mw_mifonts;
  content: "\f125";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-save-to {
  text-align: center;
  background: none;
}
.icon-save-to:before {
  font-family: Mw_mifonts;
  content: "\f126";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-save-sure {
  text-align: center;
  background: none;
}
.icon-save-sure:before {
  font-family: Mw_mifonts;
  content: "\f127";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-export-all {
  text-align: center;
  background: none;
}
.icon-export-all:before {
  font-family: Mw_mifonts;
  content: "\f128";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-set-col {
  text-align: center;
  background: none;
}
.icon-set-col:before {
  font-family: Mw_mifonts;
  content: "\f129";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-template {
  text-align: center;
  background: none;
}
.icon-template:before {
  font-family: Mw_mifonts;
  content: "\f130";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-adjust-inventory {
  text-align: center;
  background: none;
}
.icon-adjust-inventory:before {
  font-family: Mw_mifonts;
  content: "\f131";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel-money {
  text-align: center;
  background: none;
}
.icon-cancel-money:before {
  font-family: Mw_mifonts;
  content: "\f132";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-set-zero {
  text-align: center;
  background: none;
}
.icon-set-zero:before {
  font-family: Mw_mifonts;
  content: "\f133";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-accept-money {
  text-align: center;
  background: none;
}
.icon-accept-money:before {
  font-family: Mw_mifonts;
  content: "\f134";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-show-set {
  text-align: center;
  background: none;
}
.icon-show-set:before {
  font-family: Mw_mifonts;
  content: "\f135";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-camera,.icon-w-camera {
  text-align: center;
  background: none;
}
.icon-camera:before,.icon-w-camera:before {
  font-family: Mw_mifonts;
  content: "\f136";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-write {
  text-align: center;
  background: none;
}
.icon-pat-write:before {
  font-family: Mw_mifonts;
  content: "\f137";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-fx {
  text-align: center;
  background: none;
}
.icon-fx:before {
  font-family: Mw_mifonts;
  content: "\f138";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-muti-key {
  text-align: center;
  background: none;
}
.icon-muti-key:before {
  font-family: Mw_mifonts;
  content: "\f139";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-forbid,.icon-no {
  text-align: center;
  background: none;
}
.icon-forbid:before,.icon-no:before {
  font-family: Mw_mifonts;
  content: "\f140";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ignore {
  text-align: center;
  background: none;
}
.icon-ignore:before {
  font-family: Mw_mifonts;
  content: "\f141";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-export-data,.icon-export-paper {
  text-align: center;
  background: none;
}
.icon-export-data:before,.icon-export-paper:before {
  font-family: Mw_mifonts;
  content: "\f142";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-re-ignore {
  text-align: center;
  background: none;
}
.icon-re-ignore:before {
  font-family: Mw_mifonts;
  content: "\f143";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-unuse,.icon-big-stop {
  text-align: center;
  background: none;
}
.icon-unuse:before,.icon-big-stop:before {
  font-family: Mw_mifonts;
  content: "\f144";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-arrow-up {
  text-align: center;
  background: none;
}
.icon-arrow-up:before {
  font-family: Mw_mifonts;
  content: "\f145";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-person-seal {
  text-align: center;
  background: none;
}
.icon-person-seal:before {
  font-family: Mw_mifonts;
  content: "\f146";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-scanning,.icon-w-scan-code {
  text-align: center;
  background: none;
}
.icon-scanning:before,.icon-w-scan-code:before {
  font-family: Mw_mifonts;
  content: "\f147";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-return,.icon-big-slide-return {
  text-align: center;
  background: none;
}
.icon-return:before,.icon-big-slide-return:before {
  font-family: Mw_mifonts;
  content: "\f148";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-doc-caseload {
  text-align: center;
  background: none;
}
.icon-doc-caseload:before {
  font-family: Mw_mifonts;
  content: "\f149";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-snowflake-blue {
  text-align: center;
  background: none;
}
.icon-snowflake-blue:before {
  font-family: Mw_mifonts;
  content: "\f150";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-speci-mt {
  text-align: center;
  background: none;
}
.icon-speci-mt:before {
  font-family: Mw_mifonts;
  content: "\f151";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-print-box,.icon-big-print-box {
  text-align: center;
  background: none;
}
.icon-print-box:before,.icon-big-print-box:before {
  font-family: Mw_mifonts;
  content: "\f152";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tube-add {
  text-align: center;
  background: none;
}
.icon-tube-add:before {
  font-family: Mw_mifonts;
  content: "\f153";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tube-del {
  text-align: center;
  background: none;
}
.icon-tube-del:before {
  font-family: Mw_mifonts;
  content: "\f154";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-wrench-blue,.icon-big-maint {
  text-align: center;
  background: none;
}
.icon-wrench-blue:before,.icon-big-maint:before {
  font-family: Mw_mifonts;
  content: "\f155";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-gen {
  text-align: center;
  background: none;
}
.icon-gen:before {
  font-family: Mw_mifonts;
  content: "\f156";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-submit {
  text-align: center;
  background: none;
}
.icon-paper-submit:before {
  font-family: Mw_mifonts;
  content: "\f157";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-open-book {
  text-align: center;
  background: none;
}
.icon-open-book:before {
  font-family: Mw_mifonts;
  content: "\f158";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-track {
  text-align: center;
  background: none;
}
.icon-track:before {
  font-family: Mw_mifonts;
  content: "\f159";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-eye,.icon-big-book-eye {
  text-align: center;
  background: none;
}
.icon-paper-eye:before,.icon-big-book-eye:before {
  font-family: Mw_mifonts;
  content: "\f160";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-run-red,.icon-arrow-blue {
  text-align: center;
  background: none;
}
.icon-run-red:before,.icon-arrow-blue:before {
  font-family: Mw_mifonts;
  content: "\f161";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-compare-no {
  text-align: center;
  background: none;
}
.icon-compare-no:before {
  font-family: Mw_mifonts;
  content: "\f162";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-compare-yes {
  text-align: center;
  background: none;
}
.icon-compare-yes:before {
  font-family: Mw_mifonts;
  content: "\f163";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-compare {
  text-align: center;
  background: none;
}
.icon-compare:before {
  font-family: Mw_mifonts;
  content: "\f164";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-arrow,.icon-big-paper-arrow {
  text-align: center;
  background: none;
}
.icon-paper-arrow:before,.icon-big-paper-arrow:before {
  font-family: Mw_mifonts;
  content: "\f165";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-adm-same {
  text-align: center;
  background: none;
}
.icon-adm-same:before {
  font-family: Mw_mifonts;
  content: "\f166";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-all-screen {
  text-align: center;
  background: none;
}
.icon-all-screen:before {
  font-family: Mw_mifonts;
  content: "\f167";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-analysis {
  text-align: center;
  background: none;
}
.icon-analysis:before {
  font-family: Mw_mifonts;
  content: "\f168";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-audit-x {
  text-align: center;
  background: none;
}
.icon-audit-x:before {
  font-family: Mw_mifonts;
  content: "\f169";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-base-info,.icon-big-idcard {
  text-align: center;
  background: none;
}
.icon-base-info:before,.icon-big-idcard:before {
  font-family: Mw_mifonts;
  content: "\f170";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-blue-drug-ok {
  text-align: center;
  background: none;
}
.icon-blue-drug-ok:before {
  font-family: Mw_mifonts;
  content: "\f171";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-blue-move {
  text-align: center;
  background: none;
}
.icon-blue-move:before {
  font-family: Mw_mifonts;
  content: "\f172";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-book-green {
  text-align: center;
  background: none;
}
.icon-book-green:before {
  font-family: Mw_mifonts;
  content: "\f173";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bottle-drug {
  text-align: center;
  background: none;
}
.icon-bottle-drug:before {
  font-family: Mw_mifonts;
  content: "\f174";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cal-pen {
  text-align: center;
  background: none;
}
.icon-cal-pen:before {
  font-family: Mw_mifonts;
  content: "\f175";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel-ref {
  text-align: center;
  background: none;
}
.icon-cancel-ref:before {
  font-family: Mw_mifonts;
  content: "\f176";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel-top {
  text-align: center;
  background: none;
}
.icon-cancel-top:before {
  font-family: Mw_mifonts;
  content: "\f177";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-check,.icon-checkbox,.icon-big-blue-frame-ok {
  text-align: center;
  background: none;
}
.icon-check:before,.icon-checkbox:before,.icon-big-blue-frame-ok:before {
  font-family: Mw_mifonts;
  content: "\f178";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-chg-doctor-grant,.icon-doctor-green-pen {
  text-align: center;
  background: none;
}
.icon-chg-doctor-grant:before,.icon-doctor-green-pen:before {
  font-family: Mw_mifonts;
  content: "\f179";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-clock-record {
  text-align: center;
  background: none;
}
.icon-clock-record:before {
  font-family: Mw_mifonts;
  content: "\f180";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-barbell {
  text-align: center;
  background: none;
}
.icon-barbell:before {
  font-family: Mw_mifonts;
  content: "\f181";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-target-arrow {
  text-align: center;
  background: none;
}
.icon-target-arrow:before {
  font-family: Mw_mifonts;
  content: "\f182";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-contain {
  text-align: center;
  background: none;
}
.icon-contain:before {
  font-family: Mw_mifonts;
  content: "\f183";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-doctor-green-no {
  text-align: center;
  background: none;
}
.icon-doctor-green-no:before {
  font-family: Mw_mifonts;
  content: "\f184";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-down-arrow-box {
  text-align: center;
  background: none;
}
.icon-down-arrow-box:before {
  font-family: Mw_mifonts;
  content: "\f185";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-drug-arrow-red {
  text-align: center;
  background: none;
}
.icon-drug-arrow-red:before {
  font-family: Mw_mifonts;
  content: "\f186";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-drug-audit {
  text-align: center;
  background: none;
}
.icon-drug-audit:before {
  font-family: Mw_mifonts;
  content: "\f187";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-drug-clock {
  text-align: center;
  background: none;
}
.icon-drug-clock:before {
  font-family: Mw_mifonts;
  content: "\f188";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-drug-link,.icon-durg-ref {
  text-align: center;
  background: none;
}
.icon-drug-link:before,.icon-durg-ref:before {
  font-family: Mw_mifonts;
  content: "\f189";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-dsh-water {
  text-align: center;
  background: none;
}
.icon-dsh-water:before {
  font-family: Mw_mifonts;
  content: "\f190";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-durg-freq {
  text-align: center;
  background: none;
}
.icon-durg-freq:before {
  font-family: Mw_mifonts;
  content: "\f191";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-face-red {
  text-align: center;
  background: none;
}
.icon-face-red:before {
  font-family: Mw_mifonts;
  content: "\f192";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-find-adm {
  text-align: center;
  background: none;
}
.icon-find-adm:before {
  font-family: Mw_mifonts;
  content: "\f193";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-fire {
  text-align: center;
  background: none;
}
.icon-fire:before {
  font-family: Mw_mifonts;
  content: "\f194";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-format-line {
  text-align: center;
  background: none;
}
.icon-format-line:before {
  font-family: Mw_mifonts;
  content: "\f195";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-format-line-dott {
  text-align: center;
  background: none;
}
.icon-format-line-dott:before {
  font-family: Mw_mifonts;
  content: "\f196";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-format-line-num {
  text-align: center;
  background: none;
}
.icon-format-line-num:before {
  font-family: Mw_mifonts;
  content: "\f197";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-gen-barcode {
  text-align: center;
  background: none;
}
.icon-gen-barcode:before {
  font-family: Mw_mifonts;
  content: "\f198";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-icd {
  text-align: center;
  background: none;
}
.icon-icd:before {
  font-family: Mw_mifonts;
  content: "\f199";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-injector {
  text-align: center;
  background: none;
}
.icon-injector:before {
  font-family: Mw_mifonts;
  content: "\f200";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-knw-submit {
  text-align: center;
  background: none;
}
.icon-knw-submit:before {
  font-family: Mw_mifonts;
  content: "\f201";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-location,.icon-big-position {
  text-align: center;
  background: none;
}
.icon-location:before,.icon-big-position:before {
  font-family: Mw_mifonts;
  content: "\f202";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-lock {
  text-align: center;
  background: none;
}
.icon-lock:before {
  font-family: Mw_mifonts;
  content: "\f203";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-move-left-most {
  text-align: center;
  background: none;
}
.icon-move-left-most:before {
  font-family: Mw_mifonts;
  content: "\f204";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-move-up-most {
  text-align: center;
  background: none;
}
.icon-move-up-most:before {
  font-family: Mw_mifonts;
  content: "\f205";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mtpaper-add {
  text-align: center;
  background: none;
}
.icon-mtpaper-add:before {
  font-family: Mw_mifonts;
  content: "\f206";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mtpaper-arrw-lftp {
  text-align: center;
  background: none;
}
.icon-mtpaper-arrw-lftp:before {
  font-family: Mw_mifonts;
  content: "\f207";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mtpaper-redo {
  text-align: center;
  background: none;
}
.icon-mtpaper-redo:before {
  font-family: Mw_mifonts;
  content: "\f208";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mtpaper-undo {
  text-align: center;
  background: none;
}
.icon-mtpaper-undo:before {
  font-family: Mw_mifonts;
  content: "\f209";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mttext {
  text-align: center;
  background: none;
}
.icon-mttext:before {
  font-family: Mw_mifonts;
  content: "\f210";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mutpaper-tri {
  text-align: center;
  background: none;
}
.icon-mutpaper-tri:before {
  font-family: Mw_mifonts;
  content: "\f211";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mutpaper-x {
  text-align: center;
  background: none;
}
.icon-mutpaper-x:before {
  font-family: Mw_mifonts;
  content: "\f212";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-nail {
  text-align: center;
  background: none;
}
.icon-nail:before {
  font-family: Mw_mifonts;
  content: "\f213";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-no-conatin {
  text-align: center;
  background: none;
}
.icon-no-conatin:before {
  font-family: Mw_mifonts;
  content: "\f214";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-bed {
  text-align: center;
  background: none;
}
.icon-paper-bed:before {
  font-family: Mw_mifonts;
  content: "\f215";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper,.icon-paper-blue-line,.icon-paper-table {
  text-align: center;
  background: none;
}
.icon-paper:before,.icon-paper-blue-line:before,.icon-paper-table:before {
  font-family: Mw_mifonts;
  content: "\f216";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-chart {
  text-align: center;
  background: none;
}
.icon-paper-chart:before {
  font-family: Mw_mifonts;
  content: "\f217";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-drug {
  text-align: center;
  background: none;
}
.icon-paper-drug:before {
  font-family: Mw_mifonts;
  content: "\f218";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-link-pen {
  text-align: center;
  background: none;
}
.icon-paper-link-pen:before {
  font-family: Mw_mifonts;
  content: "\f219";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-new {
  text-align: center;
  background: none;
}
.icon-paper-new:before {
  font-family: Mw_mifonts;
  content: "\f220";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-no {
  text-align: center;
  background: none;
}
.icon-paper-no:before {
  font-family: Mw_mifonts;
  content: "\f221";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-ok {
  text-align: center;
  background: none;
}
.icon-paper-ok:before {
  font-family: Mw_mifonts;
  content: "\f222";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-opr-record {
  text-align: center;
  background: none;
}
.icon-paper-opr-record:before {
  font-family: Mw_mifonts;
  content: "\f223";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-save {
  text-align: center;
  background: none;
}
.icon-paper-save:before {
  font-family: Mw_mifonts;
  content: "\f224";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-upgrade,.icon-paper-upgrade-add {
  text-align: center;
  background: none;
}
.icon-paper-upgrade:before,.icon-paper-upgrade-add:before {
  font-family: Mw_mifonts;
  content: "\f225";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-x {
  text-align: center;
  background: none;
}
.icon-paper-x:before {
  font-family: Mw_mifonts;
  content: "\f226";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-add-red {
  text-align: center;
  background: none;
}
.icon-pat-add-red:before {
  font-family: Mw_mifonts;
  content: "\f227";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-alert-red,.icon-pat-alert-yellow {
  text-align: center;
  background: none;
}
.icon-pat-alert-red:before,.icon-pat-alert-yellow:before {
  font-family: Mw_mifonts;
  content: "\f228";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-house {
  text-align: center;
  background: none;
}
.icon-pat-house:before {
  font-family: Mw_mifonts;
  content: "\f229";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-house-switch {
  text-align: center;
  background: none;
}
.icon-pat-house-switch:before {
  font-family: Mw_mifonts;
  content: "\f230";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-opr {
  text-align: center;
  background: none;
}
.icon-pat-opr:before {
  font-family: Mw_mifonts;
  content: "\f231";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-radio,.icon-big-blue-white-circle {
  text-align: center;
  background: none;
}
.icon-radio:before,.icon-big-blue-white-circle:before {
  font-family: Mw_mifonts;
  content: "\f232";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ref {
  text-align: center;
  background: none;
}
.icon-ref:before {
  font-family: Mw_mifonts;
  content: "\f233";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-repeat-drug {
  text-align: center;
  background: none;
}
.icon-repeat-drug:before {
  font-family: Mw_mifonts;
  content: "\f234";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-right-arrow {
  text-align: center;
  background: none;
}
.icon-right-arrow:before {
  font-family: Mw_mifonts;
  content: "\f235";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-switch {
  text-align: center;
  background: none;
}
.icon-switch:before {
  font-family: Mw_mifonts;
  content: "\f236";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tel {
  text-align: center;
  background: none;
}
.icon-tel:before {
  font-family: Mw_mifonts;
  content: "\f237";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-text {
  text-align: center;
  background: none;
}
.icon-text:before {
  font-family: Mw_mifonts;
  content: "\f238";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-trans-pat {
  text-align: center;
  background: none;
}
.icon-trans-pat:before {
  font-family: Mw_mifonts;
  content: "\f239";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tube {
  text-align: center;
  background: none;
}
.icon-tube:before {
  font-family: Mw_mifonts;
  content: "\f240";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-unlock,.icon-big-unlock {
  text-align: center;
  background: none;
}
.icon-unlock:before,.icon-big-unlock:before {
  font-family: Mw_mifonts;
  content: "\f241";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-virus {
  text-align: center;
  background: none;
}
.icon-virus:before {
  font-family: Mw_mifonts;
  content: "\f242";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-virus-drug {
  text-align: center;
  background: none;
}
.icon-virus-drug:before {
  font-family: Mw_mifonts;
  content: "\f243";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-water-drop {
  text-align: center;
  background: none;
}
.icon-water-drop:before {
  font-family: Mw_mifonts;
  content: "\f244";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-yellow-qa {
  text-align: center;
  background: none;
}
.icon-yellow-qa:before {
  font-family: Mw_mifonts;
  content: "\f245";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-export-report {
  text-align: center;
  background: none;
}
.icon-export-report:before {
  font-family: Mw_mifonts;
  content: "\f246";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-add-report {
  text-align: center;
  background: none;
}
.icon-add-report:before {
  font-family: Mw_mifonts;
  content: "\f247";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-take-report {
  text-align: center;
  background: none;
}
.icon-take-report:before {
  font-family: Mw_mifonts;
  content: "\f248";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-plane {
  text-align: center;
  background: none;
}
.icon-paper-plane:before {
  font-family: Mw_mifonts;
  content: "\f249";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-undo-paper-plane {
  text-align: center;
  background: none;
}
.icon-undo-paper-plane:before {
  font-family: Mw_mifonts;
  content: "\f250";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-h24-stat {
  text-align: center;
  background: none;
}
.icon-h24-stat:before {
  font-family: Mw_mifonts;
  content: "\f251";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stat {
  text-align: center;
  background: none;
}
.icon-stat:before {
  font-family: Mw_mifonts;
  content: "\f252";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-wax-stat {
  text-align: center;
  background: none;
}
.icon-wax-stat:before {
  font-family: Mw_mifonts;
  content: "\f253";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-wax-tat-stat {
  text-align: center;
  background: none;
}
.icon-wax-tat-stat:before {
  font-family: Mw_mifonts;
  content: "\f254";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-stat {
  text-align: center;
  background: none;
}
.icon-paper-stat:before {
  font-family: Mw_mifonts;
  content: "\f255";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-slice-stat {
  text-align: center;
  background: none;
}
.icon-slice-stat:before {
  font-family: Mw_mifonts;
  content: "\f256";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-slice-tat-stat {
  text-align: center;
  background: none;
}
.icon-slice-tat-stat:before {
  font-family: Mw_mifonts;
  content: "\f257";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-slice-only {
  text-align: center;
  background: none;
}
.icon-slice-only:before {
  font-family: Mw_mifonts;
  content: "\f258";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-sample-stat {
  text-align: center;
  background: none;
}
.icon-sample-stat:before {
  font-family: Mw_mifonts;
  content: "\f259";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-pen {
  text-align: center;
  background: none;
}
.icon-paper-pen:before {
  font-family: Mw_mifonts;
  content: "\f260";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-wating {
  text-align: center;
  background: none;
}
.icon-wating:before {
  font-family: Mw_mifonts;
  content: "\f261";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-user-black {
  text-align: center;
  background: none;
}
.icon-user-black:before {
  font-family: Mw_mifonts;
  content: "\f262";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-report-check-black {
  text-align: center;
  background: none;
}
.icon-report-check-black:before {
  font-family: Mw_mifonts;
  content: "\f263";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-up-gray {
  text-align: center;
  background: none;
}
.icon-up-gray:before {
  font-family: Mw_mifonts;
  content: "\f264";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-down-gray {
  text-align: center;
  background: none;
}
.icon-down-gray:before {
  font-family: Mw_mifonts;
  content: "\f265";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-tooth,.icon-big-tooth {
  text-align: center;
  background: none;
}
.icon-tooth:before,.icon-big-tooth:before {
  font-family: Mw_mifonts;
  content: "\f266";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paid,.icon-big-paid {
  text-align: center;
  background: none;
}
.icon-paid:before,.icon-big-paid:before {
  font-family: Mw_mifonts;
  content: "\f267";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-return-paid {
  text-align: center;
  background: none;
}
.icon-return-paid:before {
  font-family: Mw_mifonts;
  content: "\f268";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-int-bill {
  text-align: center;
  background: none;
}
.icon-int-bill:before {
  font-family: Mw_mifonts;
  content: "\f269";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel-int-bill {
  text-align: center;
  background: none;
}
.icon-cancel-int-bill:before {
  font-family: Mw_mifonts;
  content: "\f270";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-reprint-inv {
  text-align: center;
  background: none;
}
.icon-reprint-inv:before {
  font-family: Mw_mifonts;
  content: "\f271";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-print-inv {
  text-align: center;
  background: none;
}
.icon-print-inv:before {
  font-family: Mw_mifonts;
  content: "\f272";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-rebill {
  text-align: center;
  background: none;
}
.icon-rebill:before {
  font-family: Mw_mifonts;
  content: "\f273";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pat-fee-det {
  text-align: center;
  background: none;
}
.icon-pat-fee-det:before {
  font-family: Mw_mifonts;
  content: "\f274";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-find-paid-det {
  text-align: center;
  background: none;
}
.icon-find-paid-det:before {
  font-family: Mw_mifonts;
  content: "\f275";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-find-ord-det {
  text-align: center;
  background: none;
}
.icon-find-ord-det:before {
  font-family: Mw_mifonts;
  content: "\f276";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-find-fee-itm {
  text-align: center;
  background: none;
}
.icon-find-fee-itm:before {
  font-family: Mw_mifonts;
  content: "\f277";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-skip-no,.icon-big-skip-no,.icon-w-skip-no {
  text-align: center;
  background: none;
}
.icon-skip-no:before,.icon-big-skip-no:before,.icon-w-skip-no:before {
  font-family: Mw_mifonts;
  content: "\f278";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-injector-water {
  text-align: center;
  background: none;
}
.icon-injector-water:before {
  font-family: Mw_mifonts;
  content: "\f279";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-alert-pen {
  text-align: center;
  background: none;
}
.icon-alert-pen:before {
  font-family: Mw_mifonts;
  content: "\f280";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-board-alert {
  text-align: center;
  background: none;
}
.icon-board-alert:before {
  font-family: Mw_mifonts;
  content: "\f281";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-minus {
  text-align: center;
  background: none;
}
.icon-minus:before {
  font-family: Mw_mifonts;
  content: "\f282";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-alarm,.icon-big-alert-yellow {
  text-align: center;
  background: none;
}
.icon-alarm:before,.icon-big-alert-yellow:before {
  font-family: Mw_mifonts;
  content: "\f283";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ip-cfg {
  text-align: center;
  background: none;
}
.icon-ip-cfg:before {
  font-family: Mw_mifonts;
  content: "\f284";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-have-son-node {
  text-align: center;
  background: none;
}
.icon-have-son-node:before {
  font-family: Mw_mifonts;
  content: "\f285";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mater-info {
  text-align: center;
  background: none;
}
.icon-mater-info:before {
  font-family: Mw_mifonts;
  content: "\f286";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-send-msg {
  text-align: center;
  background: none;
}
.icon-send-msg:before {
  font-family: Mw_mifonts;
  content: "\f287";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-price-maint {
  text-align: center;
  background: none;
}
.icon-price-maint:before {
  font-family: Mw_mifonts;
  content: "\f288";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ga-maint {
  text-align: center;
  background: none;
}
.icon-ga-maint:before {
  font-family: Mw_mifonts;
  content: "\f289";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-qua-pro-sort {
  text-align: center;
  background: none;
}
.icon-qua-pro-sort:before {
  font-family: Mw_mifonts;
  content: "\f290";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-change-pay-way {
  text-align: center;
  background: none;
}
.icon-change-pay-way:before {
  font-family: Mw_mifonts;
  content: "\f291";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-qua-pro-dis {
  text-align: center;
  background: none;
}
.icon-qua-pro-dis:before {
  font-family: Mw_mifonts;
  content: "\f292";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-qua-pro-blue {
  text-align: center;
  background: none;
}
.icon-qua-pro-blue:before {
  font-family: Mw_mifonts;
  content: "\f293";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-rectangle-flow {
  text-align: center;
  background: none;
}
.icon-rectangle-flow:before {
  font-family: Mw_mifonts;
  content: "\f294";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-switch {
  text-align: center;
  background: none;
}
.icon-paper-switch:before {
  font-family: Mw_mifonts;
  content: "\f295";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bell-blue-no {
  text-align: center;
  background: none;
}
.icon-bell-blue-no:before {
  font-family: Mw_mifonts;
  content: "\f296";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bell-blue,.icon-bell-yellow,.icon-big-msg {
  text-align: center;
  background: none;
}
.icon-bell-blue:before,.icon-bell-yellow:before,.icon-big-msg:before {
  font-family: Mw_mifonts;
  content: "\f297";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-have-message {
  text-align: center;
  background: none;
}
.icon-have-message:before {
  font-family: Mw_mifonts;
  content: "\f298";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-three-cuboid-green {
  text-align: center;
  background: none;
}
.icon-three-cuboid-green:before {
  font-family: Mw_mifonts;
  content: "\f299";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-print-arr-bo,.icon-print-arr-bo-gray {
  text-align: center;
  background: none;
}
.icon-print-arr-bo:before,.icon-print-arr-bo-gray:before {
  font-family: Mw_mifonts;
  content: "\f300";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-triangle-gray-right,.icon-triangle-green-right {
  text-align: center;
  background: none;
}
.icon-triangle-gray-right:before,.icon-triangle-green-right:before {
  font-family: Mw_mifonts;
  content: "\f301";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-triangle-green-left,.icon-triangle-gray-left {
  text-align: center;
  background: none;
}
.icon-triangle-green-left:before,.icon-triangle-gray-left:before {
  font-family: Mw_mifonts;
  content: "\f302";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-pay,.icon-paper-pay-gray {
  text-align: center;
  background: none;
}
.icon-paper-pay:before,.icon-paper-pay-gray:before {
  font-family: Mw_mifonts;
  content: "\f303";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-compu-torus,.icon-compu-torus-gray {
  text-align: center;
  background: none;
}
.icon-compu-torus:before,.icon-compu-torus-gray:before {
  font-family: Mw_mifonts;
  content: "\f304";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-two-recta-gear,.icon-two-recta-gear-gray {
  text-align: center;
  background: none;
}
.icon-two-recta-gear:before,.icon-two-recta-gear-gray:before {
  font-family: Mw_mifonts;
  content: "\f305";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-compu-run,.icon-compu-run-gray {
  text-align: center;
  background: none;
}
.icon-compu-run:before,.icon-compu-run-gray:before {
  font-family: Mw_mifonts;
  content: "\f306";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-blue-add,.icon-paper-blue-add-gray {
  text-align: center;
  background: none;
}
.icon-paper-blue-add:before,.icon-paper-blue-add-gray:before {
  font-family: Mw_mifonts;
  content: "\f307";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-trian-recta-right,.icon-big-next,.icon-trian-recta-right-gray {
  text-align: center;
  background: none;
}
.icon-trian-recta-right:before,.icon-big-next:before,.icon-trian-recta-right-gray:before {
  font-family: Mw_mifonts;
  content: "\f308";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-box-red-add,.icon-box-red-add-gray {
  text-align: center;
  background: none;
}
.icon-box-red-add:before,.icon-box-red-add-gray:before {
  font-family: Mw_mifonts;
  content: "\f309";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-green-chart,.icon-gray-chart {
  text-align: center;
  background: none;
}
.icon-green-chart:before,.icon-gray-chart:before {
  font-family: Mw_mifonts;
  content: "\f310";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-report-eye,.icon-report-eye-gray {
  text-align: center;
  background: none;
}
.icon-report-eye:before,.icon-report-eye-gray:before {
  font-family: Mw_mifonts;
  content: "\f311";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-trian-recta-left,.icon-big-pre,.icon-trian-recta-left-gray {
  text-align: center;
  background: none;
}
.icon-trian-recta-left:before,.icon-big-pre:before,.icon-trian-recta-left-gray:before {
  font-family: Mw_mifonts;
  content: "\f312";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-clock-pen,.icon-clock-pen-gray {
  text-align: center;
  background: none;
}
.icon-clock-pen:before,.icon-clock-pen-gray:before {
  font-family: Mw_mifonts;
  content: "\f313";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-drug-eye,.icon-drug-eye-gray {
  text-align: center;
  background: none;
}
.icon-drug-eye:before,.icon-drug-eye-gray:before {
  font-family: Mw_mifonts;
  content: "\f314";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-herb-back {
  text-align: center;
  background: none;
}
.icon-herb-back:before {
  font-family: Mw_mifonts;
  content: "\f315";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-herb-pre {
  text-align: center;
  background: none;
}
.icon-herb-pre:before {
  font-family: Mw_mifonts;
  content: "\f316";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-herb-next {
  text-align: center;
  background: none;
}
.icon-herb-next:before {
  font-family: Mw_mifonts;
  content: "\f317";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-herb-no {
  text-align: center;
  background: none;
}
.icon-herb-no:before {
  font-family: Mw_mifonts;
  content: "\f318";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-herb-ok {
  text-align: center;
  background: none;
}
.icon-herb-ok:before {
  font-family: Mw_mifonts;
  content: "\f319";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-decoct-herb {
  text-align: center;
  background: none;
}
.icon-decoct-herb:before {
  font-family: Mw_mifonts;
  content: "\f320";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-decoct-change {
  text-align: center;
  background: none;
}
.icon-decoct-change:before {
  font-family: Mw_mifonts;
  content: "\f321";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-eye-r {
  text-align: center;
  background: none;
}
.icon-paper-eye-r:before {
  font-family: Mw_mifonts;
  content: "\f322";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-pre {
  text-align: center;
  background: none;
}
.icon-paper-pre:before {
  font-family: Mw_mifonts;
  content: "\f323";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-no-dot {
  text-align: center;
  background: none;
}
.icon-no-dot:before {
  font-family: Mw_mifonts;
  content: "\f324";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-abort-order {
  text-align: center;
  background: none;
}
.icon-abort-order:before {
  font-family: Mw_mifonts;
  content: "\f325";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cancel-order {
  text-align: center;
  background: none;
}
.icon-cancel-order:before {
  font-family: Mw_mifonts;
  content: "\f326";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-copy-drug {
  text-align: center;
  background: none;
}
.icon-copy-drug:before {
  font-family: Mw_mifonts;
  content: "\f327";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-copy-prn {
  text-align: center;
  background: none;
}
.icon-copy-prn:before {
  font-family: Mw_mifonts;
  content: "\f328";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-copy-sos {
  text-align: center;
  background: none;
}
.icon-copy-sos:before {
  font-family: Mw_mifonts;
  content: "\f329";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-exe-order {
  text-align: center;
  background: none;
}
.icon-exe-order:before {
  font-family: Mw_mifonts;
  content: "\f330";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-replace-order {
  text-align: center;
  background: none;
}
.icon-replace-order:before {
  font-family: Mw_mifonts;
  content: "\f331";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stop-order {
  text-align: center;
  background: none;
}
.icon-stop-order:before {
  font-family: Mw_mifonts;
  content: "\f332";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-write-order,.icon-big-paper-pen {
  text-align: center;
  background: none;
}
.icon-write-order:before,.icon-big-paper-pen:before {
  font-family: Mw_mifonts;
  content: "\f333";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-add-note {
  text-align: center;
  background: none;
}
.icon-add-note:before {
  font-family: Mw_mifonts;
  content: "\f334";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-apply-adm {
  text-align: center;
  background: none;
}
.icon-apply-adm:before {
  font-family: Mw_mifonts;
  content: "\f335";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-apply-check {
  text-align: center;
  background: none;
}
.icon-apply-check:before {
  font-family: Mw_mifonts;
  content: "\f336";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-apply-opr {
  text-align: center;
  background: none;
}
.icon-apply-opr:before {
  font-family: Mw_mifonts;
  content: "\f337";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-check-reg {
  text-align: center;
  background: none;
}
.icon-check-reg:before {
  font-family: Mw_mifonts;
  content: "\f338";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-cfg {
  text-align: center;
  background: none;
}
.icon-paper-cfg:before {
  font-family: Mw_mifonts;
  content: "\f339";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-tri {
  text-align: center;
  background: none;
}
.icon-paper-tri:before {
  font-family: Mw_mifonts;
  content: "\f340";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-resort {
  text-align: center;
  background: none;
}
.icon-resort:before {
  font-family: Mw_mifonts;
  content: "\f341";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-save-tmpl {
  text-align: center;
  background: none;
}
.icon-save-tmpl:before {
  font-family: Mw_mifonts;
  content: "\f342";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-stamp {
  text-align: center;
  background: none;
}
.icon-paper-stamp:before {
  font-family: Mw_mifonts;
  content: "\f343";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-money {
  text-align: center;
  background: none;
}
.icon-paper-money:before {
  font-family: Mw_mifonts;
  content: "\f344";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-arrow-down {
  text-align: center;
  background: none;
}
.icon-paper-arrow-down:before {
  font-family: Mw_mifonts;
  content: "\f345";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-arrow-up {
  text-align: center;
  background: none;
}
.icon-paper-arrow-up:before {
  font-family: Mw_mifonts;
  content: "\f346";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-link {
  text-align: center;
  background: none;
}
.icon-paper-link:before {
  font-family: Mw_mifonts;
  content: "\f347";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-set-paper {
  text-align: center;
  background: none;
}
.icon-set-paper:before {
  font-family: Mw_mifonts;
  content: "\f348";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-red-cancel-paper {
  text-align: center;
  background: none;
}
.icon-red-cancel-paper:before {
  font-family: Mw_mifonts;
  content: "\f349";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-ques {
  text-align: center;
  background: none;
}
.icon-paper-ques:before {
  font-family: Mw_mifonts;
  content: "\f350";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-key {
  text-align: center;
  background: none;
}
.icon-paper-key:before {
  font-family: Mw_mifonts;
  content: "\f351";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-group {
  text-align: center;
  background: none;
}
.icon-paper-group:before {
  font-family: Mw_mifonts;
  content: "\f352";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-clock-bue {
  text-align: center;
  background: none;
}
.icon-paper-clock-bue:before {
  font-family: Mw_mifonts;
  content: "\f353";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-orange {
  text-align: center;
  background: none;
}
.icon-big-paper-orange:before {
  font-family: Mw_mifonts;
  content: "\f354";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-person-green {
  text-align: center;
  background: none;
}
.icon-big-person-green:before {
  font-family: Mw_mifonts;
  content: "\f355";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-report-yel-pen {
  text-align: center;
  background: none;
}
.icon-big-report-yel-pen:before {
  font-family: Mw_mifonts;
  content: "\f356";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-two-pill-gray {
  text-align: center;
  background: none;
}
.icon-big-two-pill-gray:before {
  font-family: Mw_mifonts;
  content: "\f357";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bold {
  text-align: center;
  background: none;
}
.icon-bold:before {
  font-family: Mw_mifonts;
  content: "\f358";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-font {
  text-align: center;
  background: none;
}
.icon-font:before {
  font-family: Mw_mifonts;
  content: "\f359";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-strikethrough {
  text-align: center;
  background: none;
}
.icon-strikethrough:before {
  font-family: Mw_mifonts;
  content: "\f360";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-underline {
  text-align: center;
  background: none;
}
.icon-underline:before {
  font-family: Mw_mifonts;
  content: "\f361";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-incline {
  text-align: center;
  background: none;
}
.icon-incline:before {
  font-family: Mw_mifonts;
  content: "\f362";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-subscript {
  text-align: center;
  background: none;
}
.icon-subscript:before {
  font-family: Mw_mifonts;
  content: "\f363";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-superscript {
  text-align: center;
  background: none;
}
.icon-superscript:before {
  font-family: Mw_mifonts;
  content: "\f364";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-indentation {
  text-align: center;
  background: none;
}
.icon-indentation:before {
  font-family: Mw_mifonts;
  content: "\f365";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-unindent {
  text-align: center;
  background: none;
}
.icon-unindent:before {
  font-family: Mw_mifonts;
  content: "\f366";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-justify {
  text-align: center;
  background: none;
}
.icon-align-justify:before {
  font-family: Mw_mifonts;
  content: "\f367";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-left {
  text-align: center;
  background: none;
}
.icon-align-left:before {
  font-family: Mw_mifonts;
  content: "\f368";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-center {
  text-align: center;
  background: none;
}
.icon-align-center:before {
  font-family: Mw_mifonts;
  content: "\f369";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-right {
  text-align: center;
  background: none;
}
.icon-align-right:before {
  font-family: Mw_mifonts;
  content: "\f370";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paste-board {
  text-align: center;
  background: none;
}
.icon-paste-board:before {
  font-family: Mw_mifonts;
  content: "\f371";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-arrow-left-top,.icon-arrow-le-bo-gray {
  text-align: center;
  background: none;
}
.icon-arrow-left-top:before,.icon-arrow-le-bo-gray:before {
  font-family: Mw_mifonts;
  content: "\f372";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-white-p-red {
  text-align: center;
  background: none;
}
.icon-big-white-p-red:before {
  font-family: Mw_mifonts;
  content: "\f373";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-undo {
  text-align: center;
  background: none;
}
.icon-undo:before {
  font-family: Mw_mifonts;
  content: "\f374";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-redo {
  text-align: center;
  background: none;
}
.icon-redo:before {
  font-family: Mw_mifonts;
  content: "\f375";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-back {
  text-align: center;
  background: none;
}
.icon-back:before {
  font-family: Mw_mifonts;
  content: "\f376";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-three-blue-bar {
  text-align: center;
  background: none;
}
.icon-big-three-blue-bar:before {
  font-family: Mw_mifonts;
  content: "\f377";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-doctor-green {
  text-align: center;
  background: none;
}
.icon-big-doctor-green:before {
  font-family: Mw_mifonts;
  content: "\f378";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-favorite-add {
  text-align: center;
  background: none;
}
.icon-big-favorite-add:before {
  font-family: Mw_mifonts;
  content: "\f379";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-book-arrow {
  text-align: center;
  background: none;
}
.icon-big-book-arrow:before {
  font-family: Mw_mifonts;
  content: "\f380";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-book-ref {
  text-align: center;
  background: none;
}
.icon-big-book-ref:before {
  font-family: Mw_mifonts;
  content: "\f381";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-book-arrow-rt {
  text-align: center;
  background: none;
}
.icon-big-book-arrow-rt:before {
  font-family: Mw_mifonts;
  content: "\f382";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-book-arrow-ok {
  text-align: center;
  background: none;
}
.icon-big-book-arrow-ok:before {
  font-family: Mw_mifonts;
  content: "\f383";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-book-to-book {
  text-align: center;
  background: none;
}
.icon-big-book-to-book:before {
  font-family: Mw_mifonts;
  content: "\f384";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-ring-blue {
  text-align: center;
  background: none;
}
.icon-big-ring-blue:before {
  font-family: Mw_mifonts;
  content: "\f385";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-ring {
  text-align: center;
  background: none;
}
.icon-big-ring:before {
  font-family: Mw_mifonts;
  content: "\f386";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-search {
  text-align: center;
  background: none;
}
.icon-big-paper-search:before {
  font-family: Mw_mifonts;
  content: "\f387";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-close-eye {
  text-align: center;
  background: none;
}
.icon-big-close-eye:before {
  font-family: Mw_mifonts;
  content: "\f388";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-return {
  text-align: center;
  background: none;
}
.icon-big-return:before {
  font-family: Mw_mifonts;
  content: "\f389";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-meterage {
  text-align: center;
  background: none;
}
.icon-big-meterage:before {
  font-family: Mw_mifonts;
  content: "\f390";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-inspect {
  text-align: center;
  background: none;
}
.icon-big-inspect:before {
  font-family: Mw_mifonts;
  content: "\f391";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-disuse {
  text-align: center;
  background: none;
}
.icon-big-disuse:before {
  font-family: Mw_mifonts;
  content: "\f392";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-change-account {
  text-align: center;
  background: none;
}
.icon-big-change-account:before {
  font-family: Mw_mifonts;
  content: "\f393";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-card {
  text-align: center;
  background: none;
}
.icon-big-card:before {
  font-family: Mw_mifonts;
  content: "\f394";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-bar {
  text-align: center;
  background: none;
}
.icon-big-bar:before {
  font-family: Mw_mifonts;
  content: "\f395";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-37,.icon-lt-rt-37 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-37:before,.icon-lt-rt-37:before {
  font-family: Mw_mifonts;
  content: "\f396";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-46,.icon-lt-rt-46 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-46:before,.icon-lt-rt-46:before {
  font-family: Mw_mifonts;
  content: "\f580";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-64,.icon-lt-rt-64 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-64:before,.icon-lt-rt-64:before {
  font-family: Mw_mifonts;
  content: "\f579";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-55,.icon-lt-rt-55 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-55:before,.icon-lt-rt-55:before {
  font-family: Mw_mifonts;
  content: "\f397";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-73,.icon-lt-rt-73 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-73:before,.icon-lt-rt-73:before {
  font-family: Mw_mifonts;
  content: "\f398";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-19,.icon-lt-rt-19 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-19:before,.icon-lt-rt-19:before {
  font-family: Mw_mifonts;
  content: "\f399";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-82,.icon-lt-rt-82 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-82:before,.icon-lt-rt-82:before {
  font-family: Mw_mifonts;
  content: "\f533";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-lt-rt-28,.icon-lt-rt-28 {
  text-align: center;
  background: none;
}
.icon-big-lt-rt-28:before,.icon-lt-rt-28:before {
  font-family: Mw_mifonts;
  content: "\f534";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-delete-col {
  text-align: center;
  background: none;
}
.icon-big-delete-col:before {
  font-family: Mw_mifonts;
  content: "\f400";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-delete-row {
  text-align: center;
  background: none;
}
.icon-big-delete-row:before {
  font-family: Mw_mifonts;
  content: "\f401";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-delete-table {
  text-align: center;
  background: none;
}
.icon-big-delete-table:before {
  font-family: Mw_mifonts;
  content: "\f402";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-insert-col {
  text-align: center;
  background: none;
}
.icon-big-insert-col:before {
  font-family: Mw_mifonts;
  content: "\f403";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-insert-row {
  text-align: center;
  background: none;
}
.icon-big-insert-row:before {
  font-family: Mw_mifonts;
  content: "\f404";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-insert-table {
  text-align: center;
  background: none;
}
.icon-big-insert-table:before {
  font-family: Mw_mifonts;
  content: "\f405";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-split-cells {
  text-align: center;
  background: none;
}
.icon-big-split-cells:before {
  font-family: Mw_mifonts;
  content: "\f406";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-help {
  text-align: center;
  background: none;
}
.icon-big-help:before {
  font-family: Mw_mifonts;
  content: "\f407";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper {
  text-align: center;
  background: none;
}
.icon-big-paper:before {
  font-family: Mw_mifonts;
  content: "\f408";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-question {
  text-align: center;
  background: none;
}
.icon-big-question:before {
  font-family: Mw_mifonts;
  content: "\f409";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-rad {
  text-align: center;
  background: none;
}
.icon-big-rad:before {
  font-family: Mw_mifonts;
  content: "\f410";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-balance {
  text-align: center;
  background: none;
}
.icon-big-balance:before {
  font-family: Mw_mifonts;
  content: "\f411";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-open-file {
  text-align: center;
  background: none;
}
.icon-big-open-file:before {
  font-family: Mw_mifonts;
  content: "\f412";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-waxblock-return {
  text-align: center;
  background: none;
}
.icon-big-waxblock-return:before {
  font-family: Mw_mifonts;
  content: "\f413";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-cells-smear {
  text-align: center;
  background: none;
}
.icon-big-cells-smear:before {
  font-family: Mw_mifonts;
  content: "\f414";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-slide-filed {
  text-align: center;
  background: none;
}
.icon-big-slide-filed:before {
  font-family: Mw_mifonts;
  content: "\f415";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-slide-send {
  text-align: center;
  background: none;
}
.icon-big-slide-send:before {
  font-family: Mw_mifonts;
  content: "\f416";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-dyeing {
  text-align: center;
  background: none;
}
.icon-big-dyeing:before {
  font-family: Mw_mifonts;
  content: "\f417";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-slide-add {
  text-align: center;
  background: none;
}
.icon-big-slide-add:before {
  font-family: Mw_mifonts;
  content: "\f418";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-embed {
  text-align: center;
  background: none;
}
.icon-big-embed:before {
  font-family: Mw_mifonts;
  content: "\f419";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-slide-made {
  text-align: center;
  background: none;
}
.icon-big-slide-made:before {
  font-family: Mw_mifonts;
  content: "\f420";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-book-yellow {
  text-align: center;
  background: none;
}
.icon-big-book-yellow:before {
  font-family: Mw_mifonts;
  content: "\f421";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-med-bag {
  text-align: center;
  background: none;
}
.icon-big-med-bag:before {
  font-family: Mw_mifonts;
  content: "\f422";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-save-add {
  text-align: center;
  background: none;
}
.icon-big-save-add:before {
  font-family: Mw_mifonts;
  content: "\f423";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-save-next {
  text-align: center;
  background: none;
}
.icon-big-save-next:before {
  font-family: Mw_mifonts;
  content: "\f424";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-print-run {
  text-align: center;
  background: none;
}
.icon-big-print-run:before {
  font-family: Mw_mifonts;
  content: "\f425";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-cardiogram {
  text-align: center;
  background: none;
}
.icon-big-cardiogram:before {
  font-family: Mw_mifonts;
  content: "\f426";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-conical-bottle {
  text-align: center;
  background: none;
}
.icon-big-conical-bottle:before {
  font-family: Mw_mifonts;
  content: "\f427";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-patient-mach {
  text-align: center;
  background: none;
}
.icon-big-patient-mach:before {
  font-family: Mw_mifonts;
  content: "\f428";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-movie-mach {
  text-align: center;
  background: none;
}
.icon-big-movie-mach:before {
  font-family: Mw_mifonts;
  content: "\f429";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-chopsticks-bowl {
  text-align: center;
  background: none;
}
.icon-big-chopsticks-bowl:before {
  font-family: Mw_mifonts;
  content: "\f430";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-rectangle-tree {
  text-align: center;
  background: none;
}
.icon-big-rectangle-tree:before {
  font-family: Mw_mifonts;
  content: "\f431";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-message-clock {
  text-align: center;
  background: none;
}
.icon-big-message-clock:before {
  font-family: Mw_mifonts;
  content: "\f432";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-message-cate {
  text-align: center;
  background: none;
}
.icon-big-message-cate:before {
  font-family: Mw_mifonts;
  content: "\f433";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-message-colum {
  text-align: center;
  background: none;
}
.icon-big-message-colum:before {
  font-family: Mw_mifonts;
  content: "\f434";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-message-pen {
  text-align: center;
  background: none;
}
.icon-big-message-pen:before {
  font-family: Mw_mifonts;
  content: "\f435";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-time {
  text-align: center;
  background: none;
}
.icon-big-paper-time:before {
  font-family: Mw_mifonts;
  content: "\f436";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-pre-audit {
  text-align: center;
  background: none;
}
.icon-big-pre-audit:before {
  font-family: Mw_mifonts;
  content: "\f437";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-gray {
  text-align: center;
  background: none;
}
.icon-big-paper-gray:before {
  font-family: Mw_mifonts;
  content: "\f438";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-equi-cfg {
  text-align: center;
  background: none;
}
.icon-big-equi-cfg:before {
  font-family: Mw_mifonts;
  content: "\f439";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-pat-list {
  text-align: center;
  background: none;
}
.icon-big-pat-list:before {
  font-family: Mw_mifonts;
  content: "\f440";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-search-pat {
  text-align: center;
  background: none;
}
.icon-big-search-pat:before {
  font-family: Mw_mifonts;
  content: "\f441";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-yellow {
  text-align: center;
  background: none;
}
.icon-big-paper-yellow:before {
  font-family: Mw_mifonts;
  content: "\f442";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-mach-blue-red {
  text-align: center;
  background: none;
}
.icon-big-mach-blue-red:before {
  font-family: Mw_mifonts;
  content: "\f443";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-first-second {
  text-align: center;
  background: none;
}
.icon-big-first-second:before {
  font-family: Mw_mifonts;
  content: "\f444";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-clock-back-blue {
  text-align: center;
  background: none;
}
.icon-big-clock-back-blue:before {
  font-family: Mw_mifonts;
  content: "\f445";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-clock-back-gree {
  text-align: center;
  background: none;
}
.icon-big-clock-back-gree:before {
  font-family: Mw_mifonts;
  content: "\f446";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-ca {
  text-align: center;
  background: none;
}
.icon-w-ca:before {
  font-family: Mw_mifonts;
  content: "\f447";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ca {
  text-align: center;
  background: none;
}
.icon-ca:before {
  font-family: Mw_mifonts;
  content: "\f447";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ca-green {
  text-align: center;
  background: none;
}
.icon-ca-green:before {
  font-family: Mw_mifonts;
  content: "\f447";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-box {
  text-align: center;
  background: none;
}
.icon-big-paper-box:before {
  font-family: Mw_mifonts;
  content: "\f448";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-ster-bd {
  text-align: center;
  background: none;
}
.icon-w-ster-bd:before {
  font-family: Mw_mifonts;
  content: "\f449";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-ster-leak {
  text-align: center;
  background: none;
}
.icon-w-ster-leak:before {
  font-family: Mw_mifonts;
  content: "\f450";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-verify {
  text-align: center;
  background: none;
}
.icon-verify:before {
  font-family: Mw_mifonts;
  content: "\f451";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ster-cancel {
  text-align: center;
  background: none;
}
.icon-ster-cancel:before {
  font-family: Mw_mifonts;
  content: "\f452";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ster-finish,.icon-ster-ok {
  text-align: center;
  background: none;
}
.icon-ster-finish:before,.icon-ster-ok:before {
  font-family: Mw_mifonts;
  content: "\f453";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ster-again {
  text-align: center;
  background: none;
}
.icon-ster-again:before {
  font-family: Mw_mifonts;
  content: "\f454";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ster-bat {
  text-align: center;
  background: none;
}
.icon-ster-bat:before {
  font-family: Mw_mifonts;
  content: "\f455";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ster-bio {
  text-align: center;
  background: none;
}
.icon-ster-bio:before {
  font-family: Mw_mifonts;
  content: "\f456";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-qrcode-blue {
  text-align: center;
  background: none;
}
.icon-qrcode-blue:before {
  font-family: Mw_mifonts;
  content: "\f457";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-barcode-blue {
  text-align: center;
  background: none;
}
.icon-barcode-blue:before {
  font-family: Mw_mifonts;
  content: "\f458";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-left-blue {
  text-align: center;
  background: none;
}
.icon-align-left-blue:before {
  font-family: Mw_mifonts;
  content: "\f459";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-center-blue {
  text-align: center;
  background: none;
}
.icon-align-center-blue:before {
  font-family: Mw_mifonts;
  content: "\f460";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-align-right-blue {
  text-align: center;
  background: none;
}
.icon-align-right-blue:before {
  font-family: Mw_mifonts;
  content: "\f461";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-valign-top-blue {
  text-align: center;
  background: none;
}
.icon-valign-top-blue:before {
  font-family: Mw_mifonts;
  content: "\f462";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-valign-middle-blue {
  text-align: center;
  background: none;
}
.icon-valign-middle-blue:before {
  font-family: Mw_mifonts;
  content: "\f463";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-valign-bottom-blue {
  text-align: center;
  background: none;
}
.icon-valign-bottom-blue:before {
  font-family: Mw_mifonts;
  content: "\f464";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-same-width-blue {
  text-align: center;
  background: none;
}
.icon-same-width-blue:before {
  font-family: Mw_mifonts;
  content: "\f465";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-same-height-blue {
  text-align: center;
  background: none;
}
.icon-same-height-blue:before {
  font-family: Mw_mifonts;
  content: "\f466";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-same-size-blue {
  text-align: center;
  background: none;
}
.icon-same-size-blue:before {
  font-family: Mw_mifonts;
  content: "\f467";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-move {
  text-align: center;
  background: none;
}
.icon-move:before {
  font-family: Mw_mifonts;
  content: "\f468";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-line {
  text-align: center;
  background: none;
}
.icon-line:before {
  font-family: Mw_mifonts;
  content: "\f469";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-table-blue {
  text-align: center;
  background: none;
}
.icon-table-blue:before {
  font-family: Mw_mifonts;
  content: "\f470";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-alarm-key {
  text-align: center;
  background: none;
}
.icon-alarm-key:before {
  font-family: Mw_mifonts;
  content: "\f471";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-trigger-box {
  text-align: center;
  background: none;
}
.icon-trigger-box:before {
  font-family: Mw_mifonts;
  content: "\f472";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-close {
  text-align: center;
  background: none;
}
.icon-close:before {
  font-family: Mw_mifonts;
  content: "\f480";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-cale-3day {
  text-align: center;
  background: none;
}
.icon-cale-3day:before {
  font-family: Mw_mifonts;
  content: "\f494";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-lightning {
  text-align: center;
  background: none;
}
.icon-paper-lightning:before {
  font-family: Mw_mifonts;
  content: "\f495";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-sure-readed {
  text-align: center;
  background: none;
}
.icon-sure-readed:before {
  font-family: Mw_mifonts;
  content: "\f498";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-read-details {
  text-align: center;
  background: none;
}
.icon-read-details:before {
  font-family: Mw_mifonts;
  content: "\f499";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-table-col {
  text-align: center;
  background: none;
}
.icon-table-col:before {
  font-family: Mw_mifonts;
  content: "\f216";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-w-canceldrug {
  text-align: center;
  background: none;
}
.icon-w-canceldrug:before {
  font-family: Mw_mifonts;
  content: "\f502";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ring-blue {
  text-align: center;
  background: none;
}
.icon-ring-blue:before {
  font-family: Mw_mifonts;
  content: "\f385";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-redlabel-refresh {
  text-align: center;
  background: none;
}
.icon-big-redlabel-refresh:before {
  font-family: Mw_mifonts;
  content: "\f503";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-paper-print {
  text-align: center;
  background: none;
}
.icon-big-paper-print:before {
  font-family: Mw_mifonts;
  content: "\f504";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-drug-ok {
  text-align: center;
  background: none;
}
.icon-big-drug-ok:before {
  font-family: Mw_mifonts;
  content: "\f171";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-drug-paper {
  text-align: center;
  background: none;
}
.icon-big-drug-paper:before {
  font-family: Mw_mifonts;
  content: "\f505";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-drug-x {
  text-align: center;
  background: none;
}
.icon-big-drug-x:before {
  font-family: Mw_mifonts;
  content: "\f506";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-drug-forbid {
  text-align: center;
  background: none;
}
.icon-big-drug-forbid:before {
  font-family: Mw_mifonts;
  content: "\f507";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-card-money {
  text-align: center;
  background: none;
}
.icon-big-card-money:before {
  font-family: Mw_mifonts;
  content: "\f039";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-medibottle {
  text-align: center;
  background: none;
}
.icon-big-medibottle:before {
  font-family: Mw_mifonts;
  content: "\f500";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-drug-all-ok {
  text-align: center;
  background: none;
}
.icon-big-drug-all-ok:before {
  font-family: Mw_mifonts;
  content: "\f234";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-drug-back {
  text-align: center;
  background: none;
}
.icon-big-drug-back:before {
  font-family: Mw_mifonts;
  content: "\f508";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-printer-refresh {
  text-align: center;
  background: none;
}
.icon-big-printer-refresh:before {
  font-family: Mw_mifonts;
  content: "\f271";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-medibottle-run {
  text-align: center;
  background: none;
}
.icon-big-medibottle-run:before {
  font-family: Mw_mifonts;
  content: "\f501";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-person-group {
  text-align: center;
  background: none;
}
.icon-person-group:before {
  font-family: Mw_mifonts;
  content: "\f509";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-export-pdf {
  text-align: center;
  background: none;
}
.icon-export-pdf:before {
  font-family: Mw_mifonts;
  content: "\f510";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-no1 {
  text-align: center;
  background: none;
}
.icon-no1:before {
  font-family: Mw_mifonts;
  content: "\f511";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-no2 {
  text-align: center;
  background: none;
}
.icon-no2:before {
  font-family: Mw_mifonts;
  content: "\f512";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-no3 {
  text-align: center;
  background: none;
}
.icon-no3:before {
  font-family: Mw_mifonts;
  content: "\f513";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bag {
  text-align: center;
  background: none;
}
.icon-bag:before {
  font-family: Mw_mifonts;
  content: "\f514";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bag-x {
  text-align: center;
  background: none;
}
.icon-bag-x:before {
  font-family: Mw_mifonts;
  content: "\f515";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-disp-x {
  text-align: center;
  background: none;
}
.icon-disp-x:before {
  font-family: Mw_mifonts;
  content: "\f516";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-disp-back {
  text-align: center;
  background: none;
}
.icon-disp-back:before {
  font-family: Mw_mifonts;
  content: "\f517";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-outInstc-mgr {
  text-align: center;
  background: none;
}
.icon-outInstc-mgr:before {
  font-family: Mw_mifonts;
  content: "\f520";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-book-rep {
  text-align: center;
  background: none;
}
.icon-book-rep:before {
  font-family: Mw_mifonts;
  content: "\f526";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-book-rep-v1 {
  text-align: center;
  background: none;
}
.icon-book-rep-v1:before {
  font-family: Mw_mifonts;
  content: "\f521";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-book-pen {
  text-align: center;
  background: none;
}
.icon-book-pen:before {
  font-family: Mw_mifonts;
  content: "\f522";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-settings {
  text-align: center;
  background: none;
}
.icon-paper-settings:before {
  font-family: Mw_mifonts;
  content: "\f523";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-book-settings {
  text-align: center;
  background: none;
}
.icon-book-settings:before {
  font-family: Mw_mifonts;
  content: "\f524";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-eye-scan-box {
  text-align: center;
  background: none;
}
.icon-eye-scan-box:before {
  font-family: Mw_mifonts;
  content: "\f525";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-org-frame {
  text-align: center;
  background: none;
}
.icon-org-frame:before {
  font-family: Mw_mifonts;
  content: "\f527";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-alert-pen-gray {
  text-align: center;
  background: none;
}
.icon-alert-pen-gray:before {
  font-family: Mw_mifonts;
  content: "\f528";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-set-qus {
  text-align: center;
  background: none;
}
.icon-paper-set-qus:before {
  font-family: Mw_mifonts;
  content: "\f529";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-bk-mgr {
  text-align: center;
  background: none;
}
.icon-bk-mgr:before {
  font-family: Mw_mifonts;
  content: "\f530";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-msg-unread-unprocessed {
  text-align: center;
  background: none;
}
.icon-msg-unread-unprocessed:before {
  font-family: Mw_mifonts;
  content: "\f535";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-msg-read {
  text-align: center;
  background: none;
}
.icon-msg-read:before {
  font-family: Mw_mifonts;
  content: "\f536";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-msg-read-unprocessed {
  text-align: center;
  background: none;
}
.icon-msg-read-unprocessed:before {
  font-family: Mw_mifonts;
  content: "\f537";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-msg-read-processed {
  text-align: center;
  background: none;
}
.icon-msg-read-processed:before {
  font-family: Mw_mifonts;
  content: "\f538";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pc {
  text-align: center;
  background: none;
}
.icon-pc:before {
  font-family: Mw_mifonts;
  content: "\f544";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pc-v1 {
  text-align: center;
  background: none;
}
.icon-pc-v1:before {
  font-family: Mw_mifonts;
  content: "\f539";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pc-v2 {
  text-align: center;
  background: none;
}
.icon-pc-v2:before {
  font-family: Mw_mifonts;
  content: "\f540";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ecg-adm {
  text-align: center;
  background: none;
}
.icon-ecg-adm:before {
  font-family: Mw_mifonts;
  content: "\f541";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-key2 {
  text-align: center;
  background: none;
}
.icon-key2:before {
  font-family: Mw_mifonts;
  content: "\f542";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-data-stat {
  text-align: center;
  background: none;
}
.icon-data-stat:before {
  font-family: Mw_mifonts;
  content: "\f543";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-user-settings {
  text-align: center;
  background: none;
}
.icon-user-settings:before {
  font-family: Mw_mifonts;
  content: "\f545";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-sound {
  text-align: center;
  background: none;
}
.icon-sound:before {
  font-family: Mw_mifonts;
  content: "\f492";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-needle-sticks {
  text-align: center;
  background: none;
}
.icon-needle-sticks:before {
  font-family: Mw_mifonts;
  content: "\f547";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-contact-with-fluid {
  text-align: center;
  background: none;
}
.icon-contact-with-fluid:before {
  font-family: Mw_mifonts;
  content: "\f548";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-minus {
  text-align: center;
  background: none;
}
.icon-paper-minus:before {
  font-family: Mw_mifonts;
  content: "\f549";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-fishbone-diagram {
  text-align: center;
  background: none;
}
.icon-fishbone-diagram:before {
  font-family: Mw_mifonts;
  content: "\f550";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stamp-add {
  text-align: center;
  background: none;
}
.icon-stamp-add:before {
  font-family: Mw_mifonts;
  content: "\f551";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-stamp-undo {
  text-align: center;
  background: none;
}
.icon-stamp-undo:before {
  font-family: Mw_mifonts;
  content: "\f552";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-video {
  text-align: center;
  background: none;
}
.icon-video:before {
  font-family: Mw_mifonts;
  content: "\f553";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-share {
  text-align: center;
  background: none;
}
.icon-share:before {
  font-family: Mw_mifonts;
  content: "\f554";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-share-no {
  text-align: center;
  background: none;
}
.icon-share-no:before {
  font-family: Mw_mifonts;
  content: "\f555";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-share {
  text-align: center;
  background: none;
}
.icon-paper-share:before {
  font-family: Mw_mifonts;
  content: "\f556";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-print {
  text-align: center;
  background: none;
}
.icon-paper-print:before {
  font-family: Mw_mifonts;
  content: "\f557";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-home-back {
  text-align: center;
  background: none;
}
.icon-home-back:before {
  font-family: Mw_mifonts;
  content: "\f558";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-plane-clock {
  text-align: center;
  background: none;
}
.icon-paper-plane-clock:before {
  font-family: Mw_mifonts;
  content: "\f559";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-ice-water {
  text-align: center;
  background: none;
}
.icon-ice-water:before {
  font-family: Mw_mifonts;
  content: "\f560";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-circle-down {
  text-align: center;
  background: none;
}
.icon-circle-down:before {
  font-family: Mw_mifonts;
  content: "\f561";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-template-down {
  text-align: center;
  background: none;
}
.icon-template-down:before {
  font-family: Mw_mifonts;
  content: "\f562";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-stamp {
  text-align: center;
  background: none;
}
.icon-paper-stamp:before {
  font-family: Mw_mifonts;
  content: "\f563";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-link {
  text-align: center;
  background: none;
}
.icon-paper-link:before {
  font-family: Mw_mifonts;
  content: "\f564";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-paper-unlink {
  text-align: center;
  background: none;
}
.icon-paper-unlink:before {
  font-family: Mw_mifonts;
  content: "\f565";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-shopping-cart-ok {
  text-align: center;
  background: none;
}
.icon-shopping-cart-ok:before {
  font-family: Mw_mifonts;
  content: "\f566";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-person-ok {
  text-align: center;
  background: none;
}
.icon-person-ok:before {
  font-family: Mw_mifonts;
  content: "\f567";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-mnypaper-ok {
  text-align: center;
  background: none;
}
.icon-mnypaper-ok:before {
  font-family: Mw_mifonts;
  content: "\f568";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-physics-monitor {
  text-align: center;
  background: none;
}
.icon-physics-monitor:before {
  font-family: Mw_mifonts;
  content: "\f569";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-change-x-virus {
  text-align: center;
  background: none;
}
.icon-change-x-virus:before {
  font-family: Mw_mifonts;
  content: "\f570";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-multi-del {
  text-align: center;
  background: none;
}
.icon-multi-del:before {
  font-family: Mw_mifonts;
  content: "\f571";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-pda-execution-rate {
  text-align: center;
  background: none;
}
.icon-pda-execution-rate:before {
  font-family: Mw_mifonts;
  content: "\f584";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-insert-local-image,.icon-insert-local-image {
  text-align: center;
  background: none;
}
.icon-big-insert-local-image:before,.icon-insert-local-image:before {
  font-family: Mw_mifonts;
  content: "\f585";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-edit-picture,.icon-edit-picture {
  text-align: center;
  background: none;
}
.icon-big-edit-picture:before,.icon-edit-picture:before {
  font-family: Mw_mifonts;
  content: "\f586";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-creating-a-pedigree-map,.icon-creating-a-pedigree-map {
  text-align: center;
  background: none;
}
.icon-big-creating-a-pedigree-map:before,.icon-creating-a-pedigree-map:before {
  font-family: Mw_mifonts;
  content: "\f587";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-edit-pedigree-chart,.icon-edit-pedigree-chart {
  text-align: center;
  background: none;
}
.icon-big-edit-pedigree-chart:before,.icon-edit-pedigree-chart:before {
  font-family: Mw_mifonts;
  content: "\f588";
}
/*在列上显示icon-xx时图标时,不显示图标,且不居中问题 2911539*/
.icon-big-image-properties,.icon-image-properties {
  text-align: center;
  background: none;
}
.icon-big-image-properties:before,.icon-image-properties:before {
  font-family: Mw_mifonts;
  content: "\f589";
}
.icon-inpatient:before,
.icon-outpatient:before,
.icon-emergency:before,
.icon-disabler:before,
.icon-produce:before,
.icon-children:before,
.icon-lung:before,
.icon-high:before,
.icon-spirit:before,
.icon-old:before,
.icon-poor:before,
.icon-sugar:before,
.icon-free:before,
.icon-out-poverty:before,
.icon-pregnant-woman:before,
.icon-make-oppointment:before,
.icon-allergy-word:before,
.icon-base-word:before,
.icon-close-word:before,
.icon-complex-word:before,
.icon-list-word:before,
.icon-private-word:before,
.icon-public-word:before,
.icon-text-word:before,
.icon-translate-word:before,
.icon-sort:before,
.icon-out:before,
.icon-change:before,
.icon-macpw:before,
.icon-macpworder:before,
.icon-evaluate-red:before,
.icon-evaluate-green:before,
.icon-quality:before {
  font-size: 12px;
  display: inline-block;
  width: 14px;
  height: 14px;
  line-height: 15px;
  border-radius: 2px;
  text-align: center;
}
.icon-inpatient:before {
  content: "住";
  background: #FFEDDF;
  border: 1px solid #FFB300;
  color: #FB7D00;
}
.icon-outpatient:before {
  content: "门";
  background: #F8FFF3;
  border: 1px solid #47CE27;
  color: #229A06;
}
.icon-emergency:before {
  content: "急";
  background: #FFE6E3;
  border: 1px solid #FF5B45;
  color: #DB1B00;
}
.icon-disabler:before {
  content: "残";
  background: #FFF3E1;
  border: 1px solid #CF8A3B;
  color: #AC5919;
}
.icon-produce:before {
  content: "产";
  background: #FFE9FE;
  border: 1px solid #F17AE9;
  color: #F340E7;
}
.icon-children:before {
  content: "童";
  background: #E9FFE3;
  border: 1px solid #58CF00;
  color: #12AA2C;
}
.icon-lung:before {
  content: "肺";
  background: #E6FFFB;
  border: 1px solid #3BCFB6;
  color: #05A489;
}
.icon-high:before {
  content: "高";
  background: #FFE9E9;
  border: 1px solid #FFA4A4;
  color: #FF3D3D;
}
.icon-spirit:before {
  content: "精";
  background: #F4E5F9;
  border: 1px solid #A346C4;
  color: #8C07BB;
}
.icon-old:before {
  content: "老";
  background: #FFE1CE;
  border: 1px solid #F59700;
  color: #E65800;
}
.icon-poor:before {
  content: "困";
  background: #FFE9E9;
  border: 1px solid #FFA4A4;
  color: #FF3D3D;
}
.icon-sugar:before {
  content: "糖";
  background: #FFECDD;
  border: 1px solid #FFB47C;
  color: #DE881C;
}
.icon-free:before {
  content: "免";
  background: #F0E3FF;
  border: 1px solid #B475FD;
  color: #8A24FF;
}
.icon-out-poverty:before {
  content: "脱";
  background: #D6F7F3;
  border: 1px solid #3BD2C8;
  color: #269A92;
}
.icon-pregnant-woman:before {
  content: "孕";
  background: #FFE2EE;
  border: 1px solid #FC9DCD;
  color: #F340A0;
}
.icon-make-oppointment:before {
  content: "约";
  background: #EFFFEB;
  border: 1px solid #A8E59A;
  color: #229A06;
}
.icon-allergy-word:before {
  content: "敏";
  background: #FFE9E9;
  border: 1px solid #FFA4A4;
  color: #FF3D3D;
}
.icon-base-word:before {
  content: "基";
  background: #E9EEFF;
  border: 1px solid #859EFF;
  color: #2753FF;
}
.icon-close-word:before {
  content: "闭";
  background: #ECECEC;
  border: 1px solid #A7A7A7;
  color: #6E6E6E;
}
.icon-complex-word:before {
  content: "复";
  background: #FFDBEB;
  border: 1px solid #F788BB;
  color: #D61E72;
}
.icon-list-word:before {
  content: "列";
  background: #C8F4AD;
  border: 1px solid #64B254;
  color: #307921;
}
.icon-private-word:before {
  content: "私";
  background: #FFDB98;
  border: 1px solid #FF9831;
  color: #CC4F00;
}
.icon-public-word:before {
  content: "公";
  background: #E3FFFA;
  border: 1px solid #64C8BC;
  color: #249587;
}
.icon-text-word:before {
  content: "文";
  background: #EFF9FF;
  border: 1px solid #339EFF;
  color: #0086FF;
}
.icon-translate-word:before {
  content: "译";
  background: #D3F7FF;
  border: 1px solid #76D5E9;
  color: #1093A7;
}
.icon-sort:before {
  content: "序";
  background: #E8F3FF;
  border: 1px solid #0863C2;
  color: #0052A7;
}
.icon-out:before {
  content: "出";
  background: #E3FFEE;
  border: 1px solid #00C75C;
  color: #12AA64;
}
.icon-change:before {
  content: "转";
  background: #FFFBCE;
  border: 1px solid #EAD803;
  color: #EBA300;
}
.icon-macpw:before {
  content: "径";
  background: #FFE9FE;
  border: 1px solid #F17AE9 ;
  color: #F340E7;
}
.icon-macpworder:before {
  content: "嘱";
  background: #EFF9FF ;
  border: 1px solid #22A1FF ;
  color: #0086FF;
}
.icon-evaluate-red:before {
  content: "评";
  background: #FFE6E3;
  border: 1px solid #FF5B45;
  color: #DB1B00;
}
.icon-evaluate-green:before {
  content: "评";
  background: #E3FFEE;
  border: 1px solid #00C75C;
  color: #12AA64;
}
.icon-quality:before {
  content: "质";
  background: #FFE9E9;
  border: 1px solid #FFA4A4;
  color: #FF3D3D;
}
.pic-pat-babyboy {
  background: url('icons/lite/pat/babyboy.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-babygirl {
  background: url('icons/lite/pat/babygirl.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-man {
  background: url('icons/lite/pat/man.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-oldman {
  background: url('icons/lite/pat/oldman.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-oldwoman {
  background: url('icons/lite/pat/oldwoman.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-schoolboy {
  background: url('icons/lite/pat/schoolboy.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-schoolgirl {
  background: url('icons/lite/pat/schoolgirl.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-woman {
  background: url('icons/lite/pat/woman.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-unknown-gender {
  background: url('icons/lite/pat/unknown_gender.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-doctor {
  background: url('icons/lite/usr/doctor.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-nurse {
  background: url('icons/lite/usr/nurse.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-surgeon {
  background: url('icons/lite/usr/surgeon.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-clothing-worker {
  background: url('icons/lite/usr/clothing_worker.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-medi-worker {
  background: url('icons/lite/usr/medi_worker.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-dep-director {
  background: url('icons/lite/usr/dep_director.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-hosp-director {
  background: url('icons/lite/usr/hosp_director.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-doctor-woman {
  background: url('icons/lite/usr/doctor_woman.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-adm-out {
  background: url('icons/lite/adm/out.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-adm-em {
  background: url('icons/lite/adm/em.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-adm-in {
  background: url('icons/lite/adm/in.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-cert {
  background: url('icons/lite/logon/cert.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-default {
  background: url('icons/lite/logon/default.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-face {
  background: url('icons/lite/logon/face.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-phone {
  background: url('icons/lite/logon/phone.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-pin {
  background: url('icons/lite/logon/pin.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-sound {
  background: url('icons/lite/logon/sound.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-ukey {
  background: url('icons/lite/logon/ukey.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-about-dhcc-digitalmed {
  background: url('icons/lite/about/dhcc_digitalmed.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-about-doc {
  background: url('icons/lite/about/doc.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-about-imedical-logo {
  background: url('icons/lite/about/imedical_logo.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-about-vi {
  background: url('icons/lite/about/vi.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-e403 {
  background: url('icons/lite/sysst/e403.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-e404 {
  background: url('icons/lite/sysst/e404.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-e500 {
  background: url('icons/lite/sysst/e500.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-nodata {
  background: url('icons/lite/sysst/nodata.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-nodata-region {
  background: url('icons/lite/sysst/nodata_region.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-nodata-msg {
  background: url('icons/lite/sysst/nodata_msg.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-timeout-relogon {
  background: url('icons/lite/sysst/timeout_relogon.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-welcome {
  background: url('icons/lite/sysst/welcome.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
/*好多hover的颜色*/
/*0.7;*/
/*add 2019-12-23*/
/*4px修改成3px，影响医嘱套维护界面。会多出多余的滚动条*/
/*main-color*/
/*main-hover-color*/
/*tabs-gray*/
/*main-hover-color*/
/*variables-end*/
body {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  padding: 10px;
  font-size: 14px;
  margin: 0;
  background-color: #F5F5F5;
}
body * {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  font-size: 14px;
}
h2 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  margin-bottom: 15px;
}
.demo-info {
  padding: 0 0 12px 0;
}
.demo-tip {
  display: none;
}
.r-label {
  text-align: right;
  padding-right: 10px;
}
.required-label:before {
  content: '*';
  color: red;
  vertical-align: bottom;
  line-height: 100%;
}
a {
  color: #339eff;
}
/*chrome默认了outline*/
input,
textarea {
  outline-width: 0;
}
#z-q-container {
  position: absolute;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid #339EFF;
  z-index: 9000;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  border-radius: 0 0 4px 4px;
}
/*2018-6-28 增大图标与左边间距*/
/*默认禁用所有panel的header上的icon*/
.panel-icon {
  width: 0px;
}
.showicon .panel-icon {
  left: 10px;
  width: 16px;
  font-family: "Mw_mifonts";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  font-weight: 500;
  font-size: 16px;
  line-height: normal;
}
.showicon .panel-title.panel-with-icon {
  padding-left: 32px;
}
.panel-title {
  background-image: none;
}
.panel-header,
.panel-body {
  border-color: #E2E2E2;
}
.panel-header {
  background: #ffffff;
  filter: none;
  padding: 3px 5px;
  border-radius: 4px 4px 0 0;
}
.panel-status-collapse .panel-header {
  border-radius: 4px;
}
.panel-body {
  font-size: 14px;
  border-radius: 0 0 4px 4px;
  border-top-color: #ffffff;
}
.panel-body.panel-body-noheader {
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  border-top-color: #E2E2E2;
}
.panel-title {
  font-size: 14px;
  font-weight: bold;
  color: #000000;
  height: 26px;
  line-height: 26px;
  padding-left: 5px;
}
.panel-title.panel-with-icon {
  padding-left: 5px;
}
.panel-header .panel-tool a {
  font-size: 10px;
  line-height: 16px;
  height: 16px;
  width: 16px;
  margin-left: 10px;
  /*间距修改 2944142*/
}
.panel-tool-close::before {
  content: "\f480";
}
.panel-tool-min::before {
  content: "\f479";
}
.panel-tool-max::before {
  content: "\f167";
}
.panel-tool-restore::before {
  content: "\f478";
}
.panel-tool-collapse::before {
  content: "\f264";
}
.panel-tool-expand::before {
  content: "\f265";
}
.panel-tool a:hover {
  color: #378ec4;
  background-color: #f5f5f5;
}
.panel-tool a.panel-tool-close:hover {
  color: #fF0000;
}
.panel.lookup-p > .panel-header,
.panel.combo-p > .panel-header,
.panel.window > .panel-header,
.layout > .panel > .panel-header,
.accordion > .panel > .panel-header,
.tabs-panels > .panel > .panel-header {
  padding: 5px 5px;
  border-color: #E2E2E2;
}
.panel.lookup-p > .panel-body,
.panel.combo-p > .panel-body,
.panel.window > .panel-body,
.layout > .panel > .panel-body,
.accordion > .panel > .panel-body,
.tabs-panels > .panel > .panel-body {
  border-color: #E2E2E2;
}
.layout > .panel > .panel-header {
  border-color: #E2E2E2;
}
.layout > .panel > .panel-body {
  border-color: #E2E2E2;
}
.panel .panel-body.panel-body-noborder {
  border-color: #ffffff;
}
/*自适应layout在医为浏览器会出现横纵滚动条问题，且滚动幅度刚好为滚动条宽度
* 应该是宽高计算正确了，但是横纵滚动条互相影响就互相出现了，将类panel-scroll禁止滚动
* 原来设计上panel-noscroll也是禁止滚动，但是莫名的panel-body的优先了
 */
.panel-body.panel-noscroll {
  overflow: hidden;
}
.accordion .accordion-header-selected {
  background: #4E97D7;
}
.accordion .accordion-header-selected .panel-title {
  color: #000000;
}
.accordion {
  /* 展开时使用body的下边框, 收起时使用header的下边框*/
}
.accordion.accordion-noborder .panel:first-child .accordion-header {
  border-radius: 4px 4px 0 0;
}
.accordion.accordion-noborder .panel:last-child .accordion-header.accordion-header-selected {
  border-radius: 0;
  border-bottom: 0px;
}
.accordion.accordion-noborder .panel:last-child .accordion-body,
.accordion.accordion-noborder .panel:last-child .accordion-header {
  border-radius: 0 0 4px 4px;
  border-bottom: 1px solid #E2E2E2;
  /* 奇怪这一像素不能显示*/
}
.accordion .accordion-body {
  border-radius: 0;
  border: 1px solid #E2E2E2;
  border-bottom: 0px;
  background-color: #ffffff;
  color: #7a7875;
}
.accordion .accordion-header {
  border-radius: 0;
  padding: 6px;
  border: 1px #E2E2E2 solid;
  border-bottom: 0px;
  background: #F8F8F8;
  filter: none;
}
.accordion .accordion-header .panel-title {
  padding-left: 5px;
  color: #000000;
}
.accordion .accordion-header .panel-icon {
  left: 10px;
}
.accordion .accordion-header .panel-tool {
  margin-top: -11px;
  height: 22px;
}
.accordion .accordion-header .panel-tool a {
  display: inline-block;
  width: 22px;
  height: 16px;
  vertical-align: middle;
  margin: 0;
}
.accordion .accordion-header .panel-tool .accordion-collapse::before {
  content: "\f264";
}
.accordion .accordion-header .panel-tool .accordion-expand::before {
  content: "\f265";
}
.window,
.window-shadow {
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}
.panel.window {
  padding: 0px;
  border-bottom: 0px;
  background-color: #ffffff;
}
.panel.window .window-header {
  border-color: #ffffff;
  border-bottom: 1px solid #E2E2E2;
  background: #ffffff;
  /*transparent;*/
}
.panel.window .window-header .panel-icon {
  left: 20px;
  /*1px*/
  display: none;
}
.panel.window .window-header .panel-tool {
  right: 12px;
  /*1px*/
}
.panel.window .window-header .panel-tool .panel-tool-close,
.panel.window .window-header .panel-tool .panel-tool-max,
.panel.window .window-header .panel-tool .panel-tool-restore,
.panel.window .window-header .panel-tool .panel-tool-min,
.panel.window .window-header .panel-tool .panel-tool-collapse,
.panel.window .window-header .panel-tool .panel-tool-expand {
  background-image: none;
}
.panel.window .window-header .panel-tool .panel-tool-close {
  background-image: none;
}
.panel.window .window-header .panel-tool .panel-tool-close:hover {
  background-image: none;
}
.panel.window .window-header .panel-with-icon {
  padding-left: 5px;
  /*18px*/
}
.panel.window .window-body {
  border-width: 0px;
  border-style: solid;
}
.panel.window .window-body-noheader {
  border-top-width: 1px;
}
.window-shadow {
  background: #666666;
  -moz-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.16);
  -webkit-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.16);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.16);
}
.window,
.window .window-body {
  border: none;
}
.window-proxy {
  border: 1px dashed #cccccc;
}
.window-proxy-mask,
.window-mask {
  background: #000000;
}
.window .window-header .panel-icon,
.window .window-header .panel-tool {
  top: 50%;
  margin-top: -8px;
}
.window.panel > .panel-header > .panel-title {
  color: #000000;
  font-weight: bold;
  font-size: 16px;
}
.window .dialog-content {
  border-color: #ffffff;
  overflow: auto;
}
.dialog-toolbar {
  padding: 2px 5px;
}
.dialog-tool-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.dialog-button {
  padding: 10px;
  /*text-align: right;*/
  text-align: center;
}
.dialog-button .l-btn {
  margin-left: 10px;
}
.dialog-button .l-btn:first-child {
  margin-left: 0px;
}
.dialog-toolbar,
.dialog-button {
  background: #F4F4F4;
}
.dialog-toolbar {
  border-bottom: 1px solid #dddddd;
}
/* 2018-6-28*/
.dialog-button {
  /*border-top: 1px solid #dddddd;*/
  border: none;
  background: #FFFFFF;
}
/*dialog内容区panel 不要圆角*/
.panel-body.panel-body-noheader.dialog-content {
  border-radius: 0;
}
input[type=text],
textarea {
  border-color: #CCCCCC;
  width: 148px;
  /*websys.css-width:130px;*/
}
/*不增加type=text，当<input>定义时不带type时选择不到*/
/*textbox[type=text] 覆盖老的选择器*/
input.textbox,
.textbox[type=text],
textarea.textbox,
select.textbox,
.textareabox-text,
input.validatebox-text {
  width: 148px;
  /*--2018-11-22*/
  margin: 0;
  padding: 0 0 0 5px;
  box-sizing: content-box;
  border-radius: 2px;
  border: 1px solid #CCCCCC;
  color: #000000;
  font-size: 14px;
}
input.textbox:active,
.textbox[type=text]:active,
textarea.textbox:active,
select.textbox:active,
.textareabox-text:active,
input.validatebox-text:active {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
input.textbox:hover,
.textbox[type=text]:hover,
textarea.textbox:hover,
select.textbox:hover,
.textareabox-text:hover,
input.validatebox-text:hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
input.textbox:focus,
.textbox[type=text]:focus,
textarea.textbox:focus,
select.textbox:focus,
.textareabox-text:focus,
input.validatebox-text:focus {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
input.textbox {
  line-height: 28px;
  height: 28px;
}
.textbox:disabled,
.textareabox-text:disabled,
.validatebox-text:disabled,
.searchbox-text:disabled,
textarea:disabled,
input:not([type]):disabled,
input[type="text"]:disabled {
  background-color: #F5F5F5;
  border-color: #CCCCCC;
  color: #999999;
  cursor: not-allowed;
}
.checkbox.disabled,
.radio.disabled,
.filebox.disabled input {
  cursor: not-allowed;
}
.spinner .textbox {
  width: 151px;
}
.triggerbox .textbox {
  width: 151px;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999999;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #999999;
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #999999;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999999;
}
.searchbox-button,
.combo-arrow,
.triggerbox.triggerbox-plain .triggerbox-button {
  background: none;
  width: 30px;
  height: 30px;
  opacity: 1;
  filter: alpha(opacity=100);
  font: normal normal normal 14px/1 Mw_mifonts;
  display: inline-block;
  font-size: 14px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  line-height: 28px;
  color: #7E7E7E;
}
.spinner-arrow .spinner-arrow-down,
.spinner-arrow .spinner-arrow-up,
.panel-tool a,
.tabs-icon,
.tabs-close,
.tabs-scroller-left,
.tabs-scroller-right,
.m-btn-downarrow {
  background: none;
  width: 30px;
  height: 15px;
  opacity: 1;
  filter: alpha(opacity=100);
  font: normal normal normal 13px/1 Mw_mifonts;
  font-size: 13px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  line-height: 15px;
  color: #7E7E7E;
  text-decoration: none;
}
/*宽应该为16px，如果为30px会导致树父节点后移[2791549]*/
/*只影响accordiontree，其它tree还是用图片 [2791392]*/
.accordiontree .tree-hit {
  background: none;
  width: 16px;
  height: 16px;
  opacity: 1;
  filter: alpha(opacity=100);
  font: normal normal normal 13px/1 Mw_mifonts;
  font-size: 13px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  line-height: 16px;
  color: #7E7E7E;
  text-decoration: none;
}
.combo .combo-text {
  font-size: 14px;
  border: 0;
  line-height: 28px;
  height: 28px;
  padding: 0px 0px 0px 5px;
  *height: 26px;
  *line-height: 26px;
  _height: 26px;
  _line-height: 26px;
}
.combo .combo-arrow::before {
  content: "\f476";
}
.combotree-f + .combo .combo-arrow::before {
  content: "\f475";
}
.combo,
.combo-panel {
  background-color: #ffffff;
}
.combo {
  border-radius: 2px;
  border-color: #CCCCCC;
  background-color: #ffffff;
}
.combo.combo-p-active {
  border-color: #339EFF;
  background-color: #EFF9FF;
}
.combo.combo-p-active .combo-text {
  background-color: #EFF9FF;
}
.combo.combo-p-active .combo-arrow {
  color: #339EFF;
  /*2891835*/
}
.combo:hover,
.combo.combo-hover {
  border-color: #339EFF;
  background-color: #EFF9FF;
}
/* focus-widthin--ie11也没支持，chrome可以
.combo:focus-within{
  border-color: @input-border-focus-color;
}*/
.combo-arrow.combo-arrow-hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
  color: #339EFF;
}
.combo.disabled {
  border-color: #CCCCCC;
}
.combo.disabled .combo-arrow {
  background-color: #F5F5F5;
  color: #BBBBBB;
}
.panel.combo-p > .combo-panel {
  border-color: #339EFF;
}
.panel.combo-p {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
}
.panel.combo-p.combo-p-top {
  border-radius: 4px 4px 0 0;
}
.panel.combo-p.combo-p-top .combo-panel {
  border-radius: 4px 4px 0 0;
}
.panel.combo-p.combo-p-bottom {
  border-radius: 0 0 4px 4px;
}
.panel.combo-p.combo-p-bottom .combo-panel {
  border-radius: 0 0 4px 4px;
}
.combobox-item,
.combobox-group,
._hisui_combobox-selectall {
  font-size: 14px;
  padding: 3px;
  padding-right: 0px;
  padding-left: 5px;
  line-height: 24px;
  white-space: nowrap;
}
._hisui_combobox-selectall {
  background: #f6f6f6;
  cursor: pointer;
  border-left: 1px #339EFF solid;
  border-right: 1px #339EFF solid;
  border-top: 1px #339EFF solid;
  height: 26px;
}
.btop > .combo-panel {
  border-top-width: 0;
}
.bbtm > .combo-panel {
  border-bottom-width: 0;
}
.bbtm > ._hisui_combobox-selectall {
  border-bottom: 1px #E2E2E2 solid;
}
.combobox-gitem {
  padding-left: 10px;
}
.combobox-item-hover {
  background-color: #E2E2E2;
  color: #000000;
}
.combobox-item-selected {
  background-color: #E5E5E5;
  color: #000000;
}
.combobox-item .combobox-checkbox,
._hisui_combobox-selectall .combobox-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  background: url("images/lite/checkbox.png") no-repeat;
  background-position: 0 0;
  width: 24px;
  height: 24px;
}
.combobox-item-selected.combobox-item .combobox-checkbox,
._hisui_combobox-selectall.checked .combobox-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  background: url("images/lite/checkbox.png") no-repeat;
  background-position: -48px 0;
  width: 24px;
  height: 24px;
}
.combobox-item-disabled.combobox-item .combobox-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  background: url("images/lite/checkbox.png") no-repeat;
  background-position: -72px 0;
  width: 24px;
  height: 24px;
}
.layout-expand-body-title > div {
  text-align: center;
  font-weight: bold;
  color: #000000;
  /*收起时竖向文字不显示问题*/
}
/*改为只控制其子级panel-header 的panel-tool  不让其影响放在layout区域下面的panel cryze 2018-10-20*/
.layout-panel > .panel-header .panel-tool a:hover,
.layout-expand > .panel-header .panel-tool a:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.layout-split-north {
  border-bottom: 5px solid transparent;
}
.layout-split-south {
  border-top: 5px solid transparent;
}
.layout-split-east {
  border-left: 5px solid transparent;
}
.layout-split-west {
  border-right: 5px solid transparent;
}
.layout-expand {
  background-color: #ffffff;
}
.layout-expand-over {
  background-color: #ffffff;
}
.layout-button-right::before {
  content: "\f235";
}
.layout-button-left::before {
  content: "\f482";
}
.layout-button-up::before {
  content: "\f483";
}
.layout-button-down::before {
  content: "\f481";
}
/*改为只控制其子级panel-header 的panel-tool 不让其影响放在layout区域下面的panel cryze 2018-10-20*/
.layout-panel > .panel-header.panel-header-gray .panel-tool a:hover,
.layout-expand > .panel-header.panel-header-gray .panel-tool a:hover {
  background-color: #e5e5e5;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.layout-expand .panel-header.panel-header-gray {
  background-color: #f9f9fa;
}
.layout-expand .panel-header-gray + .panel-body {
  background-color: #f9f9fa;
}
.layout-expand .panel-header-gray + .panel-body .layout-expand-body-title > div {
  text-align: center;
  font-weight: bold;
  color: #000;
}
.layout > .panel.layout-panel > .panel-header {
  padding: 4px 5px;
}
/*
.layout-expand {
  background-color: #F7F7F7;
}
.layout-expand-over {
  background-color: #F9F9FF;
}
.layout-expand {
  .panel-body{
    border-color:#cccccc;
  }
  .panel-header{
    border-color: #cccccc;
  }
}

}*/
/*2018-6-28 增大图标与左边间距*/
.tabs-icon {
  left: 10px;
  width: 16px;
  font-weight: 500;
  font-size: 16px;
  line-height: normal;
}
.tabs-scroller-left::before {
  content: "\f482";
  display: block;
  margin-top: 10px;
  margin-left: 0px;
}
.tabs-scroller-right::before {
  content: "\f235";
  display: block;
  margin-top: 10px;
  margin-left: 2px;
}
.tabs-scroller-right,
.tabs-scroller-left {
  width: 20px;
  font-size: 12px;
}
.tabs-scroller-right.tabs-scroller-over,
.tabs-scroller-left.tabs-scroller-over {
  opacity: 1;
  background-color: #ffffff;
  color: #339EFF;
}
.tabs-header {
  padding-top: 0px;
  border-radius: 4px 4px 0 0;
  border-width: 1px;
  border-bottom-width: 0px;
}
.tabs-panels {
  border-radius: 0 0 4px 4px;
}
.tabs-panels > .panel > .panel-body.panel-body-noheader {
  border-radius: 0px;
}
.tabs {
  height: 36px;
  padding-left: 0px;
  border-width: 0 0 0px 0;
  border-bottom: 1px solid #E2E2E2;
  overflow: visible;
  /*tabs-brand no hover no pointer  cryze 2018-3-15*/
}
.tabs li {
  height: 34px;
  /*wanghc 2018-3-19  win7-ie11*/
  margin: 0px;
  overflow: hidden;
}
.tabs li:not(:last-child)::after {
  content: none;
}
.tabs li.tabs-selected {
  overflow: visible;
}
.tabs li.tabs-selected a.tabs-inner {
  background: #FFFFFF;
  border: 0 solid;
  border-bottom: 3px solid #339EFF;
  color: #339EFF;
  font-weight: normal;
}
.tabs li.tabs-selected a.tabs-inner:hover {
  background: #FFFFFF;
  color: #339EFF;
  font-weight: normal;
}
.tabs li.tabs-selected::after {
  content: none;
}
.tabs li.tabs-selected a.tabs-close::before {
  content: "\f480";
}
.tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  color: #000000;
  background: #FFFFFF;
  border-width: 0;
  border-bottom: 3px solid #FFFFFF;
  filter: none;
}
.tabs li a.tabs-inner:hover {
  border-top-color: #eff9ff;
  background: #FFFFFF;
  color: #339EFF;
  filter: none;
}
.tabs li a.tabs-inner .tabs-title.tabs-closable {
  padding-right: 24px;
}
.tabs li a.tabs-inner .tabs-icon {
  margin-top: -8px;
}
.tabs li a.tabs-inner .tabs-title.tabs-with-icon {
  padding-left: 20px;
}
.tabs li a.tabs-close {
  background-image: none;
  right: 10px;
  height: 16px;
  width: 16px;
}
.tabs li a.tabs-close::before {
  content: "\f480";
}
.tabs li a.tabs-close:hover {
  color: red;
  background-color: transparent;
}
.tabs li.tabs-brand a.tabs-inner {
  cursor: default;
}
.tabs li.tabs-brand a.tabs-inner:hover {
  border-top-color: #FFFFFF;
  background: #FFFFFF;
  color: #000000;
}
.tabs li.tabs-brand a.tabs-inner .tabs-title {
  font-weight: bold;
}
.tabs li.tabs-brand a.tabs-inner .tabs-icon {
  /* 极简brand页签不显示图标 2023-02-03 */
  width: 0;
  overflow: hidden;
}
.tabs li.tabs-brand a.tabs-inner .tabs-title.tabs-with-icon {
  /*极简brand页签不显示图标 2023-02-03 */
  padding-left: 0;
}
/*.tabs li a.tabs-inner {
  color:@tabs-header-font-color;
  background:@tabs-header-bgcolor;
}*/
.tabs-title {
  font-size: 14px;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.tabs-header-left .tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.tabs-header-right .tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.tabs-scroller-left {
  background: none;
  /*2018-6-27 UI建议修改 1px->4px*/
}
.tabs-scroller-right {
  background: none;
  /*2018-6-27 UI建议修改 -15px->-12px*/
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  background: #ffffff;
}
.tabs-header,
.tabs-tool {
  background-color: #FFFFFF;
  /*modify*/
}
.tabs-header-plain {
  background: transparent;
}
.tabs-header,
.tabs-scroller-left,
.tabs-scroller-right,
.tabs-tool,
.tabs-panels,
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner,
.tabs-header-left .tabs li.tabs-selected a.tabs-inner,
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-color: #E2E2E2;
}
.tabs-scroller-left {
  border-left-width: 0px ;
}
.tabs-scroller-right {
  border-right-width: 0px ;
}
.tabs-tool {
  border-left-width: 0px;
}
.tabs-tool .l-btn {
  margin-top: 3px;
  margin-right: 8px;
}
.tabs-p-tool a:hover {
  background-color: #339EFF;
}
.tabs li a:hover.tabs-color {
  background-color: #339EFF;
}
.tabs-scroller-over {
  background-color: #339EFF;
}
.tabs-header-bottom {
  padding: 0;
}
.tabs-header-bottom.tabs-header {
  border-bottom-width: 0;
}
.tabs-header-bottom .tabs {
  height: 36px;
  padding: 0px;
  border-width: 0 0 0px 0;
}
.tabs-header-bottom .tabs li {
  height: 36px;
  /*wanghc 2018-3-19  win7-ie11*/
  margin: 0px;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  border-top: 0px;
  border-bottom: 3px solid #FFFFFF;
}
.tabs-header-bottom .tabs li a.tabs-inner:hover {
  border-top: 0px solid #eaf2ff;
  border-bottom-color: #eff9ff;
  background: #eff9ff;
  color: #000000;
  filter: none;
}
.tabs-header-bottom .tabs li a.tabs-inner .tabs-icon {
  margin-top: -9px;
}
.tabs-header-bottom .tabs li a.tabs-close {
  margin-top: -10px;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  border-left: 0px solid #ffffff;
  border-top: 0px solid #ffffff;
  border-top: 0px;
  border-bottom: 3px solid #339EFF;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
}
.tabs-header-left {
  padding: 0;
}
.tabs-header-left.tabs-header {
  border-radius: 4px 0 0 4px;
  border-width: 1px;
  border-right-width: 0px;
}
.tabs-header-left .tabs {
  padding: 0px;
  border-right: 1px solid #e2e2e2;
}
.tabs-header-left .tabs li {
  height: 36px;
  /*wanghc 2018-3-19  win7-ie11*/
  float: left;
  margin: 0px;
}
.tabs-header-left .tabs li.tabs-brand {
  border-bottom: 1px solid #e2e2e2;
}
.tabs-header-left .tabs li.tabs-brand a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
  border: 1px solid transparent;
  border-left: 3px solid transparent;
}
.tabs-header-left .tabs li::after {
  content: none;
}
.tabs-header-left .tabs li a.tabs-inner {
  border: 1px solid #FFFFFF;
  border-left: 3px solid #FFFFFF;
}
.tabs-header-left .tabs li a.tabs-inner:hover {
  background: #FFFFFF;
  color: #339EFF;
  border: 1px solid #eff9ff;
  border-left: 3px solid #eff9ff;
}
.tabs-header-left .tabs li a.tabs-inner .tabs-icon {
  margin-top: -8px;
}
.tabs-header-left .tabs li.tabs-selected {
  border-right: 1px solid #ffffff;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  border-left: 3px solid #339EFF;
  border-top: 1px solid #E2E2E2;
  border-bottom: 1px solid #E2E2E2;
  border-right: 1px solid transparent;
  background: none;
}
.tabs-header-right {
  padding: 0;
}
.tabs-header-right.tabs-header {
  border-radius: 0 4px 4px 0;
  border-width: 1px;
  border-left-width: 0px;
}
.tabs-header-right .tabs {
  padding: 0px;
  border-left: 1px solid #e2e2e2;
}
.tabs-header-right .tabs li {
  height: 36px;
  /*wanghc 2018-3-19  win7-ie11*/
  float: right;
  margin: 0px;
}
.tabs-header-right .tabs li.tabs-brand {
  border-bottom: 1px solid #e2e2e2;
}
.tabs-header-right .tabs li.tabs-brand a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
  border: 1px solid transparent;
  border-right: 3px solid transparent;
}
.tabs-header-right .tabs li::after {
  content: none;
}
.tabs-header-right .tabs li a.tabs-inner {
  border: 1px solid #FFFFFF;
  border-right: 3px solid #FFFFFF;
}
.tabs-header-right .tabs li a.tabs-inner:hover {
  background: #eff9ff;
  color: #339EFF;
  border: 1px solid #eff9ff;
  border-right: 3px solid #eff9ff;
}
.tabs-header-right .tabs li a.tabs-inner .tabs-icon {
  margin-top: -8px;
}
.tabs-header-right .tabs li.tabs-selected {
  border-left: 1px solid #ffffff;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-right: 3px solid #339EFF;
  border-top: 1px solid #E2E2E2;
  border-bottom: 1px solid #E2E2E2;
  border-left: 1px solid  #ffffff;
  background: none;
}
/*--tabs-gray--*/
.tabs-header-bottom {
  padding: 0;
}
.tabs-header-bottom.tabs-header {
  border-bottom-width: 0;
}
.tabs-header-bottom .tabs {
  height: 36px;
  padding: 0px;
  border-width: 0 0 0px 0;
}
.tabs-header-bottom .tabs li {
  height: 35px;
  /*wanghc 2018-3-19  win7-ie11*/
  margin: 0px;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  border-top: 0px;
  border-bottom: 3px solid #FFFFFF;
}
.tabs-header-bottom .tabs li a.tabs-inner:hover {
  border-top: 0px solid #eaf2ff;
  border-bottom-color: #eff9ff;
  background: #eff9ff;
  color: #000000;
  filter: none;
}
.tabs-header-bottom .tabs li a.tabs-inner .tabs-icon {
  margin-top: -9px;
}
.tabs-header-bottom .tabs li a.tabs-close {
  margin-top: -10px;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  border-left: 0px solid #ffffff;
  border-top: 0px solid #ffffff;
  border-top: 0px;
  border-bottom: 3px solid #339EFF;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
}
/*--tabs-gray--*/
.tabs-container.tabs-gray .tabs-header-bottom {
  padding: 0;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs-scroller-left {
  border-color: #F7F7F7;
  background: none ;
  border-top: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs-scroller-right {
  border-color: #F7F7F7;
  background: none;
  border-top: 1px solid #cccccc;
  border-left: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-header-bottom.tabs-header {
  border: 1px solid #CCC;
  border-top: 0;
}
.tabs-container.tabs-gray .tabs-header-bottom.tabs-header .tabs {
  border: 0;
  border-top: 1px solid #CCC;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs {
  height: 36px;
  padding: 0;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li {
  border: 0;
  margin: 0;
  height: 35px;
  overflow: hidden;
  border-right: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li::after {
  content: none;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li.tabs-selected {
  height: 36px;
  margin-top: -1px;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  color: #1584D2;
  background-color: #ffffff;
  /*modify*/
  border: 0 solid;
  border-bottom-width: 3px;
  border-color: #017BCE;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li.tabs-selected a.tabs-inner:hover {
  border-color: #017BCE;
}
.tabs-gray .tabs-header {
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.tabs-gray .tabs-header.tabs-header-bottom {
  -moz-border-radius: 0 0 4px 4px ;
  -webkit-border-radius: 0 0 4px 4px ;
  border-radius: 0 0 4px 4px ;
}
.tabs-gray .tabs-header.tabs-header-bottom.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-header.tabs-header-left {
  -moz-border-radius: 4px 0 0 4px ;
  -webkit-border-radius: 4px 0 0 4px ;
  border-radius: 4px 0 0 4px ;
}
.tabs-gray .tabs-header.tabs-header-left.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-header.tabs-header-right {
  -moz-border-radius: 0 4px 4px 0 ;
  -webkit-border-radius: 0 4px 4px 0 ;
  border-radius: 0 4px 4px 0 ;
}
.tabs-gray .tabs-header.tabs-header-right.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-header.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels {
  -moz-border-radius: 0 0 4px 4px ;
  -webkit-border-radius: 0 0 4px 4px ;
  border-radius: 0 0 4px 4px ;
}
.tabs-gray .tabs-panels.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-top {
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.tabs-gray .tabs-panels.tabs-panels-top.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-right {
  -moz-border-radius: 0 4px 4px 0 ;
  -webkit-border-radius: 0 4px 4px 0 ;
  border-radius: 0 4px 4px 0 ;
}
.tabs-gray .tabs-panels.tabs-panels-right.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-left {
  -moz-border-radius: 4px 0 0 4px ;
  -webkit-border-radius: 4px 0 0 4px ;
  border-radius: 4px 0 0 4px ;
}
.tabs-gray .tabs-panels.tabs-panels-left.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-keywords .tabs-header {
  background-color: transparent;
  border: 0px;
}
.tabs-keywords .tabs-panels {
  border: 0px;
}
.tabs-keywords .tabs {
  border-width: 0;
  overflow: hidden;
}
.tabs-keywords .tabs li::after {
  content: none;
}
.tabs-keywords .tabs li a.tabs-inner {
  border-radius: 4px;
  border: 0px;
  background-color: #E5E5E5;
  color: #000000;
  border: 1px solid #E5E5E5;
}
.tabs-keywords .tabs li a.tabs-inner:hover {
  background: #EFF9FF;
  color: #339EFF;
  border: 1px solid #E5E5E5;
}
.tabs-keywords .tabs li.tabs-selected a.tabs-inner {
  background-color: #EFF9FF;
  background-image: url(images/keywords_arrow.png);
  background-position: right 0 bottom 0;
  background-repeat: no-repeat;
  color: #339EFF;
  border: 1px solid #48B9FF;
}
.tabs-gray-btm .tabs li a.tabs-close {
  margin-top: -14px;
}
.tabs-gray-btm .tabs a.tabs-inner {
  border-bottom: 1px solid #E2E2E2;
}
.tabs-system li a.tabs-inner::after {
  content: ' ';
  border-right: 1px solid #E2E2E2;
  right: 0;
  position: absolute;
  top: 8px;
  font-size: 14px;
  line-height: 14px;
  width: 1px;
  height: 20px;
}
.tabs-system li a.tabs-inner:hover {
  background: none;
}
.tabs-system li a.tabs-inner:hover + .tabs-close {
  color: #339EFF;
}
.tabs-system li.tabs-selected {
  background-color: #339EFF;
}
.tabs-system li.tabs-selected a.tabs-inner {
  background: #eff9ff;
  font-weight: 700;
}
.tabs-system li.tabs-selected .tabs-close {
  color: #339EFF;
}
.l-btn-text {
  min-width: 0;
  /*@linkbutton-text-width;*/
  line-height: 28px;
  font-size: 14px;
  padding: 0 15px;
  margin: 0px;
}
.l-btn-icon {
  width: 16px;
  height: 16px;
  position: absolute;
  background-color: #007BE9;
  top: 0px;
  margin-top: 0;
  /*top: @top-4IE8@x9;*/
  /*实测ie8不需要*/
  font-family: "Mw_mifonts";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  padding: 6px 5px 6px 8px;
}
.showicon.l-btn.l-btn-small .l-btn-icon-left .l-btn-icon {
  left: 0px;
}
.showicon.l-btn.l-btn-small .l-btn-icon-left .l-btn-text {
  margin: 0 0 0 30px;
}
.showicon.l-btn.l-btn-small .l-btn-icon-left .l-btn-text.l-btn-empty {
  margin: 0px;
}
.l-btn-plain .l-btn-icon-left .l-btn-icon {
  left: 0px;
  font-size: 16px;
}
.l-btn-left.l-btn-icon-left {
  /*在style加width时，图标偏移*/
  width: 100%;
  width: auto\9;
  /*IE6,7,8*/
  /*2018-11-14*/
  border-radius: 2px;
}
.l-btn-icon-left .l-btn-text {
  margin: 0 0 0 0px;
}
.l-btn-icon-left .l-btn-empty + .l-btn-icon {
  width: 16px;
  padding-left: 6px;
  /*表格中无文字图标居中*/
}
.l-btn-plain .l-btn-icon-left .l-btn-text {
  margin: 0px;
  min-width: 0px;
  padding: 0 8px 0 29px;
}
.l-btn-icon-left .l-btn-icon {
  left: -30px;
  /*默认不显示图标*/
}
.l-btn-icon-right .l-btn-text {
  margin: 0 4px 0 0;
}
.l-btn-plain .l-btn-icon-left .l-btn-text.l-btn-empty {
  margin: 0;
  padding: 0;
}
.l-btn-left .l-btn-empty {
  width: 28px;
  min-width: 28px;
  margin: 0;
  padding: 0;
}
.l-btn-plain:hover {
  background: #eff9ff;
  color: #339eff;
  border: 1px solid #b7d2ff;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.l-btn-focus {
  outline: transparent dotted thin;
  background-color: #006FD2;
}
.l-btn {
  color: #ffffff;
  background: #339EFF;
  filter: none;
  border: 0px solid #339EFF;
  border-radius: 2px;
  text-align: center;
}
.l-btn.active {
  background: #006FD2;
  color: #ffffff;
  border: 0px solid #006FD2;
  filter: none;
}
.l-btn.active .l-btn-icon {
  background-color: #006FD2;
}
.l-btn:hover {
  background: #006FD2;
  color: #ffffff;
  border: 0px solid #006FD2;
  filter: none;
}
.l-btn:hover .l-btn-icon {
  background-color: #006FD2;
}
.l-btn.l-btn-plain {
  border: 0;
  padding: 0;
}
.l-btn.l-btn-selected {
  background: #006FD2;
  color: #ffffff;
}
.l-btn.l-btn-plain.l-btn-focus {
  color: #000000;
}
/*特殊情况（按钮在蓝色病人信息条上时）：hover背景色为#0063a7*/
.l-btn.hover-dark:hover {
  background: #0063a7;
}
.l-btn.hover-dark:hover .l-btn-icon {
  background-color: #0063a7;
}
.l-btn-plain {
  color: #000000;
  background: transparent;
  border: 0px solid transparent;
  padding: 0px;
  filter: none;
}
.l-btn-plain:hover {
  background: #eff9ff;
  color: #339eff;
  border: 0px solid #b7d2ff;
  padding: 0px;
}
.l-btn-plain .l-btn-left .l-btn-icon {
  background-color: transparent;
}
.l-btn-plain.l-btn-plain-selected {
  background: #eff9ff;
  color: #000000;
}
.l-btn-disabled,
.l-btn-disabled:hover,
.l-btn.hover-dark.l-btn-disabled:hover,
.l-btn.hover-dark.l-btn-disabled {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background: #E5E5E5;
  color: #999999;
  border-color: #E5E5E5;
  cursor: no-drop;
}
.l-btn-disabled .l-btn-icon,
.l-btn-disabled:hover .l-btn-icon,
.l-btn.hover-dark.l-btn-disabled:hover .l-btn-icon,
.l-btn.hover-dark.l-btn-disabled .l-btn-icon {
  background-color: #D1D1D1;
}
.l-btn-plain.l-btn-disabled {
  background-color: transparent;
  border-color: transparent;
}
.l-btn-plain.l-btn-disabled .l-btn-left {
  background-color: transparent;
  color: #999999;
  border-color: transparent;
}
.l-btn-plain.l-btn-disabled .l-btn-left .l-btn-icon {
  background-color: transparent;
  opacity: 1;
  filter: alpha(opacity=100);
}
.l-btn-plain.l-btn-disabled .l-btn-left .l-btn-text {
  opacity: 1;
  filter: alpha(opacity=100);
}
/*
.l-btn-disabled,
.l-btn-disabled:hover {
  background:#ffffff;
  color: #444;
}
.hisui-linkbutton.l-btn-disabled,.hisui-linkbutton.l-btn-disabled:hover {
    background:@linkbutton-bgcolor;
    color: #444;    
}*/
/*.linkbutton-plain{
  color: #000000;
  background: transparent;
  border: 1px solid transparent;
  padding: 1px;
  filter: none;
  .l-btn-left .l-btn-icon {
    background-color: transparent;
  }
}*/
a.l-btn-link {
  color: #40A2DE;
}
a.l-btn-link:hover {
  color: #378ec4;
}
/*
.l-btn.big.l-btn-plain{
  .l-btn-icon-left {
    .l-btn-icon{
      left: 50%;
      margin-left: -14px;
    }
   .l-btn-text{
      margin:0 4px 0 0;
      padding:24px 2px 2px 0;
    }
  }
}*/
/*
wanghc 20180516 .big后移，在ie7下这种选择器会变成最后一个类选择
.l-btn.l-btn-plain.big <==> .big
*/
.l-btn.l-btn-plain.big {
  padding: 0;
  border: 0;
}
.l-btn.l-btn-plain.big .l-btn-icon-left {
  padding: 4px 10px;
  width: auto;
}
.l-btn.l-btn-plain.big .l-btn-icon-left .l-btn-icon {
  height: 28px;
  line-height: 28px;
  top: 4px;
  position: absolute;
  width: 100%;
  font-size: 28px;
  padding-left: 5px;
  padding-top: 0px;
}
.l-btn.l-btn-plain.big .l-btn-icon-left .l-btn-text {
  padding: 38px 0 0px;
  /*43--38px 20180929*/
  /*44修改成38px 20230130*/
  line-height: 14px;
  margin: 0;
  text-align: center;
}
.l-btn.l-btn-plain.big:hover {
  border: 0;
  padding: 0;
  background: #eff9ff;
  color: #339EFF;
}
.l-btn.l-btn-plain.big:hover .l-btn-text {
  color: #339EFF;
  /*#21ba45;*/
}
.datagrid-btn-separator.big {
  float: none;
  height: 65px;
  border-left: 1px solid #e2e2e2;
  margin: 0px 4px;
  display: inline-block;
  vertical-align: middle;
}
.l-btn-plain.l-btn.l-btn-small.l-btn-focus {
  background-color: #e5e5e5;
  outline: transparent dotted thin;
  background-color: #eff9ff;
}
.l-btn-plain.l-btn.l-btn-small.l-btn-focus .l-btn-left {
  color: #339eff;
}
/*增加其它色系按钮*/
.l-btn.l-btn-small.l-btn-focus {
  background-color: #006FD2;
  outline: #006FD2 dotted thin;
}
.l-btn.l-btn-small.green {
  background-color: #28BA05;
}
.l-btn.l-btn-small.green.l-btn-disabled {
  background: #E5E5E5;
}
.l-btn.l-btn-small.green:hover,
.l-btn.l-btn-small.green.l-btn-focus {
  background-color: #1A8700;
}
.l-btn.l-btn-small.green:hover.l-btn-disabled,
.l-btn.l-btn-small.green.l-btn-focus.l-btn-disabled {
  background: #E5E5E5;
}
.l-btn.l-btn-small.yellow {
  background-color: #ff9933;
}
.l-btn.l-btn-small.yellow.l-btn-disabled {
  background: #E5E5E5;
}
.l-btn.l-btn-small.yellow:hover,
.l-btn.l-btn-small.yellow.l-btn-focus {
  background-color: #DF7000;
}
.l-btn.l-btn-small.yellow:hover.l-btn-disabled,
.l-btn.l-btn-small.yellow.l-btn-focus.l-btn-disabled {
  background: #E5E5E5;
}
.l-btn.l-btn-small.red {
  background-color: #EE0F0F;
}
.l-btn.l-btn-small.red.l-btn-disabled {
  background: #E5E5E5;
}
.l-btn.l-btn-small.red:hover,
.l-btn.l-btn-small.red.l-btn-focus {
  background-color: #C80000;
}
.l-btn.l-btn-small.red:hover.l-btn-disabled,
.l-btn.l-btn-small.red.l-btn-focus.l-btn-disabled {
  background: #E5E5E5;
}
.big .l-btn-icon {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  /*需求号：3169718 去除字体定义,在before上有定义fontfamily。不去除时在医为浏览器下，big图标不能居中 2023-01-03*/
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  font-size: 14px;
}
.datagrid-header .datagrid-cell span {
  font-size: 14px;
}
.datagrid-header .datagrid-header-autowrap .datagrid-cell {
  white-space: pre-wrap;
  word-break: break-all;
}
/*---*/
.datagrid-header-row,
.datagrid-row {
  height: 35px;
}
.datagrid-header-row td,
.datagrid-row td {
  border-left: 0;
  border-right: 0;
}
.datagrid-row {
  height: 34px;
}
.datagrid-header,
.datagrid-td-rownumber {
  background: #F8F8F8;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #E2E2E2;
  border-style: solid;
}
.datagrid-row-selected,
.datagrid-row-over.datagrid-row-selected {
  /* selected>hover */
  background: #FFEDCE;
  color: #C88003;
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #E5F3FF;
  color: #000000;
  cursor: default;
}
.datagrid-cell,
.datagrid-cell-group {
  /*text-overflow: ellipsis;*/
  /*2018-12-4 showTip*/
  padding: 0 8px;
}
/*toolbar 一点改动*/
.datagrid-toolbar {
  background-color: #ffffff;
}
.datagrid-toolbar .l-btn-plain {
  padding: 0 0 0 ;
  border: 0;
  margin-top: 2px;
  margin-bottom: 2px;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-text {
  line-height: 26px;
  padding: 0 8px 0 29px;
  margin: 0 0 0 ;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-empty {
  margin: 0;
  width: 22px;
  padding: 0 12px 0 0;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-icon {
  height: 16px;
  width: 16px;
  line-height: 16px;
  padding: 5px 5px 5px 8px;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-empty + .l-btn-icon {
  left: 0;
}
.datagrid-toolbar .l-btn-plain:hover {
  padding: 0 0 0 ;
  border: 0;
}
.datagrid-toolbar .l-btn-plain-disabled .l-btn-left .l-btn-icon {
  color: #bbbbbb;
}
/*toolbar 一点改动*/
.datagrid-btoolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
  background: #F4F4F4;
  border-color: #dddddd;
  background-color: #ffffff;
}
.datagrid-btoolbar .l-btn-plain {
  padding: 0 0 0 ;
  border: 0;
  margin-top: 3px;
  margin-bottom: 3px;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-text {
  line-height: 22px;
  padding: 0 10px 0 31px;
  margin: 0 0 0 ;
  color: #666666;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-empty {
  margin: 0;
  width: 22px;
  padding: 0 5px 0 0;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-icon {
  left: 5px;
  height: 22px;
  width: 26px;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-empty + .l-btn-icon {
  left: 0;
}
.datagrid-btoolbar .l-btn-plain:hover {
  padding: 0 0 0 ;
  border: 0;
  background-color: #eff9ff;
}
.datagrid-btoolbar .l-btn-plain:hover .l-btn-text {
  color: #339eff;
}
.datagrid-body .datagrid-editable {
  padding: 1px;
  /*2942041*/
}
.datagrid-body .datagrid-editable .datagrid-editable-input {
  border: 1px solid #CCCCCC;
  background-color: #ffffff;
  margin: 0;
  padding: 0 0 0 5px;
  line-height: 28px;
}
.datagrid-body .datagrid-editable .datagrid-editable-input:hover,
.datagrid-body .datagrid-editable .datagrid-editable-input:active,
.datagrid-body .datagrid-editable .datagrid-editable-input:focus {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
td.datagrid-value-changed {
  background: url('images/dirty.gif') no-repeat 0 0;
}
.datagrid-header-row td,
.datagrid-row td {
  border-left: 0px solid transparent;
  border-right: 1px solid transparent;
}
.table-splitline .datagrid-header-row td,
.table-splitline .datagrid-row td {
  border-right: 1px solid #E2E2E2;
}
.datagrid-view1 td:last-child,
.datagrid-view1 td:first-child {
  border-right-color: #E2E2E2;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  padding-bottom: 1px;
  border-width: 0 1px 0 0;
}
.propertygrid .datagrid-group {
  height: 21px;
  overflow: hidden;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.propertygrid .datagrid-group span {
  font-weight: bold;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  border-color: #dddddd;
}
.propertygrid .datagrid-view1 .datagrid-group {
  border-color: #E0ECFF;
}
.propertygrid .datagrid-view2 .datagrid-group {
  border-color: #dddddd;
}
.propertygrid .datagrid-group,
.propertygrid .datagrid-view1 .datagrid-body,
.propertygrid .datagrid-view1 .datagrid-row-over,
.propertygrid .datagrid-view1 .datagrid-row-selected {
  background: #E0ECFF;
}
.pagination span.l-btn-text.l-btn-empty {
  line-height: 24px;
  width: 16px;
  min-width: 16px;
  margin: 0 4px;
}
.pagination .l-btn-plain .l-btn-icon-left .l-btn-icon {
  text-align: center;
  vertical-align: top;
  width: 14px;
  height: 14px;
  font-size: 12px;
  line-height: 14px;
}
.pagination span.l-btn-icon {
  width: 16px;
  height: 16px;
  background-image: none;
  padding-left: 5px;
  /*20220925 向右偏太多*/
}
.pagination span.l-btn-icon.pagination-first::before {
  content: "\f312";
}
.pagination span.l-btn-icon.pagination-prev::before {
  content: "\f302";
}
.pagination span.l-btn-icon.pagination-next::before {
  content: "\f301";
}
.pagination span.l-btn-icon.pagination-last::before {
  content: "\f308";
}
.pagination span.l-btn-icon.pagination-load::before {
  content: "\f029";
}
.pagination .l-btn-plain .l-btn-icon-left .l-btn-text.l-btn-empty {
  margin: 0 0 0 9px ;
}
.pagination .pagination-num:active,
.pagination .pagination-num:focus,
.pagination .pagination-num:hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 12px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 12px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 22px;
}
.calendar-title {
  text-align: center;
  height: 22px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 2px;
  padding: 0 3px;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  width: 14px;
  height: 14px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth {
  left: 20px;
  background: url('images/calendar_arrows.png') no-repeat -18px -2px;
}
.calendar-nextmonth {
  right: 20px;
  background: url('images/calendar_arrows.png') no-repeat -34px -2px;
}
.calendar-prevyear {
  left: 3px;
  background: url('images/calendar_arrows.png') no-repeat -1px -2px;
}
.calendar-nextyear {
  right: 3px;
  background: url('images/calendar_arrows.png') no-repeat -49px -2px;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-other-month {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.calendar-disabled {
  opacity: 0.2;
  filter: alpha(opacity=20);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 12px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 40px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  margin: 0;
  padding: 2px;
  font-weight: bold;
  font-size: 12px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 21px;
  height: 21px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 2px 2px;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -45px 2px;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-body th,
.calendar-menu-month {
  color: #4d4d4d;
}
.calendar-day {
  color: #000000;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #95B8E7;
}
.calendar {
  border-color: #95B8E7;
}
.calendar-header {
  background: #E0ECFF;
}
.calendar-body,
.calendar-menu {
  background: #ffffff;
}
.calendar-body th {
  background: #F4F4F4;
  padding: 2px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #eaf2ff;
  color: #000000;
}
.calendar-hover {
  border: 1px solid #b7d2ff;
  padding: 0;
}
.calendar-selected {
  background-color: #ffe48d;
  color: #000000;
  border: 1px solid #ffab3f;
  padding: 0;
}
/*日历最下面的today close*/
.datebox-button a {
  font-size: 12px;
}
.datebox-button {
  background-color: #F4F4F4;
}
.datebox-button a {
  color: #444;
}
.datebox .combo-arrow {
  background: none;
}
.datetimebox-f + .datebox .combo-arrow::before {
  content: "\f044";
}
.datebox .combo-arrow::before {
  content: "\f024";
}
/*.numberbox {
  border: 1px solid #95B8E7;
  margin: 0;
  padding: 0 2px;
  vertical-align: middle;
}
*/
.spinner {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  /*20221010 disabled样式覆盖:hover样式*/
}
.spinner .spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}
.spinner .spinner-arrow .spinner-arrow-hover {
  opacity: 1;
  filter: alpha(opacity=100);
  color: #339EFF;
  background-color: #d5eefe;
}
.spinner:hover,
.spinner.spinner-hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.spinner.disabled {
  background-color: #F5F5F5;
  border-color: #CCCCCC;
}
.spinner.disabled .spinner-arrow {
  background-color: #F5F5F5;
}
.spinner.disabled .spinner-arrow .spinner-arrow-up {
  background-image: none;
  color: #BBBBBB;
}
.spinner.disabled .spinner-arrow .spinner-arrow-down {
  background-image: none;
  color: #BBBBBB;
}
.spinner .spinner-text {
  font-size: 14px;
  border: 0px;
  line-height: 28px;
  height: 28px;
  margin: 0;
  padding: 0 0 0 5px;
  *margin-top: -1px;
  *height: 26px;
  *line-height: 26px;
  _height: 26px;
  _line-height: 26px;
  vertical-align: baseline;
}
.spinner-arrow-up::before {
  content: "\f477";
}
.spinner-arrow-down::before {
  content: "\f476";
}
.spinner {
  border-color: #CCCCCC;
}
.progressbar {
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  overflow: hidden;
  position: relative;
}
.progressbar-text {
  text-align: center;
  position: absolute;
}
.progressbar-value {
  position: relative;
  overflow: hidden;
  width: 0;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.progressbar {
  border-color: #95B8E7;
}
.progressbar-text {
  color: #000000;
  font-size: 12px;
}
.progressbar-value .progressbar-text {
  background-color: #ffe48d;
  color: #000000;
}
.searchbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.searchbox.disabled {
  border-color: #CCCCCC;
}
.searchbox.disabled .searchbox-button {
  background-color: #F5F5F5;
  background-image: url('images/searchbox_button_disable.png');
}
.searchbox:hover,
.searchbox.searchbox-hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.searchbox .searchbox-text {
  font-size: 14px;
  border: 0;
  margin: 0;
  padding: 0 5px;
  *margin-top: -1px;
  vertical-align: top;
}
.searchbox .searchbox-text:active {
  background-color: #EFF9FF;
}
.searchbox .searchbox-text:disabled {
  background-color: #F5F5F5;
}
.searchbox .searchbox-text:focus {
  background-color: #EFF9FF;
}
.searchbox .searchbox-text:focus + span > span {
  /*20190710*/
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
}
.searchbox .searchbox-prompt {
  font-size: 14px;
  color: #ccc;
}
.searchbox-button {
  width: 30px;
  height: 28px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 1;
  filter: alpha(opacity=100);
}
.searchbox-button.searchbox-button-hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
  color: #339EFF;
}
.searchbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox-button::before {
  content: "\f018";
}
.searchbox {
  border-color: #CCCCCC;
  /*#95B8E7;*/
  background-color: none;
}
.searchbox .l-btn-plain {
  background: #E0ECFF;
}
.searchbox .l-btn-plain-disabled,
.searchbox .l-btn-plain-disabled:hover {
  opacity: 1;
  filter: alpha(opacity=100);
}
.slider-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-h {
  height: 22px;
}
.slider-v {
  width: 22px;
}
.slider-inner {
  position: relative;
  height: 6px;
  top: 7px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
}
.slider-handle {
  position: absolute;
  display: block;
  outline: none;
  width: 20px;
  height: 20px;
  top: -7px;
  margin-left: -10px;
}
.slider-tip {
  position: absolute;
  display: inline-block;
  line-height: 12px;
  font-size: 12px;
  white-space: nowrap;
  top: -22px;
}
.slider-rule {
  position: relative;
  top: 15px;
}
.slider-rule span {
  position: absolute;
  display: inline-block;
  font-size: 0;
  height: 5px;
  border-width: 0 0 0 1px;
  border-style: solid;
}
.slider-rulelabel {
  position: relative;
  top: 20px;
}
.slider-rulelabel span {
  position: absolute;
  display: inline-block;
  font-size: 12px;
}
.slider-v .slider-inner {
  width: 6px;
  left: 7px;
  top: 0;
  float: left;
}
.slider-v .slider-handle {
  left: 3px;
  margin-top: -10px;
}
.slider-v .slider-tip {
  left: -10px;
  margin-top: -6px;
}
.slider-v .slider-rule {
  float: left;
  top: 0;
  left: 16px;
}
.slider-v .slider-rule span {
  width: 5px;
  height: 'auto';
  border-left: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.slider-v .slider-rulelabel {
  float: left;
  top: 0;
  left: 23px;
}
.slider-handle {
  background: url('images/slider_handle.png') no-repeat;
}
.slider-inner {
  border-color: #95B8E7;
  background: #E0ECFF;
}
.slider-rule span {
  border-color: #95B8E7;
}
.slider-rulelabel span {
  color: #000000;
}
.menu {
  padding: 0;
  background-color: #fff;
  border-color: #40a2de;
  color: #000;
  border: none;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}
.menu-icon {
  left: 10px;
  width: 16px;
  font-family: "Mw_mifonts";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  font-weight: 500;
  font-size: 16px;
  line-height: normal;
  color: #000;
}
.menu-item {
  height: 36px;
  border: none;
}
.menu-text {
  height: 35px;
  line-height: 35px;
  float: left;
  padding-left: 41px ;
  /*28px;*/
}
.menu-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 15px;
  top: 50%;
  margin-top: -8px;
}
.menu-line {
  display: none;
}
.menu-active {
  border-radius: 0;
}
.menu-text,
.menu-text span {
  font-size: 14px;
}
/*.menu-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #ccc;
  -moz-box-shadow: 2px 2px 3px #cccccc;
  -webkit-box-shadow: 2px 2px 3px #cccccc;
  box-shadow: 2px 2px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2, MakeShadow=false, ShadowOpacity=0.2); 
}*/
.menu-shadow {
  -moz-box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  -webkit-box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
}
.menu-active {
  border-color: #e5e5e5;
  color: #000000;
  background: #e5e5e5;
}
.menu-rightarrow {
  background: url(images/calendar_arrows.png) no-repeat -32px center;
}
.menu-rightarrow {
  right: 15px;
}
.menu-no-icon .menu-text {
  padding-left: 15px;
}
.l-btn-plain.m-btn .l-btn-text {
  color: #000000;
}
.m-btn-downarrow,
.s-btn-downarrow {
  color: #ffffff;
}
.l-btn-plain .m-btn-downarrow,
.l-btn-plain .s-btn-downarrow {
  color: #7E7E7E;
}
.l-btn.m-btn-active {
  background: #006FD2;
  border: 0px solid #006FD2;
}
.l-btn.l-btn-plain.m-btn-active {
  background: #EFF9FF;
  border: 0px solid #EFF9FF;
}
.m-btn .l-btn-text {
  min-width: 40px;
  line-height: 26px;
}
.m-btn .l-btn-left .l-btn-text {
  margin-right: 16px;
  margin-left: 0px;
  padding-left: 8px;
}
.m-btn .l-btn-left.l-btn-icon-left .l-btn-text {
  margin-right: 16px;
  margin-left: 24px;
  padding-right: 6px;
  padding-left: 5px;
}
.m-btn .l-btn-left .m-btn-line {
  width: 24px;
}
.l-btn .m-btn-downarrow,
.l-btn .s-btn-downarrow {
  width: 28px;
  height: 28px;
}
.l-btn .m-btn-downarrow::before,
.l-btn .s-btn-downarrow::before {
  content: "\f476";
}
.m-btn-plain-active,
.s-btn-plain-active {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.m-btn-plain-active {
  background-color: #EFF9FF;
}
.m-btn-plain-active .l-btn-left .l-btn-text,
.m-btn-plain-active .l-btn-icon {
  color: #339eff;
}
.m-btn-plain-active .m-btn-downarrow {
  color: #339eff;
}
/*ie11加了兼容模式后,
  内核成ie7
  .menubutton-blue.l-btn.l-btn-plain .l-btn-left .l-btn-icon{display:none;}
  会选中 .l-btn-plain .l-btn-left .l-btn-icon{display:none;}

*/
.l-btn.l-btn-plain.menubutton-blue {
  border: 0;
  padding: 0;
  background-color: #339EFF;
}
.l-btn.l-btn-plain.menubutton-blue:hover {
  background-color: #006FD2;
}
.l-btn.l-btn-plain.menubutton-blue.m-btn-plain-active {
  /*展开时一直保持底色*/
  background-color: #006FD2;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left {
  width: 100%;
  text-align: left;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .l-btn-text {
  color: #fff;
  margin-left: 0;
  padding-left: 10px;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .l-btn-icon {
  display: none;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .m-btn-downarrow {
  color: #FFFFFF;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .m-btn-downarrow::before {
  content: "\f476";
}
.menu.menubutton-blue {
  border-radius: 0;
}
.menu.menubutton-blue .menu-item {
  background-color: #339EFF;
  height: 30px;
  border-bottom: 1px solid #339EFF;
}
.menu.menubutton-blue .menu-item:hover {
  background-color: #006FD2;
}
.menu.menubutton-blue .menu-item .menu-text {
  color: #fff;
  padding-left: 10px;
  line-height: 30px;
}
.menu.menubutton-blue .menu-item .menu-icon {
  display: none;
}
.menu.menubutton-blue .menu-item:last-child {
  border-bottom: 0px;
}
.menu.menubutton-blue .menu-sep {
  display: none;
}
.l-btn.l-btn-plain.menubutton-toolbar {
  border: 0;
  padding: 0;
  background-color: transparent;
}
.l-btn.l-btn-plain.menubutton-toolbar:hover {
  background-color: #EFF9FF;
}
.l-btn.l-btn-plain.menubutton-toolbar.m-btn-plain-active {
  background-color: #EFF9FF;
}
.l-btn.l-btn-plain.menubutton-toolbar.m-btn-plain-active .l-btn-left .l-btn-text,
.l-btn.l-btn-plain.menubutton-toolbar.m-btn-plain-active .l-btn-icon {
  color: #339eff;
}
.l-btn.l-btn-plain.menubutton-toolbar.m-btn-plain-active .m-btn-downarrow {
  color: #339eff;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left {
  width: 100%;
  text-align: left;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .l-btn-text {
  line-height: 26px;
  padding: 0 26px 0 31px;
  margin: 0 0 0 ;
  color: #666666;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .l-btn-icon {
  height: 16px;
  width: 16px;
  line-height: 16px;
  padding: 5px 5px 5px 8px;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .m-btn-downarrow {
  height: 16px;
  width: 16px;
  right: 10px;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .m-btn-downarrow::before {
  content: "\f476";
}
.menu.menubutton-toolbar .menu-item {
  background-color: #fff;
  height: 35px;
  /*26px*/
  border: 0px;
}
.menu.menubutton-toolbar .menu-item:hover {
  background-color: #E5E5E5;
}
.menu.menubutton-toolbar .menu-item .menu-text {
  color: #000;
  padding-left: 10px;
  /*10px*/
  line-height: 35px;
  /*26px*/
}
.menu.menubutton-toolbar .menu-item .menu-icon {
  display: none;
}
.menu.menubutton-toolbar .menu-item:last-child {
  border-bottom: 0px;
}
.menu.menubutton-toolbar .menu-sep {
  margin-left: 10px;
  margin-top: 0;
  margin-bottom: 0;
  border-bottom: 0px;
}
.s-btn:hover .m-btn-line,
.s-btn-active .m-btn-line,
.s-btn-plain-active .m-btn-line {
  display: inline-block;
}
.l-btn:hover .s-btn-downarrow,
.s-btn-active .s-btn-downarrow,
.s-btn-plain-active .s-btn-downarrow {
  border-style: solid;
  border-color: #aac5e7;
  border-width: 0 0 0 1px;
}
.s-btn .l-btn-left.l-btn-icon-left .l-btn-text {
  padding-right: 16px;
}
.l-btn-plain.m-btn:hover .l-btn-left .l-btn-text {
  color: #339eff;
}
.l-btn-plain.m-btn:hover .l-btn-left .m-btn-downarrow {
  color: #339eff;
}
.menubutton-blue.l-btn-plain.m-btn:hover .l-btn-left .l-btn-text {
  color: #ffffff;
}
.messager-body {
  padding: 10px;
  overflow: hidden;
  word-wrap: break-word;
}
.messager-button {
  text-align: center;
  padding-top: 10px;
}
.messager-button .l-btn {
  width: 70px;
  text-align: center;
}
.messager-icon {
  float: left;
  width: 32px;
  height: 32px;
  margin: 0 10px 10px 0;
}
.messager-info {
  background: url('images/lite/messager_icons.png') no-repeat scroll 0 0;
}
.messager-question {
  background: url('images/lite/messager_icons.png') no-repeat scroll -32px 0;
}
.messager-error {
  background: url('images/lite/messager_icons.png') no-repeat scroll -64px 0;
}
.messager-warning {
  background: url('images/lite/messager_icons.png') no-repeat scroll -96px 0;
}
.messager-success {
  background: url('images/lite/messager_icons.png') no-repeat scroll -128px 0;
}
.messager-progress {
  padding: 10px;
}
.messager-p-msg {
  margin-bottom: 5px;
}
.messager-body .messager-input {
  width: 96%;
  border-radius: 2px;
  padding: 1px 0 1px 10px;
  border: 1px solid #cccccc;
  /*2943214*/
  height: 26px;
}
/*--popover--*/
.messager-popover {
  position: absolute;
  border-radius: 5px;
  display: block;
  padding: 6px 15px;
  font-weight: bold;
  font-size: 14px;
  z-index: 10000;
}
.messager-popover.success {
  color: #13AE37;
  background: #EFFFEB;
  border: 1px #A8E59A solid;
}
.messager-popover.info {
  color: #339EFF;
  background: #EFF9FF;
  border: 1px #9CD0FF solid;
}
.messager-popover.alert {
  color: #DF7000;
  background: #FFF1E3;
  border: 1px #FF9933 solid;
}
.messager-popover.error {
  color: #EE0F0F;
  background: #FFE9E9;
  border: 1px #FFA4A4 solid;
}
.messager-popover-icon {
  float: left;
  width: 16px;
  height: 16px;
  margin-right: 10px;
  margin-top: 2px;
}
.messager-popover-icon.success {
  background: url('images/lite/messager_popover.png') no-repeat scroll 0 0;
}
.messager-popover-icon.alert {
  background: url('images/lite/messager_popover.png') no-repeat scroll -16px 0;
}
.messager-popover-icon.error {
  background: url('images/lite/messager_popover.png') no-repeat scroll -32px 0;
}
.messager-popover-icon.info {
  background: url('images/lite/messager_popover.png') no-repeat scroll -48px 0;
}
.messager-popover .content {
  height: 18px;
  float: left;
}
.messager-popover .close {
  float: right;
  width: 16px;
  height: 16px;
  display: block;
}
.messager-popover.success .close {
  background: url('images/lite/messager_popover.png') no-repeat scroll -64px 0;
}
.messager-popover.alert .close {
  background: url('images/lite/messager_popover.png') no-repeat scroll -80px 0;
}
.messager-popover.error .close {
  background: url('images/lite/messager_popover.png') no-repeat scroll -96px 0;
}
.messager-popover.info .close {
  background: url('images/lite/messager_popover.png') no-repeat scroll -112px 0;
}
.messager-popover .close:hover {
  background: url('images/lite/messager_popover.png') no-repeat scroll -128px 0;
  cursor: pointer;
}
.tree-node {
  height: auto;
}
.tree-title {
  font-size: 14px;
  height: auto;
  line-height: 28px;
  padding: 0 5px;
  /*2942057*/
}
.tree-node-selected {
  background: #E5E5E5;
}
.tree-node-hover {
  background: #E5E5E5;
}
/*增加.tree>li>ul解决 大节点也变色背景问题 */
.tree > li > ul .tree-node-selected .tree-title {
  background: #E5E5E5;
}
.tree-checkbox,
.tree-collapsed,
.tree-expanded,
.tree-file,
.tree-folder,
.tree-indent {
  margin-top: 5px;
}
/*线条*/
.tree-lines {
  /*高度不固定时  tree-folder tree-file tree-folder-open   都加了一个类 tree-icon-lines*/
}
.tree-lines .tree-collapsed,
.tree-lines .tree-expanded,
.tree-lines .tree-indent {
  margin-top: 0;
  height: auto;
  height: 28px;
}
.tree-lines .tree-line {
  background: url('images/lite/tree_lines.png') no-repeat -113px center;
}
.tree-lines .tree-join {
  background: url('images/lite/tree_lines.png') no-repeat -129px center;
}
.tree-lines .tree-expanded {
  background: url('images/lite/tree_lines.png') no-repeat -81px center;
}
.tree-lines .tree-collapsed {
  background: url('images/lite/tree_lines.png') no-repeat -65px center;
}
.tree-lines .tree-root-first .tree-expanded {
  background: url('images/lite/tree_lines.png') no-repeat -200px center;
}
.tree-lines .tree-root-first .tree-collapsed {
  background: url('images/lite/tree_lines.png') no-repeat -184px center;
}
.tree-lines .tree-node-last .tree-expanded,
.tree-lines .tree-root-one .tree-expanded {
  background: url('images/lite/tree_lines.png') no-repeat -17px center;
}
.tree-lines .tree-node-last .tree-collapsed,
.tree-lines .tree-root-one .tree-collapsed {
  background: url('images/lite/tree_lines.png') no-repeat -1px center;
}
.tree-lines .tree-joinbottom {
  background: url('images/lite/tree_lines.png') no-repeat -97px center;
}
.tree-lines .tree-folder.tree-folder-open.tree-icon-lines {
  margin: 0;
  background: url('images/lite/tree_lines.png') no-repeat -151px center;
}
.tree-lines .tree-folder.tree-icon-lines,
.tree-lines .tree-file.tree-icon-lines {
  margin: 0;
  background: url('images/lite/tree_lines.png') no-repeat -167px center;
}
/*将树的样式整成手风琴 加个类accordiontree 尝试下*/
.accordiontree.tree {
  border: 1px solid #E5E5E5;
}
.accordiontree.tree > li > .tree-node {
  height: 36px;
  line-height: 36px;
  background-color: #F8F8F8;
  position: relative;
}
.accordiontree.tree > li > .tree-node .tree-hit {
  position: absolute;
  height: 14px;
  width: 14px;
  top: 50%;
  margin-top: -7px;
  right: 10px;
}
.accordiontree.tree > li > .tree-node .tree-hit.tree-collapsed::before {
  content: "\f265";
}
.accordiontree.tree > li > .tree-node .tree-hit.tree-expanded::before {
  content: "\f264";
}
.accordiontree.tree > li > .tree-node .tree-hit:hover {
  color: #378ec4;
  background-color: #eff9ff;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  opacity: 1.0;
  filter: opacity(100);
}
.accordiontree.tree > li > .tree-node .tree-icon {
  display: none;
}
.accordiontree.tree > li > .tree-node .tree-title {
  height: 36px;
  line-height: 36px;
  color: #000;
  padding: 0 34px 0 15px;
}
.accordiontree.tree > li > .tree-node.tree-node-hover {
  background-color: #E5E5E5;
}
.accordiontree.tree > li > ul {
  background-color: #ffffff;
}
.accordiontree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 36px;
  line-height: 36px;
  position: relative;
}
.accordiontree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #E5F3FF;
}
.accordiontree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #E5F3FF;
}
.accordiontree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  /*2019-12-12 解决选中行变黄底色问题 */
  background: #E5F3FF;
}
.accordiontree.tree > li > ul .tree-node .tree-hit {
  width: 15px;
  height: 18px;
  margin-top: 9px;
}
.accordiontree.tree > li > ul .tree-node .tree-indent {
  width: 15px;
}
.accordiontree.tree > li > ul .tree-node > span.tree-indent:first-child {
  width: 0px;
}
.accordiontree.tree > li > ul .tree-node .tree-icon {
  display: none;
}
.accordiontree.tree > li > ul .tree-node .tree-title {
  height: 36px;
  line-height: 36px;
  color: #000;
  padding: 0 10px 0 0;
}
.treegrid-tr-tree-div-hidden {
  display: none;
}
.validatebox-text {
  height: 28px;
  line-height: 28px;
  margin: 0;
  padding: 0 0 0 5px;
  box-sizing: content-box;
  border: 1px solid #CCCCCC;
  background-color: #ffffff;
  color: #000000;
  font-size: 14px;
  vertical-align: middle;
}
.validatebox-text:active {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.validatebox-text:focus {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.validatebox-text:focus + span > span,
.validatebox-text:focus + input + span > span {
  /*20190710 numberspinner--input+input+span*/
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
}
.validatebox-text:hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
/*2019-07-10 span:hover---输入框与arrow都变色*/
.searchbox:hover input,
.lookup:hover input,
.combo:hover input,
.spinner:hover input,
.searchbox:hover span > span,
.lookup:hover span > span,
.combo:hover span > span,
.spinner:hover span > span {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
}
/*2022-10-10 组件禁用时背景色不变*/
.searchbox.disabled:hover input,
.lookup.disabled:hover input,
.combo.disabled:hover input,
.spinner.disabled:hover input,
.searchbox.disabled:hover span > span,
.lookup.disabled:hover span > span,
.combo.disabled:hover span > span,
.spinner.disabled:hover span > span {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #F5F5F5;
}
.validatebox-text.validatebox-invalid {
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
  border-color: #ffa4a4;
  background-color: #ffe9e9;
}
.validatebox-text.validatebox-invalid::-webkit-input-placeholder {
  color: #ee0f0f;
}
textarea.validatebox-text.validatebox-invalid {
  background-position: right 10px bottom 10px;
  background-position-x: 90%\9;
  background-position-y: 50%\9;
}
input[type=text]::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
input::-ms-reveal {
  display: none;
  width: 0px;
  height: 0px;
}
input:-webkit-input-placeholder {
  /* WebKit 核心浏览器 如谷歌Chrome */
  color: #999;
}
input:-moz-placeholder {
  /* 火狐浏览器 */
  color: #999;
}
input:-ms-input-placeholder {
  /* IE 10+ */
  color: #999;
}
.tooltip {
  padding: 5px 10px 5px;
}
.tooltip-content {
  font-size: 14px;
  word-break: break-all;
}
.tooltip-arrow-outer,
.tooltip-arrow {
  _border-color: #4f75aa;
  _filter: chroma(color=#4f75aa);
}
.tooltip {
  background-color: rgba(0, 0, 0, 0.7);
  border-color: #4f75aa;
  border-width: 0;
  color: #ffffff;
}
.tooltip-right .tooltip-arrow-outer {
  border-right-color: #4f75aa;
  border-width: 0;
}
.tooltip-right .tooltip-arrow {
  border-right-color: #ffffff;
}
.tooltip-left .tooltip-arrow-outer {
  border-left-color: #4f75aa;
  border-width: 0;
}
.tooltip-left .tooltip-arrow {
  border-left-color: #ffffff;
}
.tooltip-top .tooltip-arrow-outer {
  border-top-color: #4f75aa;
  border-width: 0;
}
.tooltip-top .tooltip-arrow {
  border-top-color: #ffffff;
}
.tooltip-bottom .tooltip-arrow-outer {
  border-bottom-color: #4f75aa;
  border-width: 0;
}
.tooltip-bottom .tooltip-arrow {
  border-bottom-color: #ffffff;
}
label.checkbox,
label.radio {
  background: url('images/lite/checkbox_lite_v.png') no-repeat;
  padding-left: 21px;
  cursor: pointer;
  line-height: 22px;
  height: 21px;
  vertical-align: middle;
  display: inline-block;
}
label.checkbox.right,
label.radio.right {
  padding-left: 0px;
  padding-right: 24px;
}
label.radio {
  background-position-x: -6px;
  background-position-y: -120px;
}
label.radio:hover {
  background-position-y: -144px;
}
label.radio.hover {
  background-position-y: -144px;
}
label.radio.checked {
  background-position-y: -168px;
}
label.radio.disabled {
  background-position-y: -192px;
}
label.radio.checked.disabled {
  background-position-y: -216px;
}
label.radio.invalid {
  background-position-y: -264px;
}
label.radio.right {
  background-position-x: right;
}
label.checkbox,
label.hischeckbox_square-blue.radio {
  background-position-x: -6px;
  background-position-y: 0;
}
label.checkbox:hover,
label.hischeckbox_square-blue.radio:hover {
  background-position-y: -24px;
}
label.checkbox.hover,
label.hischeckbox_square-blue.radio.hover {
  background-position-y: -24px;
}
label.checkbox.checked,
label.hischeckbox_square-blue.radio.checked {
  background-position-y: -48px;
}
label.checkbox.disabled,
label.hischeckbox_square-blue.radio.disabled {
  background-position-y: -72px;
}
label.checkbox.checked.disabled,
label.hischeckbox_square-blue.radio.checked.disabled {
  background-position-y: -96px;
}
label.checkbox.invalid,
label.hischeckbox_square-blue.radio.invalid {
  background-position-y: -240px;
}
label.checkbox.right,
label.hischeckbox_square-blue.radio.right {
  background-position-x: right;
}
.datagrid-header-row label.checkbox {
  margin-top: -4px;
  margin-left: 2px;
}
.has-switch {
  display: inline-block;
  cursor: pointer;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  height: 26px;
  border: none;
  border-color: transparent;
  background-color: transparent;
  position: relative;
  text-align: left;
  overflow: hidden;
  line-height: 8px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  vertical-align: middle;
  min-width: 80px;
}
.has-switch.switch-mini {
  min-width: 56px;
  height: 20px;
}
.has-switch.switch-mini i.switch-mini-icons {
  height: 1.20em;
  line-height: 9px;
  vertical-align: text-top;
  text-align: center;
  transform: scale(0.6);
  margin-top: -1px;
  margin-bottom: -1px;
}
.has-switch.switch-small {
  min-width: 80px;
}
.has-switch.switch-large {
  min-width: 120px;
}
.has-switch.deactivate {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default !important;
}
.has-switch.deactivate label,
.has-switch.deactivate span {
  cursor: default !important;
}
.has-switch > div {
  display: inline-block;
  width: 150%;
  position: relative;
  top: 0;
}
.has-switch > div.switch-animate {
  -webkit-transition: left 0.5s;
  -moz-transition: left 0.5s;
  -o-transition: left 0.5s;
  transition: left 0.5s;
}
.has-switch > div.switch-off {
  background-color: #bdbdbd;
  left: -50%;
  /*-49->-50 */
}
.has-switch > div.switch-off label {
  border: 1px solid #bdbdbd;
}
.has-switch > div.switch-on {
  background-color: #48B9FF;
  left: 0%;
}
.has-switch > div.switch-on label {
  border: 1px solid #48B9FF;
}
.has-switch input[type=radio],
.has-switch input[type=checkbox] {
  display: none;
}
.has-switch span,
.has-switch label {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  display: inline-block;
  height: 26px;
  padding-bottom: 3px;
  padding-top: 2px;
  font-size: 14px;
  line-height: 20px;
}
.has-switch span.switch-mini,
.has-switch label.switch-mini {
  padding-bottom: 4px;
  padding-top: 3px;
  font-size: 10px;
  line-height: 12px;
  height: 20px;
}
.has-switch span.switch-small,
.has-switch label.switch-small {
  padding-bottom: 3px;
  padding-top: 3px;
  line-height: 18px;
}
.has-switch span.switch-large,
.has-switch label.switch-large {
  padding-bottom: 9px;
  padding-top: 9px;
  font-size: 16px;
  line-height: normal;
}
.has-switch label {
  text-align: center;
  margin-top: 0;
  margin-bottom: 0;
  z-index: 100;
  width: 34%;
  color: #333333;
  border-radius: 4px;
  background-color: #fff;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch label.disabled,
.has-switch label[disabled] {
  color: #333333;
  background-color: #e6e6e6;
  *background-color: #d9d9d9;
}
.has-switch label:active,
.has-switch label.active {
  background-color: #cccccc;
}
.has-switch label i {
  color: #000;
  line-height: 18px;
  pointer-events: none;
}
.has-switch span {
  text-align: center;
  z-index: 1;
  width: 33%;
}
.has-switch span.switch-left {
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  border-bottom-left-radius: 4px;
}
.has-switch span.switch-right {
  color: #fff;
  position: relative;
  top: 0;
  background-color: #bdbdbd;
  border-color: #ffffff #ffffff #d9d9d9;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #ffffff;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-right.disabled,
.has-switch span.switch-right[disabled] {
  color: #333333;
  background-color: #ffffff;
  *background-color: #f2f2f2;
}
.has-switch span.switch-primary,
.has-switch span.switch-left {
  color: #ffffff;
  position: relative;
  top: 0;
  background-color: #48B9FF;
  border-color: #0088cc #0088cc #005580;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #0088cc;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-info {
  color: #ffffff;
  background-color: #41a7c5;
  background-image: -moz-linear-gradient(top, #2f96b4, #5bc0de);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#2f96b4), to(#5bc0de));
  background-image: -webkit-linear-gradient(top, #2f96b4, #5bc0de);
  background-image: -o-linear-gradient(top, #2f96b4, #5bc0de);
  background-image: linear-gradient(to bottom, #2f96b4, #5bc0de);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff2f96b4', endColorstr='#ff5bc0de', GradientType=0);
  border-color: #5bc0de #5bc0de #28a1c5;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #5bc0de;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-info:hover,
.has-switch span.switch-info:focus,
.has-switch span.switch-info:active,
.has-switch span.switch-info.active,
.has-switch span.switch-info.disabled,
.has-switch span.switch-info[disabled] {
  color: #ffffff;
  background-color: #5bc0de;
  *background-color: #46b8da;
}
.has-switch span.switch-info:active,
.has-switch span.switch-info.active {
  background-color: #31b0d5;
}
.has-switch span.switch-success {
  color: #ffffff;
  background-color: #58b058;
  background-image: -moz-linear-gradient(top, #51a351, #62c462);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#51a351), to(#62c462));
  background-image: -webkit-linear-gradient(top, #51a351, #62c462);
  background-image: -o-linear-gradient(top, #51a351, #62c462);
  background-image: linear-gradient(to bottom, #51a351, #62c462);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff51a351', endColorstr='#ff62c462', GradientType=0);
  border-color: #62c462 #62c462 #3b9e3b;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #62c462;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-success:hover,
.has-switch span.switch-success:focus,
.has-switch span.switch-success:active,
.has-switch span.switch-success.active,
.has-switch span.switch-success.disabled,
.has-switch span.switch-success[disabled] {
  color: #ffffff;
  background-color: #62c462;
  *background-color: #4fbd4f;
}
.has-switch span.switch-success:active,
.has-switch span.switch-success.active {
  background-color: #42b142;
}
.has-switch span.switch-warning {
  color: #ffffff;
  background-color: #f9a123;
  background-image: -moz-linear-gradient(top, #f89406, #fbb450);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f89406), to(#fbb450));
  background-image: -webkit-linear-gradient(top, #f89406, #fbb450);
  background-image: -o-linear-gradient(top, #f89406, #fbb450);
  background-image: linear-gradient(to bottom, #f89406, #fbb450);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff89406', endColorstr='#fffbb450', GradientType=0);
  border-color: #fbb450 #fbb450 #f89406;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #fbb450;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-warning:hover,
.has-switch span.switch-warning:focus,
.has-switch span.switch-warning:active,
.has-switch span.switch-warning.active,
.has-switch span.switch-warning.disabled,
.has-switch span.switch-warning[disabled] {
  color: #ffffff;
  background-color: #fbb450;
  *background-color: #faa937;
}
.has-switch span.switch-warning:active,
.has-switch span.switch-warning.active {
  background-color: #fa9f1e;
}
.has-switch span.switch-danger {
  color: #ffffff;
  background-color: #d14641;
  background-image: -moz-linear-gradient(top, #bd362f, #ee5f5b);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#bd362f), to(#ee5f5b));
  background-image: -webkit-linear-gradient(top, #bd362f, #ee5f5b);
  background-image: -o-linear-gradient(top, #bd362f, #ee5f5b);
  background-image: linear-gradient(to bottom, #bd362f, #ee5f5b);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffbd362f', endColorstr='#ffee5f5b', GradientType=0);
  border-color: #ee5f5b #ee5f5b #e51d18;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #ee5f5b;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-danger:hover,
.has-switch span.switch-danger:focus,
.has-switch span.switch-danger:active,
.has-switch span.switch-danger.active,
.has-switch span.switch-danger.disabled,
.has-switch span.switch-danger[disabled] {
  color: #ffffff;
  background-color: #ee5f5b;
  *background-color: #ec4844;
}
.has-switch span.switch-danger:active,
.has-switch span.switch-danger.active {
  background-color: #e9322d;
}
.has-switch span.switch-default {
  color: #333333;
  background-color: #f0f0f0;
  background-image: -moz-linear-gradient(top, #e6e6e6, #ffffff);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e6e6e6), to(#ffffff));
  background-image: -webkit-linear-gradient(top, #e6e6e6, #ffffff);
  background-image: -o-linear-gradient(top, #e6e6e6, #ffffff);
  background-image: linear-gradient(to bottom, #e6e6e6, #ffffff);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe6e6e6', endColorstr='#ffffffff', GradientType=0);
  border-color: #ffffff #ffffff #d9d9d9;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #ffffff;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-default:hover,
.has-switch span.switch-default:focus,
.has-switch span.switch-default:active,
.has-switch span.switch-default.active,
.has-switch span.switch-default.disabled,
.has-switch span.switch-default[disabled] {
  color: #333333;
  background-color: #ffffff;
  *background-color: #f2f2f2;
}
.has-switch span.switch-default:active,
.has-switch span.switch-default.active {
  background-color: #e6e6e6;
}
/*增加灰色 2018-6-28*/
.has-switch span.switch-gray {
  color: #ffffff;
  background-color: #bdbdbd;
  background-repeat: repeat-x;
  border-color: #bdbdbd #bdbdbd #bdbdbd;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #bdbdbd;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-gray:hover,
.has-switch span.switch-gray:focus,
.has-switch span.switch-gray:active,
.has-switch span.switch-gray.active,
.has-switch span.switch-gray.disabled,
.has-switch span.switch-gray[disabled] {
  color: #ffffff;
  background-color: #bdbdbd;
  *background-color: #bdbdbd;
}
.has-switch span.switch-gray:active,
.has-switch span.switch-gray.active {
  background-color: #bdbdbd;
}
.filebox {
  position: relative;
  border: 1px solid #CCCCCC;
  background-color: #fff;
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  -moz-border-radius: 2px 0 0 2px;
  -webkit-border-radius: 2px 0 0 2px;
  border-radius: 2px 2px 2px 2px;
}
.filebox.filebox-left {
  -moz-border-radius: 0 2px 2px 0;
  -webkit-border-radius: 0 2px 2px 0;
  border-radius: 2px 2px 2px 2px;
}
.filebox.filebox-left.filebox-no-txet {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.filebox.filebox-no-txet {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.filebox:hover,
.filebox.filebox-hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.filebox:hover .filebox-text {
  background-color: #EFF9FF;
}
.filebox.disabled {
  border-color: #CCCCCC;
  background-color: #F5F5F5;
}
.filebox.disabled .filebox-text {
  background-color: #F5F5F5;
  color: #999999;
}
.filebox .filebox-button {
  position: absolute;
  padding: 0;
  vertical-align: top;
  border-radius: 0 0 0 0;
}
.filebox .filebox-button-right {
  right: 0;
}
.filebox .filebox-button-left {
  left: 0;
}
.filebox .filebox-text {
  border: 0;
  margin: 0;
  padding: 0 0 0 5px;
  white-space: normal;
  vertical-align: top;
  outline-style: none;
  resize: none;
}
.filebox .filebox-value {
  vertical-align: top;
  position: absolute;
  top: 0;
  left: -5000px;
}
.filebox.filebox-plain .filebox-button.l-btn {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn .l-btn-text {
  background: #339EFF;
}
.filebox.filebox-plain .filebox-button.l-btn .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn .l-btn-icon {
  background-color: transparent;
  opacity: 1;
  /*0.7;*/
  filter: alpha(opacity=70);
  color: #7E7E7E;
  left: 0;
}
.filebox.filebox-plain .filebox-button.l-btn:hover .l-btn-icon {
  color: #339EFF;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-text {
  background: #E5E5E5;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-icon {
  background-color: transparent;
  opacity: 1;
  filter: alpha(opacity=100);
  color: #BBBBBB;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled:hover .l-btn-text {
  background: #E5E5E5;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled:hover .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled:hover .l-btn-icon {
  opacity: 1;
  filter: alpha(opacity=100);
}
.filebox-label {
  display: inline-block;
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: pointer;
  left: 0;
  top: 0;
  z-index: 10;
}
.webui-popover-content {
  display: none;
}
.webui-popover-rtl {
  direction: rtl;
  text-align: right;
}
.webui-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 8999;
  /*9999;*/
  display: none;
  min-width: 50px;
  min-height: 32px;
  padding: 0px;
  text-align: left;
  white-space: normal;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #E2E2E2;
  border-radius: 4px;
  -webkit-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.2);
}
.webui-popover.top,
.webui-popover.top-left,
.webui-popover.top-right {
  margin-top: -10px;
}
.webui-popover.right,
.webui-popover.right-top,
.webui-popover.right-bottom {
  margin-left: 10px;
}
.webui-popover.bottom,
.webui-popover.bottom-left,
.webui-popover.bottom-right {
  margin-top: 10px;
}
.webui-popover.left,
.webui-popover.left-top,
.webui-popover.left-bottom {
  margin-left: -10px;
}
.webui-popover.pop {
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  -o-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.pop-out {
  -webkit-transition-property: "opacity,transform";
  -o-transition-property: "opacity,transform";
  transition-property: "opacity,transform";
  -webkit-transition: .15s linear;
  -o-transition: .15s linear;
  transition: .15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.fade,
.webui-popover.fade-out {
  -webkit-transition: opacity .15s linear;
  -o-transition: opacity .15s linear;
  transition: opacity .15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.out {
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.in {
  -webkit-transform: none;
  -o-transform: none;
  transform: none;
  opacity: 1;
  filter: alpha(opacity=100);
}
.webui-popover .webui-popover-content {
  padding: 9px 10px;
  /*14px->10px*/
  overflow: auto;
  display: block;
}
.webui-popover .webui-popover-content > div:first-child {
  width: 99%;
}
.webui-popover-inner .close {
  font-family: arial;
  margin: 8px 10px 0 0;
  float: right;
  font-size: 16px;
  font-weight: 700;
  line-height: 16px;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .2;
  filter: alpha(opacity=20);
  text-decoration: none;
}
.webui-popover-inner .close:hover,
.webui-popover-inner .close:focus {
  opacity: .5;
  filter: alpha(opacity=50);
}
.webui-popover-inner .close:after {
  content: "\00D7";
  width: .8em;
  height: .8em;
  padding: 4px;
  position: relative;
}
.webui-popover-title {
  padding: 8px 10px;
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  line-height: 18px;
  background-color: #F6F6F6;
  border-bottom: 1px solid #E2E2E2;
  border-bottom-color: #E2E2E2;
  border-radius: 4px 4px 0 0;
  display: block;
  color: #000000;
}
.webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
  display: none;
}
.webui-popover-inverse {
  background-color: #333;
  color: #eee;
}
.webui-popover-inverse .webui-popover-title {
  background: #333;
  border-bottom: 1px solid #3b3b3b;
  color: #eee;
}
.webui-no-padding .webui-popover-content {
  padding: 0;
}
.webui-no-padding .list-group-item {
  border-right: none;
  border-left: none;
}
.webui-no-padding .list-group-item:first-child {
  border-top: 0;
}
.webui-no-padding .list-group-item:last-child {
  border-bottom: 0;
}
.webui-popover > .webui-arrow,
.webui-popover > .webui-arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.webui-popover > .webui-arrow {
  border-width: 11px;
}
.webui-popover > .webui-arrow:after {
  border-width: 10px;
  content: "";
}
.webui-popover.top > .webui-arrow,
.webui-popover.top-right > .webui-arrow,
.webui-popover.top-left > .webui-arrow {
  bottom: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-color: #E2E2E2;
  /*2939739*/
  border-bottom-width: 0;
}
.webui-popover.top > .webui-arrow:after,
.webui-popover.top-right > .webui-arrow:after,
.webui-popover.top-left > .webui-arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-top-color: #fff;
  border-bottom-width: 0;
}
.webui-popover.right > .webui-arrow,
.webui-popover.right-top > .webui-arrow,
.webui-popover.right-bottom > .webui-arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #E2E2E2;
}
.webui-popover.right > .webui-arrow:after,
.webui-popover.right-top > .webui-arrow:after,
.webui-popover.right-bottom > .webui-arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #fff;
}
.webui-popover.bottom > .webui-arrow,
.webui-popover.bottom-right > .webui-arrow,
.webui-popover.bottom-left > .webui-arrow {
  top: -11px;
  left: 50%;
  margin-left: -11px;
  border-bottom-color: #E2E2E2;
  border-top-width: 0;
}
.webui-popover.bottom > .webui-arrow:after,
.webui-popover.bottom-right > .webui-arrow:after,
.webui-popover.bottom-left > .webui-arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #fff;
  border-top-width: 0;
}
.webui-popover.bottom-right > .webui-arrow:after {
  border-bottom-color: #F6F6F6;
}
.webui-popover.left > .webui-arrow,
.webui-popover.left-top > .webui-arrow,
.webui-popover.left-bottom > .webui-arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #E2E2E2;
}
.webui-popover.left > .webui-arrow:after,
.webui-popover.left-top > .webui-arrow:after,
.webui-popover.left-bottom > .webui-arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #fff;
  bottom: -10px;
}
.webui-popover-inverse.top > .webui-arrow,
.webui-popover-inverse.top-left > .webui-arrow,
.webui-popover-inverse.top-right > .webui-arrow,
.webui-popover-inverse.top > .webui-arrow:after,
.webui-popover-inverse.top-left > .webui-arrow:after,
.webui-popover-inverse.top-right > .webui-arrow:after {
  border-top-color: #333;
}
.webui-popover-inverse.right > .webui-arrow,
.webui-popover-inverse.right-top > .webui-arrow,
.webui-popover-inverse.right-bottom > .webui-arrow,
.webui-popover-inverse.right > .webui-arrow:after,
.webui-popover-inverse.right-top > .webui-arrow:after,
.webui-popover-inverse.right-bottom > .webui-arrow:after {
  border-right-color: #333;
}
.webui-popover-inverse.bottom > .webui-arrow,
.webui-popover-inverse.bottom-left > .webui-arrow,
.webui-popover-inverse.bottom-right > .webui-arrow,
.webui-popover-inverse.bottom > .webui-arrow:after,
.webui-popover-inverse.bottom-left > .webui-arrow:after,
.webui-popover-inverse.bottom-right > .webui-arrow:after {
  border-bottom-color: #333;
}
.webui-popover-inverse.left > .webui-arrow,
.webui-popover-inverse.left-top > .webui-arrow,
.webui-popover-inverse.left-bottom > .webui-arrow,
.webui-popover-inverse.left > .webui-arrow:after,
.webui-popover-inverse.left-top > .webui-arrow:after,
.webui-popover-inverse.left-bottom > .webui-arrow:after {
  border-left-color: #333;
}
.webui-popover i.icon-refresh:before {
  content: "";
}
.webui-popover i.icon-refresh {
  display: block;
  width: 30px;
  height: 30px;
  font-size: 20px;
  top: 50%;
  left: 50%;
  position: absolute;
  margin-left: -15px;
  margin-right: -15px;
  background: url(../img/loading.gif) no-repeat;
}
@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
.webui-popover-backdrop {
  background-color: rgba(0, 0, 0, 0.65);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8998;
  /*9998 应该小于window-mask的z-index:9000,不然messager.alert显示有问题*/
}
.webui-popover .dropdown-menu {
  display: block;
  position: relative;
  top: 0;
  border: none;
  box-shadow: none;
  float: none;
}
.comboq {
  box-sizing: border-box;
  height: 28px;
}
input.comboq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
.comboq.disabled {
  background-image: url('images/combo_arrow_disable.png');
  background-color: #F5F5F5;
  color: #ccc;
}
.comboq {
  background-image: url(images/combo_arrow.png);
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
input.comboq.textbox {
  padding-right: 30px;
  width: 118px;
  /*textbox样式为content-box, 减去附加的padding,保持与非q系统一样长*/
}
.comboq.bginone {
  background-image: none;
  padding-right: 0px;
}
#z-q-container.comboq-p-top {
  border-radius: 4px 4px 0 0;
}
#z-q-container.comboq-p-bottom {
  border-radius: 0 0 4px 4px;
}
input.comboq-active {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.lookup {
  background-image: url('images/lite/lookup_arrow_7E7E7E.png');
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
.lookup:hover,
.lookup:focus {
  background-image: url(images/lookup_arrow.png);
}
.lookup.disabled {
  background-image: url('images/lookup_arrow_disable.png');
  background-color: #F5F5F5;
  color: #ccc;
}
.kw-chapter {
  clear: both;
  font-size: 16px;
  font-weight: bold;
  color: #339eff;
  padding: 15px 0 5px;
}
.kw-chapter a {
  border-right: 5px #339eff solid;
  width: 0px;
  height: 30px;
  padding: 0px;
  margin-right: 5px;
}
.kw-line {
  border-bottom: 1px #eeeeee solid;
  margin: 5px 0;
}
.kw-section {
  margin-bottom: 5px;
  padding-top: 5px;
  clear: both;
}
.kw-section-header {
  font-weight: 600;
  margin-bottom: 5px;
}
.keywords li {
  list-style: none;
  padding: 0;
  margin: 0;
}
ul.kw-section-list {
  padding: 0px;
  margin: 0px;
}
ul.kw-section-list::after {
  content: "";
  display: block;
  clear: both;
}
.kw-section-list > li {
  float: left;
  margin: 5px 5px 5px 0px;
}
.kw-section-list > li a {
  border-radius: 2px;
  display: block;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: #E5E5E5;
  color: #000000;
  text-decoration: none;
  padding: 0 9px;
  border: 1px solid #E5E5E5;
}
.kw-section-list > li a:hover {
  cursor: pointer;
  background: #EFF9FF;
  color: #339EFF;
  border: 1px solid #339eff;
}
.kw-section-list > li.selected a {
  background-color: #EFF9FF;
  background-image: url(images/keywords_arrow.png);
  background-position: right 0 bottom 0;
  background-repeat: no-repeat;
  color: #339EFF;
  border: 1px solid #48B9FF;
}
.keywords-labelred .kw-section-list > li a {
  background: #E5E5E5;
  color: #000000;
  border: 1px solid transparent;
}
.keywords-labelred .kw-section-list > li a:hover {
  cursor: pointer;
  background: #F1F1F1;
  color: #FF4401;
}
.keywords-labelred .kw-section-list > li.selected a {
  background: #FFE9DB;
  color: #FF4401;
}
.triggerbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  /*hover bgcolor*/
}
.triggerbox.disabled {
  border-color: #CCCCCC;
  background-color: #F5F5F5;
}
.triggerbox.disabled .triggerbox-button {
  background-color: transparent;
  cursor: not-allowed;
  color: #BBBBBB;
}
.triggerbox:hover input,
.triggerbox:hover span > span {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
}
.triggerbox:hover,
.triggerbox.triggerbox-hover {
  background-color: #EFF9FF;
  border-color: #339EFF;
}
.triggerbox .triggerbox-text {
  font-size: 14px;
  border: 0;
  margin: 0;
  padding: 0 0 0 5px;
  margin-top: -1px;
  vertical-align: top;
  /*focus bgcolor*/
}
.triggerbox .triggerbox-text:active,
.triggerbox .triggerbox-text:focus {
  background-color: #EFF9FF;
}
.triggerbox .triggerbox-text:active + span > span,
.triggerbox .triggerbox-text:focus + span > span {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
}
.triggerbox .triggerbox-text:disabled {
  background-color: #F5F5F5;
}
.triggerbox .triggerbox-prompt {
  font-size: 14px;
  color: #ccc;
}
.triggerbox-button {
  width: 30px;
  height: 28px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.triggerbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
  color: #339EFF;
}
.triggerbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.triggerbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.triggerbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.triggerbox {
  border-color: #CCCCCC;
  /*#95B8E7;*/
  background-color: #40a2de;
}
.triggerbox.triggerbox-plain {
  background-color: #ffffff;
}
.triggerbox.triggerbox-plain.disabled {
  background-color: #F5F5F5;
  color: #BBBBBB;
  border-color: #CCCCCC;
  /*.triggerbox-button {
        &.icon-trigger-box{
          background: url(icons/trigger_box_disabled.png) center center no-repeat;
        }
        &.icon-copy-blue{
          background: url(icons/copy_blue_disabled.png) center center no-repeat;
        }
        &.icon-folder{
          background: url(icons/folder_disabled.png) center center no-repeat;
        }
        &.icon-img-blue{
          background: url(icons/img_blue_disabled.png) center center no-repeat;

        }
      }*/
}
.triggerbox.triggerbox-plain .triggerbox-button.triggerbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background-color: #EFF9FF;
  color: #339EFF;
}
.triggerbox .l-btn-plain {
  background: #E0ECFF;
}
.triggerbox .l-btn-plain-disabled,
.triggerbox .l-btn-plain-disabled:hover {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.dateboxq {
  background-image: url('images/lite/datebox_arrow_7E7E7E.png');
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
.dateboxq:hover,
.dateboxq:focus {
  background-image: url('images/lite/datebox_arrow.png');
}
/*disabled应该覆盖hover效果,移到:hover后面*/
.dateboxq.disabled {
  background-image: url('images/lite/datebox_arrow_disable.png');
  background-color: #F5F5F5;
  color: #ccc;
}
input.dateboxq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
.datetimeboxq-f.disabled {
  background-image: url('images/lite/datebox_arrow_disable.png');
  background-color: #F5F5F5;
  color: #ccc;
}
.datetimeboxq-f {
  background-image: url('images/lite/datebox_arrow_7E7E7E.png');
  background-repeat: no-repeat;
  background-position: right 8px center;
}
input.datetimeboxq-f::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
/*common css*/
.hstep-container {
  font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Wenquanyi Micro Hei", "Microsoft Yahei", Arial, sans-serif;
  display: inline-block;
  position: relative;
  color: #000;
  width: 700px;
  height: 60px;
  font-size: 18px;
}
.hstep-container ul.hstep-container-steps {
  list-style: none;
  position: absolute;
  top: 2px;
  z-index: 10;
}
.hstep-container ul.hstep-container-steps li {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  float: left;
  width: 100px;
  height: 85px;
}
.hstep-container ul.hstep-container-steps li .cnode {
  background-image: url('images/lite/hstep.png');
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  margin: 10px 0px 0px 6px;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
.hstep-container ul.hstep-container-steps li.done {
  color: #000000;
}
.hstep-container ul.hstep-container-steps li.done .cnode {
  background-position: 0px -60px;
}
.hstep-container ul.hstep-container-steps li.undone {
  color: #999999;
}
.hstep-container ul.hstep-container-steps li.undone .cnode {
  background-position: 0px -80px;
}
.hstep-container ul.hstep-container-steps li.active {
  color: #13AE37;
  background-color: transparent;
}
.hstep-container ul.hstep-container-steps li.active .cnode {
  margin-top: 7px;
  background-position: 0px -40px;
  margin-bottom: 3px;
}
.hstep-container ul.hstep-container-steps li.active .cntt {
  color: #13AE37;
}
.hstep-container ul.hstep-container-steps li.hover {
  color: #339EFF;
}
.hstep-container ul.hstep-container-steps li.hover .cnode {
  margin-top: 6px;
  background-position: 0px -100px;
  margin-bottom: 4px;
}
.hstep-container ul.hstep-container-steps li.hover .cntt {
  color: #339EFF;
}
.hstep-container .hstep-progress-highlight {
  background: #18af66;
}
.hstep-container .hstep-progress {
  width: 400px;
  height: 1px;
  position: absolute;
  top: 35px;
  left: 15px;
  float: left;
  margin-right: 10px;
  overflow: hidden;
}
.hstep-container .hstep-progress .hstep-progress-bar {
  width: 400px;
  height: 20px;
  background: #e4e4e4;
  display: inline-block;
}
.hstep-container .hstep-progress .hstep-progress-bar .hstep-progress-highlight {
  height: 20px;
  display: block;
}
.hstep-container ul,
.hstep-container li,
.hstep-container p {
  margin: 0;
  padding: 0;
}
/*common css*/
.vstep-container {
  font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Wenquanyi Micro Hei", "Microsoft Yahei", Arial, sans-serif;
  display: inline-block;
  position: relative;
  color: #000;
  height: 700px;
  font-size: 18px;
}
.vstep-container ul.vstep-container-steps {
  list-style: none;
  position: absolute;
  top: 2px;
  z-index: 10;
}
.vstep-container ul.vstep-container-steps li {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  height: 85px;
}
.vstep-container ul.vstep-container-steps li * {
  float: left;
  margin: 5px 5px 0 0px;
}
.vstep-container ul.vstep-container-steps li .cnode {
  background-image: url('images/lite/hstep.png');
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  margin: 10px 0px 0px 10px;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
.vstep-container ul.vstep-container-steps li .title {
  margin-right: 20px;
  margin-left: 5px;
}
.vstep-container ul.vstep-container-steps li.done {
  color: #000000;
}
.vstep-container ul.vstep-container-steps li.done .cnode {
  background-position: 0px -60px;
}
.vstep-container ul.vstep-container-steps li.undone {
  color: #999999;
}
.vstep-container ul.vstep-container-steps li.undone .cnode {
  background-position: 0px -80px;
}
.vstep-container ul.vstep-container-steps li.active {
  color: #13AE37;
  background-color: transparent;
}
.vstep-container ul.vstep-container-steps li.active .cnode {
  margin-top: 7px;
  background-position: 0px -40px;
  margin-bottom: 3px;
}
.vstep-container ul.vstep-container-steps li.active .cntt {
  color: #13AE37;
}
.vstep-container ul.vstep-container-steps li.hover {
  color: #339EFF;
}
.vstep-container ul.vstep-container-steps li.hover .cnode {
  margin-top: 6px;
  background-position: 0px -100px;
  margin-bottom: 4px;
}
.vstep-container ul.vstep-container-steps li.hover .cntt {
  color: #339EFF;
}
.vstep-container .vstep-progress-highlight {
  background: #18af66;
}
.vstep-container .vstep-progress {
  width: 1px;
  height: 400px;
  position: absolute;
  top: 20px;
  left: 17px;
  float: left;
  margin-right: 10px;
  overflow: hidden;
}
.vstep-container .vstep-progress .vstep-progress-bar {
  width: 20px;
  height: 400px;
  background: #e4e4e4;
  display: inline-block;
}
.vstep-container .vstep-progress .vstep-progress-bar .vstep-progress-highlight {
  height: 20px;
  display: block;
}
.vstep-container ul,
.vstep-container li,
.vstep-container p {
  margin: 0;
  padding: 0;
}
.timeboxq.disabled {
  background-image: none;
  background-color: #F5F5F5;
  color: #ccc;
}
.timeboxq {
  background-image: none;
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
input.timeboxq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
/**蓝色根节点**/
/**蓝色标题 灰色根节点**/
/*0-32 中间为白色搜索图标*/
/* 176-192 向左折叠*/
/* 192-208 向右展开*/
/* 256-272 白色下箭头*/
/* 272-288 白色上箭头*/
/* 112-128 灰色下箭头*/
/* 128-144 灰色上箭头*/
/* 144-160 小灰色右箭头*/
/* 160-176 小灰色上箭头*/
/* 224-240 细的灰色下箭头*/
/* 240-256 细的灰色上箭头*/
/* 64-80 小蓝色右箭头*/
/* 80-96 小蓝色上箭头*/
.menutree-default-width {
  width: 180px;
  height: 1px;
  padding: 0;
  margin: 0;
  position: absolute;
  top: -100px;
}
.menutree-default-min-width {
  width: 40px;
  height: 1px;
  padding: 0;
  margin: 0;
  position: absolute;
  top: -100px;
}
/*menutree*/
.menutree .tree-folder {
  background: none;
}
.menutree .tree-folder-open {
  background: none;
}
.menutree .tree-file {
  background: none;
}
.menutree .menutree-hidden {
  /*用于控制元素的隐藏*/
  display: none;
}
.menutree > .panel-body {
  border-radius: 0;
  background-color: transparent;
  overflow: hidden;
}
.menutree .menutree-collapse-wrap {
  height: 26px;
  margin-bottom: 4px;
  text-align: center;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.menutree .menutree-collapse-wrap .menutree-collapse {
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-top: 6px;
  cursor: pointer;
}
.menutree .menutree-collapse-wrap .menutree-collapse.menutree-expanded {
  background: url('images/menutree.png') -176px center no-repeat;
}
.menutree .menutree-collapse-wrap .menutree-collapse.menutree-collapsed {
  background: url('images/menutree.png') -192px center no-repeat;
}
.menutree .menutree-searchbox-wrap {
  margin-bottom: 4px;
}
.menutree .menutree-tree-wrap {
  border: 1px solid #e2e2e2;
  border-radius: 4px;
  background-color: #ffffff;
  overflow: auto;
}
.menutree .menutree-tree-wrap .menutree-tree.tree {
  border: 0px solid #E5E5E5;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  height: 38px;
  background-color: #f8f8f8;
  line-height: 38px;
  font-weight: bold;
  color: #000000;
  position: relative;
  border-bottom: 1px solid #e2e2e2;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit {
  position: absolute;
  height: 16px;
  width: 16px;
  top: 50%;
  margin-top: -8px;
  right: 10px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  background: url('images/menutree.png') -224px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  background: url('images/menutree.png') -240px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-icon {
  display: none;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-title {
  height: 38px;
  line-height: 38px;
  color: #000000;
  padding: 0 34px 0 13px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #f8f8f8;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul {
  background-color: transparent;
  border-bottom: 1px solid #e2e2e2;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 36px;
  line-height: 36px;
  position: relative;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #e5f3ff;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover .tree-title {
  background: #e5f3ff;
  color: #339eff;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #e5f3ff;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  background: #e5f3ff;
  color: #339eff;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit {
  width: 16px;
  height: 16px;
  margin-top: 9px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-collapsed {
  background: url('images/menutree.png') -144px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-collapsed.tree-collapsed-hover {
  background: url('images/menutree.png') -144px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-expanded {
  background: url('images/menutree.png') -80px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-expanded.tree-expanded-hover {
  background: url('images/menutree.png') -80px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-indent {
  width: 15px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node > span.tree-indent:first-child {
  width: 0px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-icon {
  display: none;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-title {
  height: 36px;
  line-height: 36px;
  color: #000;
  padding: 0 10px 0 0;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li:last-child > ul {
  border: 0;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .menutree-tip-count {
  display: block;
  position: absolute;
  right: 20px;
  top: 10px;
  width: auto;
  padding-left: 5px;
  padding-right: 5px;
  height: 16px;
  background-color: #e4e4e4;
  border-radius: 4px;
  line-height: 16px;
  text-align: center;
  font-size: 12px;
  color: #666666;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .tree-node-selected .menutree-tip-count,
.menutree .menutree-tree-wrap .menutree-tree.tree .tree-node-hover .menutree-tip-count {
  background-color: #FFE9E9;
  color: #EE0F0F;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .menutree-reg-word {
  background-color: yellow;
  color: #000;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .menutree-node-hidden {
  display: none;
}
.menutree .menutree-tree-wrap.menutree-tree-norootcollapse .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree .menutree-tree-wrap.menutree-tree-collapsible .menutree-tree.tree > li > .tree-node .tree-icon {
  display: inline-block;
  height: 38px;
  margin: 0;
  width: 16px;
  margin-left: 13px;
  font-weight: normal;
}
.menutree .menutree-tree-wrap.menutree-tree-collapsible .menutree-tree.tree > li > .tree-node .tree-title {
  padding-left: 10px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree-title {
  display: none;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree {
  border: 0px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  height: 38px;
  background-color: #f8f8f8;
  line-height: 38px;
  font-weight: bold;
  color: #000000;
  position: relative;
  border-bottom: 1px solid #e2e2e2;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit {
  position: absolute;
  height: 16px;
  width: 16px;
  top: 50%;
  margin-top: -8px;
  right: 10px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  background: url('images/menutree.png') -224px center no-repeat;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  background: url('images/menutree.png') -240px center no-repeat;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-title {
  height: 38px;
  line-height: 38px;
  color: #000000;
  padding: 0 34px 0 13px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #f8f8f8;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-selected {
  background-color: #f8f8f8;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul {
  background-color: transparent;
  border-bottom: 1px solid #e2e2e2;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 36px;
  line-height: 36px;
  position: relative;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #e5f3ff;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-hover .tree-title {
  background: #e5f3ff;
  color: #339eff;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #e5f3ff;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  background: #e5f3ff;
  color: #339eff;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node .tree-title {
  color: #000;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li:last-child > ul {
  border: 0;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree .menutree-tip-count {
  background-color: #E4E4E4;
  color: #666666;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree .tree-node-selected .menutree-tip-count,
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree .tree-node-hover .menutree-tip-count {
  background-color: #FFE9E9;
  color: #EE0F0F;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle.menutree-tree-norootcollapse .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-min > .panel-body {
  border-radius: 0;
  background-color: transparent;
  overflow: hidden;
}
.menutree.menutree-min .menutree-searchbox-wrap {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap {
  background-color: #f8f8f8;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree {
  border: 0px;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  border: 0px;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-title {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li.menutree-root-hover > .tree-node {
  background-color: #339eff;
  color: #ffffff;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-hover {
  background-color: #339eff;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-selected {
  background-color: #339eff;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > ul {
  background-color: #ffffff;
  position: absolute;
  border: 0;
  display: none!important;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle {
  background-color: #f8f8f8;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree-title {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree {
  border: 0px;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  border: 0px;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-title {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #f8f8f8;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-selected {
  background-color: #f8f8f8;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-icon {
  margin-left: 10px;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul {
  border: 0px;
  background-color: #ffffff;
  display: none!important;
  position: absolute;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li.menutree-root-hover > .tree-node {
  background-color: #339eff;
  color: #ffffff;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-hover {
  background-color: #339eff;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-selected {
  background-color: #339eff;
}
.menutree.menutree-sp {
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.2);
}
.menutree.menutree-sp > .panel-body {
  border-radius: 0;
  background-color: transparent;
  overflow: auto;
}
.menutree.menutree-sp .menutree-tree-wrap {
  overflow: hidden;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  height: 38px;
  background-color: #f8f8f8;
  line-height: 38px;
  font-weight: normal;
  color: #000000;
  position: relative;
  border-bottom: 1px solid #e2e2e2;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  display: none;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-title {
  height: 38px;
  line-height: 38px;
  color: #000000;
  padding: 0 34px 0 13px;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #f8f8f8;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node.tree-node-selected {
  background-color: #f8f8f8;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul {
  background-color: transparent;
  border: 0px;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 35px;
  line-height: 35px;
  position: relative;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #e5f3ff;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover .tree-title {
  background: #e5f3ff;
  color: #339eff;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #e5f3ff;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  background: #e5f3ff;
  color: #339eff;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-title {
  color: #000;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree .menutree-tip-count {
  background-color: #E4E4E4;
  color: #666666;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree .tree-node-selected .menutree-tip-count,
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree .tree-node-hover .menutree-tip-count {
  background-color: #FFE9E9;
  color: #EE0F0F;
}
.z-q-clearbtnicon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  background: url(images/tabs_icons_2.png) no-repeat 0px -1px;
}
.z-q-clearbtnicon:hover,
.z-q-clearbtnicon:focus {
  background-position-x: -36px;
  color: red;
  cursor: pointer;
}
/*
width可以改变右边滚动条的宽度
height可以改变下边滚动条的高度
*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  box-sizing: border-box;
}
/*
设置了滚动条按钮的样式图标
*/
::-webkit-scrollbar-button {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAAUCAYAAADRA14pAAADr0lEQVRYR71Yy04iQRQtE10IRiSBOLbOUvZmfkIlLHXjI+jCDzAm8w8TJKxZyENdqEui8BPuDVtsHCNGQcFEWUzdSt/KtbqqqZ44U0kn1V2n69xz63W6x5h9iXFoNADe521dftnibJlt+7PCjdmycpzz9vbmmvCTk5PzvK0NuNvbWyNuYWEBcbbUX8obSvBgMDAKiUQiUrDLi0nNPC9eYqwFfyWvTvAPHsm1JhqHEl9dXbHV1VUJo4Lv7u6k4JOTE7a5uSlxc3Nz/0ww5VXjR15VMIjFoop2+v2+EAJisaDoaDQqR/j+/l7gjo+PJW5ra0vUZ2dnqWDko1zqM+fi4kL0RxOMMaytrUneXC4ncMhFYzg4OBA4KpiK1Yl2Xl9fXSqWip6ampLEDw8PbrVa9U2S7e1tlkwmdYIBC6J1CXfOz8/ljAHRNIbFxcXs0tJShb/rHB4eShxw0RgymczPVCr1CwWrRCOJeQesVqtJUevr61JwPp+XxHt7e6xYLErc/v6+OqVHJvrl5cWt1+u+BEIMNzc3UvDj46NbqYD2zwViaLfbWsGBU+vs7EwIASIow+GQYSA8e5K4UCgIHBBBeX9/Z+VyWdT5CAliJabApdTr9UR/VDTGQPeO5+dngUMuGgPiQu3S3W7XHR8fl2IxaHjWbDalYAhwYmJCNH98fEht8KzVaukEA8a4WQIvdgKiacJjsZicWZgYwJZKJZlwiGF6etq3hpWk+24dzKAOODMzI4lhrZs6I2t9FB+2+3ghcZjIIF4YCJiFUJA31AjbCrY8N/9aMH2RCrbhDSP4OydKBETZ4W09fn3jV8SAG/Dnv/kFFtS22PC2eGdWOJNg3fnos3iXl5ci6HQ6zTxryVRbeXp6KjAbGxvMYCt1XDQZgpdy0UbV0lI+ikNuk9NCLN21fU4LQXA2ersgbP+fXBZiwG05jqNzWUE7NLwueHVmBxrpLg3c4OwoJ9aR+6udFgvhsugA/DeHZ3JaWi+tOi1q9bxdkHU6nU8uCxwPlkQiEeSjTaKFw8M+1JGmDg+4EUddFsSA3KFGWGcAVlZWBId3zrGnpyff4Z/NZgUmHo+bBIc2Hgqv+Cy14Q61hlUDgNkEcs8AMPXwR8zOzo48/Olc5vWRaxh4qctCsdCPajzAcFBOrI8yHtpdWj2HG42G6G95eZl55yHDLyokOjo6EtXd3V1Gvqh061e3jAAnjAfloi/Tcxi4KR/FIXeYc9jmFwpw2PwGUgY58NaG1/rX0h9d1DUzJEP0JgAAAABJRU5ErkJggg==);
  background-color: #F1F1F1;
  background-repeat: no-repeat;
}
::-webkit-scrollbar-button:vertical:decrement {
  width: 8px;
  height: 10px;
  background-position-x: -1px;
  background-position-y: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
::-webkit-scrollbar-button:vertical:increment {
  width: 8px;
  height: 10px;
  background-position-x: -31px;
  background-position-y: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
::-webkit-scrollbar-button:horizontal:decrement {
  /*左边箭头*/
  width: 8px;
  height: 8px;
  background-position-x: 0px;
  background-position-y: -11px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
::-webkit-scrollbar-button:horizontal:increment {
  width: 8px;
  height: 8px;
  background-position-x: -30px;
  background-position-y: -11px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
/*
corner角落的意思，可以改变Y滚动条与X滚动条交叉的右下角的样式
*/
::-webkit-scrollbar-corner {
  background-color: #AFCEDE;
  border-radius: 1px;
}
/*
这就是滚动条的样式了
*/
::-webkit-scrollbar-thumb {
  background-color: #D4D4D4;
  border-radius: 4px;
  border-width: 0 ;
}
/*
悬浮时
*/
::-webkit-scrollbar-thumb:hover {
  background-color: #AFCEDE;
  border-radius: 4px;
  border-width: 0 ;
}
/*
滚动条空白处的样式
*/
::-webkit-scrollbar-track-piece {
  background-color: #F1F1F1;
  border-width: 0 ;
}
