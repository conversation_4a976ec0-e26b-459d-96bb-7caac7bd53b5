!function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="e500")}({"30db":function(e,t,n){"use strict";(function(e,i){var r;n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return L})),n.d(t,"b",(function(){return S})),n.d(t,"c",(function(){return N})),n.d(t,"e",(function(){return y}));let s=!1,o=!1,a=!1,l=!1,u=!1,h=!1,c=!1,d=void 0,f="en",m=void 0,g=void 0;const _="object"==typeof self?self:"object"==typeof e?e:{};let p=void 0;void 0!==_.vscode&&void 0!==_.vscode.process?p=_.vscode.process:void 0!==i&&(p=i);const C="string"==typeof(null===(r=null==p?void 0:p.versions)||void 0===r?void 0:r.electron)&&"renderer"===p.type;if("object"!=typeof navigator||C){if("object"==typeof p){s="win32"===p.platform,o="darwin"===p.platform,a="linux"===p.platform,l=a&&!!p.env.SNAP&&!!p.env.SNAP_REVISION,d="en",f="en";const t=p.env.VSCODE_NLS_CONFIG;if(t)try{const e=JSON.parse(t),n=e.availableLanguages["*"];d=e.locale,f=n||"en",m=e._translationsConfigFile}catch(e){}u=!0}}else g=navigator.userAgent,s=g.indexOf("Windows")>=0,o=g.indexOf("Macintosh")>=0,c=(g.indexOf("Macintosh")>=0||g.indexOf("iPad")>=0||g.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,a=g.indexOf("Linux")>=0,h=!0,d=navigator.language,f=d;let b=0;o?b=1:s?b=3:a&&(b=2);const L=s,S=o,N=h,y=function(){if(_.setImmediate)return _.setImmediate.bind(_);if("function"==typeof _.postMessage&&!_.importScripts){let e=[];_.addEventListener("message",t=>{if(t.data&&t.data.vscodeSetImmediateId)for(let n=0,i=e.length;n<i;n++){const i=e[n];if(i.id===t.data.vscodeSetImmediateId)return e.splice(n,1),void i.callback()}});let t=0;return n=>{const i=++t;e.push({id:i,callback:n}),_.postMessage({vscodeSetImmediateId:i},"*")}}if("function"==typeof(null==p?void 0:p.nextTick))return p.nextTick.bind(p);const e=Promise.resolve();return t=>e.then(t)}()}).call(this,n("c8ba"),n("f28c"))},c317:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return a}));var i=n("30db");let r;if(void 0!==i.a.vscode&&void 0!==i.a.vscode.process){const e=i.a.vscode.process;r={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd:()=>e.cwd(),nextTick:e=>Object(i.e)(e)}}else r=void 0!==e?{get platform(){return e.platform},get arch(){return e.arch},get env(){return Object({NODE_ENV:"production",BASE_URL:""})},cwd:()=>Object({NODE_ENV:"production",BASE_URL:""}).VSCODE_CWD||e.cwd(),nextTick:t=>e.nextTick(t)}:{get platform(){return i.d?"win32":i.b?"darwin":"linux"},get arch(){},nextTick:e=>Object(i.e)(e),get env(){return{}},cwd:()=>"/"};const s=r.cwd,o=r.env,a=r.platform}).call(this,n("f28c"))},c8ba:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},e500:function(e,t,n){"use strict";n.r(t),n.d(t,"initialize",(function(){return sn}));const i=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{if(e.stack)throw new Error(e.message+"\n\n"+e.stack);throw e},0)}}emit(e){this.listeners.forEach(t=>{t(e)})}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function r(e){o(e)||i.onUnexpectedError(e)}function s(e){if(e instanceof Error){let{name:t,message:n}=e;return{$isError:!0,name:t,message:n,stack:e.stacktrace||e.stack}}return e}function o(e){return e instanceof Error&&"Canceled"===e.name&&"Canceled"===e.message}function a(e){const t=this;let n,i=!1;return function(){return i||(i=!0,n=e.apply(t,arguments)),n}}var l;Error,function(e){e.is=function(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]};const t=Object.freeze([]);e.empty=function(){return t},e.single=function*(e){yield e},e.from=function(e){return e||t},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){for(const n of e)if(t(n))return!0;return!1},e.find=function(e,t){for(const n of e)if(t(n))return n},e.filter=function*(e,t){for(const n of e)t(n)&&(yield n)},e.map=function*(e,t){let n=0;for(const i of e)yield t(i,n++)},e.concat=function*(...e){for(const t of e)for(const e of t)yield e},e.concatNested=function*(e){for(const t of e)for(const e of t)yield e},e.reduce=function(e,t,n){let i=n;for(const r of e)i=t(i,r);return i},e.slice=function*(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]},e.consume=function(t,n=Number.POSITIVE_INFINITY){const i=[];if(0===n)return[i,t];const r=t[Symbol.iterator]();for(let s=0;s<n;s++){const t=r.next();if(t.done)return[i,e.empty()];i.push(t.value)}return[i,{[Symbol.iterator]:()=>r}]},e.equals=function(e,t,n=((e,t)=>e===t)){const i=e[Symbol.iterator](),r=t[Symbol.iterator]();for(;;){const e=i.next(),t=r.next();if(e.done!==t.done)return!1;if(e.done)return!0;if(!n(e.value,t.value))return!1}}}(l||(l={}));let u=null;function h(e){return null==u||u.trackDisposable(e),e}function c(e){null==u||u.markAsDisposed(e)}function d(e,t){null==u||u.setParent(e,t)}class f extends Error{constructor(e){super(`Encountered errors while disposing of store. Errors: [${e.join(", ")}]`),this.errors=e}}function m(e){if(l.is(e)){let t=[];for(const n of e)if(n)try{n.dispose()}catch(e){t.push(e)}if(1===t.length)throw t[0];if(t.length>1)throw new f(t);return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function g(...e){const t=_(()=>m(e));return function(e,t){if(u)for(const n of e)u.setParent(n,t)}(e,t),t}function _(e){const t=h({dispose:a(()=>{c(t),e()})});return t}class p{constructor(){this._toDispose=new Set,this._isDisposed=!1,h(this)}dispose(){this._isDisposed||(c(this),this._isDisposed=!0,this.clear())}clear(){try{m(this._toDispose.values())}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return d(e,this),this._isDisposed?p.DISABLE_DISPOSED_WARNING:this._toDispose.add(e),e}}p.DISABLE_DISPOSED_WARNING=!1;class C{constructor(){this._store=new p,h(this),d(this._store,this)}dispose(){c(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}C.None=Object.freeze({dispose(){}});class b{constructor(e){this.element=e,this.next=b.Undefined,this.prev=b.Undefined}}b.Undefined=new b(void 0);class L{constructor(){this._first=b.Undefined,this._last=b.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===b.Undefined}clear(){let e=this._first;for(;e!==b.Undefined;){const t=e.next;e.prev=b.Undefined,e.next=b.Undefined,e=t}this._first=b.Undefined,this._last=b.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){const n=new b(e);if(this._first===b.Undefined)this._first=n,this._last=n;else if(t){const e=this._last;this._last=n,n.prev=e,e.next=n}else{const e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let i=!1;return()=>{i||(i=!0,this._remove(n))}}shift(){if(this._first!==b.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==b.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==b.Undefined&&e.next!==b.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===b.Undefined&&e.next===b.Undefined?(this._first=b.Undefined,this._last=b.Undefined):e.next===b.Undefined?(this._last=this._last.prev,this._last.next=b.Undefined):e.prev===b.Undefined&&(this._first=this._first.next,this._first.prev=b.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==b.Undefined;)yield e.element,e=e.next}}var S=n("30db");const N=S.a.performance&&"function"==typeof S.a.performance.now;class y{constructor(e){this._highResolution=N&&e,this._startTime=this._now(),this._stopTime=-1}static create(e=!0){return new y(e)}stop(){this._stopTime=this._now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?S.a.performance.now():Date.now()}}var E;!function(e){function t(e){return(t,n=null,i)=>{let r,s=!1;return r=e(e=>{if(!s)return r?r.dispose():s=!0,t.call(n,e)},null,i),s&&r.dispose(),r}}function n(e,t){return o((n,i=null,r)=>e(e=>n.call(i,t(e)),null,r))}function i(e,t){return o((n,i=null,r)=>e(e=>{t(e),n.call(i,e)},null,r))}function r(e,t){return o((n,i=null,r)=>e(e=>t(e)&&n.call(i,e),null,r))}function s(e,t,i){let r=i;return n(e,e=>(r=t(r,e),r))}function o(e){let t;const n=new w({onFirstListenerAdd(){t=e(n.fire,n)},onLastListenerRemove(){t.dispose()}});return n.event}function a(e,t,n=100,i=!1,r){let s,o=void 0,a=void 0,l=0;const u=new w({leakWarningThreshold:r,onFirstListenerAdd(){s=e(e=>{l++,o=t(o,e),i&&!a&&(u.fire(o),o=void 0),clearTimeout(a),a=setTimeout(()=>{const e=o;o=void 0,a=void 0,(!i||l>1)&&u.fire(e),l=0},n)})},onLastListenerRemove(){s.dispose()}});return u.event}function l(e,t=((e,t)=>e===t)){let n,i=!0;return r(e,e=>{const r=i||!t(e,n);return i=!1,n=e,r})}e.None=()=>C.None,e.once=t,e.map=n,e.forEach=i,e.filter=r,e.signal=function(e){return e},e.any=function(...e){return(t,n=null,i)=>g(...e.map(e=>e(e=>t.call(n,e),null,i)))},e.reduce=s,e.debounce=a,e.latch=l,e.split=function(t,n){return[e.filter(t,n),e.filter(t,e=>!n(e))]},e.buffer=function(e,t=!1,n=[]){let i=n.slice(),r=e(e=>{i?i.push(e):o.fire(e)});const s=()=>{i&&i.forEach(e=>o.fire(e)),i=null},o=new w({onFirstListenerAdd(){r||(r=e(e=>o.fire(e)))},onFirstListenerDidAdd(){i&&(t?setTimeout(s):s())},onLastListenerRemove(){r&&r.dispose(),r=null}});return o.event};class u{constructor(e){this.event=e}map(e){return new u(n(this.event,e))}forEach(e){return new u(i(this.event,e))}filter(e){return new u(r(this.event,e))}reduce(e,t){return new u(s(this.event,e,t))}latch(){return new u(l(this.event))}debounce(e,t=100,n=!1,i){return new u(a(this.event,e,t,n,i))}on(e,t,n){return this.event(e,t,n)}once(e,n,i){return t(this.event)(e,n,i)}}e.chain=function(e){return new u(e)},e.fromNodeEventEmitter=function(e,t,n=(e=>e)){const i=(...e)=>r.fire(n(...e)),r=new w({onFirstListenerAdd:()=>e.on(t,i),onLastListenerRemove:()=>e.removeListener(t,i)});return r.event},e.fromDOMEventEmitter=function(e,t,n=(e=>e)){const i=(...e)=>r.fire(n(...e)),r=new w({onFirstListenerAdd:()=>e.addEventListener(t,i),onLastListenerRemove:()=>e.removeEventListener(t,i)});return r.event},e.toPromise=function(e){return new Promise(n=>t(e)(n))}}(E||(E={}));class A{constructor(e){this._listenerCount=0,this._invocationCount=0,this._elapsedOverall=0,this._name=`${e}_${A._idPool++}`}start(e){this._stopWatch=new y(!0),this._listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this._elapsedOverall+=e,this._invocationCount+=1,this._stopWatch=void 0}}}A._idPool=0;class w{constructor(e){var t;this._disposed=!1,this._options=e,this._leakageMon=void 0,this._perfMon=(null===(t=this._options)||void 0===t?void 0:t._profName)?new A(this._options._profName):void 0}get event(){return this._event||(this._event=(e,t,n)=>{var i;this._listeners||(this._listeners=new L);const r=this._listeners.isEmpty();r&&this._options&&this._options.onFirstListenerAdd&&this._options.onFirstListenerAdd(this);const s=this._listeners.push(t?[e,t]:e);r&&this._options&&this._options.onFirstListenerDidAdd&&this._options.onFirstListenerDidAdd(this),this._options&&this._options.onListenerDidAdd&&this._options.onListenerDidAdd(this,e,t);const o=null===(i=this._leakageMon)||void 0===i?void 0:i.check(this._listeners.size),a=_(()=>{o&&o(),!this._disposed&&(s(),this._options&&this._options.onLastListenerRemove)&&(this._listeners&&!this._listeners.isEmpty()||this._options.onLastListenerRemove(this))});return n instanceof p?n.add(a):Array.isArray(n)&&n.push(a),a}),this._event}fire(e){var t,n;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new L);for(let t of this._listeners)this._deliveryQueue.push([t,e]);for(null===(t=this._perfMon)||void 0===t||t.start(this._deliveryQueue.size);this._deliveryQueue.size>0;){const[t,n]=this._deliveryQueue.shift();try{"function"==typeof t?t.call(void 0,n):t[0].call(t[1],n)}catch(e){r(e)}}null===(n=this._perfMon)||void 0===n||n.stop()}}dispose(){var e,t,n,i,r;this._disposed||(this._disposed=!0,null===(e=this._listeners)||void 0===e||e.clear(),null===(t=this._deliveryQueue)||void 0===t||t.clear(),null===(i=null===(n=this._options)||void 0===n?void 0:n.onLastListenerRemove)||void 0===i||i.call(n),null===(r=this._leakageMon)||void 0===r||r.dispose())}}function v(e){const t=[];for(const n of function(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}(e))"function"==typeof e[n]&&t.push(n);return t}function M(e){return e>=65&&e<=90}function O(e){return 55296<=e&&e<=56319}function T(e){return 56320<=e&&e<=57343}function K(e,t){return t-56320+(e-55296<<10)+65536}String.fromCharCode(65279);class R{constructor(){this._data=JSON.parse("[0,0,0,51592,51592,11,44424,44424,11,72251,72254,5,7150,7150,7,48008,48008,11,55176,55176,11,128420,128420,14,3276,3277,5,9979,9980,14,46216,46216,11,49800,49800,11,53384,53384,11,70726,70726,5,122915,122916,5,129320,129327,14,2558,2558,5,5906,5908,5,9762,9763,14,43360,43388,8,45320,45320,11,47112,47112,11,48904,48904,11,50696,50696,11,52488,52488,11,54280,54280,11,70082,70083,1,71350,71350,7,73111,73111,5,127892,127893,14,128726,128727,14,129473,129474,14,2027,2035,5,2901,2902,5,3784,3789,5,6754,6754,5,8418,8420,5,9877,9877,14,11088,11088,14,44008,44008,5,44872,44872,11,45768,45768,11,46664,46664,11,47560,47560,11,48456,48456,11,49352,49352,11,50248,50248,11,51144,51144,11,52040,52040,11,52936,52936,11,53832,53832,11,54728,54728,11,69811,69814,5,70459,70460,5,71096,71099,7,71998,71998,5,72874,72880,5,119149,119149,7,127374,127374,14,128335,128335,14,128482,128482,14,128765,128767,14,129399,129400,14,129680,129685,14,1476,1477,5,2377,2380,7,2759,2760,5,3137,3140,7,3458,3459,7,4153,4154,5,6432,6434,5,6978,6978,5,7675,7679,5,9723,9726,14,9823,9823,14,9919,9923,14,10035,10036,14,42736,42737,5,43596,43596,5,44200,44200,11,44648,44648,11,45096,45096,11,45544,45544,11,45992,45992,11,46440,46440,11,46888,46888,11,47336,47336,11,47784,47784,11,48232,48232,11,48680,48680,11,49128,49128,11,49576,49576,11,50024,50024,11,50472,50472,11,50920,50920,11,51368,51368,11,51816,51816,11,52264,52264,11,52712,52712,11,53160,53160,11,53608,53608,11,54056,54056,11,54504,54504,11,54952,54952,11,68108,68111,5,69933,69940,5,70197,70197,7,70498,70499,7,70845,70845,5,71229,71229,5,71727,71735,5,72154,72155,5,72344,72345,5,73023,73029,5,94095,94098,5,121403,121452,5,126981,127182,14,127538,127546,14,127990,127990,14,128391,128391,14,128445,128449,14,128500,128505,14,128752,128752,14,129160,129167,14,129356,129356,14,129432,129442,14,129648,129651,14,129751,131069,14,173,173,4,1757,1757,1,2274,2274,1,2494,2494,5,2641,2641,5,2876,2876,5,3014,3016,7,3262,3262,7,3393,3396,5,3570,3571,7,3968,3972,5,4228,4228,7,6086,6086,5,6679,6680,5,6912,6915,5,7080,7081,5,7380,7392,5,8252,8252,14,9096,9096,14,9748,9749,14,9784,9786,14,9833,9850,14,9890,9894,14,9938,9938,14,9999,9999,14,10085,10087,14,12349,12349,14,43136,43137,7,43454,43456,7,43755,43755,7,44088,44088,11,44312,44312,11,44536,44536,11,44760,44760,11,44984,44984,11,45208,45208,11,45432,45432,11,45656,45656,11,45880,45880,11,46104,46104,11,46328,46328,11,46552,46552,11,46776,46776,11,47000,47000,11,47224,47224,11,47448,47448,11,47672,47672,11,47896,47896,11,48120,48120,11,48344,48344,11,48568,48568,11,48792,48792,11,49016,49016,11,49240,49240,11,49464,49464,11,49688,49688,11,49912,49912,11,50136,50136,11,50360,50360,11,50584,50584,11,50808,50808,11,51032,51032,11,51256,51256,11,51480,51480,11,51704,51704,11,51928,51928,11,52152,52152,11,52376,52376,11,52600,52600,11,52824,52824,11,53048,53048,11,53272,53272,11,53496,53496,11,53720,53720,11,53944,53944,11,54168,54168,11,54392,54392,11,54616,54616,11,54840,54840,11,55064,55064,11,65438,65439,5,69633,69633,5,69837,69837,1,70018,70018,7,70188,70190,7,70368,70370,7,70465,70468,7,70712,70719,5,70835,70840,5,70850,70851,5,71132,71133,5,71340,71340,7,71458,71461,5,71985,71989,7,72002,72002,7,72193,72202,5,72281,72283,5,72766,72766,7,72885,72886,5,73104,73105,5,92912,92916,5,113824,113827,4,119173,119179,5,121505,121519,5,125136,125142,5,127279,127279,14,127489,127490,14,127570,127743,14,127900,127901,14,128254,128254,14,128369,128370,14,128400,128400,14,128425,128432,14,128468,128475,14,128489,128494,14,128715,128720,14,128745,128745,14,128759,128760,14,129004,129023,14,129296,129304,14,129340,129342,14,129388,129392,14,129404,129407,14,129454,129455,14,129485,129487,14,129659,129663,14,129719,129727,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2363,2363,7,2402,2403,5,2507,2508,7,2622,2624,7,2691,2691,7,2786,2787,5,2881,2884,5,3006,3006,5,3072,3072,5,3170,3171,5,3267,3268,7,3330,3331,7,3406,3406,1,3538,3540,5,3655,3662,5,3897,3897,5,4038,4038,5,4184,4185,5,4352,4447,8,6068,6069,5,6155,6157,5,6448,6449,7,6742,6742,5,6783,6783,5,6966,6970,5,7042,7042,7,7143,7143,7,7212,7219,5,7412,7412,5,8206,8207,4,8294,8303,4,8596,8601,14,9410,9410,14,9742,9742,14,9757,9757,14,9770,9770,14,9794,9794,14,9828,9828,14,9855,9855,14,9882,9882,14,9900,9903,14,9929,9933,14,9963,9967,14,9987,9988,14,10006,10006,14,10062,10062,14,10175,10175,14,11744,11775,5,42607,42607,5,43043,43044,7,43263,43263,5,43444,43445,7,43569,43570,5,43698,43700,5,43766,43766,5,44032,44032,11,44144,44144,11,44256,44256,11,44368,44368,11,44480,44480,11,44592,44592,11,44704,44704,11,44816,44816,11,44928,44928,11,45040,45040,11,45152,45152,11,45264,45264,11,45376,45376,11,45488,45488,11,45600,45600,11,45712,45712,11,45824,45824,11,45936,45936,11,46048,46048,11,46160,46160,11,46272,46272,11,46384,46384,11,46496,46496,11,46608,46608,11,46720,46720,11,46832,46832,11,46944,46944,11,47056,47056,11,47168,47168,11,47280,47280,11,47392,47392,11,47504,47504,11,47616,47616,11,47728,47728,11,47840,47840,11,47952,47952,11,48064,48064,11,48176,48176,11,48288,48288,11,48400,48400,11,48512,48512,11,48624,48624,11,48736,48736,11,48848,48848,11,48960,48960,11,49072,49072,11,49184,49184,11,49296,49296,11,49408,49408,11,49520,49520,11,49632,49632,11,49744,49744,11,49856,49856,11,49968,49968,11,50080,50080,11,50192,50192,11,50304,50304,11,50416,50416,11,50528,50528,11,50640,50640,11,50752,50752,11,50864,50864,11,50976,50976,11,51088,51088,11,51200,51200,11,51312,51312,11,51424,51424,11,51536,51536,11,51648,51648,11,51760,51760,11,51872,51872,11,51984,51984,11,52096,52096,11,52208,52208,11,52320,52320,11,52432,52432,11,52544,52544,11,52656,52656,11,52768,52768,11,52880,52880,11,52992,52992,11,53104,53104,11,53216,53216,11,53328,53328,11,53440,53440,11,53552,53552,11,53664,53664,11,53776,53776,11,53888,53888,11,54000,54000,11,54112,54112,11,54224,54224,11,54336,54336,11,54448,54448,11,54560,54560,11,54672,54672,11,54784,54784,11,54896,54896,11,55008,55008,11,55120,55120,11,64286,64286,5,66272,66272,5,68900,68903,5,69762,69762,7,69817,69818,5,69927,69931,5,70003,70003,5,70070,70078,5,70094,70094,7,70194,70195,7,70206,70206,5,70400,70401,5,70463,70463,7,70475,70477,7,70512,70516,5,70722,70724,5,70832,70832,5,70842,70842,5,70847,70848,5,71088,71089,7,71102,71102,7,71219,71226,5,71231,71232,5,71342,71343,7,71453,71455,5,71463,71467,5,71737,71738,5,71995,71996,5,72000,72000,7,72145,72147,7,72160,72160,5,72249,72249,7,72273,72278,5,72330,72342,5,72752,72758,5,72850,72871,5,72882,72883,5,73018,73018,5,73031,73031,5,73109,73109,5,73461,73462,7,94031,94031,5,94192,94193,7,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,126976,126979,14,127184,127231,14,127344,127345,14,127405,127461,14,127514,127514,14,127561,127567,14,127778,127779,14,127896,127896,14,127985,127986,14,127995,127999,5,128326,128328,14,128360,128366,14,128378,128378,14,128394,128397,14,128405,128406,14,128422,128423,14,128435,128443,14,128453,128464,14,128479,128480,14,128484,128487,14,128496,128498,14,128640,128709,14,128723,128724,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129096,129103,14,129292,129292,14,129311,129311,14,129329,129330,14,129344,129349,14,129360,129374,14,129394,129394,14,129402,129402,14,129413,129425,14,129445,129450,14,129466,129471,14,129483,129483,14,129511,129535,14,129653,129655,14,129667,129670,14,129705,129711,14,129731,129743,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2307,2307,7,2366,2368,7,2382,2383,7,2434,2435,7,2497,2500,5,2519,2519,5,2563,2563,7,2631,2632,5,2677,2677,5,2750,2752,7,2763,2764,7,2817,2817,5,2879,2879,5,2891,2892,7,2914,2915,5,3008,3008,5,3021,3021,5,3076,3076,5,3146,3149,5,3202,3203,7,3264,3265,7,3271,3272,7,3298,3299,5,3390,3390,5,3402,3404,7,3426,3427,5,3535,3535,5,3544,3550,7,3635,3635,7,3763,3763,7,3893,3893,5,3953,3966,5,3981,3991,5,4145,4145,7,4157,4158,5,4209,4212,5,4237,4237,5,4520,4607,10,5970,5971,5,6071,6077,5,6089,6099,5,6277,6278,5,6439,6440,5,6451,6456,7,6683,6683,5,6744,6750,5,6765,6770,7,6846,6846,5,6964,6964,5,6972,6972,5,7019,7027,5,7074,7077,5,7083,7085,5,7146,7148,7,7154,7155,7,7222,7223,5,7394,7400,5,7416,7417,5,8204,8204,5,8233,8233,4,8288,8292,4,8413,8416,5,8482,8482,14,8986,8987,14,9193,9203,14,9654,9654,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9775,14,9792,9792,14,9800,9811,14,9825,9826,14,9831,9831,14,9852,9853,14,9872,9873,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9936,9936,14,9941,9960,14,9974,9974,14,9982,9985,14,9992,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10145,10145,14,11013,11015,14,11503,11505,5,12334,12335,5,12951,12951,14,42612,42621,5,43014,43014,5,43047,43047,7,43204,43205,5,43335,43345,5,43395,43395,7,43450,43451,7,43561,43566,5,43573,43574,5,43644,43644,5,43710,43711,5,43758,43759,7,44005,44005,5,44012,44012,7,44060,44060,11,44116,44116,11,44172,44172,11,44228,44228,11,44284,44284,11,44340,44340,11,44396,44396,11,44452,44452,11,44508,44508,11,44564,44564,11,44620,44620,11,44676,44676,11,44732,44732,11,44788,44788,11,44844,44844,11,44900,44900,11,44956,44956,11,45012,45012,11,45068,45068,11,45124,45124,11,45180,45180,11,45236,45236,11,45292,45292,11,45348,45348,11,45404,45404,11,45460,45460,11,45516,45516,11,45572,45572,11,45628,45628,11,45684,45684,11,45740,45740,11,45796,45796,11,45852,45852,11,45908,45908,11,45964,45964,11,46020,46020,11,46076,46076,11,46132,46132,11,46188,46188,11,46244,46244,11,46300,46300,11,46356,46356,11,46412,46412,11,46468,46468,11,46524,46524,11,46580,46580,11,46636,46636,11,46692,46692,11,46748,46748,11,46804,46804,11,46860,46860,11,46916,46916,11,46972,46972,11,47028,47028,11,47084,47084,11,47140,47140,11,47196,47196,11,47252,47252,11,47308,47308,11,47364,47364,11,47420,47420,11,47476,47476,11,47532,47532,11,47588,47588,11,47644,47644,11,47700,47700,11,47756,47756,11,47812,47812,11,47868,47868,11,47924,47924,11,47980,47980,11,48036,48036,11,48092,48092,11,48148,48148,11,48204,48204,11,48260,48260,11,48316,48316,11,48372,48372,11,48428,48428,11,48484,48484,11,48540,48540,11,48596,48596,11,48652,48652,11,48708,48708,11,48764,48764,11,48820,48820,11,48876,48876,11,48932,48932,11,48988,48988,11,49044,49044,11,49100,49100,11,49156,49156,11,49212,49212,11,49268,49268,11,49324,49324,11,49380,49380,11,49436,49436,11,49492,49492,11,49548,49548,11,49604,49604,11,49660,49660,11,49716,49716,11,49772,49772,11,49828,49828,11,49884,49884,11,49940,49940,11,49996,49996,11,50052,50052,11,50108,50108,11,50164,50164,11,50220,50220,11,50276,50276,11,50332,50332,11,50388,50388,11,50444,50444,11,50500,50500,11,50556,50556,11,50612,50612,11,50668,50668,11,50724,50724,11,50780,50780,11,50836,50836,11,50892,50892,11,50948,50948,11,51004,51004,11,51060,51060,11,51116,51116,11,51172,51172,11,51228,51228,11,51284,51284,11,51340,51340,11,51396,51396,11,51452,51452,11,51508,51508,11,51564,51564,11,51620,51620,11,51676,51676,11,51732,51732,11,51788,51788,11,51844,51844,11,51900,51900,11,51956,51956,11,52012,52012,11,52068,52068,11,52124,52124,11,52180,52180,11,52236,52236,11,52292,52292,11,52348,52348,11,52404,52404,11,52460,52460,11,52516,52516,11,52572,52572,11,52628,52628,11,52684,52684,11,52740,52740,11,52796,52796,11,52852,52852,11,52908,52908,11,52964,52964,11,53020,53020,11,53076,53076,11,53132,53132,11,53188,53188,11,53244,53244,11,53300,53300,11,53356,53356,11,53412,53412,11,53468,53468,11,53524,53524,11,53580,53580,11,53636,53636,11,53692,53692,11,53748,53748,11,53804,53804,11,53860,53860,11,53916,53916,11,53972,53972,11,54028,54028,11,54084,54084,11,54140,54140,11,54196,54196,11,54252,54252,11,54308,54308,11,54364,54364,11,54420,54420,11,54476,54476,11,54532,54532,11,54588,54588,11,54644,54644,11,54700,54700,11,54756,54756,11,54812,54812,11,54868,54868,11,54924,54924,11,54980,54980,11,55036,55036,11,55092,55092,11,55148,55148,11,55216,55238,9,65056,65071,5,65529,65531,4,68097,68099,5,68159,68159,5,69446,69456,5,69688,69702,5,69808,69810,7,69815,69816,7,69821,69821,1,69888,69890,5,69932,69932,7,69957,69958,7,70016,70017,5,70067,70069,7,70079,70080,7,70089,70092,5,70095,70095,5,70191,70193,5,70196,70196,5,70198,70199,5,70367,70367,5,70371,70378,5,70402,70403,7,70462,70462,5,70464,70464,5,70471,70472,7,70487,70487,5,70502,70508,5,70709,70711,7,70720,70721,7,70725,70725,7,70750,70750,5,70833,70834,7,70841,70841,7,70843,70844,7,70846,70846,7,70849,70849,7,71087,71087,5,71090,71093,5,71100,71101,5,71103,71104,5,71216,71218,7,71227,71228,7,71230,71230,7,71339,71339,5,71341,71341,5,71344,71349,5,71351,71351,5,71456,71457,7,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123628,123631,5,125252,125258,5,126980,126980,14,127183,127183,14,127245,127247,14,127340,127343,14,127358,127359,14,127377,127386,14,127462,127487,6,127491,127503,14,127535,127535,14,127548,127551,14,127568,127569,14,127744,127777,14,127780,127891,14,127894,127895,14,127897,127899,14,127902,127984,14,127987,127989,14,127991,127994,14,128000,128253,14,128255,128317,14,128329,128334,14,128336,128359,14,128367,128368,14,128371,128377,14,128379,128390,14,128392,128393,14,128398,128399,14,128401,128404,14,128407,128419,14,128421,128421,14,128424,128424,14,128433,128434,14,128444,128444,14,128450,128452,14,128465,128467,14,128476,128478,14,128481,128481,14,128483,128483,14,128488,128488,14,128495,128495,14,128499,128499,14,128506,128591,14,128710,128714,14,128721,128722,14,128725,128725,14,128728,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129664,129666,14,129671,129679,14,129686,129704,14,129712,129718,14,129728,129730,14,129744,129750,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2259,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3134,3136,5,3142,3144,5,3157,3158,5,3201,3201,5,3260,3260,5,3263,3263,5,3266,3266,5,3270,3270,5,3274,3275,7,3285,3286,5,3328,3329,5,3387,3388,5,3391,3392,7,3398,3400,7,3405,3405,5,3415,3415,5,3457,3457,5,3530,3530,5,3536,3537,7,3542,3542,5,3551,3551,5,3633,3633,5,3636,3642,5,3761,3761,5,3764,3772,5,3864,3865,5,3895,3895,5,3902,3903,7,3967,3967,7,3974,3975,5,3993,4028,5,4141,4144,5,4146,4151,5,4155,4156,7,4182,4183,7,4190,4192,5,4226,4226,5,4229,4230,5,4253,4253,5,4448,4519,9,4957,4959,5,5938,5940,5,6002,6003,5,6070,6070,7,6078,6085,7,6087,6088,7,6109,6109,5,6158,6158,4,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6848,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7673,5,8203,8203,4,8205,8205,13,8232,8232,4,8234,8238,4,8265,8265,14,8293,8293,4,8400,8412,5,8417,8417,5,8421,8432,5,8505,8505,14,8617,8618,14,9000,9000,14,9167,9167,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9776,9783,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9935,14,9937,9937,14,9939,9940,14,9961,9962,14,9968,9973,14,9975,9978,14,9981,9981,14,9986,9986,14,9989,9989,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10084,14,10133,10135,14,10160,10160,14,10548,10549,14,11035,11036,14,11093,11093,14,11647,11647,5,12330,12333,5,12336,12336,14,12441,12442,5,12953,12953,14,42608,42610,5,42654,42655,5,43010,43010,5,43019,43019,5,43045,43046,5,43052,43052,5,43188,43203,7,43232,43249,5,43302,43309,5,43346,43347,7,43392,43394,5,43443,43443,5,43446,43449,5,43452,43453,5,43493,43493,5,43567,43568,7,43571,43572,7,43587,43587,5,43597,43597,7,43696,43696,5,43703,43704,5,43713,43713,5,43756,43757,5,43765,43765,7,44003,44004,7,44006,44007,7,44009,44010,7,44013,44013,5,44033,44059,12,44061,44087,12,44089,44115,12,44117,44143,12,44145,44171,12,44173,44199,12,44201,44227,12,44229,44255,12,44257,44283,12,44285,44311,12,44313,44339,12,44341,44367,12,44369,44395,12,44397,44423,12,44425,44451,12,44453,44479,12,44481,44507,12,44509,44535,12,44537,44563,12,44565,44591,12,44593,44619,12,44621,44647,12,44649,44675,12,44677,44703,12,44705,44731,12,44733,44759,12,44761,44787,12,44789,44815,12,44817,44843,12,44845,44871,12,44873,44899,12,44901,44927,12,44929,44955,12,44957,44983,12,44985,45011,12,45013,45039,12,45041,45067,12,45069,45095,12,45097,45123,12,45125,45151,12,45153,45179,12,45181,45207,12,45209,45235,12,45237,45263,12,45265,45291,12,45293,45319,12,45321,45347,12,45349,45375,12,45377,45403,12,45405,45431,12,45433,45459,12,45461,45487,12,45489,45515,12,45517,45543,12,45545,45571,12,45573,45599,12,45601,45627,12,45629,45655,12,45657,45683,12,45685,45711,12,45713,45739,12,45741,45767,12,45769,45795,12,45797,45823,12,45825,45851,12,45853,45879,12,45881,45907,12,45909,45935,12,45937,45963,12,45965,45991,12,45993,46019,12,46021,46047,12,46049,46075,12,46077,46103,12,46105,46131,12,46133,46159,12,46161,46187,12,46189,46215,12,46217,46243,12,46245,46271,12,46273,46299,12,46301,46327,12,46329,46355,12,46357,46383,12,46385,46411,12,46413,46439,12,46441,46467,12,46469,46495,12,46497,46523,12,46525,46551,12,46553,46579,12,46581,46607,12,46609,46635,12,46637,46663,12,46665,46691,12,46693,46719,12,46721,46747,12,46749,46775,12,46777,46803,12,46805,46831,12,46833,46859,12,46861,46887,12,46889,46915,12,46917,46943,12,46945,46971,12,46973,46999,12,47001,47027,12,47029,47055,12,47057,47083,12,47085,47111,12,47113,47139,12,47141,47167,12,47169,47195,12,47197,47223,12,47225,47251,12,47253,47279,12,47281,47307,12,47309,47335,12,47337,47363,12,47365,47391,12,47393,47419,12,47421,47447,12,47449,47475,12,47477,47503,12,47505,47531,12,47533,47559,12,47561,47587,12,47589,47615,12,47617,47643,12,47645,47671,12,47673,47699,12,47701,47727,12,47729,47755,12,47757,47783,12,47785,47811,12,47813,47839,12,47841,47867,12,47869,47895,12,47897,47923,12,47925,47951,12,47953,47979,12,47981,48007,12,48009,48035,12,48037,48063,12,48065,48091,12,48093,48119,12,48121,48147,12,48149,48175,12,48177,48203,12,48205,48231,12,48233,48259,12,48261,48287,12,48289,48315,12,48317,48343,12,48345,48371,12,48373,48399,12,48401,48427,12,48429,48455,12,48457,48483,12,48485,48511,12,48513,48539,12,48541,48567,12,48569,48595,12,48597,48623,12,48625,48651,12,48653,48679,12,48681,48707,12,48709,48735,12,48737,48763,12,48765,48791,12,48793,48819,12,48821,48847,12,48849,48875,12,48877,48903,12,48905,48931,12,48933,48959,12,48961,48987,12,48989,49015,12,49017,49043,12,49045,49071,12,49073,49099,12,49101,49127,12,49129,49155,12,49157,49183,12,49185,49211,12,49213,49239,12,49241,49267,12,49269,49295,12,49297,49323,12,49325,49351,12,49353,49379,12,49381,49407,12,49409,49435,12,49437,49463,12,49465,49491,12,49493,49519,12,49521,49547,12,49549,49575,12,49577,49603,12,49605,49631,12,49633,49659,12,49661,49687,12,49689,49715,12,49717,49743,12,49745,49771,12,49773,49799,12,49801,49827,12,49829,49855,12,49857,49883,12,49885,49911,12,49913,49939,12,49941,49967,12,49969,49995,12,49997,50023,12,50025,50051,12,50053,50079,12,50081,50107,12,50109,50135,12,50137,50163,12,50165,50191,12,50193,50219,12,50221,50247,12,50249,50275,12,50277,50303,12,50305,50331,12,50333,50359,12,50361,50387,12,50389,50415,12,50417,50443,12,50445,50471,12,50473,50499,12,50501,50527,12,50529,50555,12,50557,50583,12,50585,50611,12,50613,50639,12,50641,50667,12,50669,50695,12,50697,50723,12,50725,50751,12,50753,50779,12,50781,50807,12,50809,50835,12,50837,50863,12,50865,50891,12,50893,50919,12,50921,50947,12,50949,50975,12,50977,51003,12,51005,51031,12,51033,51059,12,51061,51087,12,51089,51115,12,51117,51143,12,51145,51171,12,51173,51199,12,51201,51227,12,51229,51255,12,51257,51283,12,51285,51311,12,51313,51339,12,51341,51367,12,51369,51395,12,51397,51423,12,51425,51451,12,51453,51479,12,51481,51507,12,51509,51535,12,51537,51563,12,51565,51591,12,51593,51619,12,51621,51647,12,51649,51675,12,51677,51703,12,51705,51731,12,51733,51759,12,51761,51787,12,51789,51815,12,51817,51843,12,51845,51871,12,51873,51899,12,51901,51927,12,51929,51955,12,51957,51983,12,51985,52011,12,52013,52039,12,52041,52067,12,52069,52095,12,52097,52123,12,52125,52151,12,52153,52179,12,52181,52207,12,52209,52235,12,52237,52263,12,52265,52291,12,52293,52319,12,52321,52347,12,52349,52375,12,52377,52403,12,52405,52431,12,52433,52459,12,52461,52487,12,52489,52515,12,52517,52543,12,52545,52571,12,52573,52599,12,52601,52627,12,52629,52655,12,52657,52683,12,52685,52711,12,52713,52739,12,52741,52767,12,52769,52795,12,52797,52823,12,52825,52851,12,52853,52879,12,52881,52907,12,52909,52935,12,52937,52963,12,52965,52991,12,52993,53019,12,53021,53047,12,53049,53075,12,53077,53103,12,53105,53131,12,53133,53159,12,53161,53187,12,53189,53215,12,53217,53243,12,53245,53271,12,53273,53299,12,53301,53327,12,53329,53355,12,53357,53383,12,53385,53411,12,53413,53439,12,53441,53467,12,53469,53495,12,53497,53523,12,53525,53551,12,53553,53579,12,53581,53607,12,53609,53635,12,53637,53663,12,53665,53691,12,53693,53719,12,53721,53747,12,53749,53775,12,53777,53803,12,53805,53831,12,53833,53859,12,53861,53887,12,53889,53915,12,53917,53943,12,53945,53971,12,53973,53999,12,54001,54027,12,54029,54055,12,54057,54083,12,54085,54111,12,54113,54139,12,54141,54167,12,54169,54195,12,54197,54223,12,54225,54251,12,54253,54279,12,54281,54307,12,54309,54335,12,54337,54363,12,54365,54391,12,54393,54419,12,54421,54447,12,54449,54475,12,54477,54503,12,54505,54531,12,54533,54559,12,54561,54587,12,54589,54615,12,54617,54643,12,54645,54671,12,54673,54699,12,54701,54727,12,54729,54755,12,54757,54783,12,54785,54811,12,54813,54839,12,54841,54867,12,54869,54895,12,54897,54923,12,54925,54951,12,54953,54979,12,54981,55007,12,55009,55035,12,55037,55063,12,55065,55091,12,55093,55119,12,55121,55147,12,55149,55175,12,55177,55203,12,55243,55291,10,65024,65039,5,65279,65279,4,65520,65528,4,66045,66045,5,66422,66426,5,68101,68102,5,68152,68154,5,68325,68326,5,69291,69292,5,69632,69632,7,69634,69634,7,69759,69761,5]")}static getInstance(){return R._INSTANCE||(R._INSTANCE=new R),R._INSTANCE}getGraphemeBreakType(e){if(e<32)return 10===e?3:13===e?2:4;if(e<127)return 0;const t=this._data,n=t.length/3;let i=1;for(;i<=n;)if(e<t[3*i])i*=2;else{if(!(e>t[3*i+1]))return t[3*i+2];i=2*i+1}return 0}}R._INSTANCE=null;class V{constructor(e,t,n,i){this.vsWorker=e,this.req=t,this.method=n,this.args=i,this.type=0}}class P{constructor(e,t,n,i){this.vsWorker=e,this.seq=t,this.res=n,this.err=i,this.type=1}}class I{constructor(e,t,n,i){this.vsWorker=e,this.req=t,this.eventName=n,this.arg=i,this.type=2}}class x{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}}class k{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}}class D{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t){const n=String(++this._lastSentReq);return new Promise((i,r)=>{this._pendingReplies[n]={resolve:i,reject:r},this._send(new V(this._workerId,n,e,t))})}listen(e,t){let n=null;const i=new w({onFirstListenerAdd:()=>{n=String(++this._lastSentReq),this._pendingEmitters.set(n,i),this._send(new I(this._workerId,n,e,t))},onLastListenerRemove:()=>{this._pendingEmitters.delete(n),this._send(new k(this._workerId,n)),n=null}});return i.event}handleMessage(e){e&&e.vsWorker&&(-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e))}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq])return;let t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;return e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),void t.reject(n)}t.resolve(e.res)}_handleRequestMessage(e){let t=e.req;this._handler.handleMessage(e.method,e.args).then(e=>{this._send(new P(this._workerId,t,e,void 0))},e=>{e.detail instanceof Error&&(e.detail=s(e.detail)),this._send(new P(this._workerId,t,void 0,s(e)))})}_handleSubscribeEventMessage(e){const t=e.req,n=this._handler.handleEvent(e.eventName,e.arg)(e=>{this._send(new x(this._workerId,t,e))});this._pendingEvents.set(t,n)}_handleEventMessage(e){this._pendingEmitters.has(e.req)&&this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){this._pendingEvents.has(e.req)&&(this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req))}_send(e){let t=[];if(0===e.type)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else 1===e.type&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}}function F(e){return"o"===e[0]&&"n"===e[1]&&M(e.charCodeAt(2))}function U(e){return/^onDynamic/.test(e)&&M(e.charCodeAt(9))}function B(e,t,n){const i=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},r=e=>function(t){return n(e,t)};let s={};for(const o of e)U(o)?s[o]=r(o):F(o)?s[o]=n(o,void 0):s[o]=i(o);return s}class q{constructor(e,t){this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new D({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t)=>this._handleMessage(e,t),handleEvent:(e,t)=>this._handleEvent(e,t)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t){if("$initialize"===e)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!=typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch(e){return Promise.reject(e)}}_handleEvent(e,t){if(!this._requestHandler)throw new Error("Missing requestHandler");if(U(e)){const n=this._requestHandler[e].call(this._requestHandler,t);if("function"!=typeof n)throw new Error(`Missing dynamic event ${e} on request handler.`);return n}if(F(e)){const t=this._requestHandler[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on request handler.`);return t}throw new Error("Malformed event name "+e)}initialize(e,t,n,i){this._protocol.setWorkerId(e);const r=B(i,(e,t)=>this._protocol.sendMessage(e,t),(e,t)=>this._protocol.listen(e,t));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(r),Promise.resolve(v(this._requestHandler))):(t&&(void 0!==t.baseUrl&&delete t.baseUrl,void 0!==t.paths&&void 0!==t.paths.vs&&delete t.paths.vs,void 0!==typeof t.trustedTypesPolicy&&delete t.trustedTypesPolicy,t.catchError=!0,S.a.require.config(t)),new Promise((e,t)=>{(0,S.a.require)([n],n=>{this._requestHandler=n.create(r),this._requestHandler?e(v(this._requestHandler)):t(new Error("No RequestHandler!"))},t)}))}}class H{constructor(e,t,n,i){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function W(e,t){return(t<<5)-t+e|0}function j(e,t){t=W(149417,t);for(let n=0,i=e.length;n<i;n++)t=W(e.charCodeAt(n),t);return t}function $(e,t,n=32){const i=n-t;return(e<<t|(~((1<<i)-1)&e)>>>i)>>>0}function z(e,t=0,n=e.byteLength,i=0){for(let r=0;r<n;r++)e[t+r]=i}function G(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(e=>e.toString(16).padStart(2,"0")).join(""):function(e,t,n="0"){for(;e.length<t;)e=n+e;return e}((e>>>0).toString(16),t/4)}class Y{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){const t=e.length;if(0===t)return;const n=this._buff;let i,r,s=this._buffLen,o=this._leftoverHighSurrogate;for(0!==o?(i=o,r=-1,o=0):(i=e.charCodeAt(0),r=0);;){let a=i;if(O(i)){if(!(r+1<t)){o=i;break}{const t=e.charCodeAt(r+1);T(t)?(r++,a=K(i,t)):a=65533}}else T(i)&&(a=65533);if(s=this._push(n,s,a),r++,!(r<t))break;i=e.charCodeAt(r)}this._buffLen=s,this._leftoverHighSurrogate=o}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(1984&n)>>>6,e[t++]=128|(63&n)>>>0):n<65536?(e[t++]=224|(61440&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0):(e[t++]=240|(1835008&n)>>>18,e[t++]=128|(258048&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),G(this._h0)+G(this._h1)+G(this._h2)+G(this._h3)+G(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,z(this._buff,this._buffLen),this._buffLen>56&&(this._step(),z(this._buff));const e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){const e=Y._bigBlock32,t=this._buffDV;for(let h=0;h<64;h+=4)e.setUint32(h,t.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)e.setUint32(h,$(e.getUint32(h-12,!1)^e.getUint32(h-32,!1)^e.getUint32(h-56,!1)^e.getUint32(h-64,!1),1),!1);let n,i,r,s=this._h0,o=this._h1,a=this._h2,l=this._h3,u=this._h4;for(let h=0;h<80;h++)h<20?(n=o&a|~o&l,i=1518500249):h<40?(n=o^a^l,i=1859775393):h<60?(n=o&a|o&l|a&l,i=2400959708):(n=o^a^l,i=3395469782),r=$(s,5)+n+u+i+e.getUint32(4*h,!1)&4294967295,u=l,l=a,a=$(o,30),o=s,s=r;this._h0=this._h0+s&4294967295,this._h1=this._h1+o&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+l&4294967295,this._h4=this._h4+u&4294967295}}Y._bigBlock32=new DataView(new ArrayBuffer(320));class Q{constructor(e){this.source=e}getElements(){const e=this.source,t=new Int32Array(e.length);for(let n=0,i=e.length;n<i;n++)t[n]=e.charCodeAt(n);return t}}function X(e,t,n){return new te(new Q(e),new Q(t)).ComputeDiff(n).changes}class Z{static Assert(e,t){if(!e)throw new Error(t)}}class J{static Copy(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}static Copy2(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}}class ee{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new H(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class te{constructor(e,t,n=null){this.ContinueProcessingPredicate=n,this._originalSequence=e,this._modifiedSequence=t;const[i,r,s]=te._getElements(e),[o,a,l]=te._getElements(t);this._hasStrings=s&&l,this._originalStringElements=i,this._originalElementsOrHash=r,this._modifiedStringElements=o,this._modifiedElementsOrHash=a,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&"string"==typeof e[0]}static _getElements(e){const t=e.getElements();if(te._isStringArray(t)){const e=new Int32Array(t.length);for(let n=0,i=t.length;n<i;n++)e[n]=j(t[n],0);return[t,e,!0]}return t instanceof Int32Array?[[],t,!1]:[[],new Int32Array(t),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}ElementsAreStrictEqual(e,t){return!!this.ElementsAreEqual(e,t)&&te._getStrictElement(this._originalSequence,e)===te._getStrictElement(this._modifiedSequence,t)}static _getStrictElement(e,t){return"function"==typeof e.getStrictElement?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,i,r){const s=[!1];let o=this.ComputeDiffRecursive(e,t,n,i,s);return r&&(o=this.PrettifyChanges(o)),{quitEarly:s[0],changes:o}}ComputeDiffRecursive(e,t,n,i,r){for(r[0]=!1;e<=t&&n<=i&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&i>=n&&this.ElementsAreEqual(t,i);)t--,i--;if(e>t||n>i){let r;return n<=i?(Z.Assert(e===t+1,"originalStart should only be one more than originalEnd"),r=[new H(e,0,n,i-n+1)]):e<=t?(Z.Assert(n===i+1,"modifiedStart should only be one more than modifiedEnd"),r=[new H(e,t-e+1,n,0)]):(Z.Assert(e===t+1,"originalStart should only be one more than originalEnd"),Z.Assert(n===i+1,"modifiedStart should only be one more than modifiedEnd"),r=[]),r}const s=[0],o=[0],a=this.ComputeRecursionPoint(e,t,n,i,s,o,r),l=s[0],u=o[0];if(null!==a)return a;if(!r[0]){const s=this.ComputeDiffRecursive(e,l,n,u,r);let o=[];return o=r[0]?[new H(l+1,t-(l+1)+1,u+1,i-(u+1)+1)]:this.ComputeDiffRecursive(l+1,t,u+1,i,r),this.ConcatenateChanges(s,o)}return[new H(e,t-e+1,n,i-n+1)]}WALKTRACE(e,t,n,i,r,s,o,a,l,u,h,c,d,f,m,g,_,p){let C=null,b=null,L=new ee,S=t,N=n,y=d[0]-g[0]-i,E=-1073741824,A=this.m_forwardHistory.length-1;do{const t=y+e;t===S||t<N&&l[t-1]<l[t+1]?(f=(h=l[t+1])-y-i,h<E&&L.MarkNextChange(),E=h,L.AddModifiedElement(h+1,f),y=t+1-e):(f=(h=l[t-1]+1)-y-i,h<E&&L.MarkNextChange(),E=h-1,L.AddOriginalElement(h,f+1),y=t-1-e),A>=0&&(e=(l=this.m_forwardHistory[A])[0],S=1,N=l.length-1)}while(--A>=-1);if(C=L.getReverseChanges(),p[0]){let e=d[0]+1,t=g[0]+1;if(null!==C&&C.length>0){const n=C[C.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}b=[new H(e,c-e+1,t,m-t+1)]}else{L=new ee,S=s,N=o,y=d[0]-g[0]-a,E=1073741824,A=_?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const e=y+r;e===S||e<N&&u[e-1]>=u[e+1]?(f=(h=u[e+1]-1)-y-a,h>E&&L.MarkNextChange(),E=h+1,L.AddOriginalElement(h+1,f+1),y=e+1-r):(f=(h=u[e-1])-y-a,h>E&&L.MarkNextChange(),E=h,L.AddModifiedElement(h+1,f+1),y=e-1-r),A>=0&&(r=(u=this.m_reverseHistory[A])[0],S=1,N=u.length-1)}while(--A>=-1);b=L.getChanges()}return this.ConcatenateChanges(C,b)}ComputeRecursionPoint(e,t,n,i,r,s,o){let a=0,l=0,u=0,h=0,c=0,d=0;e--,n--,r[0]=0,s[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const f=t-e+(i-n),m=f+1,g=new Int32Array(m),_=new Int32Array(m),p=i-n,C=t-e,b=e-n,L=t-i,S=(C-p)%2==0;g[p]=e,_[C]=t,o[0]=!1;for(let N=1;N<=f/2+1;N++){let f=0,y=0;u=this.ClipDiagonalBound(p-N,N,p,m),h=this.ClipDiagonalBound(p+N,N,p,m);for(let e=u;e<=h;e+=2){a=e===u||e<h&&g[e-1]<g[e+1]?g[e+1]:g[e-1]+1,l=a-(e-p)-b;const n=a;for(;a<t&&l<i&&this.ElementsAreEqual(a+1,l+1);)a++,l++;if(g[e]=a,a+l>f+y&&(f=a,y=l),!S&&Math.abs(e-C)<=N-1&&a>=_[e])return r[0]=a,s[0]=l,n<=_[e]&&N<=1448?this.WALKTRACE(p,u,h,b,C,c,d,L,g,_,a,t,r,l,i,s,S,o):null}const E=(f-e+(y-n)-N)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(f,E))return o[0]=!0,r[0]=f,s[0]=y,E>0&&N<=1448?this.WALKTRACE(p,u,h,b,C,c,d,L,g,_,a,t,r,l,i,s,S,o):(e++,n++,[new H(e,t-e+1,n,i-n+1)]);c=this.ClipDiagonalBound(C-N,N,C,m),d=this.ClipDiagonalBound(C+N,N,C,m);for(let m=c;m<=d;m+=2){a=m===c||m<d&&_[m-1]>=_[m+1]?_[m+1]-1:_[m-1],l=a-(m-C)-L;const f=a;for(;a>e&&l>n&&this.ElementsAreEqual(a,l);)a--,l--;if(_[m]=a,S&&Math.abs(m-p)<=N&&a<=g[m])return r[0]=a,s[0]=l,f>=g[m]&&N<=1448?this.WALKTRACE(p,u,h,b,C,c,d,L,g,_,a,t,r,l,i,s,S,o):null}if(N<=1447){let e=new Int32Array(h-u+2);e[0]=p-u+1,J.Copy2(g,u,e,1,h-u+1),this.m_forwardHistory.push(e),e=new Int32Array(d-c+2),e[0]=C-c+1,J.Copy2(_,c,e,1,d-c+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(p,u,h,b,C,c,d,L,g,_,a,t,r,l,i,s,S,o)}PrettifyChanges(e){for(let t=0;t<e.length;t++){const n=e[t],i=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,r=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,s=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<i&&n.modifiedStart+n.modifiedLength<r&&(!s||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){const e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}let a=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],a)&&(e[t]=a[0],e.splice(t+1,1),t--)}for(let t=e.length-1;t>=0;t--){const n=e[t];let i=0,r=0;if(t>0){const n=e[t-1];i=n.originalStart+n.originalLength,r=n.modifiedStart+n.modifiedLength}const s=n.originalLength>0,o=n.modifiedLength>0;let a=0,l=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){const t=n.originalStart-e,u=n.modifiedStart-e;if(t<i||u<r)break;if(s&&!this.OriginalElementsAreEqual(t,t+n.originalLength))break;if(o&&!this.ModifiedElementsAreEqual(u,u+n.modifiedLength))break;const h=(t===i&&u===r?5:0)+this._boundaryScore(t,n.originalLength,u,n.modifiedLength);h>l&&(l=h,a=e)}n.originalStart-=a,n.modifiedStart-=a;const u=[null];t>0&&this.ChangesOverlap(e[t-1],e[t],u)&&(e[t-1]=u[0],e.splice(t,1),t++)}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){const n=e[t-1],i=e[t],r=i.originalStart-n.originalStart-n.originalLength,s=n.originalStart,o=i.originalStart+i.originalLength,a=o-s,l=n.modifiedStart,u=i.modifiedStart+i.modifiedLength,h=u-l;if(r<5&&a<20&&h<20){const e=this._findBetterContiguousSequence(s,a,l,h,r);if(e){const[t,s]=e;t===n.originalStart+n.originalLength&&s===n.modifiedStart+n.modifiedLength||(n.originalLength=t-n.originalStart,n.modifiedLength=s-n.modifiedStart,i.originalStart=t+r,i.modifiedStart=s+r,i.originalLength=o-i.originalStart,i.modifiedLength=u-i.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,i,r){if(t<r||i<r)return null;const s=e+t-r+1,o=n+i-r+1;let a=0,l=0,u=0;for(let h=e;h<s;h++)for(let e=n;e<o;e++){const t=this._contiguousSequenceScore(h,e,r);t>0&&t>a&&(a=t,l=h,u=e)}return a>0?[l,u]:null}_contiguousSequenceScore(e,t,n){let i=0;for(let r=0;r<n;r++){if(!this.ElementsAreEqual(e+r,t+r))return 0;i+=this._originalStringElements[e+r].length}return i}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,i){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,i)?1:0)}ConcatenateChanges(e,t){let n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){const i=new Array(e.length+t.length-1);return J.Copy(e,0,i,0,e.length-1),i[e.length-1]=n[0],J.Copy(t,1,i,e.length,t.length-1),i}{const n=new Array(e.length+t.length);return J.Copy(e,0,n,0,e.length),J.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(Z.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),Z.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){const i=e.originalStart;let r=e.originalLength;const s=e.modifiedStart;let o=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(r=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(o=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new H(i,r,s,o),!0}return n[0]=null,!1}ClipDiagonalBound(e,t,n,i){if(e>=0&&e<i)return e;const r=t%2==0;return e<0?r===(n%2==0)?0:1:r===((i-n-1)%2==0)?i-1:i-2}}var ne=n("c317");class ie extends Error{constructor(e,t,n){let i;"string"==typeof t&&0===t.indexOf("not ")?(i="must not be",t=t.replace(/^not /,"")):i="must be";const r=-1!==e.indexOf(".")?"property":"argument";let s=`The "${e}" ${r} ${i} of type ${t}`;s+=". Received type "+typeof n,super(s),this.code="ERR_INVALID_ARG_TYPE"}}function re(e,t){if("string"!=typeof e)throw new ie(t,"string",e)}function se(e){return 47===e||92===e}function oe(e){return 47===e}function ae(e){return e>=65&&e<=90||e>=97&&e<=122}function le(e,t,n,i){let r="",s=0,o=-1,a=0,l=0;for(let u=0;u<=e.length;++u){if(u<e.length)l=e.charCodeAt(u);else{if(i(l))break;l=47}if(i(l)){if(o===u-1||1===a);else if(2===a){if(r.length<2||2!==s||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2)){if(r.length>2){const e=r.lastIndexOf(n);-1===e?(r="",s=0):(r=r.slice(0,e),s=r.length-1-r.lastIndexOf(n)),o=u,a=0;continue}if(0!==r.length){r="",s=0,o=u,a=0;continue}}t&&(r+=r.length>0?n+"..":"..",s=2)}else r.length>0?r+=`${n}${e.slice(o+1,u)}`:r=e.slice(o+1,u),s=u-o-1;o=u,a=0}else 46===l&&-1!==a?++a:a=-1}return r}function ue(e,t){if(null===t||"object"!=typeof t)throw new ie("pathObject","Object",t);const n=t.dir||t.root,i=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${i}`:`${n}${e}${i}`:i}const he={resolve(...e){let t="",n="",i=!1;for(let r=e.length-1;r>=-1;r--){let s;if(r>=0){if(s=e[r],re(s,"path"),0===s.length)continue}else 0===t.length?s=ne.a():(s=ne.b["="+t]||ne.a(),(void 0===s||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&92===s.charCodeAt(2))&&(s=t+"\\"));const o=s.length;let a=0,l="",u=!1;const h=s.charCodeAt(0);if(1===o)se(h)&&(a=1,u=!0);else if(se(h))if(u=!0,se(s.charCodeAt(1))){let e=2,t=e;for(;e<o&&!se(s.charCodeAt(e));)e++;if(e<o&&e!==t){const n=s.slice(t,e);for(t=e;e<o&&se(s.charCodeAt(e));)e++;if(e<o&&e!==t){for(t=e;e<o&&!se(s.charCodeAt(e));)e++;e!==o&&e===t||(l=`\\\\${n}\\${s.slice(t,e)}`,a=e)}}}else a=1;else ae(h)&&58===s.charCodeAt(1)&&(l=s.slice(0,2),a=2,o>2&&se(s.charCodeAt(2))&&(u=!0,a=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(i){if(t.length>0)break}else if(n=`${s.slice(a)}\\${n}`,i=u,u&&t.length>0)break}return n=le(n,!i,"\\",se),i?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){re(e,"path");const t=e.length;if(0===t)return".";let n,i=0,r=!1;const s=e.charCodeAt(0);if(1===t)return oe(s)?"\\":e;if(se(s))if(r=!0,se(e.charCodeAt(1))){let r=2,s=r;for(;r<t&&!se(e.charCodeAt(r));)r++;if(r<t&&r!==s){const o=e.slice(s,r);for(s=r;r<t&&se(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&!se(e.charCodeAt(r));)r++;if(r===t)return`\\\\${o}\\${e.slice(s)}\\`;r!==s&&(n=`\\\\${o}\\${e.slice(s,r)}`,i=r)}}}else i=1;else ae(s)&&58===e.charCodeAt(1)&&(n=e.slice(0,2),i=2,t>2&&se(e.charCodeAt(2))&&(r=!0,i=3));let o=i<t?le(e.slice(i),!r,"\\",se):"";return 0!==o.length||r||(o="."),o.length>0&&se(e.charCodeAt(t-1))&&(o+="\\"),void 0===n?r?"\\"+o:o:r?`${n}\\${o}`:`${n}${o}`},isAbsolute(e){re(e,"path");const t=e.length;if(0===t)return!1;const n=e.charCodeAt(0);return se(n)||t>2&&ae(n)&&58===e.charCodeAt(1)&&se(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let t,n;for(let s=0;s<e.length;++s){const i=e[s];re(i,"path"),i.length>0&&(void 0===t?t=n=i:t+="\\"+i)}if(void 0===t)return".";let i=!0,r=0;if("string"==typeof n&&se(n.charCodeAt(0))){++r;const e=n.length;e>1&&se(n.charCodeAt(1))&&(++r,e>2&&(se(n.charCodeAt(2))?++r:i=!1))}if(i){for(;r<t.length&&se(t.charCodeAt(r));)r++;r>=2&&(t="\\"+t.slice(r))}return he.normalize(t)},relative(e,t){if(re(e,"from"),re(t,"to"),e===t)return"";const n=he.resolve(e),i=he.resolve(t);if(n===i)return"";if((e=n.toLowerCase())===(t=i.toLowerCase()))return"";let r=0;for(;r<e.length&&92===e.charCodeAt(r);)r++;let s=e.length;for(;s-1>r&&92===e.charCodeAt(s-1);)s--;const o=s-r;let a=0;for(;a<t.length&&92===t.charCodeAt(a);)a++;let l=t.length;for(;l-1>a&&92===t.charCodeAt(l-1);)l--;const u=l-a,h=o<u?o:u;let c=-1,d=0;for(;d<h;d++){const n=e.charCodeAt(r+d);if(n!==t.charCodeAt(a+d))break;92===n&&(c=d)}if(d!==h){if(-1===c)return i}else{if(u>h){if(92===t.charCodeAt(a+d))return i.slice(a+d+1);if(2===d)return i.slice(a+d)}o>h&&(92===e.charCodeAt(r+d)?c=d:2===d&&(c=3)),-1===c&&(c=0)}let f="";for(d=r+c+1;d<=s;++d)d!==s&&92!==e.charCodeAt(d)||(f+=0===f.length?"..":"\\..");return a+=c,f.length>0?`${f}${i.slice(a,l)}`:(92===i.charCodeAt(a)&&++a,i.slice(a,l))},toNamespacedPath(e){if("string"!=typeof e)return e;if(0===e.length)return"";const t=he.resolve(e);if(t.length<=2)return e;if(92===t.charCodeAt(0)){if(92===t.charCodeAt(1)){const e=t.charCodeAt(2);if(63!==e&&46!==e)return"\\\\?\\UNC\\"+t.slice(2)}}else if(ae(t.charCodeAt(0))&&58===t.charCodeAt(1)&&92===t.charCodeAt(2))return"\\\\?\\"+t;return e},dirname(e){re(e,"path");const t=e.length;if(0===t)return".";let n=-1,i=0;const r=e.charCodeAt(0);if(1===t)return se(r)?e:".";if(se(r)){if(n=i=1,se(e.charCodeAt(1))){let r=2,s=r;for(;r<t&&!se(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&se(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&!se(e.charCodeAt(r));)r++;if(r===t)return e;r!==s&&(n=i=r+1)}}}}else ae(r)&&58===e.charCodeAt(1)&&(n=t>2&&se(e.charCodeAt(2))?3:2,i=n);let s=-1,o=!0;for(let a=t-1;a>=i;--a)if(se(e.charCodeAt(a))){if(!o){s=a;break}}else o=!1;if(-1===s){if(-1===n)return".";s=n}return e.slice(0,s)},basename(e,t){void 0!==t&&re(t,"ext"),re(e,"path");let n,i=0,r=-1,s=!0;if(e.length>=2&&ae(e.charCodeAt(0))&&58===e.charCodeAt(1)&&(i=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=i;--n){const l=e.charCodeAt(n);if(se(l)){if(!s){i=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(r=n):(o=-1,r=a))}return i===r?r=a:-1===r&&(r=e.length),e.slice(i,r)}for(n=e.length-1;n>=i;--n)if(se(e.charCodeAt(n))){if(!s){i=n+1;break}}else-1===r&&(s=!1,r=n+1);return-1===r?"":e.slice(i,r)},extname(e){re(e,"path");let t=0,n=-1,i=0,r=-1,s=!0,o=0;e.length>=2&&58===e.charCodeAt(1)&&ae(e.charCodeAt(0))&&(t=i=2);for(let a=e.length-1;a>=t;--a){const t=e.charCodeAt(a);if(se(t)){if(!s){i=a+1;break}}else-1===r&&(s=!1,r=a+1),46===t?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1)}return-1===n||-1===r||0===o||1===o&&n===r-1&&n===i+1?"":e.slice(n,r)},format:ue.bind(null,"\\"),parse(e){re(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.length;let i=0,r=e.charCodeAt(0);if(1===n)return se(r)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(se(r)){if(i=1,se(e.charCodeAt(1))){let t=2,r=t;for(;t<n&&!se(e.charCodeAt(t));)t++;if(t<n&&t!==r){for(r=t;t<n&&se(e.charCodeAt(t));)t++;if(t<n&&t!==r){for(r=t;t<n&&!se(e.charCodeAt(t));)t++;t===n?i=t:t!==r&&(i=t+1)}}}}else if(ae(r)&&58===e.charCodeAt(1)){if(n<=2)return t.root=t.dir=e,t;if(i=2,se(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let s=-1,o=i,a=-1,l=!0,u=e.length-1,h=0;for(;u>=i;--u)if(r=e.charCodeAt(u),se(r)){if(!l){o=u+1;break}}else-1===a&&(l=!1,a=u+1),46===r?-1===s?s=u:1!==h&&(h=1):-1!==s&&(h=-1);return-1!==a&&(-1===s||0===h||1===h&&s===a-1&&s===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,s),t.base=e.slice(o,a),t.ext=e.slice(s,a))),t.dir=o>0&&o!==i?e.slice(0,o-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},ce={resolve(...e){let t="",n=!1;for(let i=e.length-1;i>=-1&&!n;i--){const r=i>=0?e[i]:ne.a();re(r,"path"),0!==r.length&&(t=`${r}/${t}`,n=47===r.charCodeAt(0))}return t=le(t,!n,"/",oe),n?"/"+t:t.length>0?t:"."},normalize(e){if(re(e,"path"),0===e.length)return".";const t=47===e.charCodeAt(0),n=47===e.charCodeAt(e.length-1);return 0===(e=le(e,!t,"/",oe)).length?t?"/":n?"./":".":(n&&(e+="/"),t?"/"+e:e)},isAbsolute:e=>(re(e,"path"),e.length>0&&47===e.charCodeAt(0)),join(...e){if(0===e.length)return".";let t;for(let n=0;n<e.length;++n){const i=e[n];re(i,"path"),i.length>0&&(void 0===t?t=i:t+="/"+i)}return void 0===t?".":ce.normalize(t)},relative(e,t){if(re(e,"from"),re(t,"to"),e===t)return"";if((e=ce.resolve(e))===(t=ce.resolve(t)))return"";const n=e.length,i=n-1,r=t.length-1,s=i<r?i:r;let o=-1,a=0;for(;a<s;a++){const n=e.charCodeAt(1+a);if(n!==t.charCodeAt(1+a))break;47===n&&(o=a)}if(a===s)if(r>s){if(47===t.charCodeAt(1+a))return t.slice(1+a+1);if(0===a)return t.slice(1+a)}else i>s&&(47===e.charCodeAt(1+a)?o=a:0===a&&(o=0));let l="";for(a=1+o+1;a<=n;++a)a!==n&&47!==e.charCodeAt(a)||(l+=0===l.length?"..":"/..");return`${l}${t.slice(1+o)}`},toNamespacedPath:e=>e,dirname(e){if(re(e,"path"),0===e.length)return".";const t=47===e.charCodeAt(0);let n=-1,i=!0;for(let r=e.length-1;r>=1;--r)if(47===e.charCodeAt(r)){if(!i){n=r;break}}else i=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename(e,t){void 0!==t&&re(t,"ext"),re(e,"path");let n,i=0,r=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=0;--n){const l=e.charCodeAt(n);if(47===l){if(!s){i=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(r=n):(o=-1,r=a))}return i===r?r=a:-1===r&&(r=e.length),e.slice(i,r)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!s){i=n+1;break}}else-1===r&&(s=!1,r=n+1);return-1===r?"":e.slice(i,r)},extname(e){re(e,"path");let t=-1,n=0,i=-1,r=!0,s=0;for(let o=e.length-1;o>=0;--o){const a=e.charCodeAt(o);if(47!==a)-1===i&&(r=!1,i=o+1),46===a?-1===t?t=o:1!==s&&(s=1):-1!==t&&(s=-1);else if(!r){n=o+1;break}}return-1===t||-1===i||0===s||1===s&&t===i-1&&t===n+1?"":e.slice(t,i)},format:ue.bind(null,"/"),parse(e){re(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=47===e.charCodeAt(0);let i;n?(t.root="/",i=1):i=0;let r=-1,s=0,o=-1,a=!0,l=e.length-1,u=0;for(;l>=i;--l){const t=e.charCodeAt(l);if(47!==t)-1===o&&(a=!1,o=l+1),46===t?-1===r?r=l:1!==u&&(u=1):-1!==r&&(u=-1);else if(!a){s=l+1;break}}if(-1!==o){const i=0===s&&n?1:s;-1===r||0===u||1===u&&r===o-1&&r===s+1?t.base=t.name=e.slice(i,o):(t.name=e.slice(i,r),t.base=e.slice(i,o),t.ext=e.slice(r,o))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};ce.win32=he.win32=he,ce.posix=he.posix=ce,"win32"===ne.c?he.normalize:ce.normalize,"win32"===ne.c?he.resolve:ce.resolve,"win32"===ne.c?he.relative:ce.relative,"win32"===ne.c?he.dirname:ce.dirname,"win32"===ne.c?he.basename:ce.basename,"win32"===ne.c?he.extname:ce.extname,"win32"===ne.c?he.sep:ce.sep;const de=/^\w[\w\d+.-]*$/,fe=/^\//,me=/^\/\//;function ge(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!de.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!fe.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(me.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const _e="/",pe=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class Ce{constructor(e,t,n,i,r,s=!1){"object"==typeof e?(this.scheme=e.scheme||"",this.authority=e.authority||"",this.path=e.path||"",this.query=e.query||"",this.fragment=e.fragment||""):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||"",this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==_e&&(t=_e+t):t=_e}return t}(this.scheme,n||""),this.query=i||"",this.fragment=r||"",ge(this,s))}static isUri(e){return e instanceof Ce||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}get fsPath(){return Ee(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:i,query:r,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=""),void 0===n?n=this.authority:null===n&&(n=""),void 0===i?i=this.path:null===i&&(i=""),void 0===r?r=this.query:null===r&&(r=""),void 0===s?s=this.fragment:null===s&&(s=""),t===this.scheme&&n===this.authority&&i===this.path&&r===this.query&&s===this.fragment?this:new Le(t,n,i,r,s)}static parse(e,t=!1){const n=pe.exec(e);return n?new Le(n[2]||"",ve(n[4]||""),ve(n[5]||""),ve(n[7]||""),ve(n[9]||""),t):new Le("","","","","")}static file(e){let t="";if(S.d&&(e=e.replace(/\\/g,_e)),e[0]===_e&&e[1]===_e){const n=e.indexOf(_e,2);-1===n?(t=e.substring(2),e=_e):(t=e.substring(2,n),e=e.substring(n)||_e)}return new Le("file",t,e,"","")}static from(e){const t=new Le(e.scheme,e.authority,e.path,e.query,e.fragment);return ge(t,!0),t}static joinPath(e,...t){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return n=S.d&&"file"===e.scheme?Ce.file(he.join(Ee(e,!0),...t)).path:ce.join(e.path,...t),e.with({path:n})}toString(e=!1){return Ae(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof Ce)return e;{const t=new Le(e);return t._formatted=e.external,t._fsPath=e._sep===be?e.fsPath:null,t}}return e}}const be=S.d?1:void 0;class Le extends Ce{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Ee(this,!1)),this._fsPath}toString(e=!1){return e?Ae(this,!0):(this._formatted||(this._formatted=Ae(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=be),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const Se={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Ne(e,t){let n=void 0,i=-1;for(let r=0;r<e.length;r++){const s=e.charCodeAt(r);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s)-1!==i&&(n+=encodeURIComponent(e.substring(i,r)),i=-1),void 0!==n&&(n+=e.charAt(r));else{void 0===n&&(n=e.substr(0,r));const t=Se[s];void 0!==t?(-1!==i&&(n+=encodeURIComponent(e.substring(i,r)),i=-1),n+=t):-1===i&&(i=r)}}return-1!==i&&(n+=encodeURIComponent(e.substring(i))),void 0!==n?n:e}function ye(e){let t=void 0;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);35===i||63===i?(void 0===t&&(t=e.substr(0,n)),t+=Se[i]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function Ee(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,S.d&&(n=n.replace(/\//g,"\\")),n}function Ae(e,t){const n=t?ye:Ne;let i="",{scheme:r,authority:s,path:o,query:a,fragment:l}=e;if(r&&(i+=r,i+=":"),(s||"file"===r)&&(i+=_e,i+=_e),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.indexOf(":"),-1===e?i+=n(t,!1):(i+=n(t.substr(0,e),!1),i+=":",i+=n(t.substr(e+1),!1)),i+="@"}s=s.toLowerCase(),e=s.indexOf(":"),-1===e?i+=n(s,!1):(i+=n(s.substr(0,e),!1),i+=s.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){const e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){const e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}i+=n(o,!0)}return a&&(i+="?",i+=n(a,!1)),l&&(i+="#",i+=t?l:Ne(l,!1)),i}const we=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ve(e){return e.match(we)?e.replace(we,e=>function e(t){try{return decodeURIComponent(t)}catch(n){return t.length>3?t.substr(0,3)+e(t.substr(3)):t}}(e)):e}class Me{constructor(e,t){this.lineNumber=e,this.column=t}with(e=this.lineNumber,t=this.column){return e===this.lineNumber&&t===this.column?this:new Me(e,t)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(e){return Me.equals(this,e)}static equals(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(e){return Me.isBefore(this,e)}static isBefore(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}isBeforeOrEqual(e){return Me.isBeforeOrEqual(this,e)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}static compare(e,t){let n=0|e.lineNumber,i=0|t.lineNumber;return n===i?(0|e.column)-(0|t.column):n-i}clone(){return new Me(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(e){return new Me(e.lineNumber,e.column)}static isIPosition(e){return e&&"number"==typeof e.lineNumber&&"number"==typeof e.column}}class Oe{constructor(e,t,n,i){e>n||e===n&&t>i?(this.startLineNumber=n,this.startColumn=i,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=i)}isEmpty(){return Oe.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(e){return Oe.containsPosition(this,e)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn)}containsRange(e){return Oe.containsRange(this,e)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)}strictContainsRange(e){return Oe.strictContainsRange(this,e)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)}plusRange(e){return Oe.plusRange(this,e)}static plusRange(e,t){let n,i,r,s;return t.startLineNumber<e.startLineNumber?(n=t.startLineNumber,i=t.startColumn):t.startLineNumber===e.startLineNumber?(n=t.startLineNumber,i=Math.min(t.startColumn,e.startColumn)):(n=e.startLineNumber,i=e.startColumn),t.endLineNumber>e.endLineNumber?(r=t.endLineNumber,s=t.endColumn):t.endLineNumber===e.endLineNumber?(r=t.endLineNumber,s=Math.max(t.endColumn,e.endColumn)):(r=e.endLineNumber,s=e.endColumn),new Oe(n,i,r,s)}intersectRanges(e){return Oe.intersectRanges(this,e)}static intersectRanges(e,t){let n=e.startLineNumber,i=e.startColumn,r=e.endLineNumber,s=e.endColumn,o=t.startLineNumber,a=t.startColumn,l=t.endLineNumber,u=t.endColumn;return n<o?(n=o,i=a):n===o&&(i=Math.max(i,a)),r>l?(r=l,s=u):r===l&&(s=Math.min(s,u)),n>r||n===r&&i>s?null:new Oe(n,i,r,s)}equalsRange(e){return Oe.equalsRange(this,e)}static equalsRange(e,t){return!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return Oe.getEndPosition(this)}static getEndPosition(e){return new Me(e.endLineNumber,e.endColumn)}getStartPosition(){return Oe.getStartPosition(this)}static getStartPosition(e){return new Me(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(e,t){return new Oe(this.startLineNumber,this.startColumn,e,t)}setStartPosition(e,t){return new Oe(e,t,this.endLineNumber,this.endColumn)}collapseToStart(){return Oe.collapseToStart(this)}static collapseToStart(e){return new Oe(e.startLineNumber,e.startColumn,e.startLineNumber,e.startColumn)}static fromPositions(e,t=e){return new Oe(e.lineNumber,e.column,t.lineNumber,t.column)}static lift(e){return e?new Oe(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):null}static isIRange(e){return e&&"number"==typeof e.startLineNumber&&"number"==typeof e.startColumn&&"number"==typeof e.endLineNumber&&"number"==typeof e.endColumn}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){const n=0|e.startLineNumber,i=0|t.startLineNumber;if(n===i){const n=0|e.startColumn,i=0|t.startColumn;if(n===i){const n=0|e.endLineNumber,i=0|t.endLineNumber;return n===i?(0|e.endColumn)-(0|t.endColumn):n-i}return n-i}return n-i}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}}function Te(e,t,n,i){return new te(e,t,n).ComputeDiff(i)}class Ke{constructor(e){const t=[],n=[];for(let i=0,r=e.length;i<r;i++)t[i]=xe(e[i],1),n[i]=ke(e[i],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const i=[],r=[],s=[];let o=0;for(let a=t;a<=n;a++){const t=this.lines[a],n=e?this._startColumns[a]:1,l=e?this._endColumns[a]:t.length+1;for(let e=n;e<l;e++)i[o]=t.charCodeAt(e-1),r[o]=a+1,s[o]=e,o++}return new Re(i,r,s)}}class Re{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}getElements(){return this._charCodes}getStartLineNumber(e){return this._lineNumbers[e]}getStartColumn(e){return this._columns[e]}getEndLineNumber(e){return this._lineNumbers[e]}getEndColumn(e){return this._columns[e]+1}}class Ve{constructor(e,t,n,i,r,s,o,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=i,this.modifiedStartLineNumber=r,this.modifiedStartColumn=s,this.modifiedEndLineNumber=o,this.modifiedEndColumn=a}static createFromDiffChange(e,t,n){let i,r,s,o,a,l,u,h;return 0===e.originalLength?(i=0,r=0,s=0,o=0):(i=t.getStartLineNumber(e.originalStart),r=t.getStartColumn(e.originalStart),s=t.getEndLineNumber(e.originalStart+e.originalLength-1),o=t.getEndColumn(e.originalStart+e.originalLength-1)),0===e.modifiedLength?(a=0,l=0,u=0,h=0):(a=n.getStartLineNumber(e.modifiedStart),l=n.getStartColumn(e.modifiedStart),u=n.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),h=n.getEndColumn(e.modifiedStart+e.modifiedLength-1)),new Ve(i,r,s,o,a,l,u,h)}}class Pe{constructor(e,t,n,i,r){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=i,this.charChanges=r}static createFromDiffResult(e,t,n,i,r,s,o){let a,l,u,h,c=void 0;if(0===t.originalLength?(a=n.getStartLineNumber(t.originalStart)-1,l=0):(a=n.getStartLineNumber(t.originalStart),l=n.getEndLineNumber(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(u=i.getStartLineNumber(t.modifiedStart)-1,h=0):(u=i.getStartLineNumber(t.modifiedStart),h=i.getEndLineNumber(t.modifiedStart+t.modifiedLength-1)),s&&t.originalLength>0&&t.originalLength<20&&t.modifiedLength>0&&t.modifiedLength<20&&r()){const s=n.createCharSequence(e,t.originalStart,t.originalStart+t.originalLength-1),a=i.createCharSequence(e,t.modifiedStart,t.modifiedStart+t.modifiedLength-1);let l=Te(s,a,r,!0).changes;o&&(l=function(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let i=1,r=e.length;i<r;i++){const r=e[i],s=r.originalStart-(n.originalStart+n.originalLength),o=r.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(s,o)<3?(n.originalLength=r.originalStart+r.originalLength-n.originalStart,n.modifiedLength=r.modifiedStart+r.modifiedLength-n.modifiedStart):(t.push(r),n=r)}return t}(l)),c=[];for(let e=0,t=l.length;e<t;e++)c.push(Ve.createFromDiffChange(l[e],s,a))}return new Pe(a,l,u,h,c)}}class Ie{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Ke(e),this.modified=new Ke(t),this.continueLineDiff=De(n.maxComputationTime),this.continueCharDiff=De(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};const e=Te(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const e=[];for(let n=0,i=t.length;n<i;n++)e.push(Pe.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}const i=[];let r=0,s=0;for(let o=-1,a=t.length;o<a;o++){const e=o+1<a?t[o+1]:null,n=e?e.originalStart:this.originalLines.length,l=e?e.modifiedStart:this.modifiedLines.length;for(;r<n&&s<l;){const e=this.originalLines[r],t=this.modifiedLines[s];if(e!==t){{let n=xe(e,1),o=xe(t,1);for(;n>1&&o>1;){if(e.charCodeAt(n-2)!==t.charCodeAt(o-2))break;n--,o--}(n>1||o>1)&&this._pushTrimWhitespaceCharChange(i,r+1,1,n,s+1,1,o)}{let n=ke(e,1),o=ke(t,1);const a=e.length+1,l=t.length+1;for(;n<a&&o<l;){if(e.charCodeAt(n-1)!==e.charCodeAt(o-1))break;n++,o++}(n<a||o<l)&&this._pushTrimWhitespaceCharChange(i,r+1,n,a,s+1,o,l)}}r++,s++}e&&(i.push(Pe.createFromDiffResult(this.shouldIgnoreTrimWhitespace,e,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),r+=e.originalLength,s+=e.modifiedLength)}return{quitEarly:n,changes:i}}_pushTrimWhitespaceCharChange(e,t,n,i,r,s,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,i,r,s,o))return;let a=void 0;this.shouldComputeCharChanges&&(a=[new Ve(t,n,t,i,r,s,r,o)]),e.push(new Pe(t,t,r,r,a))}_mergeTrimWhitespaceCharChange(e,t,n,i,r,s,o){const a=e.length;if(0===a)return!1;const l=e[a-1];return 0!==l.originalEndLineNumber&&0!==l.modifiedEndLineNumber&&l.originalEndLineNumber+1===t&&l.modifiedEndLineNumber+1===r&&(l.originalEndLineNumber=t,l.modifiedEndLineNumber=r,this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Ve(t,n,t,i,r,s,r,o)),!0)}}function xe(e,t){const n=function(e){for(let t=0,n=e.length;t<n;t++){const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1}(e);return-1===n?t:n+1}function ke(e,t){const n=function(e,t=e.length-1){for(let n=t;n>=0;n--){const t=e.charCodeAt(n);if(32!==t&&9!==t)return n}return-1}(e);return-1===n?t:n+2}function De(e){if(0===e)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}function Fe(e){return e<0?0:e>255?255:0|e}function Ue(e){return e<0?0:e>4294967295?4294967295:0|e}class Be{constructor(e,t){this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}}class qe{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=Ue(e);const n=this.values,i=this.prefixSum,r=t.length;return 0!==r&&(this.values=new Uint32Array(n.length+r),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+r),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}changeValue(e,t){return e=Ue(e),t=Ue(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=Ue(e),t=Ue(t);const n=this.values,i=this.prefixSum;if(e>=n.length)return!1;let r=n.length-e;return t>=r&&(t=r),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=Ue(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,i=0,r=0,s=0;for(;t<=n;)if(i=t+(n-t)/2|0,r=this.prefixSum[i],s=r-this.values[i],e<s)n=i-1;else{if(!(e>=r))break;t=i+1}return new Be(i,e-s)}}const He=function(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of"`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?")e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}(),We={maxLen:1e3,windowSize:15,timeBudget:150};function je(e,t,n,i){let r;for(;r=e.exec(t);){const t=r.index||0;if(t<=n&&e.lastIndex>=n)return r;if(i>0&&t>i)return null}return null}class $e{constructor(e){let t=Fe(e);this._defaultValue=t,this._asciiMap=$e._createAsciiMap(t),this._map=new Map}static _createAsciiMap(e){let t=new Uint8Array(256);for(let n=0;n<256;n++)t[n]=e;return t}set(e,t){let n=Fe(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}}class ze{constructor(e,t,n){const i=new Uint8Array(e*t);for(let r=0,s=e*t;r<s;r++)i[r]=n;this._data=i,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}}class Ge{constructor(e){let t=0,n=0;for(let r=0,s=e.length;r<s;r++){let[i,s,o]=e[r];s>t&&(t=s),i>n&&(n=i),o>n&&(n=o)}t++,n++;let i=new ze(n,t,0);for(let r=0,s=e.length;r<s;r++){let[t,n,s]=e[r];i.set(t,n,s)}this._states=i,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}let Ye=null,Qe=null;class Xe{static _createLink(e,t,n,i,r){let s=r-1;do{const n=t.charCodeAt(s);if(2!==e.get(n))break;s--}while(s>i);if(i>0){const e=t.charCodeAt(i-1),n=t.charCodeAt(s);(40===e&&41===n||91===e&&93===n||123===e&&125===n)&&s--}return{range:{startLineNumber:n,startColumn:i+1,endLineNumber:n,endColumn:s+2},url:t.substring(i,s+1)}}static computeLinks(e,t=function(){return null===Ye&&(Ye=new Ge([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Ye}()){const n=function(){if(null===Qe){Qe=new $e(0);const e=" \t<>'\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…";for(let n=0;n<e.length;n++)Qe.set(e.charCodeAt(n),1);const t=".,;";for(let n=0;n<t.length;n++)Qe.set(t.charCodeAt(n),2)}return Qe}();let i=[];for(let r=1,s=e.getLineCount();r<=s;r++){const s=e.getLineContent(r),o=s.length;let a=0,l=0,u=0,h=1,c=!1,d=!1,f=!1,m=!1;for(;a<o;){let e=!1;const o=s.charCodeAt(a);if(13===h){let t;switch(o){case 40:c=!0,t=0;break;case 41:t=c?0:1;break;case 91:f=!0,d=!0,t=0;break;case 93:f=!1,t=d?0:1;break;case 123:m=!0,t=0;break;case 125:t=m?0:1;break;case 39:t=34===u||96===u?0:1;break;case 34:t=39===u||96===u?0:1;break;case 96:t=39===u||34===u?0:1;break;case 42:t=42===u?1:0;break;case 124:t=124===u?1:0;break;case 32:t=f?0:1;break;default:t=n.get(o)}1===t&&(i.push(Xe._createLink(n,s,r,l,a)),e=!0)}else if(12===h){let t;91===o?(d=!0,t=0):t=n.get(o),1===t?e=!0:h=13}else h=t.nextState(h,o),0===h&&(e=!0);e&&(h=1,c=!1,d=!1,m=!1,l=a+1,u=o),a++}13===h&&i.push(Xe._createLink(n,s,r,l,o))}return i}}class Ze{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,t,n,i,r){if(e&&t){let n=this.doNavigateValueSet(t,r);if(n)return{range:e,value:n}}if(n&&i){let e=this.doNavigateValueSet(i,r);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){let n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}numberReplace(e,t){let n=Math.pow(10,e.length-(e.lastIndexOf(".")+1)),i=Number(e),r=parseFloat(e);return isNaN(i)||isNaN(r)||i!==r?null:0!==i||t?(i=Math.floor(i*n),i+=t?n:-n,String(i/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let i=null;for(let r=0,s=e.length;null===i&&r<s;r++)i=this.valueSetReplace(e[r],t,n);return i}valueSetReplace(e,t,n){let i=e.indexOf(t);return i>=0?(i+=n?1:-1,i<0?i=e.length-1:i%=e.length,e[i]):null}}Ze.INSTANCE=new Ze;const Je=Object.freeze((function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));var et,tt;(tt=et||(et={})).isCancellationToken=function(e){return e===tt.None||e===tt.Cancelled||e instanceof nt||!(!e||"object"!=typeof e)&&"boolean"==typeof e.isCancellationRequested&&"function"==typeof e.onCancellationRequested},tt.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:E.None}),tt.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Je});class nt{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Je:(this._emitter||(this._emitter=new w),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class it{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new nt),this._token}cancel(){this._token?this._token instanceof nt&&this._token.cancel():this._token=et.Cancelled}dispose(e=!1){e&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof nt&&this._token.dispose():this._token=et.None}}class rt{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const st=new rt,ot=new rt,at=new rt,lt=new Array(230),ut={},ht=[],ct=Object.create(null),dt=Object.create(null),ft=[],mt=[];for(let on=0;on<=193;on++)ft[on]=-1;for(let on=0;on<=126;on++)mt[on]=-1;var gt,_t,pt,Ct,bt,Lt,St,Nt,yt,Et,At,wt,vt,Mt,Ot,Tt,Kt,Rt,Vt,Pt,It,xt,kt,Dt,Ft,Ut,Bt,qt,Ht,Wt,jt,$t,zt,Gt,Yt,Qt;!function(){const e=[[0,1,0,"None",0,"unknown",0,"VK_UNKNOWN","",""],[0,1,1,"Hyper",0,"",0,"","",""],[0,1,2,"Super",0,"",0,"","",""],[0,1,3,"Fn",0,"",0,"","",""],[0,1,4,"FnLock",0,"",0,"","",""],[0,1,5,"Suspend",0,"",0,"","",""],[0,1,6,"Resume",0,"",0,"","",""],[0,1,7,"Turbo",0,"",0,"","",""],[0,1,8,"Sleep",0,"",0,"VK_SLEEP","",""],[0,1,9,"WakeUp",0,"",0,"","",""],[31,0,10,"KeyA",31,"A",65,"VK_A","",""],[32,0,11,"KeyB",32,"B",66,"VK_B","",""],[33,0,12,"KeyC",33,"C",67,"VK_C","",""],[34,0,13,"KeyD",34,"D",68,"VK_D","",""],[35,0,14,"KeyE",35,"E",69,"VK_E","",""],[36,0,15,"KeyF",36,"F",70,"VK_F","",""],[37,0,16,"KeyG",37,"G",71,"VK_G","",""],[38,0,17,"KeyH",38,"H",72,"VK_H","",""],[39,0,18,"KeyI",39,"I",73,"VK_I","",""],[40,0,19,"KeyJ",40,"J",74,"VK_J","",""],[41,0,20,"KeyK",41,"K",75,"VK_K","",""],[42,0,21,"KeyL",42,"L",76,"VK_L","",""],[43,0,22,"KeyM",43,"M",77,"VK_M","",""],[44,0,23,"KeyN",44,"N",78,"VK_N","",""],[45,0,24,"KeyO",45,"O",79,"VK_O","",""],[46,0,25,"KeyP",46,"P",80,"VK_P","",""],[47,0,26,"KeyQ",47,"Q",81,"VK_Q","",""],[48,0,27,"KeyR",48,"R",82,"VK_R","",""],[49,0,28,"KeyS",49,"S",83,"VK_S","",""],[50,0,29,"KeyT",50,"T",84,"VK_T","",""],[51,0,30,"KeyU",51,"U",85,"VK_U","",""],[52,0,31,"KeyV",52,"V",86,"VK_V","",""],[53,0,32,"KeyW",53,"W",87,"VK_W","",""],[54,0,33,"KeyX",54,"X",88,"VK_X","",""],[55,0,34,"KeyY",55,"Y",89,"VK_Y","",""],[56,0,35,"KeyZ",56,"Z",90,"VK_Z","",""],[22,0,36,"Digit1",22,"1",49,"VK_1","",""],[23,0,37,"Digit2",23,"2",50,"VK_2","",""],[24,0,38,"Digit3",24,"3",51,"VK_3","",""],[25,0,39,"Digit4",25,"4",52,"VK_4","",""],[26,0,40,"Digit5",26,"5",53,"VK_5","",""],[27,0,41,"Digit6",27,"6",54,"VK_6","",""],[28,0,42,"Digit7",28,"7",55,"VK_7","",""],[29,0,43,"Digit8",29,"8",56,"VK_8","",""],[30,0,44,"Digit9",30,"9",57,"VK_9","",""],[21,0,45,"Digit0",21,"0",48,"VK_0","",""],[3,1,46,"Enter",3,"Enter",13,"VK_RETURN","",""],[9,1,47,"Escape",9,"Escape",27,"VK_ESCAPE","",""],[1,1,48,"Backspace",1,"Backspace",8,"VK_BACK","",""],[2,1,49,"Tab",2,"Tab",9,"VK_TAB","",""],[10,1,50,"Space",10,"Space",32,"VK_SPACE","",""],[83,0,51,"Minus",83,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[81,0,52,"Equal",81,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[87,0,53,"BracketLeft",87,"[",219,"VK_OEM_4","[","OEM_4"],[89,0,54,"BracketRight",89,"]",221,"VK_OEM_6","]","OEM_6"],[88,0,55,"Backslash",88,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,0,56,"IntlHash",0,"",0,"","",""],[80,0,57,"Semicolon",80,";",186,"VK_OEM_1",";","OEM_1"],[90,0,58,"Quote",90,"'",222,"VK_OEM_7","'","OEM_7"],[86,0,59,"Backquote",86,"`",192,"VK_OEM_3","`","OEM_3"],[82,0,60,"Comma",82,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[84,0,61,"Period",84,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[85,0,62,"Slash",85,"/",191,"VK_OEM_2","/","OEM_2"],[8,1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL","",""],[59,1,64,"F1",59,"F1",112,"VK_F1","",""],[60,1,65,"F2",60,"F2",113,"VK_F2","",""],[61,1,66,"F3",61,"F3",114,"VK_F3","",""],[62,1,67,"F4",62,"F4",115,"VK_F4","",""],[63,1,68,"F5",63,"F5",116,"VK_F5","",""],[64,1,69,"F6",64,"F6",117,"VK_F6","",""],[65,1,70,"F7",65,"F7",118,"VK_F7","",""],[66,1,71,"F8",66,"F8",119,"VK_F8","",""],[67,1,72,"F9",67,"F9",120,"VK_F9","",""],[68,1,73,"F10",68,"F10",121,"VK_F10","",""],[69,1,74,"F11",69,"F11",122,"VK_F11","",""],[70,1,75,"F12",70,"F12",123,"VK_F12","",""],[0,1,76,"PrintScreen",0,"",0,"","",""],[79,1,77,"ScrollLock",79,"ScrollLock",145,"VK_SCROLL","",""],[7,1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE","",""],[19,1,79,"Insert",19,"Insert",45,"VK_INSERT","",""],[14,1,80,"Home",14,"Home",36,"VK_HOME","",""],[11,1,81,"PageUp",11,"PageUp",33,"VK_PRIOR","",""],[20,1,82,"Delete",20,"Delete",46,"VK_DELETE","",""],[13,1,83,"End",13,"End",35,"VK_END","",""],[12,1,84,"PageDown",12,"PageDown",34,"VK_NEXT","",""],[17,1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",""],[15,1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",""],[18,1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",""],[16,1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",""],[78,1,89,"NumLock",78,"NumLock",144,"VK_NUMLOCK","",""],[108,1,90,"NumpadDivide",108,"NumPad_Divide",111,"VK_DIVIDE","",""],[103,1,91,"NumpadMultiply",103,"NumPad_Multiply",106,"VK_MULTIPLY","",""],[106,1,92,"NumpadSubtract",106,"NumPad_Subtract",109,"VK_SUBTRACT","",""],[104,1,93,"NumpadAdd",104,"NumPad_Add",107,"VK_ADD","",""],[3,1,94,"NumpadEnter",3,"",0,"","",""],[94,1,95,"Numpad1",94,"NumPad1",97,"VK_NUMPAD1","",""],[95,1,96,"Numpad2",95,"NumPad2",98,"VK_NUMPAD2","",""],[96,1,97,"Numpad3",96,"NumPad3",99,"VK_NUMPAD3","",""],[97,1,98,"Numpad4",97,"NumPad4",100,"VK_NUMPAD4","",""],[98,1,99,"Numpad5",98,"NumPad5",101,"VK_NUMPAD5","",""],[99,1,100,"Numpad6",99,"NumPad6",102,"VK_NUMPAD6","",""],[100,1,101,"Numpad7",100,"NumPad7",103,"VK_NUMPAD7","",""],[101,1,102,"Numpad8",101,"NumPad8",104,"VK_NUMPAD8","",""],[102,1,103,"Numpad9",102,"NumPad9",105,"VK_NUMPAD9","",""],[93,1,104,"Numpad0",93,"NumPad0",96,"VK_NUMPAD0","",""],[107,1,105,"NumpadDecimal",107,"NumPad_Decimal",110,"VK_DECIMAL","",""],[92,0,106,"IntlBackslash",92,"OEM_102",226,"VK_OEM_102","",""],[58,1,107,"ContextMenu",58,"ContextMenu",93,"","",""],[0,1,108,"Power",0,"",0,"","",""],[0,1,109,"NumpadEqual",0,"",0,"","",""],[71,1,110,"F13",71,"F13",124,"VK_F13","",""],[72,1,111,"F14",72,"F14",125,"VK_F14","",""],[73,1,112,"F15",73,"F15",126,"VK_F15","",""],[74,1,113,"F16",74,"F16",127,"VK_F16","",""],[75,1,114,"F17",75,"F17",128,"VK_F17","",""],[76,1,115,"F18",76,"F18",129,"VK_F18","",""],[77,1,116,"F19",77,"F19",130,"VK_F19","",""],[0,1,117,"F20",0,"",0,"VK_F20","",""],[0,1,118,"F21",0,"",0,"VK_F21","",""],[0,1,119,"F22",0,"",0,"VK_F22","",""],[0,1,120,"F23",0,"",0,"VK_F23","",""],[0,1,121,"F24",0,"",0,"VK_F24","",""],[0,1,122,"Open",0,"",0,"","",""],[0,1,123,"Help",0,"",0,"","",""],[0,1,124,"Select",0,"",0,"","",""],[0,1,125,"Again",0,"",0,"","",""],[0,1,126,"Undo",0,"",0,"","",""],[0,1,127,"Cut",0,"",0,"","",""],[0,1,128,"Copy",0,"",0,"","",""],[0,1,129,"Paste",0,"",0,"","",""],[0,1,130,"Find",0,"",0,"","",""],[0,1,131,"AudioVolumeMute",112,"AudioVolumeMute",173,"VK_VOLUME_MUTE","",""],[0,1,132,"AudioVolumeUp",113,"AudioVolumeUp",175,"VK_VOLUME_UP","",""],[0,1,133,"AudioVolumeDown",114,"AudioVolumeDown",174,"VK_VOLUME_DOWN","",""],[105,1,134,"NumpadComma",105,"NumPad_Separator",108,"VK_SEPARATOR","",""],[110,0,135,"IntlRo",110,"ABNT_C1",193,"VK_ABNT_C1","",""],[0,1,136,"KanaMode",0,"",0,"","",""],[0,0,137,"IntlYen",0,"",0,"","",""],[0,1,138,"Convert",0,"",0,"","",""],[0,1,139,"NonConvert",0,"",0,"","",""],[0,1,140,"Lang1",0,"",0,"","",""],[0,1,141,"Lang2",0,"",0,"","",""],[0,1,142,"Lang3",0,"",0,"","",""],[0,1,143,"Lang4",0,"",0,"","",""],[0,1,144,"Lang5",0,"",0,"","",""],[0,1,145,"Abort",0,"",0,"","",""],[0,1,146,"Props",0,"",0,"","",""],[0,1,147,"NumpadParenLeft",0,"",0,"","",""],[0,1,148,"NumpadParenRight",0,"",0,"","",""],[0,1,149,"NumpadBackspace",0,"",0,"","",""],[0,1,150,"NumpadMemoryStore",0,"",0,"","",""],[0,1,151,"NumpadMemoryRecall",0,"",0,"","",""],[0,1,152,"NumpadMemoryClear",0,"",0,"","",""],[0,1,153,"NumpadMemoryAdd",0,"",0,"","",""],[0,1,154,"NumpadMemorySubtract",0,"",0,"","",""],[0,1,155,"NumpadClear",0,"",0,"","",""],[0,1,156,"NumpadClearEntry",0,"",0,"","",""],[5,1,0,"",5,"Ctrl",17,"VK_CONTROL","",""],[4,1,0,"",4,"Shift",16,"VK_SHIFT","",""],[6,1,0,"",6,"Alt",18,"VK_MENU","",""],[57,1,0,"",57,"Meta",0,"VK_COMMAND","",""],[5,1,157,"ControlLeft",5,"",0,"VK_LCONTROL","",""],[4,1,158,"ShiftLeft",4,"",0,"VK_LSHIFT","",""],[6,1,159,"AltLeft",6,"",0,"VK_LMENU","",""],[57,1,160,"MetaLeft",57,"",0,"VK_LWIN","",""],[5,1,161,"ControlRight",5,"",0,"VK_RCONTROL","",""],[4,1,162,"ShiftRight",4,"",0,"VK_RSHIFT","",""],[6,1,163,"AltRight",6,"",0,"VK_RMENU","",""],[57,1,164,"MetaRight",57,"",0,"VK_RWIN","",""],[0,1,165,"BrightnessUp",0,"",0,"","",""],[0,1,166,"BrightnessDown",0,"",0,"","",""],[0,1,167,"MediaPlay",0,"",0,"","",""],[0,1,168,"MediaRecord",0,"",0,"","",""],[0,1,169,"MediaFastForward",0,"",0,"","",""],[0,1,170,"MediaRewind",0,"",0,"","",""],[114,1,171,"MediaTrackNext",119,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK","",""],[115,1,172,"MediaTrackPrevious",120,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK","",""],[116,1,173,"MediaStop",121,"MediaStop",178,"VK_MEDIA_STOP","",""],[0,1,174,"Eject",0,"",0,"","",""],[117,1,175,"MediaPlayPause",122,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE","",""],[0,1,176,"MediaSelect",123,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT","",""],[0,1,177,"LaunchMail",124,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL","",""],[0,1,178,"LaunchApp2",125,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2","",""],[0,1,179,"LaunchApp1",0,"",0,"VK_MEDIA_LAUNCH_APP1","",""],[0,1,180,"SelectTask",0,"",0,"","",""],[0,1,181,"LaunchScreenSaver",0,"",0,"","",""],[0,1,182,"BrowserSearch",115,"BrowserSearch",170,"VK_BROWSER_SEARCH","",""],[0,1,183,"BrowserHome",116,"BrowserHome",172,"VK_BROWSER_HOME","",""],[112,1,184,"BrowserBack",117,"BrowserBack",166,"VK_BROWSER_BACK","",""],[113,1,185,"BrowserForward",118,"BrowserForward",167,"VK_BROWSER_FORWARD","",""],[0,1,186,"BrowserStop",0,"",0,"VK_BROWSER_STOP","",""],[0,1,187,"BrowserRefresh",0,"",0,"VK_BROWSER_REFRESH","",""],[0,1,188,"BrowserFavorites",0,"",0,"VK_BROWSER_FAVORITES","",""],[0,1,189,"ZoomToggle",0,"",0,"","",""],[0,1,190,"MailReply",0,"",0,"","",""],[0,1,191,"MailForward",0,"",0,"","",""],[0,1,192,"MailSend",0,"",0,"","",""],[109,1,0,"",109,"KeyInComposition",229,"","",""],[111,1,0,"",111,"ABNT_C2",194,"VK_ABNT_C2","",""],[91,1,0,"",91,"OEM_8",223,"VK_OEM_8","",""],[0,1,0,"",0,"",0,"VK_CLEAR","",""],[0,1,0,"",0,"",0,"VK_KANA","",""],[0,1,0,"",0,"",0,"VK_HANGUL","",""],[0,1,0,"",0,"",0,"VK_JUNJA","",""],[0,1,0,"",0,"",0,"VK_FINAL","",""],[0,1,0,"",0,"",0,"VK_HANJA","",""],[0,1,0,"",0,"",0,"VK_KANJI","",""],[0,1,0,"",0,"",0,"VK_CONVERT","",""],[0,1,0,"",0,"",0,"VK_NONCONVERT","",""],[0,1,0,"",0,"",0,"VK_ACCEPT","",""],[0,1,0,"",0,"",0,"VK_MODECHANGE","",""],[0,1,0,"",0,"",0,"VK_SELECT","",""],[0,1,0,"",0,"",0,"VK_PRINT","",""],[0,1,0,"",0,"",0,"VK_EXECUTE","",""],[0,1,0,"",0,"",0,"VK_SNAPSHOT","",""],[0,1,0,"",0,"",0,"VK_HELP","",""],[0,1,0,"",0,"",0,"VK_APPS","",""],[0,1,0,"",0,"",0,"VK_PROCESSKEY","",""],[0,1,0,"",0,"",0,"VK_PACKET","",""],[0,1,0,"",0,"",0,"VK_DBE_SBCSCHAR","",""],[0,1,0,"",0,"",0,"VK_DBE_DBCSCHAR","",""],[0,1,0,"",0,"",0,"VK_ATTN","",""],[0,1,0,"",0,"",0,"VK_CRSEL","",""],[0,1,0,"",0,"",0,"VK_EXSEL","",""],[0,1,0,"",0,"",0,"VK_EREOF","",""],[0,1,0,"",0,"",0,"VK_PLAY","",""],[0,1,0,"",0,"",0,"VK_ZOOM","",""],[0,1,0,"",0,"",0,"VK_NONAME","",""],[0,1,0,"",0,"",0,"VK_PA1","",""],[0,1,0,"",0,"",0,"VK_OEM_CLEAR","",""]];let t=[],n=[];for(const i of e){const[e,r,s,o,a,l,u,h,c,d]=i;if(n[s]||(n[s]=!0,ht[s]=o,ct[o]=s,dt[o.toLowerCase()]=s,r&&(ft[s]=a,0!==a&&3!==a&&5!==a&&4!==a&&6!==a&&57!==a&&(mt[a]=s))),!t[a]){if(t[a]=!0,!l)throw new Error(`String representation missing for key code ${a} around scan code ${o}`);st.define(a,l),ot.define(a,c||l),at.define(a,d||c||l)}u&&(lt[u]=a),h&&(ut[h]=a)}mt[3]=46}(),function(e){e.toString=function(e){return st.keyCodeToStr(e)},e.fromString=function(e){return st.strToKeyCode(e)},e.toUserSettingsUS=function(e){return ot.keyCodeToStr(e)},e.toUserSettingsGeneral=function(e){return at.keyCodeToStr(e)},e.fromUserSettings=function(e){return ot.strToKeyCode(e)||at.strToKeyCode(e)},e.toElectronAccelerator=function(e){if(e>=93&&e<=108)return null;switch(e){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return st.keyCodeToStr(e)}}(gt||(gt={}));class Xt extends Oe{constructor(e,t,n,i){super(e,t,n,i),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=i}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return Xt.selectionsEqual(this,e)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,t){return 0===this.getDirection()?new Xt(this.startLineNumber,this.startColumn,e,t):new Xt(e,t,this.startLineNumber,this.startColumn)}getPosition(){return new Me(this.positionLineNumber,this.positionColumn)}setStartPosition(e,t){return 0===this.getDirection()?new Xt(e,t,this.endLineNumber,this.endColumn):new Xt(this.endLineNumber,this.endColumn,e,t)}static fromPositions(e,t=e){return new Xt(e.lineNumber,e.column,t.lineNumber,t.column)}static liftSelection(e){return new Xt(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,i=e.length;n<i;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&"number"==typeof e.selectionStartLineNumber&&"number"==typeof e.selectionStartColumn&&"number"==typeof e.positionLineNumber&&"number"==typeof e.positionColumn}static createWithDirection(e,t,n,i,r){return 0===r?new Xt(e,t,n,i):new Xt(n,i,e,t)}}class Zt{constructor(e,t,n){this._tokenBrand=void 0,this.offset=0|e,this.type=t,this.language=n}toString(){return"("+this.offset+", "+this.type+")"}}!function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"}(_t||(_t={})),function(e){e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"}(pt||(pt={})),function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"}(Ct||(Ct={})),function(e){e[e.Deprecated=1]="Deprecated"}(bt||(bt={})),function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"}(Lt||(Lt={})),function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"}(St||(St={})),function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"}(Nt||(Nt={})),function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(yt||(yt={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(Et||(Et={})),function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"}(At||(At={})),function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.guides=13]="guides",e[e.codeLens=14]="codeLens",e[e.codeLensFontFamily=15]="codeLensFontFamily",e[e.codeLensFontSize=16]="codeLensFontSize",e[e.colorDecorators=17]="colorDecorators",e[e.columnSelection=18]="columnSelection",e[e.comments=19]="comments",e[e.contextmenu=20]="contextmenu",e[e.copyWithSyntaxHighlighting=21]="copyWithSyntaxHighlighting",e[e.cursorBlinking=22]="cursorBlinking",e[e.cursorSmoothCaretAnimation=23]="cursorSmoothCaretAnimation",e[e.cursorStyle=24]="cursorStyle",e[e.cursorSurroundingLines=25]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=26]="cursorSurroundingLinesStyle",e[e.cursorWidth=27]="cursorWidth",e[e.disableLayerHinting=28]="disableLayerHinting",e[e.disableMonospaceOptimizations=29]="disableMonospaceOptimizations",e[e.domReadOnly=30]="domReadOnly",e[e.dragAndDrop=31]="dragAndDrop",e[e.emptySelectionClipboard=32]="emptySelectionClipboard",e[e.extraEditorClassName=33]="extraEditorClassName",e[e.fastScrollSensitivity=34]="fastScrollSensitivity",e[e.find=35]="find",e[e.fixedOverflowWidgets=36]="fixedOverflowWidgets",e[e.folding=37]="folding",e[e.foldingStrategy=38]="foldingStrategy",e[e.foldingHighlight=39]="foldingHighlight",e[e.foldingImportsByDefault=40]="foldingImportsByDefault",e[e.unfoldOnClickAfterEndOfLine=41]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=42]="fontFamily",e[e.fontInfo=43]="fontInfo",e[e.fontLigatures=44]="fontLigatures",e[e.fontSize=45]="fontSize",e[e.fontWeight=46]="fontWeight",e[e.formatOnPaste=47]="formatOnPaste",e[e.formatOnType=48]="formatOnType",e[e.glyphMargin=49]="glyphMargin",e[e.gotoLocation=50]="gotoLocation",e[e.hideCursorInOverviewRuler=51]="hideCursorInOverviewRuler",e[e.hover=52]="hover",e[e.inDiffEditor=53]="inDiffEditor",e[e.inlineSuggest=54]="inlineSuggest",e[e.letterSpacing=55]="letterSpacing",e[e.lightbulb=56]="lightbulb",e[e.lineDecorationsWidth=57]="lineDecorationsWidth",e[e.lineHeight=58]="lineHeight",e[e.lineNumbers=59]="lineNumbers",e[e.lineNumbersMinChars=60]="lineNumbersMinChars",e[e.linkedEditing=61]="linkedEditing",e[e.links=62]="links",e[e.matchBrackets=63]="matchBrackets",e[e.minimap=64]="minimap",e[e.mouseStyle=65]="mouseStyle",e[e.mouseWheelScrollSensitivity=66]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=67]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=68]="multiCursorMergeOverlapping",e[e.multiCursorModifier=69]="multiCursorModifier",e[e.multiCursorPaste=70]="multiCursorPaste",e[e.occurrencesHighlight=71]="occurrencesHighlight",e[e.overviewRulerBorder=72]="overviewRulerBorder",e[e.overviewRulerLanes=73]="overviewRulerLanes",e[e.padding=74]="padding",e[e.parameterHints=75]="parameterHints",e[e.peekWidgetDefaultFocus=76]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=77]="definitionLinkOpensInPeek",e[e.quickSuggestions=78]="quickSuggestions",e[e.quickSuggestionsDelay=79]="quickSuggestionsDelay",e[e.readOnly=80]="readOnly",e[e.renameOnType=81]="renameOnType",e[e.renderControlCharacters=82]="renderControlCharacters",e[e.renderFinalNewline=83]="renderFinalNewline",e[e.renderLineHighlight=84]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=85]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=86]="renderValidationDecorations",e[e.renderWhitespace=87]="renderWhitespace",e[e.revealHorizontalRightPadding=88]="revealHorizontalRightPadding",e[e.roundedSelection=89]="roundedSelection",e[e.rulers=90]="rulers",e[e.scrollbar=91]="scrollbar",e[e.scrollBeyondLastColumn=92]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=93]="scrollBeyondLastLine",e[e.scrollPredominantAxis=94]="scrollPredominantAxis",e[e.selectionClipboard=95]="selectionClipboard",e[e.selectionHighlight=96]="selectionHighlight",e[e.selectOnLineNumbers=97]="selectOnLineNumbers",e[e.showFoldingControls=98]="showFoldingControls",e[e.showUnused=99]="showUnused",e[e.snippetSuggestions=100]="snippetSuggestions",e[e.smartSelect=101]="smartSelect",e[e.smoothScrolling=102]="smoothScrolling",e[e.stickyTabStops=103]="stickyTabStops",e[e.stopRenderingLineAfter=104]="stopRenderingLineAfter",e[e.suggest=105]="suggest",e[e.suggestFontSize=106]="suggestFontSize",e[e.suggestLineHeight=107]="suggestLineHeight",e[e.suggestOnTriggerCharacters=108]="suggestOnTriggerCharacters",e[e.suggestSelection=109]="suggestSelection",e[e.tabCompletion=110]="tabCompletion",e[e.tabIndex=111]="tabIndex",e[e.unusualLineTerminators=112]="unusualLineTerminators",e[e.useShadowDOM=113]="useShadowDOM",e[e.useTabStops=114]="useTabStops",e[e.wordSeparators=115]="wordSeparators",e[e.wordWrap=116]="wordWrap",e[e.wordWrapBreakAfterCharacters=117]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=118]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=119]="wordWrapColumn",e[e.wordWrapOverride1=120]="wordWrapOverride1",e[e.wordWrapOverride2=121]="wordWrapOverride2",e[e.wrappingIndent=122]="wrappingIndent",e[e.wrappingStrategy=123]="wrappingStrategy",e[e.showDeprecated=124]="showDeprecated",e[e.inlayHints=125]="inlayHints",e[e.editorClassName=126]="editorClassName",e[e.pixelRatio=127]="pixelRatio",e[e.tabFocusMode=128]="tabFocusMode",e[e.layoutInfo=129]="layoutInfo",e[e.wrappingInfo=130]="wrappingInfo"}(wt||(wt={})),function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(vt||(vt={})),function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"}(Mt||(Mt={})),function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"}(Ot||(Ot={})),function(e){e[e.Other=0]="Other",e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(Tt||(Tt={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(Kt||(Kt={})),function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.Semicolon=80]="Semicolon",e[e.Equal=81]="Equal",e[e.Comma=82]="Comma",e[e.Minus=83]="Minus",e[e.Period=84]="Period",e[e.Slash=85]="Slash",e[e.Backquote=86]="Backquote",e[e.BracketLeft=87]="BracketLeft",e[e.Backslash=88]="Backslash",e[e.BracketRight=89]="BracketRight",e[e.Quote=90]="Quote",e[e.OEM_8=91]="OEM_8",e[e.IntlBackslash=92]="IntlBackslash",e[e.Numpad0=93]="Numpad0",e[e.Numpad1=94]="Numpad1",e[e.Numpad2=95]="Numpad2",e[e.Numpad3=96]="Numpad3",e[e.Numpad4=97]="Numpad4",e[e.Numpad5=98]="Numpad5",e[e.Numpad6=99]="Numpad6",e[e.Numpad7=100]="Numpad7",e[e.Numpad8=101]="Numpad8",e[e.Numpad9=102]="Numpad9",e[e.NumpadMultiply=103]="NumpadMultiply",e[e.NumpadAdd=104]="NumpadAdd",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=106]="NumpadSubtract",e[e.NumpadDecimal=107]="NumpadDecimal",e[e.NumpadDivide=108]="NumpadDivide",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.AudioVolumeMute=112]="AudioVolumeMute",e[e.AudioVolumeUp=113]="AudioVolumeUp",e[e.AudioVolumeDown=114]="AudioVolumeDown",e[e.BrowserSearch=115]="BrowserSearch",e[e.BrowserHome=116]="BrowserHome",e[e.BrowserBack=117]="BrowserBack",e[e.BrowserForward=118]="BrowserForward",e[e.MediaTrackNext=119]="MediaTrackNext",e[e.MediaTrackPrevious=120]="MediaTrackPrevious",e[e.MediaStop=121]="MediaStop",e[e.MediaPlayPause=122]="MediaPlayPause",e[e.LaunchMediaPlayer=123]="LaunchMediaPlayer",e[e.LaunchMail=124]="LaunchMail",e[e.LaunchApp2=125]="LaunchApp2",e[e.MAX_VALUE=126]="MAX_VALUE"}(Rt||(Rt={})),function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"}(Vt||(Vt={})),function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"}(Pt||(Pt={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(It||(It={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"}(xt||(xt={})),function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"}(kt||(kt={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(Dt||(Dt={})),function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"}(Ft||(Ft={})),function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"}(Ut||(Ut={})),function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"}(Bt||(Bt={})),function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"}(qt||(qt={})),function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"}(Ht||(Ht={})),function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(Wt||(Wt={})),function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"}(jt||(jt={})),function(e){e[e.Deprecated=1]="Deprecated"}($t||($t={})),function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"}(zt||(zt={})),function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"}(Gt||(Gt={})),function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"}(Yt||(Yt={})),function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"}(Qt||(Qt={}));class Jt{static chord(e,t){return function(e,t){return(e|(65535&t)<<16>>>0)>>>0}(e,t)}}Jt.CtrlCmd=2048,Jt.Shift=1024,Jt.Alt=512,Jt.WinCtrl=256;var en=function(e,t,n,i){return new(n||(n=Promise))((function(r,s){function o(e){try{l(i.next(e))}catch(e){s(e)}}function a(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,a)}l((i=i.apply(e,t||[])).next())}))};class tn extends class{constructor(e,t,n,i){this._uri=e,this._lines=t,this._eol=n,this._versionId=i,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const t=e.changes;for(const n of t)this._acceptDeleteRange(n.range),this._acceptInsertText(new Me(n.range.startLineNumber,n.range.startColumn),n.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let i=0;i<t;i++)n[i]=this._lines[i].length+e;this._lineStarts=new qe(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.changeValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}_acceptInsertText(e,t){if(0===t.length)return;let n=t.split(/\r\n|\r|\n/);if(1===n.length)return void this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);let i=new Uint32Array(n.length-1);for(let r=1;r<n.length;r++)this._lines.splice(e.lineNumber+r-1,0,n[r]),i[r-1]=n[r].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,i)}}{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){let n=function e(t,n,i,r,s=We){if(i.length>s.maxLen){let o=t-s.maxLen/2;return o<0?o=0:r+=o,e(t,n,i=i.substring(o,t+s.maxLen/2),r,s)}const o=Date.now(),a=t-1-r;let l=-1,u=null;for(let h=1;!(Date.now()-o>=s.timeBudget);h++){const e=a-s.windowSize*h;n.lastIndex=Math.max(0,e);const t=je(n,i,a,l);if(!t&&u)break;if(u=t,e<=0)break;l=e}if(u){let e={word:u[0],startColumn:r+1+u.index,endColumn:r+1+u.index+u[0].length};return n.lastIndex=0,e}return null}(e.column,function(e){let t=He;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}(t),this._lines[e.lineNumber-1],0);return n?new Oe(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){const t=this._lines,n=this._wordenize.bind(this);let i=0,r="",s=0,o=[];return{*[Symbol.iterator](){for(;;)if(s<o.length){const e=r.substring(o[s].start,o[s].end);s+=1,yield e}else{if(!(i<t.length))break;r=t[i],o=n(r,e),s=0,i+=1}}}}getLineWords(e,t){let n=this._lines[e-1],i=this._wordenize(n,t),r=[];for(const s of i)r.push({word:n.substring(s.start,s.end),startColumn:s.start+1,endColumn:s.end+1});return r}_wordenize(e,t){const n=[];let i;for(t.lastIndex=0;(i=t.exec(e))&&0!==i[0].length;)n.push({start:i.index,end:i.index+i[0].length});return n}getValueInRange(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);let t=this._eol,n=e.startLineNumber-1,i=e.endLineNumber-1,r=[];r.push(this._lines[n].substring(e.startColumn-1));for(let s=n+1;s<i;s++)r.push(this._lines[s]);return r.push(this._lines[i].substring(0,e.endColumn-1)),r.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();let t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){const t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!Me.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,i=!1;if(t<1)t=1,n=1,i=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,i=!0;else{let e=this._lines[t-1].length+1;n<1?(n=1,i=!0):n>e&&(n=e,i=!0)}return i?{lineNumber:t,column:n}:e}}class nn{constructor(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(e){return this._models[e]}_getModels(){let e=[];return Object.keys(this._models).forEach(t=>e.push(this._models[t])),e}acceptNewModel(e){this._models[e.url]=new tn(Ce.parse(e.url),e.lines,e.EOL,e.versionId)}acceptModelChanged(e,t){this._models[e]&&this._models[e].onEvents(t)}acceptRemovedModel(e){this._models[e]&&delete this._models[e]}computeDiff(e,t,n,i){return en(this,void 0,void 0,(function*(){const r=this._getModel(e),s=this._getModel(t);if(!r||!s)return null;const o=r.getLinesContent(),a=s.getLinesContent(),l=new Ie(o,a,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:n,shouldMakePrettyDiff:!0,maxComputationTime:i}).computeDiff(),u=!(l.changes.length>0)&&this._modelsAreIdentical(r,s);return{quitEarly:l.quitEarly,identical:u,changes:l.changes}}))}_modelsAreIdentical(e,t){const n=e.getLineCount();if(n!==t.getLineCount())return!1;for(let i=1;i<=n;i++)if(e.getLineContent(i)!==t.getLineContent(i))return!1;return!0}computeMoreMinimalEdits(e,t){return en(this,void 0,void 0,(function*(){const n=this._getModel(e);if(!n)return t;const i=[];let r=void 0;t=t.slice(0).sort((e,t)=>e.range&&t.range?Oe.compareRangesUsingStarts(e.range,t.range):(e.range?0:1)-(t.range?0:1));for(let{range:e,text:s,eol:o}of t){if("number"==typeof o&&(r=o),Oe.isEmpty(e)&&!s)continue;const t=n.getValueInRange(e);if(s=s.replace(/\r\n|\n|\r/g,n.eol),t===s)continue;if(Math.max(s.length,t.length)>nn._diffLimit){i.push({range:e,text:s});continue}const a=X(t,s,!1),l=n.offsetAt(Oe.lift(e).getStartPosition());for(const e of a){const t=n.positionAt(l+e.originalStart),r=n.positionAt(l+e.originalStart+e.originalLength),o={text:s.substr(e.modifiedStart,e.modifiedLength),range:{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:r.lineNumber,endColumn:r.column}};n.getValueInRange(o.range)!==o.text&&i.push(o)}}return"number"==typeof r&&i.push({eol:r,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}))}computeLinks(e){return en(this,void 0,void 0,(function*(){let t=this._getModel(e);return t?function(e){return e&&"function"==typeof e.getLineCount&&"function"==typeof e.getLineContent?Xe.computeLinks(e):[]}(t):null}))}textualSuggest(e,t,n,i){return en(this,void 0,void 0,(function*(){const r=new y(!0),s=new RegExp(n,i),o=new Set;e:for(let n of e){const e=this._getModel(n);if(e)for(let n of e.words(s))if(n!==t&&isNaN(Number(n))&&(o.add(n),o.size>nn._suggestionsLimit))break e}return{words:Array.from(o),duration:r.elapsed()}}))}computeWordRanges(e,t,n,i){return en(this,void 0,void 0,(function*(){let r=this._getModel(e);if(!r)return Object.create(null);const s=new RegExp(n,i),o=Object.create(null);for(let e=t.startLineNumber;e<t.endLineNumber;e++){let t=r.getLineWords(e,s);for(const n of t){if(!isNaN(Number(n.word)))continue;let t=o[n.word];t||(t=[],o[n.word]=t),t.push({startLineNumber:e,startColumn:n.startColumn,endLineNumber:e,endColumn:n.endColumn})}}return o}))}navigateValueSet(e,t,n,i,r){return en(this,void 0,void 0,(function*(){let s=this._getModel(e);if(!s)return null;let o=new RegExp(i,r);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});let a=s.getValueInRange(t),l=s.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!l)return null;let u=s.getValueInRange(l);return Ze.INSTANCE.navigateValueSet(t,a,l,u,n)}))}loadForeignModule(e,t,n){let i={host:function(e,t){const n=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)};let i={};for(const r of e)i[r]=n(r);return i}(n,(e,t)=>this._host.fhr(e,t)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(i,t),Promise.resolve(v(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(e,t){if(!this._foreignModule||"function"!=typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(e){return Promise.reject(e)}}}nn._diffLimit=1e5,nn._suggestionsLimit=1e4,"function"==typeof importScripts&&(S.a.monaco={editor:void 0,languages:void 0,CancellationTokenSource:it,Emitter:w,KeyCode:Rt,KeyMod:Jt,Position:Me,Range:Oe,Selection:Xt,SelectionDirection:Ht,MarkerSeverity:Vt,MarkerTag:Pt,Uri:Ce,Token:Zt});let rn=!1;function sn(e){if(rn)return;rn=!0;const t=new q(e=>{self.postMessage(e)},t=>new nn(t,e));self.onmessage=e=>{t.onmessage(e.data)}}self.onmessage=e=>{rn||sn(null)}},f28c:function(e,t){var n,i,r=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:s}catch(e){n=s}try{i="function"==typeof clearTimeout?clearTimeout:o}catch(e){i=o}}();var l,u=[],h=!1,c=-1;function d(){h&&l&&(h=!1,l.length?u=l.concat(u):c=-1,u.length&&f())}function f(){if(!h){var e=a(d);h=!0;for(var t=u.length;t;){for(l=u,u=[];++c<t;)l&&l[c].run();c=-1,t=u.length}l=null,h=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===o||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function m(e,t){this.fun=e,this.array=t}function g(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new m(e,t)),1!==u.length||h||a(f)},m.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=g,r.addListener=g,r.once=g,r.off=g,r.removeListener=g,r.removeAllListeners=g,r.emit=g,r.prependListener=g,r.prependOnceListener=g,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}}});