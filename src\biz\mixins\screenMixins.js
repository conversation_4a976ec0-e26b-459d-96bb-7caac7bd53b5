import { Revoke } from "@/utils/revoke"
import { isObjectFn, isArrayFn } from "@/utils/index"
import { getToken } from '@base/utils/base/token-util'
import { getToolByCode, getDefaultScreenContainer } from "../views/edc/big-screen-designer/designer/tools/index"
import UserConstant from '@base/constant/user-constant'
import { getCurrentLocale } from '@base/utils/i18n/i18n-util'
const mixin = {
  data() {
    return {
      reportId: this.$route.query.id,
      uploadUrl: this.$baseUrl + "/edc/report/import/", //  this.$route.query.id
      revoke: null, // 处理历史记录
      rightClickIndex: -1
    }
  },
  computed: {
    step() {
      return Number(100 / (this.bigscreenScaleInWorkbench * 100))
    },
    headers() {
      return {
        'access-token': getToken() || ''
      }
    },
    // 初始的缩放百分比 和 下标
    defaultSize() {
      const obj = {
        index: -1,
        size: "50"
      }
      this.sizeRange.some((item, index) => {
        if (item <= 100 * this.bigscreenScaleInWorkbench) {
          obj.index = index
          obj.size = 100 * this.bigscreenScaleInWorkbench
        }
      })
      if (obj.index === -1) {
        obj.index = 0
        obj.size = this.sizeRange[0]
      }
      return obj
    }
  },
  watch: {
    defaultSize: {
      handler(val) {
        if (val !== -1) {
          this.currentSizeRangeIndex = val.index
          this.scaleNum = val.size
        }
      },
      immediate: true
    },
    bigscreenWidth() {
      this.initVueRulerTool()
    },
    bigscreenHeight() {
      this.initVueRulerTool()
    }
  },
  created() {
    this.revoke = new Revoke()
    this.getData()
  },
  methods: {
    /**
  * @param num: 0缩小 1放大 2默认比例
  * sizeRange: [20, 40, 60, 72, 100, 150, 200, 300, 400]
  */
    setSize(num) {
      switch (num) {
        case 0: this.currentSizeRangeIndex === 0 ? '' : this.currentSizeRangeIndex -= 1
          break
        case 1: this.currentSizeRangeIndex === 8 ? '' : this.currentSizeRangeIndex += 1
          break
        case 2: this.currentSizeRangeIndex = this.defaultSize.index
      }
      this.scaleNum = this.currentSizeRangeIndex === this.defaultSize.index ? this.defaultSize.size : this.sizeRange[this.currentSizeRangeIndex]
    },
    // 初始化 修正插件样式
    initVueRulerTool() {
      const vueRulerToolDom = this.$refs["vue-ruler-tool"].$el // 操作面板 第三方插件工具
      const contentDom = vueRulerToolDom.querySelector(".vue-ruler-content")
      const vueRulerX = vueRulerToolDom.querySelector(".vue-ruler-h") // 横向标尺
      const vueRulerY = vueRulerToolDom.querySelector(".vue-ruler-v") // 纵向标尺
      contentDom.style.width = "100%"
      contentDom.style.height = "100%"

      let xHtmlContent = ""
      let yHtmlContent = ""
      let currentNum = 0
      while (currentNum < +this.bigscreenWidth) {
        xHtmlContent += `<span class="n" style="left: ${currentNum + 2}px;">${currentNum}</span>`
        currentNum += 50
      }
      currentNum = 0
      while (currentNum < +this.bigscreenHeight) {
        yHtmlContent += `<span class="n" style="top: ${currentNum + 2}px;">${currentNum}</span>`
        currentNum += 50
      }
      vueRulerX.innerHTML = xHtmlContent
      vueRulerY.innerHTML = yHtmlContent
    },
    // 初始化接口数据
    async getData() {
      let {data} = await this.$api('biz.edc.bigScreen.detailDashboard', this.reportId)
      if (!data) {
        data = {
          dashboard: null,
          reportId: this.reportId,
          widgets: null
        }
      }
      this.widgets = this.initWidgetsData(data)
      this.dashboard = this.initScreenData(data.dashboard)
      this.bigscreenWidth = this.dashboard.width
      this.bigscreenHeight = this.dashboard.height
      this.hasChanged = false
    },
    // 组件数据
    initWidgetsData(data) {
      const widgets = data.dashboard ? data.dashboard.widgets : []
      const widgetsData = []
      for (let i = 0; i < widgets.length; i++) {
        const widget = widgets[i]
        const { setup, data, position } = { ...widget.value }
        const obj = {
          type: widget.type,
          value: { setup, data, position }
        }
        const tool = this.deepClone(getToolByCode(widget.type))
        if (!tool) {
          const message = "暂未提供该组件或该组件下线了，组件code: " + widget.type
          if (process.env.NODE_ENV === "development") {
            this.$message.error(message)
          }
          continue // 找不到就跳过，避免整个报表都加载不出来
        }
        if(data.reportInterfaceConfig){
          // 绑定了接口的组件，直接使用已保存的options
          tool.options.data = this.deepClone(widget.options.data)
        }
        obj.options = this.setDefaultWidgetConfigValue(widget.value, tool.options)
        obj.value.widgetId = obj.value.setup.widgetId
        widgetsData.push(obj)
      }
      return widgetsData
    },
    // 重写默认数据
    setDefaultWidgetConfigValue(data, option) {
      this.setConfigValue(data.setup, option.setup)
      this.setConfigValue(data.position, option.position)
      this.setConfigValue(data.data, option.data)
      return option
    },
    setConfigValue(objValue, setup) {
      if (!objValue) { return }
      Object.keys(objValue).forEach(key => {
        setup.forEach(item => {
          if (isObjectFn(item) && key == item.name) {
            item.value = objValue[key]
          }
          if (isArrayFn(item)) {
            item.forEach(itemChild => {
              itemChild.list.forEach(el => {
                if (key == el.name) {
                  el.value = objValue[key]
                }
              })
            })
          }
        })
      })
    },
    // 大屏数据
    initScreenData(data) {
      const optionScreen = getToolByCode("screen").options
      this.setConfigValue(data, optionScreen.setup)
      this.setOptionsOnClickScreen()
      return {
        backgroundColor:
          (data && data.backgroundColor) || (!data ? "#1e1e1e" : ""),
        backgroundImage: (data && data.backgroundImage) || "",
        height: (data && data.height) || String(getDefaultScreenContainer().height),
        title: (data && data.title) || "",
        width: (data && data.width) || String(getDefaultScreenContainer().width)
      }
    },
    // 保存数据
    async saveData() {
      if (!this.widgets || this.widgets.length == 0) {
        return this.$message.error("请添加组件")
      }
      const { title, width, height, backgroundColor, backgroundImage } = { ...this.dashboard }
      const screenData = {
        reportId: this.reportId,
        dashboard: { title: title || '大屏', width, height, backgroundColor, backgroundImage },
        widgets: this.widgets
      }
      screenData.widgets.forEach((widget) => {
        widget.value.setup.widgetId = widget.value.widgetId
        widget.value.setup.widgetCode = widget.type
      })
      try {
        await this.$api('biz.edc.bigScreen.insertDashboard', screenData)
        this.$message.success("保存成功！")
        this.hasChanged = false
      } catch (e) {
        //
      }
    },
    // 预览
    viewScreen(name = '') {
      this.$router.push({
          path: '/big-screen-designer-tab/viewer',
          query: {
              id: this.reportId,
              name
          }
      })
    },
    BlobDownLoad(res, type) {
        let blob = new Blob([res.data], {
            type: type
        })
        const fileName = decodeURIComponent(res.headers['content-disposition'].split('filename=')[1])
        const href = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = href
        a.download = fileName + '.zip'
        a.click()
        a.remove()
        URL.revokeObjectURL(a.href)
    },
    exportDashboard(val) {
        const params = {
            reportId: this.reportId,
            showDataSet: val
        }
        axios( {
            url:`${this.$baseUrl}/edc/report/export`,
            params,
            responseType: 'blob',
            headers: {
                'access-token': getToken(),
                'client-ip': Vue.ls.get( UserConstant.IP ),
                'client-mac': Vue.ls.get( UserConstant.Mac ),
                language: getCurrentLocale()
            }
        } ).then(
            async ( response ) => {
                if ( response.data.size ) {
                    this.BlobDownLoad( response,'application/octet-stream' )
                } else {
                    this.$message( {
                        message: this.$t( '未获取到文件' ),
                        type: 'error'
                    } )
                }
            },
            ( error ) => {
                console.log( error )
            }
        )
    },
    importDashboard(params) {
      const formData = new FormData()
      formData.append('file', params.file)
      formData.append('reportId', this.$route.query.id)
      let url = `${this.$baseUrl}` + `/edc/report/import`
      const token = getToken()
      this.$axios({
        method: 'post',
        url: url,
        data: formData,
        headers: {
        'access-token': token,
        'client-ip': Vue.ls.get(UserConstant.IP),
        'client-mac': Vue.ls.get(UserConstant.Mac),
        language: getCurrentLocale()
      }
      }).then(res => {
        this.$message({
          message: this.$t('操作成功'),
          type: 'success',
          duration: 500,
          onClose: () => {
           this.getData() // 重新请求一次
          }
        })
      }).catch(() => {
        this.$message({
          message: this.$t('导入失败'),
          showClose: true,
          type: 'error'
        })
        this.$refs.upload.clearFiles()
      })
    },
    handleUndo() {
      const record = this.revoke.undo()
      if (!record) return false
      this.widgets = record
    },
    handleRedo() {
      const record = this.revoke.redo()
      if (!record) return false
      this.widgets = record
    },
    handleUpload(response, file, fileList) {
      this.$refs.upload.clearFiles()
      this.getData()
      if (response.code == "200"){
        return this.$message.success('导入成功！')
      }else{
        this.$message.error(response.msg ? response.msg : '导入失败')
      }
    },
    // 右键
    rightClick(event, index) {
      this.rightClickIndex = index
      const left = event.clientX
      const top = event.clientY
      if (left || top) {
        this.styleObj = {
          left: left + "px",
          top: top + "px",
          display: "block"
        }
      }
      this.visibleContentMenu = true
      return false
    },
    // 数组 元素互换位置
    swapArr(arr, oldIndex, newIndex) {
      arr[oldIndex] = arr.splice(newIndex, 1, arr[oldIndex])[0]
      return arr
    },
    // 删除
    deletelayer() {
      this.widgets.splice(this.rightClickIndex, 1)
    },
    // 锁定
    lockLayer() {
      const obj = this.widgets[this.rightClickIndex]
      this.$set(obj.value.position, "disabled", true)
    },
    // 解除锁定
    noLockLayer() {
      const obj = this.widgets[this.rightClickIndex]
      this.$set(obj.value.position, "disabled", false)
    },
    // 复制
    copylayer() {
      const obj = this.deepClone(this.widgets[this.rightClickIndex])
      obj.value.position.top += 40 // 复制的元素向右下角偏移一点
      obj.value.position.left += 40
      obj.value.widgetId = Number(Math.random().toString().substr(2)).toString(
        36
      )
      this.widgets.splice(this.widgets.length, 0, obj)
      this.$nextTick(() => {
        this.layerClick(this.widgets.length - 1) // 复制后定位到最新的组件
      })
    },
    // 置顶
    istopLayer() {
      if (this.rightClickIndex + 1 < this.widgets.length) {
        const temp = this.widgets.splice(this.rightClickIndex, 1)[0]
        this.widgets.push(temp)
      }
    },
    // 置底
    setlowLayer() {
      if (this.rightClickIndex != 0) {
        this.widgets.unshift(this.widgets.splice(this.rightClickIndex, 1)[0])
      }
    },
    // 上移一层
    moveupLayer() {
      if (this.rightClickIndex != 0) {
        this.widgets[this.rightClickIndex] = this.widgets.splice(
          this.rightClickIndex - 1,
          1,
          this.widgets[this.rightClickIndex]
        )[0]
      } else {
        this.widgets.push(this.widgets.shift())
      }
    },
    // 下移一层
    movedownLayer() {
      if (this.rightClickIndex != this.widgets.length - 1) {
        this.widgets[this.rightClickIndex] = this.widgets.splice(
          this.rightClickIndex + 1,
          1,
          this.widgets[this.rightClickIndex]
        )[0]
      } else {
        this.widgets.unshift(this.widgets.splice(this.rightClickIndex, 1)[0])
      }
    }
  }
}

export default mixin
