window.schema = {
  "+symptom": {
    "name": "症状(+)",
    "color": "rgb(255, 0, 0)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "symptom": "症状",
      "outcome": "转归",
      "frequency": "频率",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质"
    }
  },
  "-symptom": {
    "name": "症状(-)",
    "color": "rgb(255, 153, 153)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "symptom": "症状",
      "outcome": "转归",
      "frequency": "频率",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质"
    }
  },
  "?symptom": {
    "name": "症状(?)",
    "color": "rgb(255, 85, 85)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "symptom": "症状",
      "outcome": "转归",
      "frequency": "频率",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质"
    }
  },
  "Accompany": {
    "name": "伴随症状",
    "color": "rgb(255, 102, 153)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "symptom": "症状",
      "outcome": "转归",
      "frequency": "频率",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质"
    }
  },
  "+sign": {
    "name": "体征(+)",
    "color": "rgb(0, 153, 153)",
    "property": {
      "happen_time": "发生时间",
      "sign": "体征名",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "sign_value": "体征值",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质",
      "color": "颜色",
      "size": "大小"
    }
  },
  "-sign": {
    "name": "体征(-)",
    "color": "rgb(55, 216, 216)",
    "property": {
      "happen_time": "发生时间",
      "sign": "体征名",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "sign_value": "体征值",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质",
      "color": "颜色",
      "size": "大小"
    }
  },
  "+abnormalsign": {
    "name": "异常体征(+)",
    "color": "rgb(255, 213, 139)",
    "property": {
      "happen_time": "发生时间",
      "sign": "体征名",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "sign_value": "体征值",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质",
      "color": "颜色",
      "size": "大小"
    }
  },
  "+normalsign": {
    "name": "正常体征(+)",
    "color": "rgb(168, 255, 153)",
    "property": {
      "happen_time": "发生时间",
      "sign": "体征名",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "sign_value": "体征值",
      "incentive": "诱因",
      "last_time": "持续时间",
      "nature": "性质",
      "color": "颜色",
      "size": "大小"
    }
  },
  "sign": {
    "name": "生命体征",
    "color": "rgb(255, 204, 51)",
    "property": {
      "sign_name": "体征名",
      "sign_unit": "单位",
      "sign_value": "体征值"
    }
  },
  "generalstatus": {
    "name": "一般情况",
    "color": "rgb(153, 204, 51)",
    "property": {
      "happen_time": "发生时间",
      "sign": "项目名称",
      "sign_value": "值",
      "last_time": "持续时间"
    }
  },
  "anaesthesia": {
    "name": "麻醉方式",
    "color": "rgb(230, 159, 207)",
    "property": {
      "anaesthesia_type": "麻醉方式"
    }
  },
  "+operation": {
    "name": "手术(+)",
    "color": "rgb(51, 153, 153)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "operation_style or manipulation_style": "手术方式",
      "ris_name": "检查结果",
      "disease": "疾病名称",
      "operation or manipulation": "手术名称",
      "last_time": "持续时间",
      "nature": "性质",
      "symptom": "症状",
      "sign": "体征名",
      "sign_value": "体征值"
    }
  },
  "-operation": {
    "name": "手术(-)",
    "color": "rgb(51, 204, 204)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "operation_style or manipulation_style": "手术方式",
      "ris_name": "检查结果",
      "disease": "疾病名称",
      "operation or manipulation": "手术名称",
      "last_time": "持续时间",
      "nature": "性质",
      "symptom": "症状",
      "sign": "体征名",
      "sign_value": "体征值"
    }
  },
  "?operation": {
    "name": "手术(?)",
    "color": "rgb(205, 230, 199)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "operation_style or manipulation_style": "手术方式",
      "ris_name": "检查结果",
      "disease": "疾病名称",
      "operation or manipulation": "手术名称",
      "last_time": "持续时间",
      "nature": "性质",
      "symptom": "症状",
      "sign": "体征名",
      "sign_value": "体征值"
    }
  },
  "+manipulation": {
    "name": "操作(+)",
    "color": "rgb(153, 34, 133)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "operation_style or manipulation_style": "手术方式",
      "ris_name": "检查结果",
      "disease": "疾病名称",
      "operation or manipulation": "手术名称",
      "last_time": "持续时间",
      "nature": "性质",
      "symptom": "症状",
      "sign": "体征名",
      "sign_value": "体征值"
    }
  },
  "-manipulation": {
    "name": "操作(-)",
    "color": "rgb(204, 92, 170)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "operation_style or manipulation_style": "手术方式",
      "ris_name": "检查结果",
      "disease": "疾病名称",
      "operation or manipulation": "手术名称",
      "last_time": "持续时间",
      "nature": "性质",
      "symptom": "症状",
      "sign": "体征名",
      "sign_value": "体征值"
    }
  },
  "?manipulation": {
    "name": "操作(?)",
    "color": "rgb(230, 155, 217)",
    "property": {
      "position": "部位",
      "happen_time": "发生时间",
      "operation_style or manipulation_style": "手术方式",
      "ris_name": "检查结果",
      "disease": "疾病名称",
      "operation or manipulation": "手术名称",
      "last_time": "持续时间",
      "nature": "性质",
      "symptom": "症状",
      "sign": "体征名",
      "sign_value": "体征值"
    }
  },
  "+personalhistory": {
    "name": "个人史(+)",
    "color": "rgb(204, 204, 51)",
    "property": {
      "happen_time": "开始时间",
      "personalhistory": "个人史",
      "personalhistoryvalue": "值",
      "last_time": "持续时间",
      "wine_type": "饮酒类型",
      "frequency": "频率",
      "quantity": "量"
    }
  },
  "-personalhistory": {
    "name": "个人史(-)",
    "color": "rgb(204, 204, 153)",
    "property": {
      "happen_time": "开始时间",
      "personalhistory": "个人史",
      "personalhistoryvalue": "值",
      "last_time": "持续时间",
      "wine_type": "饮酒类型",
      "frequency": "频率",
      "quantity": "量"
    }
  },
  "?personalhistory": {
    "name": "个人史(?)",
    "color": "rgb(204, 204, 102)",
    "property": {
      "happen_time": "开始时间",
      "personalhistory": "个人史",
      "personalhistoryvalue": "值",
      "last_time": "持续时间",
      "wine_type": "饮酒类型",
      "frequency": "频率",
      "quantity": "量"
    }
  },
  "+inoculationhistory": {
    "name": "预防接种史(+)",
    "color": "rgb(44, 132, 237)",
    "property": {
      "vaccine_name": "疫苗名称"
    }
  },
  "-inoculationhistory": {
    "name": "预防接种史(-)",
    "color": "rgb(102, 160, 212)",
    "property": {
      "vaccine_name": "疫苗名称"
    }
  },
  "symptom_frequency": {
    "name": "症状频率",
    "color": "#f00",
    "property": {
      "cycle_unit": "周期单位",
      "cycle_value": "周期值",
      "times": "次数"
    }
  },
  "treatment": {
    "name": "治疗",
    "color": "#f00",
    "property": {
      "treatment_name": "治疗"
    }
  },
  "Ris": {
    "name": "检查项目",
    "color": "rgb(97, 153, 230)",
    "property": {
      "ris_name": "检查名称",
      "ris_position": "检查部位",
      "ris_time": "检查时间"
    }
  },
  "Lis_Order": {
    "name": "检验项目",
    "color": "rgb(230, 104, 88)",
    "property": {
      "lis_name": "项目名称",
      "lis_time": "检查时间"
    }
  },
  "+ris_result": {
    "name": "检查结果(+)",
    "color": "rgb(159, 230, 207)",
    "property": {
      "ris_name": "检查部位",
      "ris_result": "检查结果",
      "ris_result_unit": "单位",
      "nature": "性质",
      "ris_time": "检查时间"
    }
  },
  "-ris_result": {
    "name": "检查结果(-)",
    "color": "rgb(230, 195, 85)",
    "property": {
      "ris_name": "检查部位",
      "ris_result": "检查结果",
      "ris_result_unit": "单位",
      "nature": "性质",
      "ris_time": "检查时间"
    }
  },
  "?ris_result": {
    "name": "检查结果(?)",
    "color": "#f00",
    "property": {
      "ris_name": "检查部位",
      "ris_result": "检查结果",
      "ris_result_unit": "单位",
      "nature": "性质",
      "ris_time": "检查时间"
    }
  },
  "operation_cut": {
    "name": "手术切口",
    "color": "rgb(230, 187, 144)",
    "property": {
      "operation_cut_name": "切口名称"
    }
  },
  "operation_position": {
    "name": "手术体位",
    "color": "#f00",
    "property": {
      "operation_position_name": "手术体位名称"
    }
  },
  "emergency_result": {
    "name": "抢救结果",
    "color": "#f00",
    "property": {
      "emergency_result": "抢救结果"
    }
  },
  "nursing_assessment": {
    "name": "护理措施",
    "color": "rgb(172, 230, 128)",
    "property": {
      "nursing_name": "护理措施"
    }
  },
  "TNM_grouping": {
    "name": "TNM分期",
    "color": "#f00",
    "property": {
      "N_value": "N值",
      "T_value": "T值",
      "M_value": "M值"
    }
  },
  "pathology": {
    "name": "免疫组化",
    "color": "#f00",
    "property": {
      "pathology_name": "名称",
      "pathology_value": "值"
    }
  },
  "menstrualhistory": {
    "name": "月经史",
    "color": "rgb(0, 153, 0)",
    "property": {
      "happen_time": "发生时间",
      "menstrual_age": "初潮/闭经年龄",
      "menstrual_unit": "单位",
      "menstrual_type": "项目名称",
      "last_time": "持续时间",
      "menstrual_value": "值"
    }
  },
  "pregnancy&birth": {
    "name": "孕产史",
    "color": "rgb(255, 102, 0)",
    "property": {
      "marriage_value": "婚育状态/现存子女",
      "pregnant_times": "怀孕次数",
      "birth_times": "生育次数",
      "abortion_times": "流产次数",
      "birth_boys": "生男数",
      "birth_girls": "生女数"
    }
  },
  "marriagehistory": {
    "name": "婚育史",
    "color": "rgb(204, 204, 0)",
    "property": {
      "relative": "亲属关系",
      "marriage_type": "名称",
      "marriage_unit": "单位",
      "marriage_value": "值",
      "marriage_time": "婚龄/时间",
      "marriage_age": "结婚年龄"
    }
  },
  "-marriagehistory": {
    "name": "婚育史(-)",
    "color": "#f00",
    "property": {
      "relative": "亲属关系",
      "marriage_type": "名称",
      "marriage_unit": "单位",
      "marriage_value": "值",
      "marriage_time": "婚龄/时间",
      "marriage_age": "结婚年龄"
    }
  },
  "+familyhistory": {
    "name": "家族史(+)",
    "color": "rgb(85, 153, 255)",
    "property": {
      "health_status": "健康状态",
      "symptom": "症状",
      "disease": "疾病",
      "relative": "亲属关系",
      "operation": "手术",
      "position": "部位",
      "sign": "体征名",
      "sign_value": "体征值",
      "happen_time": "发生时间",
      "last_time": "持续时间",
      "out_come": "转归"
    }
  },
  "blood_transfusion": {
    "name": "输血记录",
    "color": "rgb(230, 76, 105)",
    "property": {
      "bt_name": "输血类型",
      "bt_value": "值",
      "bt_unit": "单位",
      "abo_type": "abo血型",
      "rh_type": "Rh血型"
    }
  },
  "Lis": {
    "name": "检验结果",
    "color": "#f00",
    "property": {
      "lis_value": "值",
      "lis_unit": "单位",
      "lis_name": "检验项目",
      "lis_result": "检验结果",
      "lis_time": "检验时间"
    }
  },
  "+allergyhistory": {
    "name": "过敏史(+)",
    "color": "rgb(0, 153, 255)",
    "property": {
      "Drug": "过敏药物",
      "allergen": "过敏原"
    }
  },
  "-allergyhistory": {
    "name": "过敏史(-)",
    "color": "rgb(102, 170, 255)",
    "property": {
      "Drug": "过敏药物",
      "allergen": "过敏原"
    }
  },
  "?allergyhistory": {
    "name": "过敏史(?)",
    "color": "rgb(188, 240, 255)",
    "property": {
      "Drug": "过敏药物",
      "allergen": "过敏原"
    }
  },
  "+Drug": {
    "name": "用药(+)",
    "color": "rgb(255, 51, 255)",
    "property": {
      "Drug": "药品名",
      "usage": "用法",
      "efficiency": "功效",
      "form": "规格",
      "dosage": "剂量",
      "happen_time": "开始时间",
      "frequency": "频率",
      "last_time": "持续时间"
    }
  },
  "-Drug": {
    "name": "用药(-)",
    "color": "rgb(255, 153, 255)",
    "property": {
      "Drug": "药品名",
      "usage": "用法",
      "efficiency": "功效",
      "form": "规格",
      "dosage": "剂量",
      "happen_time": "开始时间",
      "frequency": "频率",
      "last_time": "持续时间"
    }
  },
  "+disease": {
    "name": "疾病(+)",
    "color": "rgb(0, 255, 0)",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "-disease": {
    "name": "疾病(-)",
    "color": "rgb(153, 255, 153)",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "?disease": {
    "name": "疾病(?)",
    "color": "rgb(85, 255, 85)",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "+traumatism": {
    "name": "外伤(+)",
    "color": "#f00",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "-traumatism": {
    "name": "外伤(-)",
    "color": "#f00",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "?traumatism": {
    "name": "外伤(?)",
    "color": "#f00",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "+epidemic": {
    "name": "传染病(+)",
    "color": "#f00",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "-epidemic": {
    "name": "传染病(-)",
    "color": "#f00",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "?epidemic": {
    "name": "传染病(?)",
    "color": "#f00",
    "property": {
      "nature": "性质",
      "disease": "疾病",
      "level": "程度",
      "level_lasttime": "加重/缓解时间",
      "outcome": "转归",
      "position": "部位",
      "incentive": "诱因",
      "personalhistory": "个人史",
      "complication": "并发症",
      "operation": "手术"
    }
  },
  "+complication": {
    "name": "并发症(+)",
    "color": "#f00",
    "property": {
      "disease": "疾病",
      "operation": "手术",
      "complication": "并发症"
    }
  },
  "+blood_transfusion_history": {
    "name": "输血史(+)",
    "color": "#f00",
    "property": {
      "bt_name": "输血史"
    }
  },
  "-blood_transfusion_history": {
    "name": "输血史(-)",
    "color": "#f00",
    "property": {
      "bt_name": "输血史"
    }
  },
  "?blood_transfusion_history": {
    "name": "输血史(?)",
    "color": "#f00",
    "property": {
      "bt_name": "输血史"
    }
  },
  "+traumatismhistory": {
    "name": "外伤史(+)",
    "color": "#f00",
    "property": {
      "disease": "外伤史"
    }
  },
  "-traumatismhistory": {
    "name": "外伤史(-)",
    "color": "#f00",
    "property": {
      "disease": "外伤史"
    }
  },
  "?traumatismhistory": {
    "name": "外伤史(?)",
    "color": "#f00",
    "property": {
      "disease": "外伤史"
    }
  }
}