# 参数
```json
// 下载模板相关
downloadTemplateOption: {
    "method": "GET",   // 下载模板的请求方式
    "apiUrl": "", // 请求路径(不需要拼上/api),如:file/downloadById或者/file/downloadById
    "params": {
      "key1": "1",
      "key2": "2"
    }, // params参数,以key1=1&key2=2拼接道到路径上,可不传
    "data": {
      "key3": "3",
      "key4": "4"
    }, // body参数 POST请求时用到,可不传
    "templateFileName": "" // 下载的模板文件名
},
```

```json
// 上传excel导入数据相关
uploadFileOption: {
  "uploadTypeState": true, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
  "apiUrl": "",
  "method": "POST",
  "params": {
    "key1": "1",
    "key2": "2"
  },// params参数,以key1=1&key2=2拼接道到路径上,可不传
  "data": {
    "key3": "3",
    "key4": "4"
  } // body参数 POST请求时用到,可不传
},
```

```json
// 自定义组件基本样式,有时候组件不是在弹窗中使用,可以调整基本的居中,宽度样式 "width: 50%;margin:auto;"
customStyle: ""
```

```json
// 上传成功后如需要更新表格数据,可以选择传入此参数
tableUid: ""
```

```json
// 点击确定按钮之后的回调
afterUpload: Function
```

```json
// 点击下载模板下载之前的操作，需要在完成操作后手动触发下载模板
beforeDownload: Function
```

```json
// 导入之前用来设置相关参数
beforeUpload: Function
```

```json
// 上传成功的回调
callback: Function
```


```html
<hos-button @click="importBatch()">批量导入</hos-button>

<!-- 如果是弹窗内操作... -->
<hos-biz-dialog
  title="批量导入"
  width="40%"
  uid="customUploadDialog"
  :close-on-click-modal="false"
></hos-biz-dialog>
```

```javascript
importBatch() {
  this.$store.commit('OPEN_DIALOG', {
    // 
    component: require('@/components/upload/index.vue').default,
    _uid: 'customUploadDialog',
    props: {
      customStyle: '',
      downloadTemplateOption: {
        method: 'GET', // 请求方式
        apiUrl: 'file/downloadById', // 下载模板Api对应的接口地址
        params: {
          id: '3319255c6a131bd7c0259f6c9a007093'
        }, // 拼到请求路径上的参数
        data: {
          key1: 1,
          key2: 2
        }, // body参数 POST请求时用到
        templateFileName: '患者批量导入模板'
      },
      uploadFileOption: {
        uploadTypeState: true, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
        apiUrl: '',
        method: 'POST',
        params: {
          key: 'key'
        },
        data: {
          key3: 3,
          key4: 4
        }
      },
      tableUid: 'patientTable'
    }
  })
}
```