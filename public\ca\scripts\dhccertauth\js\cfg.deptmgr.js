﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_locstatus";

//页面选中数据
var selDeptID = "";
var isHidenPatCAOn;
var isHidenCareCAOn;

$(function() {
    initBTN();
    document.onkeydown = documentOnKeyDown;

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        if (!isShow(selOrgID)) {
            return;
        }
        initDeptGrid(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        if (!isShow(selOrgID)) {
            return;
        }
        initDeptGrid(selOrgID);
    }
})

function initBTN() {
    $("#btnStartDept").click(function(){startDept("CARECA");});
    $("#btnStopDept").click(function(){stopDept("CARECA");});

    $("#btnQueryDept").click(function(){queryDept();});

    $("#btnStartPatDept").click(function(){startDept("PATCA");});
    $("#btnStopPatDept").click(function(){stopDept("PATCA");});
}

function isShow(selOrgID) {
    var result = false;
    var errmsg = "";

    isHidenPatCAOn = false;
    isHidenCareCAOn = false;
    $("#btnStartDept").show();
    $("#btnStopDept").show();
    $("#btnStartPatDept").show();
    $("#btnStopPatDept").show();
    
    var data = {
        action: "GET_SYSOPTION",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        var arr = json.data;
        if ((arr["isCareCAOn"] != "1")&&(arr["isPatCAOn"] != "1")) {
            errmsg = "当前组织机构未开启【医护签名】和【患者签名】，无需维护签名科室！"
            isHidenCareCAOn = true;
            isHidenPatCAOn = true;
            $("#btnStartDept").hide();
            $("#btnStopDept").hide();
            $("#btnStartPatDept").hide();
            $("#btnStopPatDept").hide();
            result = false;
        } else {
            if (arr["isCareCAOn"] != "1") {
                isHidenCareCAOn = true;
                $("#btnStartDept").hide();
                $("#btnStopDept").hide();
                if ((arr["isPatCAOn"] == "1")&&(arr["allPatCAOn"] == "1"))
                    errmsg = setMessage("当前组织机构未开启【医护签名】，无需开关[医护签名]科室！",errmsg);
            } else {
                if (arr["allCareCAOn"] == "1") {
                    isHidenCareCAOn = true;
                    $("#btnStartDept").hide();
                    $("#btnStopDept").hide();
                    errmsg = setMessage("当前组织机构已配置【全院开启医护签名】，所有科室[医护签名]都为启用状态！",errmsg);
                }
            }
            if (arr["isPatCAOn"] != "1") {
                isHidenPatCAOn = true;
                $("#btnStartPatDept").hide();
                $("#btnStopPatDept").hide();
                if ((arr["isCareCAOn"] == "1")&&(arr["allCareCAOn"] == "1"))
                    errmsg = setMessage("当前组织机构未开启【患者签名】，无需开关[患者签名]科室！",errmsg);
            } else {
                if (arr["allPatCAOn"] == "1") {
                    isHidenPatCAOn = true;
                    $("#btnStartPatDept").hide();
                    $("#btnStopPatDept").hide();
                    errmsg = setMessage("当前组织机构已配置【全院开启患者签名】，所有科室[患者签名]都为启用状态！",errmsg)
                }
            }
            if (isHidenCareCAOn && isHidenPatCAOn) {
                result = false;
            } else {
                result = true;
            }
        }
        if (errmsg != "")
            $.messager.alert("提示",errmsg);
    } else {
        $.messager.alert("提示", "获取通用配置数据错误，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }

    if (!result) {
        setEnable(false,errmsg);
    } else {
        setEnable(true);
    }
    return result;
}

function setEnable(enable,errmsg) {
    if (enable) {
        $("#dept").show();
        $("#caTip").hide();
    } else {
        $("#dept").hide();
        $("#caTip").show();
        document.getElementById("caTip").innerHTML = errmsg;
    }
}

function setMessage(msg,errmsg) {
    if (errmsg != "")
        errmsg = errmsg + "<br/>";
    errmsg = errmsg + msg;
    return errmsg;
}

function queryDept() {
    var deptDesc = $("#deptDesc").val();
    var signStatus = $HUI.combobox("#deptStatus").getValue();

    var queryParams = {
        action: "GET_CTLOCLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            status: signStatus,
            ctLocDesc: deptDesc
        }
    };
    $("#dgDept").datagrid("load",queryParams);
}


function startDept(caType) {
    selDeptID = selDeptID || "";
    if (selDeptID == "") {
        $.messager.alert("提示","请选中要开启的科室");
        return;
    }

    var data = {
        action: "SET_DEPTCAON",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            ctLocID: selDeptID,
            caType: caType
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "开启科室"+ (caType == "PATCA" ? "患者签名" : "医护签名") +"成功！",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
                queryDept();
            } else {
                $.messager.alert("提示", "开启科室"+ (caType == "PATCA" ? "患者签名" : "医护签名") +"失败！", "error")
            }
        } else {
            $.messager.alert("提示", "开启科室CA失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function stopDept(caType) {
    selDeptID = selDeptID || "";
    if (selDeptID == "") {
        $.messager.alert("提示","请选中要停用的科室");
        return;
    }

    var data = {
        action: "SET_DEPTCAOFF",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            ctLocID: selDeptID,
            caType: caType
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "关闭科室"+ (caType == "PATCA" ? "患者签名" : "医护签名") +"成功！",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
                queryDept();
            } else {
                $.messager.alert("提示", "关闭科室"+ (caType == "PATCA" ? "患者签名" : "医护签名") +"失败！", "error")
            }
        } else {
            $.messager.alert("提示", "关闭科室CA失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function initDeptGrid(selOrgID) {
    var param = {
        action: "GET_CTLOCLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            status: "ALL",
            ctLocDesc: ""
        }
    };

    $("#dgDept").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbDept",
        url: ca_common_tools.getAppPath("GET_CTLOCLIST"),
        queryParams: param,
        idField:"ctLocID",
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:50,
        pageList:[2,10,30,50],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"ctLocID",title:"科室ID",hidden:true},
            {field:"ctLocCode",title:"科室代码",width:130},
            {field:"ctLocDesc",title:"科室名称",width:200},
            {
                field:"isCareCAOn",
                title:"是否已开启医护签名",
                align: "center",
                formatter: function(value,row,index)
                {
                    if (value == "1") {
                        return "<span style='color:green'>已开启</span>";
                    } else {
                        return "<span style='color:red'>未开启</span>";
                    }
                },
                hidden: isHidenCareCAOn
            },
            {
                field:"isPatCAOn",
                title:"是否已开启患者签名",
                align: "center",
                formatter: function(value,row,index)
                {
                    if (value == "1") {
                        return "<span style='color:green'>已开启</span>";
                    } else {
                        return "<span style='color:red'>未开启</span>";
                    }
                },
                hidden: isHidenPatCAOn
            }
        ]],
        onLoadError:function() {
            $.messager.alert("提示","科室列表加载失败");
        },
        onSelect:function(rowIndex,row){
            selDeptID = row.ctLocID;
            if (row.isCareCAOn == 1) {
                $("#btnStartDept").linkbutton("disable");
                $("#btnStopDept").linkbutton("enable");
            } else {
                $("#btnStopDept").linkbutton("disable");
                $("#btnStartDept").linkbutton("enable");
            }

            if (row.isPatCAOn == 1) {
                $("#btnStartPatDept").linkbutton("disable");
                $("#btnStopPatDept").linkbutton("enable");
            } else {
                $("#btnStopPatDept").linkbutton("disable");
                $("#btnStartPatDept").linkbutton("enable");
            }
        },
        onLoadSuccess:function(data){
            $("#dgDept").datagrid("clearSelections");
            $("#btnStartDept").linkbutton("disable");
            $("#btnStopDept").linkbutton("disable");
            $("#btnStartPatDept").linkbutton("disable");
            $("#btnStopPatDept").linkbutton("disable");
            selDeptID = "";
        }
    });
}

function documentOnKeyDown(e) {
    if (window.event) {
        var keyCode=window.event.keyCode;
    } else {
        var keyCode=e.which;
    }

    if (keyCode==13) {
        queryDept();
    }
}
