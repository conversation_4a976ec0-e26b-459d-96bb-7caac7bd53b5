function getUUID() {
    var d = new Date().getTime();
    if (window.performance && typeof window.performance.now === 'function') {
        d += window.performance.now(); // use high-precision timer if available
    }
    var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
}

// AES encryption and decryption
var key = CryptoJS.enc.Utf8.parse('1234123412ABCDEF'); // 16-byte hexadecimal key
var iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412'); // 16-byte initialization vector

// Decrypt method
function Decrypt(word) {
    var encryptedHexStr = CryptoJS.enc.Hex.parse(word);
    var srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    var decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
    var decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    var inside = decryptedStr.toString();
    var i = inside.substr(-32);
    return inside.split(i)[0];
}

// Decrypt BASE64 method
function Decrypt_BASE64(word) {
    return CryptoJS.enc.Base64.parse(word).toString(CryptoJS.enc.Utf8);
}

// Encrypt method
function Encrypt(word) {
    var srcs = CryptoJS.enc.Utf8.parse(word + getUUID());
    var encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
    return encrypted.ciphertext.toString().toUpperCase();
}

// Encrypt BASE64 method
function Encrypt_BASE64(word) {
    var utf8 = CryptoJS.enc.Utf8.parse(word);
    var base64 = CryptoJS.enc.Base64.stringify(utf8);
    return base64;
}

// Encrypt URL method
function EncryptUrl(url) {
    if (!url) { return ''; }
    if (url.indexOf('?') < 0) { return url; }
    var parts = url.split('?');
    var base_str = parts[0];
    var query_str = parts[1];
    var query = parseQuery(query_str);
    var new_query_str = Encrypt(query_str);
    var other = '';

    // Fields to keep unencrypted
    var keepFields = [];
    for (var i = 0; i < keepFields.length; i++) {
        var field = keepFields[i];
        if (query.hasOwnProperty(field)) {
            other += '&' + field + '=' + query[field];
        }
    }
    return base_str + '?p=' + new_query_str + other;
}

// Decrypt URL parameter by key
function getDecryptUrlByKey(key) {
    var url = window.location.href;
    if (url.indexOf('?') < 0) { return; }
    var parts = url.split('?');
    var base_str = parts[0];
    var query_str = parts[1];
    var query = parseQuery(query_str);
    if (!query.p) { return; }
    var new_query_str = Decrypt(query.p);
    var new_query = parseQuery(new_query_str);
    return new_query[key];
}

// Convert query object to string
function stringifyQuery(query) {
    var queryArray = [];
    for (var key in query) {
        if (query.hasOwnProperty(key)) {
            queryArray.push(key + '=' + query[key]);
        }
    }
    return queryArray.join('&');
}

// Parse query string into object
function parseQuery(str) {
    var query = {};
    var pairs = str.split('&');
    for (var i = 0; i < pairs.length; i++) {
        var pair = pairs[i].split('=');
        query[pair[0]] = pair[1];
    }
    return query;
}

// AES Encrypt with timestamp
function aesEncrypt(timestamp) {
    var key = CryptoJS.enc.Utf8.parse("BGxdEUOZkXka4HSj");
    var content = timestamp;

    var encrypted = CryptoJS.AES.encrypt(content, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });

    return encrypted.toString();
}
