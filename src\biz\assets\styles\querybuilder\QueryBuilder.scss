.query-builder .rule-filter-container {
  width: 300px;
}

.query-builder .rule-container,
.query-builder .rule-placeholder,
.query-builder .rules-group-container {
  position: relative;
  margin: 0;
  border-radius: 5px;
  padding: 6px 15px;
  border: 0px solid #eee;
  background: rgba(255, 255, 255, 0.9);
}

.query-builder .drag-handle,
.query-builder .error-container,
.query-builder .rule-container .rule-filter-container,
.query-builder .rule-container .rule-operator-container,
.query-builder .rule-container .rule-value-container {
  display: inline-block;
  margin: 0 5px 0 0;
  vertical-align: middle;
}

.query-builder .rules-group-container {
  padding: 0px;
  margin: 0;
  border: 0px solid #ddd;
}

.query-builder .rules-group-header {
  margin-bottom: 10px;
}

.query-builder .rules-group-header .group-conditions .btn.readonly:not(.active),
.query-builder .rules-group-header .group-conditions input[name$="_cond"] {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap;
}

.query-builder .rules-group-header .group-conditions .btn.readonly {
  border-radius: 3px;
}

.query-builder .rules-list {
  list-style: none;
  padding: 0 0 0 40px;
  margin: 0;
}

.query-builder .rule-value-container {
  padding-left: 5px;
}

.query-builder .rule-value-container label {
  margin-bottom: 0;
  font-weight: 400;
}

.query-builder .rule-value-container label.block {
  display: block;
}

.query-builder .error-container {
  display: none;
  cursor: help;
  color: red;
}

.query-builder .has-error {
  background-color: #fdd;
  border-color: #f99;
}

.query-builder .has-error .error-container {
  display: inline-block !important;
}

.hos-input.is-error .hos-input__inner,
.input-multi.is-error .hos-input__inner,
.hos-autocomplete.is-error .hos-input__inner {
  border-color: #f56c6c;
}

.query-builder .rules-list> ::after,
.query-builder .rules-list> ::before {
  content: "";
  position: absolute;
  left: -15px;
  width: 40px;
  height: calc(50% + 4px);
  border-color: #ccc;
  border-style: solid;
}

.query-builder .rules-list.sub-rules-list> ::after,
.query-builder .rules-list.sub-rules-list> ::before {
  border-color: #85ce61;
}

.query-builder .rules-list> ::before {
  top: -4px;
  border-width: 0 0 2px 2px;
}

.query-builder .rules-list>.rules-group-container::before {
  width: 40px;
}

.query-builder .rules-list> ::after {
  top: 50%;
  border-width: 0 0 0 2px;
}

.query-builder .rules-list>.rule-container:first-child::before {
  /* top: -14px;
height: calc(50% + 14px); */
  top: 20px;
  height: 0px;
  border-top-left-radius: 3px;
}

.query-builder .rules-list>.rules-group-container:first-child::before {
  top: 0px;
  height: calc(50% + 0px);
  border-top-left-radius: 3px;
  border-left: 0;
  width: 40px;
}

.query-builder .rules-list>.rule-container:first-child::after {
  /* top: 4px;
height: calc(50% + 14px); */
  top: 20px;
  height: calc(50% + 17px);
  border-top-left-radius: 3px;
}

.query-builder .rules-list> :last-child::before {
  border-radius: 0 0 0 3px;
}

.query-builder .rules-list> :last-child::after {
  display: none;
}

.query-builder.bt-checkbox-glyphicons .checkbox input[type="checkbox"]:checked+label::after {
  font-family: "Glyphicons Halflings";
  content: "\e013";
}

.query-builder.bt-checkbox-glyphicons .checkbox label::after {
  padding-left: 4px;
  padding-top: 2px;
  font-size: 9px;
}

.query-builder .error-container+.tooltip .tooltip-inner {
  color: #f99 !important;
}

.query-builder p.filter-description {
  margin: 5px 0 0 0;
  background: #d9edf7;
  border: 1px solid #bce8f1;
  color: #31708f;
  border-radius: 5px;
  padding: 2.5px 5px;
  font-size: 0.8em;
}

.query-builder .rules-group-header [data-invert] {
  margin-left: 5px;
}

.query-builder .drag-handle {
  cursor: move;
  vertical-align: middle;
  margin-left: 5px;
}

.query-builder .dragging {
  position: fixed;
  opacity: 0.5;
  z-index: 100;
}

.query-builder .dragging::after,
.query-builder .dragging::before {
  display: none;
}

.query-builder .rule-placeholder {
  border: 1px dashed #bbb;
  opacity: 0.7;
}

.rule-header>.pull-right {
  display: inline-block;
  margin: 0;
  vertical-align: middle;
  float: none;
}

.rules-group-header>.pull-right {
  float: right !important;
}

.has-error {
  background-color: #fdd;
  border-color: #f99;
}

.rule-filter-container .search-open-icon {
  cursor: pointer;
}

.rule-filter-container,
.rule-value-container {
  padding-right: 5px;
  width: 30%;
  max-width: 300px;
}

.rule-value-container {
  height: 28px;
}

.rule-operator-container {
  width: 120px;
}

/* @media screen and (max-width: 1280px) {
.rule-filter-container,
.rule-value-container {
  width: 220px;
}
.rule-operator-container {
  width: 130px;
}
}
@media screen and (max-width: 1136px) {
.rule-filter-container,
.rule-value-container {
  width: 200px;
}
.rule-operator-container {
  width: 110px;
}
} */
// .query-builder{
//   .hos-button.is-circle{
//     padding: 5px;
//   }
// }
