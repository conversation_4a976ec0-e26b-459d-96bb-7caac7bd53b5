
export const queryListApi = (params) => {
  return {
    url: 'search/tag/config/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'search/tag/config/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'search/tag/config/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'search/tag/config/insert',
    method: 'post',
    data,
  }
}

export const editTag = (data) => {
  return {
    url: 'search/tag/config/update',
    method: 'post',
    data
  }
}



