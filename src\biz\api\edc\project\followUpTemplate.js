
// 获取指定ID的随访计划信息
export const queryFollowTempalteById = (id) => {
  return {
    url: 'edc/followup-plan/template/detail/' + id,
    method: 'get'
  }
}

// 新增随访模板
export const addFollowUpTemplate = (data) => {
  return {
    url: `edc/followup-plan/template/insert`,
    method: 'post',
    data,
  }
}

// 修改随访模板
export const updateFollowUpTemplate = (data) => {
  return {
    url: `edc/followup-plan/template/update`,
    method: 'post',
    data,
  }
}

// 预览指定id的随访模板
export const previewFollowUpTemplate = (id) => {
  return {
    url: 'edc/followup-plan/preview/template/' + id,
    method: 'get'
  }
}

// 将指定随访模版的内容设置成项目随访计划
export const setTemplateAsPlan = (params) => {
  return {
    url: `edc/followup-plan/apply-by-template/${params.subProjectId}/${params.tempId}`,
    method: 'post'
  }
}

// 分页查询随访模板
export const queryFollowTempaltePage = (params) => {
  return {
    url: 'edc/followup-plan/template/page',
    method: 'get',
    params
  }
}

// 


