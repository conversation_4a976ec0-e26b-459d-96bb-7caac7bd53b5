.add-subfield-grade2-dialog {
  .hos-dialog__header {}

  .hos-dialog__body {
    padding-top: 0;
    padding-bottom: 0;
    height: 50vh;
  }

  .hos-dialog__footer {}
}

.add-subfield-grade2-container {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  .search {
    width: 250px;
    height: 28px;
    margin: 0 auto;
  }

  .field-content {
    flex-grow: 1;
    padding: 20px 0;
    overflow-y: scroll;

    .hos-tag {
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .hos-tag.is-selected {
      color: white;
      background-color: #589CFC;
    }

    .hos-tag.disable {
      color: white;
      border-color: #909399;
      background-color: #909399;
      cursor: not-allowed;
    }
  }

  .tips {
    width: 100%;
    height: 20px;
    color: #aeaeae;
    text-align: left;
  }
}