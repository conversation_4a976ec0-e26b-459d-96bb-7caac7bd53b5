﻿//页面传参信息从父页面获取
var globalInfo = parent.globalInfo;

$(function() {
    initAppName();
    initRollSettings();
    ///如果不需要输入工号，直接展示二维码
    if (!initUserCodeDiv())
        qrCodeLogon();
});

//初始化扫码app名称
function initAppName() {
    var appName = globalInfo.VenderSignTypeConfig.signOption.appName || "东华医为APP"
    document.getElementById("appName").innerHTML = ca_common_tools.trans(appName);
}

//初始化是否需要用户
function initUserCodeDiv() {
    globalInfo.needUserCode = globalInfo.VenderSignTypeConfig.signOption.qrcodeNeedUserCode || "";
    if (globalInfo.needUserCode == 1) {
        $("#divUserCode").show();
        return true;
    }
    return false;
}

//初始化轮询次数(默认300)、轮询间隔(默认1秒)
function initRollSettings() {
    globalInfo.scanQRRollInterval = (globalInfo.VenderSignTypeConfig.signOption.scanQRRollInterval || 1) * 1000;
    globalInfo.scanQRRollMax = globalInfo.VenderSignTypeConfig.signOption.scanQRRollInterval || 300;
}

//展示二维码，开始签名流程
function qrCodeLogon() {
    var userCode = "";
    if (globalInfo.needUserCode == "1") {
        userCode = $("#userCode").val() || "";
        if (userCode == "") {
            displayError("当前厂商配置获取二维码需要指定用户工号，请输入用户工号！");
            return;
        }
    }
    initDiv();

    console.log("开始签名流程:"+getCurrentTime());//输出到控制台
    console.time("获取二维码信息耗时");
    var qrCodeInfo = parent.ca_key.getLoginQrInfo({
        organizationID:globalInfo.OrgID,
        userCode:userCode
    });
    console.timeEnd("获取二维码信息耗时");
    if ("0" !== qrCodeInfo.code) {
        displayError("获取二维码信息失败:"+qrCodeInfo.msg);
        $("#divQrCodeGen").hide();
        $("#divQrCodeExp").show();
        return;
    }

    if (!showQrCode(qrCodeInfo.data))
        return;

    globalInfo.rollCount = globalInfo.scanQRRollMax;
    rollQrResult(qrCodeInfo.data.qrID);
}

//根据二维码类型展示数据(链接、二维码图片、二维码文字)
function showQrCode(qrCodeInfo) {
    if (qrCodeInfo.qrUrl !== "") {
        //显示链接时无需提示应用名称
        $("#caOperTip").hide();
        $("#qrCodeUrl").show();
        $("#qrCodeUrl").html('<iframe allowtransparency="true" src="'+ qrCodeInfo.qrUrl +'" scrolling="auto" width="100%" height="100%" frameborder="0"></iframe>');
    } else if (qrCodeInfo.qrCode !== "") {
        $("#qrCodeImg").show();
        document.getElementById("qrCodeImg").src = "data:image/jpeg;base64," + qrCodeInfo.qrCode;
    } else if (qrCodeInfo.qrText !== "") {
        $("#qrCodeText").show();
        if (globalInfo.qrcodeMaker || "" == "") {
            globalInfo.qrcodeMaker = new QRCode(document.getElementById("qrCodeText"),{width:200,height:200,correctLevel:QRCode.CorrectLevel.L});
        } 
        globalInfo.qrcodeMaker.clear()
        globalInfo.qrcodeMaker.makeCode(qrCodeInfo.qrText);	
    } else {
        displayError("获取二维码信息异常，无任何有效数据："+JSON.stringify(qrCodeInfo));
        return false;
    }
    $("#divQrCodeGen").hide();
    $("#divQrCode").show();
    return true;
}

function rollQrResult(qrID){
    globalInfo.rollCount--;
    if (globalInfo.rollCount<1) {
        hideQrCodeDiv();
        $("#divQrCodeExp").show();
        displayError("扫码认证超时");
        return ;
    }

    console.time("获取二维码扫描结果耗时");
    var loginQrResult = parent.ca_key.getLoginQrResult({
        organizationID:globalInfo.OrgID,
        qrID:qrID
    });
    console.timeEnd("获取二维码扫描结果耗时");
    if ("0" !== loginQrResult.code) {
        hideQrCodeDiv();
        $("#divQrCodeExp").show();
        displayError("获取二维码扫码信息失败:"+loginQrResult.msg);
        return;
    }
    console.log("获取二维码扫描结果:"+JSON.stringify(loginQrResult.data)+getCurrentTime());//输出到控制台
    ///扫码成功
    if (loginQrResult.data.signStatus == "FINISH") {
        console.time("证书登录耗时");
        var loginResult = parent.ca_key.login({
            certContainer:loginQrResult.data.certContainer,
            organizationID: globalInfo.OrgID,
            imageType:"Original",
            cert:loginQrResult.data.cert,
            userCertCode:loginQrResult.data.userCertCode,
            certNo:loginQrResult.data.certNo
        });
        console.timeEnd("证书登录耗时");
        if ("0" !== loginResult.code) {
            hideQrCodeDiv();
            $("#divQrCodeExp").show();
            displayError("证书登录失败:"+loginResult.msg);
            return;
        }
        console.log("证书登录成功:"+getCurrentTime());//输出到控制台
        
        console.time("获取token有效性耗时");
        var tokenIsValid = parent.ca_key.getTokenIsValid({
            organizationID:globalInfo.OrgID,
            certContainer:loginQrResult.data.certContainer,
            signToken:loginQrResult.data.signToken
        });
        console.timeEnd("获取token有效性耗时");
        if ("0" !== tokenIsValid.code) {
            hideQrCodeDiv();
            $("#divQrCodeExp").show();
            displayError("获取token有效性失败:"+tokenIsValid.msg);
            return;
        }
        console.log("获取token有效性成功:"+getCurrentTime());//输出到控制台

        ///测试通过certContainer查询信息接口，此处无实际业务意义，仅为测试接口用
        console.time("通过certContainer查询证书信息接口耗时");
        var certInfo = parent.ca_key.getCertInfoByContainer({
            organizationID:globalInfo.OrgID,
            certContainer:loginQrResult.data.certContainer
        });
        console.timeEnd("通过certContainer查询证书信息接口耗时");
        if ("0" !== certInfo.code) {
            hideQrCodeDiv();
            $("#divQrCodeExp").show();
            displayError("通过certContainer查询证书信息失败:"+certInfo.msg);
            return;
        }
        console.log("通过certContainer查询证书信息成功:"+getCurrentTime());//输出到控制台

        //获取用户信息，证书信息返回上层页面做签名
        parent.caSign(loginQrResult.data,loginResult.data);
    } else if (loginQrResult.data.signStatus == "EXPIRE") {
        displayError("获取二维码扫码状态超时:"+JSON.stringify(loginQrResult));
        hideQrCodeDiv();
        $("#divQrCodeExp").show();
    } else if (loginQrResult.data.signStatus == "TOSIGN") {
        setTimeout("rollQrResult('"+ qrID +"')", globalInfo.scanQRRollInterval);
    } else {
        displayError("获取二维码扫码状态未知:"+JSON.stringify(loginQrResult));
        hideQrCodeDiv();
        $("#divQrCodeExp").show();
    }
}

//初始化状态
function initDiv() {
    hideQrCodeDiv();
    $("#divQrCode").hide();
    $("#divQrCodeExp").hide();
    $("#divQrCodeGen").show();
    displayError("");
}

//初始化二维码div状态
function hideQrCodeDiv() {
    $("#qrCodeImg").hide();
    $("#qrCodeText").hide();
    $("#qrCodeUrl").hide();
}

//展示错误信息
function displayError(errorInfo) {
    parent.displayError(errorInfo);
}