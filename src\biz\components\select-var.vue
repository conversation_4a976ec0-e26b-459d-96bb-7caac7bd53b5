<template>
  <hos-card class="select-var" shadow="never">
    <div slot="title" v-if="title">
      {{ title }} <tips v-if="titleTips!==''" :content="titleTips" />
    </div>
    <hos-input v-if="showSearch" v-model.trim="keyWord" :clearable="true" :placeholder="$t('搜索')" @keyup.enter.native="getVarList" @clear="getVarList">
      <hos-button slot="append" icon="hos-icon-search" @click="getVarList" />
    </hos-input>
    <empty-box v-if="!varList||varList.length==0" v-loading="loading" :style="{'height':varBoxHeight}" :description="$t('暂无数据')" />
    <template v-else>
      <draggable v-loading="loading" class="var-box"
         tag="div" :list="varList" :style="{'height':varBoxHeight}"
         v-bind="{
              group: {
                  name: 'var',
                  pull: 'clone',
                  put: false
              },
              sort: false,
              ghostClass: 'ghost'
          }"
          :clone="handleClone"
          @end="handleMoveEnd"
          @start="handleMoveStart">
          <transition-group name="slide" tag="div">
          <template v-for="(item, index) in varList">
            <div v-if="!dropVarSelected[item.name]" :key="index" class="var-item-box">
              <div class="var-item drag-hide">
                <span class="var-item-name" :title="item.name">{{ item.name }}</span>
                <span style="display: flex;align-items: center;">
                  <span v-if="showMissingRate && item.missing_rate" :title="$t('缺失率') + `${fixed(+item.missing_rate * 100)}%`" class="var-missing-rate">{{ fixed(+item.missing_rate * 100) }}%</span>
                  <hos-tag v-if="item.type==varType.QUALITATIVE_VAR" type="success" size="mini">{{ $t('定性') }}</hos-tag>
                  <hos-tag v-else-if="item.type==varType.QUANTITATIVE_VAR" type="primary" size="mini">{{ $t('定量') }}</hos-tag>
                  <hos-tag v-else-if="item.type==$t('异常')" type="danger" size="mini">{{ $t('异常') }}</hos-tag>
                  <hos-tag v-else type="info" size="mini">{{ item.type || '-' }}</hos-tag>
                </span>
              </div>
              <span class="drag-show"><hos-tag style="border: 1px dashed #f56c6c;" class="var-item" :type="varTypeColor[item.type] || 'info'" size="small" :closable="true">{{ item.name }}</hos-tag></span>
            </div>
          </template>
          </transition-group>
      </draggable>
    </template>
  </hos-card>
</template>

<script>
  import { getUUID } from "@/utils/index.js"
  import Draggable from "vuedraggable"
  import { fixed } from '@/utils/index.js'
  import { mapState } from "vuex"
  export default {
    components: {
      Draggable
    },
    props: {
      title: {
        type: String,
        default() {
          return this.$t('选择变量')
        }
      },
      showSearch: {
        type: Boolean,
        default() {
          return true
        }
      },
      // 获取变量列表的方法的参数
      getVarListParams: {
        type: Function,
      },
      varBoxHeight: {
        type: String,
        default() {
          return 'calc(100vh - 220px)'
        }
      },
      titleTips: {
        type: String,
        default() {
          return ''
        }
      },
      showMissingRate: {
        type: Boolean,
        default() {
          return false
        }
      }
    },
    data() {
      return {
        loading: false,
        keyWord: '',
        varList: [],
        varTypeColor: {
          "定性": "success",
          "定量": "primary",
          "异常": "danger"
        },
        varType: { // 变量类型
          QUALITATIVE_VAR: this.$t('定性'), // 定性变量
          QUANTITATIVE_VAR: this.$t('定量') // 定量变量
        }
      }
    },
    computed: {
      ...mapState({
        allDropVarMap: state => state.dropVar.allDropVarMap,
      }),
      // 已经拖入过的
      dropVarSelected() {
        const res = {}
        Object.keys(this.allDropVarMap).forEach(key => {
          const val = this.allDropVarMap[key]
          val.forEach(varItem => {
            res[varItem.name] = true
          })
        })
        return res
      }
    },
    watch: {},
    created() {
      this.init()
    },
    methods: {
      init() {
        this.getVarList()
      },
      getVarList() {
        let params = {
          varName: this.keyWord
        }
        if (this.getVarListParams) {
          const tmp = this.getVarListParams(params)
          if (tmp) {
            params = tmp
          }
        }
        this.loading = true
        this.$api('biz.analyzer.data-detail.getVarList',{subsetId:this.subsetId, ...params}).then(res => {
          this.varList = res.data || []
        }).finally(() => {
          this.loading = false
        })
      },
      handleClone(item) {
        return {
          ...item,
          uuid: getUUID()
        }
      },
      handleMoveEnd(evt) {},
      handleMoveStart(evt) {},
      handleMove() {
        return true
      },
      fixed
    }
  }

</script>
