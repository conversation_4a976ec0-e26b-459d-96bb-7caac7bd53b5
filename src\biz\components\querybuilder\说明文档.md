# 参数
- `loading`: *Boolean*, 
- `rules`: *Array*, 默认的查询条件
- `disabled`: *Boolean*, 是否禁用，会控制子组件按钮等功能是否可用
- `maxDepth`: *Number*, 条件树深度，默认为2
- `searchItemData`: *Object*, 查询项相关数据, 默认值如下,
  ```json
  {
      "cateItems": [],
      "fieldData": [],
      "commonSearchItems": []
  }
  ```
- `singleSelectCate`: *Object*, 绑定下拉的值
- `allowDelFirst`: *Boolean*, 是否允许删除第一个条件
- `actived`: *Boolean*, 设置组件初始化时是否为激活状态

# 方法
可使用ref直接调用
- `getQuery`: 获取条件
- `getQueryDesc`: 获取条件描述
- `getQueryHtml`: 转换中文描述
- `setQuery`: 设置条件
- `resetQuery`: 重置
- `vaildQuery`: 校验条件
- `clearQuery`: 清空条件