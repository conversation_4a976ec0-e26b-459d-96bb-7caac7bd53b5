/**
 * 二次封装ajax请求，拦截响应做自动刷新token的操作
 */

if (typeof BroadcastChannel == "function") {
  var channel = new BroadcastChannel("csm-token-channel"); // 监听浏览器主tab页签refreshToken的变化，同步到当前页签中
  channel.onmessage = function (event) {
    // 根据接收到的消息更新 sessionStorage
    sessionStorage.setItem("pro__refreshToken", event.data);
  };
}

// 是否正在刷新的标记
var isRefreshing = false;
// 重试队列，每一项将是一个待执行的函数形式
var requestQueue = [];

function responseInterceptor(option, response, originSuccessHandler) {
  if (response.code == "4001") {
    if (!isRefreshing) {
      isRefreshing = true;
      doRefreshToken(function () {
        isRefreshing = false;
        // 已经刷新了token，将所有队列中的请求进行重试
        $.each(requestQueue, function (index, cb) {
          cb();
        });
        // 重试完清空这个队列
        requestQueue = [];
        $.ajax($.extend({}, option, { success: originSuccessHandler }));
      });
    } else {
      requestQueue.push(function () {
        $.ajax($.extend({}, option, { success: originSuccessHandler }));
      });
    }
  } else {
    originSuccessHandler(response);
  }
}

function getToken(key) {
  // if (!key) {
  //   key = "pro__access-token";
  // }
  // var accessTokenStr = sessionStorage.getItem(key);
  // if (accessTokenStr) {
  //   try {
  //     var res = JSON.parse(accessTokenStr).value;
  //     return res;
  //   } catch (e) {
  //     return accessTokenStr;
  //   }
  // } else {
  //   return null;
  // }
  return localStorage.getItem('his-api-access-token')
}

function doRefreshToken(callback) {
  var option = {
    url: context_path + "/security/token?grantType=refreshToken",
    type: "post",
    data: JSON.stringify({
      grantType: "refreshToken",
      refreshToken: getToken("pro__refreshToken")
    }),
    beforeSend: function (XMLHttpRequest) {
      XMLHttpRequest.setRequestHeader("Access-Token", getToken());
    },
    processData: false, // Important! Don't process the data
    contentType: false, // Important! Set contentType to false
    success: function (requestData) {
      if (requestData && requestData.code == "200" && requestData.data) {
        // 成功后...
        sessionStorage.setItem(
          "pro__refreshToken",
          JSON.stringify({
            expire: null,
            value: requestData.data.refreshToken
          })
        );
        sessionStorage.setItem("pro__access-token", JSON.stringify({ expire: null, value: requestData.data.accessToken }));
        callback();
      } else {
        console.log({
          message: "刷新token失败",
          type: "error"
        });
      }
    },
    error: function (responseData) {
      console.log({
        message: "刷新token失败",
        type: "error"
      });
    }
  };
  $.ajax(option);
}

function api(option) {
  // var originSuccessHandler = option.success;
  // option.success = function (response) {
  //   responseInterceptor(option, response, originSuccessHandler);
  // };
  $.ajax(option);
}
function ajaxGet(option) {
  api({
    url: context_path + option.url,
    type: "get",
    dataType: "json",
    async: true,
    beforeSend: function (XMLHttpRequest) {
      XMLHttpRequest.setRequestHeader("apiAccessToken", getToken());
    },
    success: function (requestData) {
      if (requestData.code == "200") {
        option.success(requestData.data);
      } else {
        if ($.isFunction(option.error)) {
          option.error();
        }
        alert(requestData.msg);
      }
    }
  });
}
function ajaxPost(option) {
  api({
    url: context_path + option.url,
    type: "post",
    dataType: "json",
    contentType: "application/json",
    data: JSON.stringify(option.data),
    async: true,
    beforeSend: function (XMLHttpRequest) {
      XMLHttpRequest.setRequestHeader("apiAccessToken", getToken());
    },
    success: function (requestData) {
      if (requestData.code == "200") {
        option.success(requestData.data);
      } else {
        if ($.isFunction(option.error)) {
          option.error();
        }
        alert(requestData.msg);
      }
    }
  });
}
