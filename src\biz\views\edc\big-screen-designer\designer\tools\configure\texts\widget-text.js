/*
 * @Descripttion: 文本json文件
 * @version:
 * @Author: qianlishi
 * @Date: 2021-08-29 06:52:13
 * @LastEditors: qianlishi <EMAIL>
 * @LastEditTime: 2023-01-11 13:18:36
 */
export const widgetText = {
  code: 'widget-text',
  type: 'text',
  tabName: '文本栏',
  label: '文本',
  icon: 'csm-wenben1',
  options: {
    // 配置
    setup: [
      {
        type: 'el-input-text',
        label: '图层名称',
        name: 'layerName',
        required: false,
        placeholder: '',
        value: '文本框'
      },
      {
        type: 'el-input-textarea',
        label: '文本内容',
        name: 'text',
        required: false,
        placeholder: '',
        value: '文本框'
      },
      {
        type: 'el-input-number',
        label: '字体字号',
        name: 'fontSize',
        required: false,
        placeholder: '',
        value: '26'
      },
      {
        type: 'vue-color',
        label: '字体颜色',
        name: 'color',
        required: false,
        placeholder: '',
        value: '#FAD400'
      },
      {
        type: 'el-input-number',
        label: '字体间距',
        name: 'letterSpacing',
        required: false,
        placeholder: '',
        value: '0'
      },
      {
        type: 'vue-color',
        label: '字体背景',
        name: 'background',
        required: false,
        placeholder: '',
        value: 'rgba(115,170,229,.0)'
      },
      {
        type: 'el-select',
        label: '文字粗细',
        name: 'fontWeight',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: 'normal', name: '正常' },
          { code: 'bold', name: '粗体' },
          { code: 'bolder', name: '特粗体' },
          { code: 'lighter', name: '细体' }
        ],
        value: 'normal'
      },
      {
        type: 'el-select',
        label: '对齐方式',
        name: 'textAlign',
        required: false,
        placeholder: '',
        selectOptions: [
          { code: 'center', name: '居中' },
          { code: 'left', name: '左对齐' },
          { code: 'right', name: '右对齐' }
        ],
        value: 'center'
      },
      {
        type: 'el-switch',
        label: '识别换行符',
        name: 'whiteSpace',
        required: false,
        placeholder: '',
        value: true
      }
    ],
    // 数据
    data: [
      {
        type: 'el-radio-group',
        label: '数据类型',
        name: 'dataType',
        require: false,
        placeholder: '',
        selectValue: true,
        selectOptions: [
          {
            code: 'staticData',
            name: '静态数据'
          },
          {
            code: 'dynamicData',
            name: '动态数据'
          }
        ],
        value: 'staticData'
      },
      {
        type: 'el-input-number',
        label: '刷新时间(毫秒)',
        name: 'refreshTime',
        relactiveDom: 'dataType',
        relactiveDomValue: 'dynamicData',
        value: 600000
      },
      {
        type: 'el-button',
        label: '静态数据',
        name: 'staticData',
        required: false,
        placeholder: '',
        relactiveDom: 'dataType',
        relactiveDomValue: 'staticData',
        value: [{value:'文本框'}]
      },
      // {
      //   type: 'dycustComponents',
      //   label: '',
      //   name: 'dynamicData',
      //   required: false,
      //   placeholder: '',
      //   relactiveDom: 'dataType',
      //   relactiveDomValue: 'dynamicData',
      //   chartType: 'widget-text',
      //   dictKey: 'TEXT_PROPERTIES',
      //   value: {}
      // }
      {
        type: 'dynamicDataMapper',
        label: '数据映射',
        name: 'dynamicData',
        required: true,
        placeholder: '',
        relactiveDom: 'dataType',
        relactiveDomValue: 'dynamicData',
        chartType: 'widget-text',
        value: {},
        params: [
          {
            type: 'el-input-text',
            label: '表名称',
            name: 'table',
            required: false,
            placeholder: '',
            value: ''
          },
          {
            type: 'el-input-text',
            label: '计数字段',
            name: 'field',
            required: false,
            placeholder: '',
            value: ''
          },
          {
            type: 'el-select',
            label: '计数方法',
            name: 'type',
            required: false,
            placeholder: '',
            selectOptions: [
              { code: 'count', name: '总数' },
              { code: 'max', name: '最大值' },
              { code: 'min', name: '最小值' },
              { code: 'avg', name: '平均值' },
              { code: 'sum', name: '求和' },
              { code: 'first', name: '第一条' },
              { code: 'last', name: '最后一条' }
            ],
            value: ''
          }
        ]
      }
    ],

    // 坐标
    position: [
      {
        type: 'el-input-number',
        label: '左边距',
        name: 'left',
        required: false,
        placeholder: '',
        value: 0
      },
      {
        type: 'el-input-number',
        label: '上边距',
        name: 'top',
        required: false,
        placeholder: '',
        value: 0
      },
      {
        type: 'el-input-number',
        label: '宽度',
        name: 'width',
        required: false,
        placeholder: '该容器在不缩放时大屏中的宽度',
        value: 100
      },
      {
        type: 'el-input-number',
        label: '高度',
        name: 'height',
        required: false,
        placeholder: '该容器在不缩放时大屏中的高度',
        value: 40
      }
    ]
  }
}
