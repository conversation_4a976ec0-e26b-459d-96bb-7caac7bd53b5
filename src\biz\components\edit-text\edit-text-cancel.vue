<template>
  <span class="edit-text text-content">
    <span v-if="!editing" class="input-text" @click="edit">
      {{ dataStr }}
      <hos-button type="text" style="height:unset;padding:0;min-width:unset;border:none;" icon="hos-icom-edit" title="编辑" />
    </span>
    <hos-input v-if="editing" ref="dataStr" v-model="inputStr" style="width: unset;" type="text"
        :placeholder="inputStr" size="mini" @blur="save">
        <span slot="append">
          <hos-button icon="hos-icom-cancel" title="取消" style="min-width:unset;" @mousedown.native="cancel" />
        </span>
    </hos-input>
    <slot />
  </span>
</template>
<script>
export default {
  name: 'EditText',
  props: {
    dataStr: {
      type: String,
      default() {
        return '点击修改'
      }
    },
    isRequired: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      editing: false,
      inputStr: '',
      isCancel: false,
    }
  },
  methods: {
    edit() {
      this.editing = true
      this.inputStr = this.dataStr
      this.$nextTick(() => {
        this.$refs['dataStr'].focus()
      })
    },
    cancel() {
      this.inputStr = this.dataStr
      this.isCancel = true
      this.editing = false
    },
    save() {
      if (this.isCancel) {
        this.isCancel = false
        return
      }
      if (this.inputStr || !this.isRequired) {
        this.editing = false
        if (this.inputStr != this.dataStr) {
          this.$emit('after-save', this.inputStr)
        }
      } else if (this.isRequired) {
        this.$message({
          message: '请填写内容',
          type: 'warning',
          duration: 1000
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .edit-text {
    &.text-content {
      /* text-align: center; */
    }

    .input-text {
      background-color: #fff;
      padding: 3px;
      cursor: pointer;
    }
  }
</style>