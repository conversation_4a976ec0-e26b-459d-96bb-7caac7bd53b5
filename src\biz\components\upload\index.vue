<!--
  * @FileDescription：基于HOS upload组件样式,封装的下载模板以及上传excel导入数据组件.
-->
<template>
  <div :loading="loading" class="upload-com" :style="customStyle">
    <div v-if="!downloadTemplateOption.isHide" class="download-template">
      <i class="hos-icon-info icon-style"></i>
      <span>{{ $t('上传文件需要使用最新模板，点击此处') }}</span>
      <span @click="importTemplate()" class="now-down">{{ $t('立即下载') }}</span>
    </div>
    <div class="select-import-type">
      <div v-if="uploadFileOption.uploadTypeState">
        <span class="three-type">{{ $t('请选择操作类型') }}</span>
        <hos-radio v-model="insertType" class="three-type" label="onlyInsert">{{
          $t('添加新数据（只做新增操作，按模板中标记的唯一标识导入表中不存在的数据）')
        }}</hos-radio>
        <hos-radio v-model="insertType" class="three-type" label="onlyUpdate">{{
          $t('更新已有数据（只做更新操作，按模板中标记的唯一标识更新表中已存在的数据）')
        }}</hos-radio>
        <hos-radio v-model="insertType" class="three-type" label="insertOrUpdate">{{
          $t('更新已有数据并添加新数据（以上两种情况同时执行）')
        }}</hos-radio>
      </div>
      <!-- 提示信息 -->
      <div class="download-tips">
        <i class="hos-icon-info tips-style"></i>
        <span>{{ $t('提示：仅允许导入 “xls” 或 “xlsx” 格式文件') }}</span>
      </div>
      <hos-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        action=""
        :http-request="uploadRequest"
        :on-remove="handleRemove"
        :on-success="handleFileSuccess"
        :on-change="handleChange"
        drag
        :file-list="fileList"
        :auto-upload="false"
      >
        <i :class="isSimple == 1 ? 'hos-icom-upload-cloud' : 'hos-icon-upload'"></i>
        <div class="hos-upload__text">
          {{ $t('将文件拖到此处，或') }}<span>{{ $t('点击上传') }}</span>
        </div>
      </hos-upload>
      <div class="import-btn" v-if="btnImport">
        <hos-button @click="handleImportFile" type="primary" :disabled="stateImportBtn">{{
          $t('导入文件')
        }}</hos-button>
      </div>
    </div>
    <!-- 错误数据成功失败条数显示 -->
    <!-- <div class="success-or-error-info">
      <div class="import-msg" v-if="isStart">
        <div class="icon-style">
          <hos-image :src="require('@base/assets/images/upload/import_info.png')"></hos-image>
        </div>
        <span class="font-style info-color">{{ $t('请导入数据') }}</span>
      </div>
    </div> -->

    <!-- <div slot="footer" class="dialog-footer">
      <hos-button @click="confirm" type="primary">{{ $t('确定') }}</hos-button>
    </div> -->
  </div>
</template>
<script>
import { getToken } from '@base/utils/base/token-util'
import axios from 'axios'
import Vue from 'vue'
import { mapState } from 'vuex'
import { uid } from '@base/components/hos-biz/utils/store-config'
import { getCurrentLocale } from '@base/utils/i18n/i18n-util'
import UserConstant from '@base/constant/user-constant'
export default {
  props: {
    // 自定义样式,比如宽度,居中样式等 'width: 50%;margin:auto;'
    customStyle: {
      require: false,
      type: String,
      default() {
        return ''
      }
    },
    // 下载模板的配置
    downloadTemplateOption: {
      require: true,
      type: Object,
      default() {
        return {
          isHide: true, // 是否隐藏下载
          method: 'GET', // 请求方式
          apiUrl: '', // 下载模板Api对应的接口地址
          params: {}, // 拼到请求路径上的参数
          data: {}, // body参数 POST请求时用到
          templateFileName: this.$t('批量导入模板')
        }
      }
    },
    // 上传的配置
    uploadFileOption: {
      require: true,
      type: Object,
      default() {
        return {
          uploadTypeState: false, // 是否需要选择导入数据类型,目前只有新增场景,后续看接口适配情况
          method: 'POST',
          apiUrl: '',
          params: {},
          data: {}
        }
      }
    },
    tableUid: {
      require: true,
      type: String
    },
    beforeDownload: {
      require: false,
      type: Function
    },
    // 处理需要追加的参数，设置到uploadFileOption上即可
    beforeUpload: {
      require: false,
      type: Function
    },
    afterUpload: {
      require: false,
      type: Function
    },
    // 回调刷新列表
    callback: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      insertType: 'onlyInsert',
      btnImport: false,
      isStart: true,
      stateImportBtn: false,
      isSimple: process.env.VUE_APP_SIMPLE_ONCE,
      loading: false,
      fileList: [],
      // 错误数据下载url
      downUrl: ''
    }
  },
  computed: {
    ...mapState({
      sUID: (state) => state.dialog[uid]
    })
  },
  methods: {
    // 下载模板
    async importTemplate() {
      if (this.beforeDownload) {
        this.beforeDownload()
      } else {
        this.downloadTemplate()
      }
    },
    downloadTemplate() {
      // 拼出正确的请求url
      let url
      if (this.downloadTemplateOption.apiUrl[0] == '/') {
        url = `${this.$baseUrl}` + this.downloadTemplateOption.apiUrl
      } else {
        url = `${this.$baseUrl}/` + this.downloadTemplateOption.apiUrl
      }
      const token = getToken()
      axios({
        url,
        ...this.downloadTemplateOption,
        responseType: 'blob',
        headers: {
          'access-token': token,
          'client-ip': Vue.ls.get(UserConstant.IP),
          'client-mac': Vue.ls.get(UserConstant.Mac),
          language: getCurrentLocale()
        }
      }).then(
        async (response) => {
          if (response.data.size) {
            this.BlobDownLoad(response)
          } else {
            this.$message({
              message: this.$t('未获取到文件'),
              type: 'error'
            })
          }
        },
        (error) => {
          console.log(error)
        }
      )
    },
    uploadRequest(params) {
      const formData = new FormData()
      formData.append('file', params.file)
      if (this.beforeUpload) {
        this.beforeUpload()
      }
      // 判断除了file字段是否在formData中还有其他字段
      const otherData = this.uploadFileOption.data
      if (otherData && JSON.stringify(otherData) !== '{}') {
        for (let key in otherData) {
          formData.append(key, otherData[key])
        }
      }

      // 处理url
      let url
      if (this.uploadFileOption.apiUrl[0] == '/') {
        url = `${this.$baseUrl}` + this.uploadFileOption.apiUrl
      } else {
        url = `${this.$baseUrl}/` + this.uploadFileOption.apiUrl
      }

      // 判断是否需要在url上拼上其他参数
      const otherParams = this.uploadFileOption.params
      if (otherParams && JSON.stringify(otherParams) !== '{}') {
        var str = '?'
        for (let key in otherParams) {
          str += key + '=' + otherParams[key] + '&'
        }
        url = url + str
      }

      // 是否在url或者formData中拼上上传类型参数
      // TODO:后续根据后台接口调整且暂定字段名称为type
      if (this.uploadFileOption.uploadTypeState) {
        // 两种都加上
        url += `type=${this.insertType}`
        formData.append('type', this.insertType)
      }

      const token = getToken()
      axios({
        url,
        method: this.uploadFileOption.method,
        data: formData,
        headers: {
          'access-token': token,
          'client-ip': Vue.ls.get(UserConstant.IP),
          'client-mac': Vue.ls.get(UserConstant.Mac),
          language: getCurrentLocale()
        }
      })
        .then(() => {
          // 成功后...
          this.$message.success('操作成功')
          this.$refs.upload.clearFiles()
          this.btnImport = false
          this.stateImportBtn = false
          if (this.afterUpload) {
            this.afterUpload()
          }
        })
        .catch(() => {
          // 上传失败手动清空文件列表
          this.$refs.upload.clearFiles()
        })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
      if (fileList.length) {
        this.btnImport = true
      }
    },
    // 移除文件
    handleRemove(file, fileList) {
      if (!fileList.length) {
        this.btnImport = false
        this.stateImportBtn = false
        this.isStart = true
        this.fileList = fileList
      }
    },
    // 文件上传成功处理
    handleImportFile() {
      const fileName = this.fileList[0].name.substring(this.fileList[0].name.lastIndexOf('.') + 1)
      const extension = fileName === 'xls'
      const extension2 = fileName === 'xlsx'
      if (extension || extension2) {
        this.$refs.upload.submit()
        this.stateImportBtn = true
      } else {
        this.$message({
          message: this.$t('提示：仅允许导入 “xls” 或 “xlsx” 格式文件'),
          type: 'warning'
        })
      }
    },
    handleFileSuccess(response, file, fileList) {
      if (response.code === '200' && response.success) {
        this.$store.commit('UPDATE_TABLE', {
          _uid: this.tableUid
        })
        this.callback()
        if (!response.data.success) {
          this.isStart = false
          this.downUrl = response.data.url
        } else {
          this.isStart = false
        }
      } else {
        this.$message.error(response.msg)
        this.fileList = []
      }
    },
    //确定按钮
    confirm() {
      if (!this.fileList.length) {
        this.$message.info(this.$t('请上传文件！'))
        return
      }
      this.$store.commit('CLOSE_DIALOG', {
        _uid: this.sUID
      })
      this.loading = false
      this.$store.commit('UPDATE_TABLE', {
        _uid: this.tableUid
      })
      if (this.afterUpload) {
        this.afterUpload()
      }
    },
    BlobDownLoad(res) {
      let blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const fileName = this.downloadTemplateOption.templateFileName
      const href = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = href
      a.download = fileName
      a.click()
      a.remove()
      URL.revokeObjectURL(a.href)
    }
  }
}
</script>

<style lang="scss">
.hos-radio {
  white-space: normal;
}

.upload-com {
  padding: 0 10px;
  .download-template {
    margin-top: 10px;
    height: 32px;
    line-height: 32px;
    background: #e8effd;
    border: 1px solid #90b3f8;
    color: #4781f3;
    border-radius: 2px;
    .icon-style {
      padding: 0 5px 0 10px;
    }
    .now-down {
      border-bottom: 1px solid #4781f3;
      cursor: pointer;
      margin-left: 6px;
    }
  }
  .select-import-type {
    border: 1px solid #dbdbdb;
    margin: 10px 0;
    padding: 10px 10px 0 10px;
    border-radius: 4px;
    .three-type {
      display: block;
      padding-bottom: 10px;
    }
    .download-tips {
      height: 32px;
      line-height: 32px;
      background: #fdeaea;
      color: #f25757;
      margin-bottom: 10px;
      border-radius: 2px;
      .tips-style {
        padding: 0 9px 0 10px;
      }
    }
    .hos-upload {
      width: 100%;
      margin-bottom: 10px;
      .hos-upload-dragger {
        height: 200px;
        width: 100%;
        border: 1px dashed #dae2ee;
        border-radius: 4px;
        .hos-upload__text {
          color: #000;
          span {
            color: #4781f3;
          }
        }
      }
    }
    .hos-upload-list__item,
    .hos-upload-list__item:first-child {
      margin-top: 0;
    }
    .import-btn {
      text-align: center;
      padding: 10px 0;
    }
  }
  .success-or-error-info {
    height: 120px;
    border: 1px solid #dbdbdb;
    margin-bottom: 10px;
    border-radius: 4px;
    .import-msg {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .icon-style {
        padding-right: 10px;
        .hos-image {
          width: 60px;
          height: 60px;
        }
      }
      .font-style {
        height: 24px;
        line-height: 24px;
        font-weight: 500;
        font-size: 16px;
      }
      .info-color {
        color: #4781f3;
      }
      .err-color {
        color: #fe5f5f;
      }
      .success-color {
        color: #5db42f;
      }
      .err-data-down {
        color: #4781f3;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }
}
</style>
