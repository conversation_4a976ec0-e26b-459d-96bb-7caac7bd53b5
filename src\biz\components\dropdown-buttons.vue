<template>
  <hos-dropdown ref="dropdown" trigger="click" :hide-on-click="false" @command="handleCommand">
    <hos-button type="primary">
      {{ title }}
      <tips style="margin-left:5px;" icon-color="#fff" placement="top" :content="$t('检索结果导出到excel')" />
      <i class="hos-icon-arrow-down hos-icon--right" />
    </hos-button>
    <hos-dropdown-menu slot="dropdown" class="btn-dropdown">
      <template v-for="(btn, i) in buttons">
        <hos-dropdown-item :key="i + '_a'" v-if="!btn.children" :command="btn.value">
          {{ btn.title }}
        </hos-dropdown-item>
        <hos-dropdown-item style="padding:0;" :key="i + '_b'" v-else>
          <NestedDropdown :item="btn" @close="closeDrop" />
        </hos-dropdown-item>
      </template>
    </hos-dropdown-menu>
  </hos-dropdown>
</template>

<script>
import NestedDropdown from './NestedDropdown.vue';

export default {
  name: 'DropdownButtons',
  components: { NestedDropdown },
  props: {
    title: {
      type: String,
      default: () => {
        return this.$t('更多操作');
      },
    },
    buttons: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  methods: {
    handleCommand(command) {
      this.$emit('clickBtn', command);
    },
    closeDrop(command) {
      if (command !== undefined) {
        this.$emit('clickBtn', command);
      }
    },
  },
};
</script>

<style scoped lang="scss">

.hos-dropdown-menu__item:focus {
  .hos-dropdown-link {
    background-color: #28ba05;
    color: #fff;
  }
}

.hos-dropdown-link {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #000;
  &:hover,
  &:focus {
    background-color: #28ba05;
    color: #fff;
  }
}
</style>
