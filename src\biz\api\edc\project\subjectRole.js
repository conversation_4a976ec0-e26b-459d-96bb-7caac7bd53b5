
export const queryListApi = (params) => {
  return {
    url: 'edc/subject-role/page',
    method: 'get',
    params,
  }
}

export const queryAllApi = (params) => {
  return {
    url: 'edc/subject-role/list',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/subject-role/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/subject-role/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/subject-role/insert',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/subject-role/update',
    method: 'post',
    data
  }
}

// 保存项目角色权限
export const bindProjectRoleAuth = (data) => {
  return {
    url: 'edc/role-auth/bind',
    method: 'post',
    data
  }
}

// 获取项目角色权限
export const getProjectRoleAuth = (params) => {
  return {
    url: 'edc/role-auth/list-by-type',
    method: 'get',
    params
  }
}

// 获取页面元素权限树
export const getProjectRoleAuthTree = (params) => {
  return {
    url: 'edc/role-auth/tree-menu-for-role',
    method: 'get',
    params
  }
}

// 回显页面元素权限
export const getProjectRolePageAuth = (params) => {
  return {
    url: 'edc/role-auth/page-auth',
    method: 'get',
    params
  }
}

// 绑定页面元素权限
export const bindProjectRolePageAuth = (data) => {
  return {
    url: `edc/role-auth/bind-page/${data.roleId}`,
    method: 'post',
    data:data.permList
  }
}

// 获取接口权限树
export const getProjectRoleInterfaceTree = (params) => {
  return {
    url: 'edc/role-auth/tree-interface-for-role',
    method: 'get',
    params
  }
}

// 获取数据权限树
export const getProjectRoleDataTree = (params) => {
  return {
    url: '/edc/role-auth/tree-scope-for-role',
    method: 'get',
    params
  }
}