import {parseTime} from '@/utils/index'
/**
 * 近一年
 */
export function getLastYear() {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const dateObj = {}
  dateObj.now = year + "-" + month + "-" + day
  // const nowMonthDay = new Date(year, month, 0).getDate() // 当前月的总天数
  // 年数往前推一年
  const lastYearDay = new Date((year - 1), parseInt(month), 0).getDate()
  if (lastYearDay < day) {
    dateObj.last = (year - 1) + "-" + month + "-" + lastYearDay
  } else {
    dateObj.last = (year - 1) + "-" + month + "-" + day
  }
  return dateObj
}

/**
 * 近三个月
 */
export function getLast3Month() {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1 // 0-11表示1-12月
  const day = now.getDate()
  const dateObj = {}
  dateObj.now = year + "-" + month + "-" + day
  const nowMonthDay = new Date(year, month, 0).getDate() // 当前月的总天数
  if (month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
    const last3MonthDay = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate() // 3个月前所在月的总天数
    if (last3MonthDay < day) { // 3个月前所在月的总天数小于现在的天日期
      dateObj.last = (year - 1) + "-" + (12 - (3 - month)) + "-" + last3MonthDay
    } else {
      dateObj.last = (year - 1) + "-" + (12 - (3 - month)) + "-" + day
    }
  } else {
    const last3MonthDay = new Date(year, (parseInt(month) - 3), 0).getDate() // 3个月前所在月的总天数
    if (last3MonthDay < day) { // 3个月前所在月的总天数小于现在的天日期
      if (day < nowMonthDay) { // 当前天日期小于当前月总天数,2月份比较特殊的月份
        dateObj.last = year + "-" + (month - 3) + "-" + (last3MonthDay - (nowMonthDay - day))
      } else {
        dateObj.last = year + "-" + (month - 3) + "-" + last3MonthDay
      }
    } else {
      dateObj.last = year + "-" + (month - 3) + "-" + day
    }
  }
  return dateObj
}

// 近一个月
export function getLastMonth() {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1 // 0-11表示1-12月
  const day = now.getDate()
  const dateObj = {}
  dateObj.now = year + "-" + month + "-" + day
  const nowMonthDay = new Date(year, month, 0).getDate() // 当前月的总天数
  if (month - 1 <= 0) {
    // eslint-disable-next-line no-irregular-whitespace
    // 如果是1月，年数往前推一年<br>
    dateObj.last = (year - 1) + "-" + 12 + "-" + day
  } else {
    const lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
    if (lastMonthDay < day) { // 1个月前所在月的总天数小于现在的天日期
      if (day < nowMonthDay) { // 当前天日期小于当前月总天数
        dateObj.last = year + "-" + (month - 1) + "-" + (lastMonthDay - (nowMonthDay - day))
      } else {
        dateObj.last = year + "-" + (month - 1) + "-" + lastMonthDay
      }
    } else {
      dateObj.last = year + "-" + (month - 1) + "-" + day
    }
  }
  return dateObj
}

// 近一周
export function getLastWeek() {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1 // 0-11表示1-12月
  const day = now.getDate()
  const dateObj = {}
  dateObj.now = year + "-" + month + "-" + day
  if (day - 7 <= 0) { // 如果在当月7日之前
    const lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate() // 1周前所在月的总天数
    if (month - 1 <= 0) { // 如果在当年的1月份
      dateObj.last = (year - 1) + "-" + 12 + "-" + (31 - (7 - day))
    } else {
      dateObj.last = year + "-" + (month - 1) + "-" + (lastMonthDay - (7 - day))
    }
  } else {
    dateObj.last = year + "-" + month + "-" + (day - 7)
  }
  return dateObj
}

/**
 * 获取指定时间在指定间隔天数之前或之后的时间
 * @param {Date} dateNow :Date类
 * @param {number} intervalDays ：间隔天数
 * @param {Boolean} bolPastTime  ：Boolean,判断在参数date之前，还是之后，
 */
export function getDateRange(dateNow, intervalDays, bolPastTime) {
  const oneDayTime = 24 * 60 * 60 * 1000
  const list = []
  let lastDay

  if (bolPastTime == true) {
    lastDay = new Date(dateNow.getTime() - intervalDays * oneDayTime)
    list.push(formateDate(lastDay))
    list.push(formateDate(dateNow))
  } else {
    lastDay = new Date(dateNow.getTime() + intervalDays * oneDayTime)
    list.push(formateDate(dateNow))
    list.push(formateDate(lastDay))
  }
  return list
}

/**
 * 格式化指定时间为字符串 yyyy-MM-dd
 * @param {Date} time 指定时间
 * @returns {string} 返回值：字符串格式时间yyyy-MM-dd
 */
export function formateDate(time = new Date(Date.now())) {
  // eslint-disable-next-line
  let { year, month, day } = formateDate2Obj(time)
  if (month < 10) {
    month = "0" + month
  }

  if (day < 10) {
    day = "0" + day
  }

  return year + "-" + month + "-" + day + ""
}

/**
 * 日期字符串转Date类型
 * @param {string} dateStr 日期字符串类型
 * @returns {Date} 返回值：日期类型
 */
export function dateStr2Date(dateStr) {
  if (!dateStr) {
    return new Date(Date.now())
  } else if (typeof dateStr !== "string") {
    throw new Error("参数dateStr需要是字符串类型")
  } else {
    const arr = dateStr.split(" ")
    let date_arr, time_arr
    if (arr.length == 2) {
      date_arr = arr[0].split("-")
      time_arr = arr[1].split(":")
    } else {
      date_arr = arr[0].split("-")
      time_arr = [0, 0, 0]
    }
    return new Date(+date_arr[0], +date_arr[1] - 1, +date_arr[2], +time_arr[0], +time_arr[1], +time_arr[2])
  }
}

/**
 * 格式化指定时间为时间对象
 * @param {Date} time 指定时间
 * @returns { {day:number,month:number,year:number} } 返回值：时间对象 (注意:month从1开始到12)
 */
export function formateDate2Obj(time = new Date(Date.now())) {
  const year = time.getFullYear()
  const month = time.getMonth() + 1
  const day = time.getDate()
  return {
    year,
    month,
    day
  }
}

/**
 * 获取指定时间在指定间隔时间下的开始时间和结束时间
 * @param { Date } date --指定时间
 * @param { {intervalTime:number,intervalTimeUnit:number|string} } timeInterval
 *       ----时间间隔（intervalTime:间隔值，intervalTimeUnit:间隔单位 1:天,2:周,3:月,4:年）
 * @return { Array<string> } 返回值：[开始时间,当前时间,结束时间]
 */
export function getDateRangeArrayByTimeInterval(date, timeInterval) {
  if (typeof date === "string") {
    date = dateStr2Date(date)
  }
  let dateStartStr = ""
  const dateNowStr = formateDate(date)
  const dateNowObj = formateDate2Obj(date)
  let dateEndStr = ""
  if (timeInterval.intervalTimeUnit == "1") { // 天
    const interval = 24 * 60 * 60 * 1000 * timeInterval.intervalTime
    dateStartStr = formateDate(new Date(date.getTime() - interval))
    dateEndStr = formateDate(new Date(date.getTime() + interval))
  } else if (timeInterval.intervalTimeUnit == "2") { // 周
    const interval = 7 * 24 * 60 * 60 * 1000 * timeInterval.intervalTime
    dateStartStr = formateDate(new Date(date.getTime() - interval))
    dateEndStr = formateDate(new Date(date.getTime() + interval))
  } else if (timeInterval.intervalTimeUnit == "3") { // 月
    const totalMonth = 12 * dateNowObj.year + dateNowObj.month - 1
    const startObj = {
      year: parseInt((totalMonth - timeInterval.intervalTime) / 12),
      month: (totalMonth - timeInterval.intervalTime) % 12,
    }
    const endObj = {
      year: parseInt((totalMonth + timeInterval.intervalTime) / 12),
      month: (totalMonth + timeInterval.intervalTime) % 12,
    }
    dateStartStr = formateDate(new Date(startObj.year, startObj.month, dateNowObj.day))
    dateEndStr = formateDate(new Date(endObj.year, endObj.month, dateNowObj.day))
  } else if (timeInterval.intervalTimeUnit == "4") { // 年
    dateStartStr = formateDate(new Date(dateNowObj.year, dateNowObj.month - 1, dateNowObj.day))
    dateEndStr = formateDate(new Date(dateNowObj.year + timeInterval.intervalTime, dateNowObj.month - 1, dateNowObj.day))
  }
  return [dateStartStr, dateNowStr, dateEndStr]
}


// 以下摘自hosui/src/utils/date-util.js
// ------------------- start ------------------------

export const range = function(n) {
  // see https://stackoverflow.com/questions/3746725/create-a-javascript-array-containing-1-n
  return Array.apply(null, {length: n}).map((_, n) => n);
};

// 获取当前日期所在月份的第一天
export const getFirstDayOfMonth = function(date) {
  const temp = new Date(date.getTime());
  temp.setDate(1);
  return temp.getDay();
};

// 获取当前日期上个月份的最后一天
export const getPrevMonthLastDays = (date, amount) => {
  if (amount <= 0) return [];
  const temp = new Date(date.getTime());
  temp.setDate(0);
  const lastDay = temp.getDate();
  return range(amount).map((_, index) => lastDay - (amount - index - 1));
};

export const getMonthDays = (date) => {
  const temp = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  const days = temp.getDate();
  return range(days).map((_, index) => index + 1);
};

export const validateRangeInOneMonth = function(start, end) {
  return (start.getMonth() === end.getMonth()) && (start.getFullYear() === end.getFullYear());
};

// 以上摘自hosui/src/utils/date-util.js
// ------------------- end ------------------------

/**
 * 根据日历中选中的日期，获取当前日历展示月展示的所有天（可能包含上个月末的日期以及下个月初的日期）
 * (参考了hosui源码中的日历组件 hosui\packages\calendar\src\date-table.vue 中的rows属性计算规则)
 * @param {Date} selectedDate 日历中选中的日期
 * @param {Number} firstDayOfWeek calendar组件设置的周起始日, 可设置为1-7，默认为1
 */
export const getCalendarDays = function(selectedDate,firstDayOfWeek=1){
  const date = selectedDate
  let firstDay = getFirstDayOfMonth(date);
  firstDay = firstDay === 0 ? 7 : firstDay;
  const offset = (7 + firstDay - firstDayOfWeek) % 7;
  const prevMonthDays = getPrevMonthLastDays(date, offset).map(day => ({
    text: day,
    type: 'prev',
    date: getFormateDate(day,'prev',date)
  }));
  const currentMonthDays = getMonthDays(date).map(day => ({
    text: day,
    type: 'current',
    date: getFormateDate(day,'current',date)
  }));
  let days = [...prevMonthDays, ...currentMonthDays];
  const nextMonthDays = range(42 - days.length).map((_, index) => ({
    text: index + 1,
    type: 'next',
    date: getFormateDate(index + 1,'next',date)
  }));
  days = days.concat(nextMonthDays);
  return days
}

export function getFormateDate(day, type, curDate) {
  if (!day || ['prev', 'current', 'next'].indexOf(type) === -1) {
    throw new Error('invalid day or type');
  }
  let prefix = curMonthDatePrefix(curDate);
  if (type === 'prev') {
    prefix = prevMonthDatePrefix(curDate);
  } else if (type === 'next') {
    prefix = nextMonthDatePrefix(curDate);
  }
  day = `00${day}`.slice(-2);
  return `${prefix}-${day}`;
}
export function prevMonthDatePrefix(date) {
  const temp = new Date(date.getTime());
  temp.setDate(0);
  return parseTime(temp, '{y}-{m}')
}
export function curMonthDatePrefix(date) {
  return parseTime(date, '{y}-{m}')
}
export function nextMonthDatePrefix(date) {
  const temp = new Date(date.getFullYear(), date.getMonth() + 1, 1);
  return parseTime(temp, '{y}-{m}')
}