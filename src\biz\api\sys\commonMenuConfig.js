export const roleBindApi = (data) => {
    return {
        url: 'edc/short-cut-menu/role-bind-add',
        method: 'post',
        data
    }
}

export const roleDeleteApi = (data) => {
    return {
        url: 'edc/short-cut-menu/role-bind-delete',
        method: 'post',
        data
    }
}

export const userBindApi = (data) => {
    return {
        url: 'edc/short-cut-menu/user-bind',
        method: 'post',
        data
    }
}

export const queryRoleBindMenu =  (params) => {
    return {
        url: 'edc/short-cut-menu/list-role-menu',
        method: 'get',
        params
    }
}

export const queryRoleBindMenuInfo =  (params) => {
    return {
        url: 'edc/short-cut-menu/list-role-menu-info',
        method: 'get',
        params
    }
}

export const queryUserBindMenu =  (params) => {
    return {
        url: 'edc/short-cut-menu/list-user-menu',
        method: 'get',
        params
    }
}

export const getInterfaceInfo = (params) => {
    return {
        url: '/hos/interface/info',
        method: 'get',
        params
    }
}

export const getHomeMenuConfig = (params) => {
    return {
        url: 'edc/short-cut-menu/get-detail',
        method: 'get',
        params
    }
}

export const getMenuList = (params) => {
    return {
        url: 'edc/short-cut-menu/auth/get-resource',
        method: 'get',
        params
    }
}