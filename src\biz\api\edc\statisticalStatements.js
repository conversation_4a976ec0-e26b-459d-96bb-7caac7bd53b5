export const listDeptPage = (params) => {
  return {
      url: `/edc/monthlyReport/dept-page`,
      method: 'get',
      params
  }
}

export const listProjectPage = (params) => {
  return {
      url: `/edc/monthlyReport/project-pat-page`,
      method: 'get',
      params
  }
}

export const listIntoGroupPage = (params) => {
  return {
      url: `/edc/monthlyReport/patient-tendency-page`,
      method: 'get',
      params
  }
}

export const listExportPage = (params) => {
  return {
      url: `/edc/monthlyReport/query-export-page`,
      method: 'get',
      params
  }
}

export const listDeptProjectPage = (params) => {
  return {
      url: `/edc/monthlyReport/org-pro-info-page`,
      method: 'get',
      params
  }
}

export const listProjectTypePage = (params) => {
  return {
      url: `/edc/monthlyReport/pro-type-info-page`,
      method: 'get',
      params
  }
}

export const listCompleteIndicatorPage = (params) => {
  return {
      url: `/edc/monthlyReport/project-complete-indicators`,
      method: 'get',
      params
  }
}

// 导出
export const exportApi = (params) => {
  return {
    url: '/edc/monthlyReport/down-load-statics',
    method: 'get',
    responseType: "blob",
    params
  }
}
