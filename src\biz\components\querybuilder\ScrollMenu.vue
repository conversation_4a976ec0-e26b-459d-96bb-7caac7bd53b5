<template>
  <div class="scrollMenu">
    <div v-if="cateItems.length>0" style="display:flex;">
      <div class="sibarmenu">
        <hos-menu v-if="!isCateItemsEmpty" class="hos-menu-vertical-demo" :default-active="activeIndex" @select="selectMenu">
          <template v-for="(item, index) in cateItems">
            <hos-menu-item v-if="item.child && item.child.length>0 && item.child[0].item && item.child[0].item.length>0" :key="index" :index="id + type + item.cateId">
              <span slot="title">{{ item.cateName }}</span>
            </hos-menu-item>
          </template>
        </hos-menu>
        <empty-box v-else :with-img="false" />
      </div>
      <div ref="itemBox" class="search-item-box" style="width:100%;" @scroll="scrollEvent">
        <template v-if="!isCateItemsEmpty">
          <template v-for="(item, index) in cateItems">
            <div v-if="item.child && item.child.length>0 && item.child[0].item && item.child[0].item.length>0" :key="index" class=" search-parent-cate">
              <!-- 添加锚点 -->
              <span :id="id + type + item.cateId" class="acontent" />
              <!-- <p class="search-parent-title acontent" :id="id + type + item.cateId">{{ item.cateName }}</p> -->
              <div v-for="(sub, subIndex) in item.child" :key="subIndex" class="search-sub-cate">
                <template v-if="sub.item.length>0">
                  <checkbox-all v-if="!singleSelect" v-slot="scope" :selected="selectItems" :all="sub.item">
                    <p class="search-sub-title">
                      <hos-checkbox :disabled="sub.childId === 'patient'" :value="scope.checked" :indeterminate="scope.indeterminate" @change="(val)=>checkAll(val,selectItems,sub.item,sub)">{{ sub.childName }}</hos-checkbox>
                    </p>
                  </checkbox-all>
                  <p v-else class="search-sub-title">{{ sub.childName }}</p>
                  <div class="search-item-content">
                    <span v-for="(sitem, sindex) in sub.item" :key="sindex" class="search-item-tag" :class="{ active:sItemInSelected(sitem) > -1 }"
                      @click="clickItem(sitem,sub)" v-html="getHeightLightItem(sitem.searchItemName)" />
                  </div>
                </template>

              </div>
            </div>
          </template>
        </template>
        <empty-box v-else />
      </div>
      <div v-if="(selectType==='search'||selectType==='export') && allTag && allTag.length>0" class="tag-container">
        <div class="tag-title">{{ $t('标签') }}:</div>
        <div v-if="allTag.length>0" class="tag-box">
          <li v-for="(item, index) in allTag" :key="index" class="tag-item" :class="{'is-active':selectedTags.indexOf(item.id)>=0}" @click="toggleTag(item)">
            <span>{{ item.tagDesc }}</span>
          </li>
        </div>
        <empty-box v-else />
      </div>
    </div>
    <template v-else>
      <empty-box />
    </template>

  </div>
</template>

<script>
import CheckboxAll from './CheckboxAll'
import { EventBus } from "./event-bus"
import { getUUID } from "@/utils/index.js"
  export default {
    name: "ScrollMenu",
    components: { CheckboxAll },
    props: {
      selectType: {
        type: String,
      },
      heightLight: { // 需要高亮的字段
        type: String,
        default() {
          return ''
        }
      },
      allTag: { // 所有的标签
        type: Array,
        default() {
          return []
        }
      },
      // 类型 全病字段还是专病字段
      type: {
        type: String,
        default() {
          return ""
        },
      },
      // 是否单选，默认是
      singleSelect: {
        type: Boolean,
        default() {
          return true
        }
      },
      // 查询项大分类
      cateItems: {
        type: Array,
        default() {
          return []
        }
      },
      // 全部关联字段信息
      fieldData: {
        type: Array,
        default() {
          return []
        }
      },
      // 公共的指标项
      commonSearchItems: {
        type: Array,
        default() {
          return []
        }
      },
      // 选中项列表
      selectItems: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        selectedTags: [],
        id: getUUID(),
        activeIndex: null,
        selectId: "",
        keyword: "",
        // selectItems: []
      }
    },
    inject: {
      "defaultSelectKey": {
        from: 'defaultSelectKey'
      }
    },
    computed: {
      isCateItemsEmpty() {
        return this.cateItems.filter(item => {
          return item.child && item.child.length > 0 && item.child[0].item && item.child[0].item.length > 0
        }).length == 0
      },
      sItemInSelected() {
        return function(sitem) {
          const ids = this.selectItems.map(i => i.searchFieldId)
          return ids.indexOf(sitem.searchFieldId)
        }
      }
    },
    watch: {
      selectId() {
        this.activeIndex = this.selectId
      }
    },
    methods: {
      getHeightLightItem(str = '') {
        if (this.heightLight) {
          return str.replaceAll(this.heightLight, `<span style="color:red;">${this.heightLight}</span>`)
        } else {
          return str
        }
      },
      // 查询项点击事件
      clickItem(item, sub) {
        const arr = this.selectItems.filter(i => {
          return i.searchItemId === item.searchItemId
        })

        if (arr && arr.length > 0) {
          // 默认选中的字段不能取消选中
          if (this.defaultSelectKey && item.fieldInfo && item.fieldInfo[this.defaultSelectKey]) {
            return
          }
          // 从选中的列表中移除
          const newSelectItems = this.selectItems.filter(i => {
            return i.searchItemId !== item.searchItemId
          })
          this.$emit("update:selectItems", newSelectItems)
        } else {
          // 获取关联的字段信息
          const fieldInfoArr = this.fieldData.filter(info => {
            return info.id === item.searchFieldId
          })

          if (fieldInfoArr && fieldInfoArr.length > 0) {
            item["fieldInfo"] = fieldInfoArr[0]

            // if(item.fieldInfo.isNegative === 1) {
            //   // 更新所有支持阴阳性的检索词map
              EventBus.$emit("updateFieldsIsNegative", item)
            // }
          } else if (!item.fieldInfo) {
            item["fieldInfo"] = {}
          }
          // 获取字段的父字段
          if (sub) {
            item["pFieldInfo"] = {
              id: sub.childId,
              name: sub.childName,
              // fieldName:this.fieldData[sindex].fieldName,
              // typeName:this.fieldData[sindex].typeName

            }
          } else {
            item["pFieldInfo"] = {}
          }
          // 特殊处理检验、检查等
          if (item.searchFieldOtherId && item.searchFieldOtherId > 0) {
            // item.fieldInfo.name = item.searchItemName + "_" + item.fieldInfo.name
            // todo 检验检查生成的时候在查询项中已经带上名称
            item.fieldInfo.name = item.searchItemName
            // 获取其他字段信息
            const otherFieldInfoArr = this.fieldData.filter(info => {
              return info.id === item.searchFieldOtherId
            })

            item.otherFieldInfo = (otherFieldInfoArr && otherFieldInfoArr.length > 0) ? otherFieldInfoArr[0] : null
          } else {
            // 展示查询项名称
            item.fieldInfo.name = item.searchItemName
          }
          item.fieldInfo.cateName = item.pFieldInfo.name
          // item.fieldInfo.fieldCode = item.pFieldInfo.fieldName;
          // item.fieldInfo.esTypeCode = item.pFieldInfo.typeName;

          // let addFields ={
          //     fieldCode,
          //     esTypeCode,
          // }
          // item.fieldInfo = Object.assign(item.pFieldInfo,addFields)
          this.selectItems.push(item)
        }

        if (this.singleSelect) {
          // this.closeSearchDialog()

          this.$emit("closeSearchDialog")
        }
      },
      // 获取全部选中的查询项
      getSelectItems() {
        const activeItem = this.selectItems ? this.selectItems : []

        if (this.singleSelect) {
          return activeItem.length > 0 ? activeItem[0] : null
        } else {
          return activeItem
        }
      },
      selectMenu(key) {
        this.toParentid(key)
      },
      // 跳转到锚点
      toParentid(key) {
        var el = document.getElementById(key)
        el.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "nearest"
        })
      },
      checkAll(value, selected, all, sub) {
        // console.log('value:', value);
        // console.log('selected:', selected);
        // console.log('all:', all);
        // console.log('sub:', sub);
        const selectedIds = selected.map(i => i.searchFieldId)
        if (value) {
          // 全选
          all.forEach(a_item => {
            if (selectedIds.indexOf(a_item.searchFieldId) < 0) {
              this.clickItem(a_item, sub)
            }
          })
        } else {
          const newSelectItems = []
          // 取消全选
           all.forEach(a_item => {
            const completeFieldInfo = this.fieldData.filter(i => i.id === a_item.searchFieldId)[0]
            if(selectedIds.indexOf(a_item.searchFieldId) >= 0) {
              if (this.defaultSelectKey && completeFieldInfo[this.defaultSelectKey] == 1) {
                // 默认选中的字段保留，其他字段全部反选
                newSelectItems.push(a_item)
              }
            }
          })
          if(all[0]) {
            const fieldInfo = this.fieldData.filter(i => i.id === all[0].searchFieldId)[0]
            selected.forEach(s => {
              if(s.fieldInfo.typeId !== fieldInfo.typeId) {
                // 不是当前点击的表的字段都需要保留
                newSelectItems.push(s)
              }
            })
          }
          this.$emit('update:selectItems', newSelectItems)
        }
      },
      scrollEvent() {
        const navContents = document.querySelectorAll(".acontent")
        // 所有锚点元素的 offsetTop
        const offsetTopArr = []
        navContents.forEach(item => {
          const temp = {
            offsetTop: item.offsetTop,
            id: item.id,
          }
          offsetTopArr.push(temp)
        })
        // 获取当前文档流的 scrollTop
        const offsetTop = this.$refs.itemBox.offsetTop
        const scrollTop = this.$refs.itemBox.scrollTop + offsetTop + 25

        let navIndex = ""
        for (let n = 0; n < offsetTopArr.length; n++) {
          // 如果 scrollTop 大于等于第 n 个元素的 offsetTop 则说明 n-1 的内容已经完全不可见
          // 那么此时导航索引就应该是 n 了
          if (scrollTop >= offsetTopArr[n].offsetTop) {
            navIndex = offsetTopArr[n].id
          }
        }
        // 把下标赋值给 vue 的 data
        this.selectId = navIndex
      },
      toggleTag(item) {
        if (this.selectedTags.indexOf(item.id) >= 0) {
          this.selectedTags = this.selectedTags.filter(id => id != item.id)
        } else {
          this.selectedTags.push(item.id)
        }
        this.$emit('change-tag', this.selectedTags)
      }
    }
  }
</script>
