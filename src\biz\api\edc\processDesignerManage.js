export const pageListApi = (params) => {
  return {
      url: '/process-design/orc/page',
      method: 'get',
      params,
  }
}

export const addApi = (data) => {
  return {
      url: '/process-design/orc/insert',
      method: 'post',
      data,
  }
}

export const editApi = (data) => {
  return {
    url: '/process-design/orc/update',
    method: 'post',
    data
  }
}

export const deleteApi = (data) => {
  return {
      url: '/process-design/orc/deletion',
      method: 'POST',
      data
  }
}

export const detailApi = (id) => {
  return {
      url: '/process-design/orc/detail/' + id,
      method: 'GET'
  }
}