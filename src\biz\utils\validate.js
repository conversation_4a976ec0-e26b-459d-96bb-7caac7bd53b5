/**
 * 邮箱
 * @param {*} s
 */
export function isEmail(s) {
  return /^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile(s) {
  return /^1[0-9]{10}$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone(s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 特殊字符验证
 * @param {*} str
 * @returns
 */
export function checkSpecialKey(str) {
  let specialKey =
      "[`~!#$^&*()=|{}':;'\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]‘'《》";
  for (let i = 0; i < str.length; i++) {
      if (specialKey.indexOf(str.substr(i, 1)) != -1) {
          return false;
      }
  }
  return true;
}

/**
 * 仅允许中文，数字，字母，下划线校验
 * @param {String} str
 * @returns {Boolean}
 */
export function checkNumChar_(str) {
  return /[0-9A-Za-z_\u4e00-\u9fa5]+$/.test(str)
}