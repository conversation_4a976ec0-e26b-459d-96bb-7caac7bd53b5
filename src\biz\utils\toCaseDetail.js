import router from "@base/router";
import {
  Encrypt,
  EncryptUrl
} from "@/utils/index"
export default function toCaseDetail (item, indexId) {
  const routeQuery = {
    regno: item.regno,
    admno: item.admno,
    admType: item["mr_adm_type"],
    indexId: indexId
}
// const routeUrl = router.resolve({
//     path: '/search/case-detail',
//     query: routeQuery
// })
// window.open(EncryptUrl(routeUrl.href), 'CaseDetail')
router.push({
  path: '/search/case-detail/index',
  query: {
    p: Encrypt(JSON.stringify(routeQuery))
  }
})
}
