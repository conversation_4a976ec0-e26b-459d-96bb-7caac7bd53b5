<template>
  <hos-container class="main" style="height: 100%">
    <hos-aside :width="isCollapse ? '40px' : '220px'">
      <hos-menu
        class="menuLeftMain leftMenu datasetMenu"
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="$route.path"
        :unique-opened="true"
        @select="menuSelect"
      >
        <side-menu :menuList="menuList" :showIcon="true" :no-more="true"></side-menu>
      </hos-menu>
    </hos-aside>
    <hos-main style="height: 100%; padding: 0;padding-left: 10px; background-color: #f5f5f5">
      <template v-if="info && subsetInfo">
        <keep-alive v-if="keepAlive">
          <router-view />
        </keep-alive>
        <router-view v-else />
      </template>
    </hos-main>
  </hos-container>
</template>

<script>
import { mapState,mapActions } from 'vuex'
import SideMenu from '@base/components/menu/SideMenu'
export default {
  name: 'DatasetView',
  components: { SideMenu },
  data() {
    return {
      isCollapse: false,
      activeMenu: {},
    }
  },
  computed: {
    ...mapState({
      info: state => state.user.userInfo,
      subsetInfo: state => state.subset.subsetInfo,
      subsetId: state => state.subset.subsetId,
    }),
    keepAlive() {
      return this.$route.meta.keepAlive
    },
    ...mapState({
      permissionMenuList: (state) => state.user.menuList,
      sysAppName: (state) => state.sys.sysAppName, // 系统应用名称
      browserTabName: (state) => state.sys.browserTabName // Title名称
    }),
    menuList() {
      return this.getAllAnalyzerMenu()
    }
  },
  created() {},
  methods: {
    ...mapActions(['setSubjectInfo']),
    getAllAnalyzerMenu(){
      // 过滤出项目视图下的所有菜单
      if (this.permissionMenuList.length > 0) {
        const tmp = this.permissionMenuList.filter((item) => item.name == 'analyzer')
        if (tmp.length > 0) {
          const edcMenu = tmp[0]
          if (edcMenu.children && edcMenu.children.length > 0) {
            const tmp = edcMenu.children.filter((item) => item.name == 'd')
            if (tmp.length > 0) {
              return tmp[0].children || []
            }
          }
        }
      }
      return []
    },
    menuSelect(index, indexPath) {
      //此处触发动态路由被点击事件
      this.findMenuByKey(this.menuList, index)
      this.$router.push({ path: this.activeMenu.path})
    },
    findMenuByKey(menus, key) {
      for (let i of menus) {
        if (i.name == key) {
          this.activeMenu = { ...i }
        } else if (i.children && i.children.length > 0) {
          this.findMenuByKey(i.children, key)
        }
      }
    }
  },
  watch:{}
}
</script>
<style lang="scss" scoped>
  .datasetMenu{
    ::v-deep{
      .hos-submenu__title:hover,.hos-menu-item:focus {
          background-color: var(--leftMenu-bg-color, #e5f3ff);
          color: var(--leftMenu-text-color, #339EFF);
      }
    }
  }
</style>