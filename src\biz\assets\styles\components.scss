
// select-tree 组件
// ********************* select-tree 组件 start ***************************
.select-tree-wrapper {
  display: flex;
  flex-direction: column;
  .hos-tree{
    overflow: auto;
  }
  // &.is-cancelable {
  //   .hos-tree-node:focus>.hos-tree-node__content {
  //     background-color: unset;
  //   }

  //   .hos-tree-node.is-current>.hos-tree-node__content {
  //     background-color: #eff7ff;
  //   }
  // }
}

// ********************* select-tree 组件 end ***************************

// CodeMirror 组件
// ********************* CodeMirror 组件 start ***************************
.CodeMirror {
  background: rgb(247, 247, 247);
  border: 1px solid #eee;
  height: calc(65vh);
}

.long-code-mirror .CodeMirror {
  height: calc(80vh);
}

.auto-code-mirror .CodeMirror {
  height: auto;
}

.sql-code-mirror {
  width: 500px;

  .CodeMirror {
    height: calc(40vh);
  }
}

.cm-matchhighlight {
  background-color: #ae00ae;
}

// ********************* CodeMirror 组件 end ***************************

// EmptyBox 组件
// ********************* EmptyBox 组件 start ***************************
.empty-description {
  color: #999;
}

.empty-box {
  text-align: center;
}

// ********************* EmptyBox 组件 end ***************************

// input-multi 组件
// ********************* input-multi 组件 start ***************************
.input-multi {
  width: 100%;
  height: 28px;
  overflow: hidden;

  &.not-focus {
    .hos-input__inner {
      height: 28px !important;
    }

    .hos-select__tags {
      flex-wrap: nowrap;
    }
  }

  &.is-focus {
    height: auto;
  }
}

// ********************* input-multi 组件 end ***************************
