
export const getUserInfo = (data) => {
    return {
        url: 'search/data-detail/statistics/select-total?indexId=' + data.indexId,
        method: 'GET',
    }
}

// 获取就诊趋势
export const getTendencyData = (params) => {
    return {
        url: `search/data-detail/statistics/select-patient-count-aggs?type=${params.type}&indexId=${params.indexId}`,
        method: 'GET',
    }
}
// 获取就诊类型数据
/**
 * 就诊趋势饼图数据，全院与专病暂时共用同一接口
 */
export const getTendencyType = (params) => {
    return {
        url: `search/data-detail/statistics/select-patient-count-spread?type=${params.type}&indexId=${params.indexId}`,
        method: 'GET',
    }
}

// 获取就诊分布数据
export const getSeeDocData = (type, indexId) => {
    return {
        url: `search/data-detail/statistics/select-top-five?type=${type}&indexId=${indexId}`,
        method: 'GET',
    }
}

// 获取患者分布数据
export const getIllCaseData = (indexId) => {
    return {
        url: `search/data-detail/statistics/spread/patient?indexId=${indexId}`,
        method: 'GET',
    }
}

// 获取top10的诊疗情况分布
export const getSeeDocDataTop10 = (params) => {
    return {
        url: `search/data-detail/statistics/select-top-ten?type=${params.type}&indexId=${params.indexId}&size=${params.size}`,
        method: 'GET',
    }
}

// 获取当前模型当前角色数据权限范围信息描述
export const getConditionDesc = (params) => {
    return {
        url: 'search/role-permission/get-condition-desc',
        method: 'get',
        params
    }
}