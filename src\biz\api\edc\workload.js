// 项目本中心成员随访量统计
export const getFollowUpMember= (params) => {
  return {
      url: 'edc/subject-statistics/follow-up-member',
      method: 'GET',
      params
  }
}

// 项目中心随访量统计
export const getFollowUpOrg= (params) => {
  return {
      url: 'edc/subject-statistics/follow-up-org',
      method: 'GET',
      params
  }
}

// 项目本中心成员表单提交量统计
export const getFormFillMember= (params) => {
  return {
      url: 'edc/subject-statistics/form-fill-member',
      method: 'GET',
      params
  }
}

// 项目中心表单提交量统计
export const getFormFillOrg= (params) => {
  return {
      url: 'edc/subject-statistics/form-fill-org',
      method: 'GET',
      params
  }
}

// 项目本中心成员入组量统计
export const getIntoGroupMember= (params) => {
  return {
      url: 'edc/subject-statistics/into-group-member',
      method: 'GET',
      params
  }
}

// 项目中心入组量统计
export const getIntoGroupOrg= (params) => {
  return {
      url: 'edc/subject-statistics/into-group-org',
      method: 'GET',
      params
  }
}