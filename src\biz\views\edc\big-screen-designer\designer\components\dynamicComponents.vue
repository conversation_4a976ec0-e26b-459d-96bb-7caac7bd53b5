<template>
  <div>
    <el-form label-width="100px" label-position="left">

      <div>
        <el-form-item label="数据获取方式">
          <el-select v-model="chartDataCode" size="mini" filterable placeholder="请选择" @change="chartDataCodeChange">
            <el-option v-for="item in chartDataCodeTypes" :key="item.code" :label="item.label" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-divider v-if="contextData.length > 0" class="dark-divider">请求参数</el-divider>
        <el-form-item v-for="(item, index) in contextData" :key="index" :label="item.label">
          <el-input v-if="item.type === 'el-input-textarea'" v-model.trim="item.value" type="textarea"
            :rows="item.textareaRows" size="mini" />
          <el-input v-else v-model.trim="item.value" size="mini" />
        </el-form-item>
        <!-- <el-divider v-if="dictItems.length>0" class="dark-divider">返回结果</el-divider>
        <el-form-item v-for="(item,index) in dictItems" :key="index+'d'" :label="item.text">
          <el-input v-model.trim="chartProperties[item.key]" size="mini" />
        </el-form-item> -->

      </div>

      <el-button style="width: 100%" type="primary" plain size="mini" @click="saveDataBtn">刷新</el-button>
    </el-form>
  </div>
</template>
<script>
import { DictMap, chartDataCodeTypes } from "../../designer/tools/index"
// import { queryAllDataSet, detailBysetId } from "@/common/api-bigscreen"
// import Dictionary from "@/components/Dictionary/index"

export default {
  name: "DynamicComponents",
  components: {
    // Dictionary
  },
  model: {
    prop: "formData",
    event: "input"
  },
  props: {
    chartType: String,
    dictKey: String,
    formData: Object
  },
  data() {
    const dictItems = DictMap[this.dictKey] || []
    const chartProperties = {}
    dictItems.forEach(item => {
      chartProperties[item.key] = item.key
    })
    return {
      chartDataCode: '',
      chartDataCodeTypes,
      contextData: [], // 查询参数
      params: {},
      dictItems, // 通过配置读取默认的属性转换方式
      chartProperties// 接口返回数据字段映射
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.restore(val)
      },
      deep: true
    }
  },
  mounted() {
    this.restore(this.formData)
  },
  methods: {
    chartDataCodeChange(val) {
      const tmp = this.chartDataCodeTypes.filter(item => item.code === this.chartDataCode)
      if (tmp.length > 0) {
        this.contextData = tmp[0].params.map(item => ({ ...item, value: '' }))
      } else {
        this.contextData = []
      }
    },
    async saveDataBtn() {
      const contextData = {}
      this.contextData.forEach(item => {
        contextData[item.code] = item.value
      })
      const params = {
        chartType: this.chartType,
        chartDataCode: this.chartDataCode,
        // chartProperties: this.chartProperties,
        chartProperties: {},
        contextData
      }
      this.$emit("input", params)
      this.$emit("change", params)
    },
    // 回显
    async restore(val) {
      if (!val) return
      // 回显数据获取方式和请求参数
      const chartDataCode = val.chartDataCode
      if (chartDataCode) {
        const tmp = this.chartDataCodeTypes.filter(item => item.code === chartDataCode)
        if (tmp.length > 0) {
          this.chartDataCode = chartDataCode
          this.contextData = tmp[0].params.map(item => ({ ...item, value: val.contextData[item.code] }))
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dark-divider {
  margin: 16px 0 !important;

  ::v-deep .el-divider__text {
    background-color: #242a30;
    color: #bfcbd9;
    font-size: 12px;
  }
}
</style>
