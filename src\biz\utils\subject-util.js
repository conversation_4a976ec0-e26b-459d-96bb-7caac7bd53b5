import * as storageUtil from "@base/utils/base/storage-util";

export function getSubjectId() {
  return localStorage.getItem('subject-id')
}

export function setSubjectId(subjectId) {
  return localStorage.setItem('subject-id', subjectId)
}

export function getSubjectInfo() {
  return storageUtil.getLocal('subject-info');
}

export function setSubjectInfo(info) {
  return storageUtil.setLocal('subject-info', info);
}