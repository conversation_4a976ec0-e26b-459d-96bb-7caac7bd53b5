
export const queryListApi = (params) => {
  return {
    url: 'edc/form-template/page',
    method: 'get',
    params,
  }
}

export const queryAll = (params) => {
  return {
    url: 'edc/form-template/list',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/form-template/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/form-template/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/form-template/insert',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/form-template/update',
    method: 'post',
    data
  }
}


