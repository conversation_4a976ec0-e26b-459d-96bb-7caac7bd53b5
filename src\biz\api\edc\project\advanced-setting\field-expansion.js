export const pageListApi = (params) => {
    return {
        url: `edc/patient-attribute/list`,
        method: 'GET',
        params
    }
}

export const addApi = (data) => {
    return {
        url: `edc/patient-attribute/insert`,
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: `edc/patient-attribute/update`,
        method: 'POST',
        data
    }
}

export const deleteApi = (data) => {
    return {
        url: `edc/patient-attribute/deletion`,
        method: 'POST',
        data
    }
}