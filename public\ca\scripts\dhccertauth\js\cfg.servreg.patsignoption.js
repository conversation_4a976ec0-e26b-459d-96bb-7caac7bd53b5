﻿$(function() {
    $("#btnSaveOption").click(function() {
        var supportSignTypeValue = "";
        var list = $("input[name='supportSignType']:checked");
        for (var i=0;i<list.length;i++)
        {
            if (supportSignTypeValue != "")
                supportSignTypeValue = supportSignTypeValue + "|";
            supportSignTypeValue = supportSignTypeValue + list[i].defaultValue;
        }
        var positionTypeValue = $("#positionType").combobox("getValue");
        var enableCombineKeyWordValue = $("#enableCombineKeyWord")[0].checked == true?"1":"0"
        var optionValue = {
            supportSignType : supportSignTypeValue,
            positionType : positionTypeValue,
            enableGroupKeyWord :enableCombineKeyWordValue
        }

        parent.patSignOptionValue = optionValue;
        parent.setOptionHtmlInfo("patSignOption",optionValue),
        parent.closeDialog("patSignOptionDiv");
    });

    $("#btnCancel").click(function() {
        parent.closeDialog("patSignOptionDiv");
    });

    setOption(parent.patSignOptionValue);
});

function setOption(patSignOption) {
    if (JSON.stringify(patSignOption) == "{}") return;

    var supportSignTypeSet = patSignOption.supportSignType || "";
    var positionTypeSet = patSignOption.positionType || "";
    var enableCombineKeyWordSet = patSignOption.enableGroupKeyWord || "";

    var list = supportSignTypeSet.split("|")
    for (var i=0;i<list.length;i++)
    {
        $("#"+list[i]).checkbox("check");
    }

    if (enableCombineKeyWordSet == "1")
    $("#enableCombineKeyWord").checkbox("check");

    $("#positionType").combobox("select",positionTypeSet);
}