import Vue from 'vue';
// element-ui
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
// 引入英文语言包
import enLocale from 'element-ui/lib/locale/lang/en'
import { getCurrentLocale } from '@base/utils/i18n/i18n-util'
// Element
Vue.use(ElementUI, {
  locale: getCurrentLocale() === 'en' ? enLocale : zhLocale
})
// 注册一个全局自定义指令 `v-focus`
Vue.directive('focus', {
  // 当被绑定的元素插入到 DOM 中时……
  inserted: function (el) {
    // 聚焦元素
    el.focus()
  }
})