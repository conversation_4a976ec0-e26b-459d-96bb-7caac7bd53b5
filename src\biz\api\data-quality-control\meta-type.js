export const queryListApi = (params) => {
    return {
        url: 'quality/medical-data-standards-meta-type/page',
        method: 'get',
        params,
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'quality/medical-data-standards-meta-type/detail/' + id,
        method: 'get',
    }
}

export const addApi = (data) => {
    return {
        url: 'quality/medical-data-standards-meta-type/insert',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'quality/medical-data-standards-meta-type/update',
        method: 'post',
        data,
    }
}

export const deleteApi = (id) => {
    return {
        url: 'quality/medical-data-standards-meta-type/delete/' + id,
        method: 'delete',
    }
}

export const getAllMetaTypesByStandardId = (id) => {
    return {
        url: `quality/medical-data-standards-meta-type/list/${id}`,
        method: 'get'
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'quality/medical-data-standards-meta-type/batch-delete',
        method: 'post',
        data
    }
}