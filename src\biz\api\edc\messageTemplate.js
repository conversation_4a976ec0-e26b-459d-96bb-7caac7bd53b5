// 消息模板
export const queryListApi = (params) => {
    return {
        url: '/wx/mp/api/v1/message/template/list',
        method: 'get',
        params,
    }
}

export const queryDetailApi = (id) => {
    return {
        url: '/wx/mp/api/v1/message/template/info/' + id,
        method: 'get',
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: '/wx/mp/api/v1/message/template/delete',
        method: 'post',
        data,
    }
}

export const addApi = (data) => {
    return {
        url: '/wx/mp/api/v1/message/template/insert',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: '/wx/mp/api/v1/message/template/update',
        method: 'post',
        data
    }
}