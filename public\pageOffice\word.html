<!--  不支持箭头函数和模版字符串 -->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" href="../static/reset.css" />
  <link rel="stylesheet" href="../static/word.css" />
  <script src="../static/hisui/js/jquery-1.8.0.min.js"></script>
</head>

<body>
  <div id="officeContent"></div>
  <div id="OfficeRight">
    <!-- <strong class="log" style="display: none;"></strong> -->
    <iframe id="tableIframe" name="tableIframe" src="./table.html"
      style="width: 100%; height: 100%; border: none"></iframe>
    <!-- <button id="logBtn">显示调试log</button> -->
    <!-- <button id="saveLogBtn">显示当前global配置</button> -->
  </div>

  <script type="text/javascript">
    window.dev = false
    window.globalConfig = {}
    var iframeDom = null
    $(document).ready(function () {
      fetchFile()

      // 创建隐藏的输入框
      const hiddenInput = $('<input type="hidden" id="hosPrintTempConfDto" value=""/>');
      hiddenInput.appendTo('#officeContent');

      $('#logBtn').click(function () {
        $('.log').toggle()
      })

      $('#saveLogBtn').click(function () {
        $('.log').show()
        $('.log').text(JSON.stringify(window.globalConfig))
      })
    })

    function fetchFile () {
      const params = JSON.parse(parseField('params'))
      const token = parseField('token')
      window.token = token

      if (!params || !params.templateCode || !token) return
      if (parseField('sourceType') === 'printPreview') {
        $('#OfficeRight').remove()
      }

      // 打印预览
      $.ajax({
        url: decodeURIComponent(parseField('url')),
        type: "post",
        headers: {
          "Access-Token": token
        },
        contentType: "application/json;charset=UTF-8",
        dataType: "json",
        data: parseField('params'),
        success: function (result) {
          const code = result.code;
          const sourceType = parseField('sourceType');
          const templateName = parseField('templateName');
          const templateCode = parseField('templateCode');
          $('.log').text(JSON.stringify(result))
          if (code == '200') {
            if (sourceType === 'printPreview') {
              $('#officeContent').css('width', '100%');
              $('#officeContent').html(result.data.pageOfficeCtrl1);
            } else if (result.data) {
              window.globalConfig = result.data;
              iframeDom = document.getElementById('tableIframe');
              $('#officeContent').append(result.data.pageOfficeCtrl1);
              checkIframeLoaded()
            }

            const title = templateName + '(' + templateCode + ')';
            document.getElementById("PageOfficeCtrl1").Caption = title;
          } else if (code == '4000' || code == '4001' || code === '401') {
            loginFailure();
          } else {
            alert('请求失败:' + result.msg);
          }
        },
        error: function (error) {
          alert(JSON.stringify(error));
        }
      });

    }

    function checkIframeLoaded () {
      var iframe = document.getElementById('tableIframe');
      var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      // 检查加载是否完成
      if (iframeDoc.readyState == 'complete') {
        // iframe加载完毕后执行的代码
        if (typeof iframe.contentWindow.renderDom === 'function') {
          iframe.contentWindow.renderDom();
        }
        return;
      }
      // 如果还没有加载完毕，设置一个定时器，10毫秒后再次检查
      window.setTimeout(checkIframeLoaded, 10);
    }

    function loginFailure () {
      window.external.CallParentFunc('logout()')
      CloseFile()
    }

    // 文档打开完毕
    function AfterDocumentOpened () {
      SetEnableFileCommand()
      if (parseField('sourceType') === 'printPreview' && parseField('isPreview') !== 'true') printWithoutPreview()
    }

    // 无预览打印
    function printWithoutPreview () {
      document.getElementById('PageOfficeCtrl1').PrintOut();
      setTimeout(function () {
        CloseFile()
      }, 2000)
    }

    // 禁用文件图标
    function SetEnableFileCommand () {
      // https://www.zhuozhengsoft.com/help/js3/PageOffice%E5%AE%A2%E6%88%B7%E7%AB%AFJS%E7%BC%96%E7%A8%8B/%E6%96%B9%E6%B3%95/SetEnableFileCommand.html
      document.getElementById("PageOfficeCtrl1").SetEnableFileCommand(3, false);
      document.getElementById("PageOfficeCtrl1").SetEnableFileCommand(5, false);
      document.getElementById("PageOfficeCtrl1").SetEnableFileCommand(6, false);
      document.getElementById("PageOfficeCtrl1").SetEnableFileCommand(7, false);
      document.getElementById("PageOfficeCtrl1").SetEnableFileCommand(8, false);
    }


    // 获取字段key的值
    function parseField (key) {
      const queryParams = parseQueryString(window.external.UserParams)
      return queryParams[key] || ''
    }

    // 转换拼接传递的字符串参数
    function parseQueryString (queryString) {
      if (!queryString) return
      var params = {};
      var pairs = queryString.split('&');

      for (var i = 0; i < pairs.length; i++) {
        var pair = pairs[i];
        var keyValue = pair.split('=');
        var key = keyValue[0];
        var value = keyValue[1];
        params[key] = value;
      }

      return params;
    }

    function getNowTime () {
      const now = new Date()
      const hours = ('0' + now.getHours()).slice(-2) // 截取最后两位，如果小时为两位数则不变，如果为一位数则补零
      const minutes = ('0' + now.getMinutes()).slice(-2) // 截取最后两位，如果分钟为两位数则不变，如果为一位数则补零

      return hours + ':' + minutes
    }

    function cellClick (Celladdress, value, left, bottom) {
      window.Celladdress = Celladdress
    }

    // 保存
    function Save () {
      if (!collectConfig()) return
      window.globalConfig.id = parseField('id') || ''
      window.globalConfig.fileId = parseField('fileId') || ''

      $('#hosPrintTempConfDto').val(JSON.stringify(window.globalConfig))
      document.getElementById('PageOfficeCtrl1').WebSave()
    }

    // 保存回调
    function afterDocumentSaved () {
      const result = document.getElementById('PageOfficeCtrl1').CustomSaveResult

      iframeDom.contentWindow.saveTipHandle(result);
    }

    // 打印预览
    function PrintPreview () {
      document.getElementById("PageOfficeCtrl1").Document.Application.Dialogs(222).Show();
    }

    // 打印设置
    function PrintSet () {
      // 1:打开 2:保存 3:另存为 4:打印 5:打印设置 6:文件属性
      document.getElementById('PageOfficeCtrl1').ShowDialog(5)
    }

    function PrintFile () {
      // 1:打开 2:保存 3:另存为 4:打印 5:打印设置 6:文件属性
      document.getElementById('PageOfficeCtrl1').ShowDialog(4)
    }

    // 关闭pageOffice窗口
    function CloseFile () {
      window.external.close()
    }

    function IsFullScreen () {
      document.getElementById('PageOfficeCtrl1').FullScreen = !document.getElementById('PageOfficeCtrl1').FullScreen
    }

    // 收集整个表单数据
    function collectConfig () {
      return iframeDom.contentWindow.collectTableConfig();
    }
  </script>
</body>

</html>