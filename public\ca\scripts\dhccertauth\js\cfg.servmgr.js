﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//获取菜单配置信息
var urlParams = ca_common_tools.getParams();
var globalInfo = {
    action : urlParams.action || ""
}

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_sysoption";

$(function() {
    hidenByAction();
    initBTN();
    initSelect();
    initAuthType();
    initVenderCode();

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        initCommon(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        initCommon(selOrgID);
    }
})

//通过传入动作显示可配置内容
function hidenByAction() {
    if (globalInfo.action == "sysmgr") {
        $("#sysStatus").hide();
    } else if (globalInfo.action == "statusmgr") {
        $("#sysOption").hide();
        initCarPrvTp();
    }
}

//初始化按钮事件
function initBTN() {
    $("#btnSaveCommon").click(function(){saveConfirm();});
}

//初始化是否选项
function initSelect() {
    var data = [{"ID":"0","Text":"否"},{"ID":"1","Text":"是"}];
    var tmpClassName = globalInfo.action == "sysmgr" ? "sysOption" : "sysStatus" + " whether";
    var tmpList = document.getElementsByClassName(tmpClassName);
    for (var i=0; i <tmpList.length ;i++) {
		initCombobox(tmpList[i].id,data,false,false);
	}
}

//初始化配置可关闭CA的医护人员类型
function initCarPrvTp() {
    var data = {
        action: "GET_ALLCAREPRVTYPE",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("disabledCarPrvTp",json.data,true,false,"carPrvTypeCode","carPrvTypeDesc");
    } else {
        $.messager.alert("提示", "获取医护人员类型失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//初始化认证方式
function initAuthType() {
    var data = {
        action: "GET_ALLAUTHTYPE",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("defaultCareAuthType",json.data,false,true,"authTypeCode","authTypeDesc");
        initCombobox("authTypeList",json.data,true,false,"authTypeCode","authTypeDesc");
    } else {
        $.messager.alert("提示", "获取认证方式数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//初始化CA厂商代码
function initVenderCode() {
    var data = {
        action: "GET_ALLVENDER",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        var tmpList = document.getElementsByClassName("vender")
        for (var i=0; i <tmpList.length ;i++) {
            initCombobox(tmpList[i].id,json.data,false,true,"venderCode","venderDesc");
        }
    } else {
        $.messager.alert("提示", "获取厂商数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//获取服务端保存配置数据
function initCommon(selOrgID) {
    selOrgID = selOrgID || "";
    if (selOrgID == "") {
        $.messager.alert("提示","获取组织机构数据错误，无法加载对应组织机构配置数据!", "info");
        return;
    }

    var data = {
        action: "GET_SYSOPTION",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            setCommon(json.data);
        } else {
            $.messager.alert("提示", "获取通用配置数据错误，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

//填充配置内容
function setCommon(data) {
    $("#authTypeList").combobox("clear");
    $("#disabledCarPrvTp").combobox("clear");

    var tmpClassName = globalInfo.action == "sysmgr" ? "sysOption" : "sysStatus";
    var tmpList = document.getElementsByClassName(tmpClassName);
    for (var i=0; i <tmpList.length ;i++) {
        var tmpID = tmpList[i].id;
        if ((tmpID == "authTypeList")||(tmpID == "disabledCarPrvTp")) {
            if (data[tmpID] || "" != "") {
                var tmpListarr = data[tmpID].split(",");
                for (var j=0;j<tmpListarr.length;j++) {
                    $("#"+tmpID).combobox("select",tmpListarr[j]);
                }
            }
        } else {
            $("#"+tmpID).combobox("select",data[tmpID] || "");
        }
    }
}

function saveConfirm() {
    $.messager.confirm("提示", "是否确认修改基础配置？", function (r) {
        if (r) {
            saveCommon();
        }
    });
}

//保存配置
function saveCommon() {
    selOrgID = selOrgID || "";
    if ((selOrgID == "")) {
        $.messager.alert("提示","未选择组织机构，无法保存对应组织机构配置数据!", "info");
        return;
    }

    var arr = [];
    var tmpList = [];
    if (globalInfo.action == "sysmgr") {
        tmpList = document.getElementsByClassName("sysOption");
    } else if (globalInfo.action == "statusmgr") {
        tmpList = document.getElementsByClassName("sysStatus");
    }
    for (var i=0; i <tmpList.length ;i++) {
        var tmpID = tmpList[i].id;
        var tmpDesc = $("#"+tmpID).attr("customAttr");
        if ((tmpID == "authTypeList")||(tmpID == "disabledCarPrvTp")) {
            pushArr(arr,tmpID,tmpDesc,$("#"+tmpID).combobox("getValues").join(","));
        } else {
            pushArr(arr,tmpID,tmpDesc,$("#"+tmpID).combobox("getValue"));
        }
    }

    var data = {
        action: "SAVE_SYSOPTION",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            sysOption: arr
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.alert("提示", "基础信息保存成功！","success")
            } else {
                $.messager.alert("提示", "保存通用配置数据失败！", "error")
            }
        } else {
            $.messager.alert("提示", "保存通用配置数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function pushArr(Arr,Name,Desc,Value) {
    var obj = {
        "sysOptionName":Name,
        "sysOptionDesc":Desc,
        "sysOptionValue":Value
    }
    Arr.push(obj);
}