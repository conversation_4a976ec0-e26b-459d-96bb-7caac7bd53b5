const t = {}

t.common = {}
t.common.username = 'User name'
t.common.name = 'Name'
t.common.gender = 'Gender'
t.common.age = 'Age'
t.common.patientTotal = 'Total number of cases'
t.common.admTotal = 'Total number of visits'
t.common.peopleCount = ''
t.common.times = 'Times'
t.common.caseDistribution = 'Case distribution'
t.common.visitTrends = 'Visit Trends'
t.common.outpatientService = 'Outpatient service'
t.common.hospitalization = 'In hospital'
t.common.emergencyTreatment = 'Emergency treatment'
t.common.other = 'Other'
t.common.physicalExamination = 'Physical examination'
t.common.genderSpread = 'Gender distribution'
t.common.visitDepartment = 'Visiting department'
t.common.male = 'Male'
t.common.female = 'Female'
t.common.province = 'Province'
t.common.occupation = 'Occupation'
t.common.nation = 'Nation'
t.common.diagnosis = 'Diagnosis'
t.common.westernMedicine = 'Western medicine'
t.common.noData = 'There is currently no data available'
t.common.remind = 'Prompt'
t.common.download = 'Download'
t.common.exporting = 'Exporting'
t.common.line = 'Lines'
t.common.exportItem = 'Export Items'
t.common.attribute = 'Attribute'
t.common.specialValue = 'Special value'
t.common.nothing = 'None'
t.common.admDate = 'Visit Date'
t.common.yes = 'Yes'
t.common.no = 'No'
t.common.surgical = 'Operation'
t.common.regno = 'Registration no'
t.common.detail = 'Detail'
t.common.idNo = 'ID'
t.common.checkOut = 'Check Out'
t.common.medical = 'Medication'
t.common.checkOn = 'Check On'
t.common.disease = 'Disease'

t.common.all = 'All'
t.common.last1year = 'Last year'
t.common.containes = 'Contain'
t.common.containLike = 'Contain(like)'
t.common.notContain = 'Not contain'
t.common.eq = 'Equal'
t.common.notEq = 'Not equal to'
t.common.before = 'Before'
t.common.after = 'After'
t.common.year = 'Year'
t.common.month = 'Month'
t.common.day = 'Day'
t.common.hour = 'Hour'
t.common.during = 'During'
t.common.outside = 'Outside'
t.common.greaterThan = 'Greater'
t.common.lessThan = 'Less'
t.common.greaterOrEq = 'Greater than or equal'
t.common.lessOrEq = 'Less than or equal'
t.common.semanticInclude = 'Semantic Inclusion'
t.common.multiple = 'Multivalued accurately matching(Any)'
t.common.multiple2 = 'Multivalued accurately matching(Including both)'
t.common.multipleLike = 'Multivalued likely matching(Any)'
t.common.multipleParentAllLike = 'Multivalued likely matching(Including both)'
t.common.null = 'Null'
t.common.notNull = 'Not Null'
t.common.positiveInclude = 'Positive includes'
t.common.negativeInclude = 'Negative includes'
t.common.openInterval = 'Open interval'
t.common.max = 'Maximum'
t.common.min = 'Minimum'

t.common.view = 'View'
t.common.viewDetail = 'Detail'
t.common.clearRecord = 'Clear Record'
t.common.confirm = 'Confirm'
t.common.cancel = 'Cancel'
t.common.delete = 'Delete'
t.common.close = 'Close'
t.common.submit = 'Submit'
t.common.search = 'Search'
t.common.reset = 'Reset'
t.common.save = 'Save'
t.common.edit = 'Edit'
t.common.goBack = 'Back'
t.common.lastPage = 'Previous page'
t.common.nextPage = 'Next page'

// 提示信息
t.msg = {}
t.msg.uploadSuccessful = 'Upload successful!'
t.msg.uploadFail = 'Upload failed. Please try again later'
t.msg.uploadTypeLimit = 'Uploading icon images can only be in JPG or PNG format!'
t.msg.uploadSizeLimit = 'The uploaded icon image can only be in JPG or PNG format, and the size of the uploaded icon image cannot exceed 10MB!'
t.msg.confirmDelete = 'Are you sure to delete this message? After deletion, you cannot view or operate again!'
t.msg.submitSuccessful = 'Submitted successfully!'
t.msg.selectPlease = 'Please select'
t.msg.conditionFilename = 'File Name/None'
t.msg.conditionBringInto = 'Inclusion criteria/None'
t.msg.conditionBringOut = 'Exclusion conditions/None'
t.msg.inputPlease1 = 'Please Input'
t.msg.confirmDeleteRecords = 'Are you sure to delete this record? After deletion, data cannot be retrieved'
t.msg.deleteSuccess = 'Successfully deleted!'
t.msg.handleSuccess = 'Operation successful！'
t.msg.statisticsCaution1 = 'Perform statistics on the proportion of all diseases, medication, tests, examinations, surgeries, traditional Chinese medicine, Chinese herbal medicine, and Western medicine related to the current search results'
t.msg.statisticsCaution2 = 'Data for major diagnoses in disease statistics（Outpatient medical record statistics Outpatient diagnosis；Inpatient medical record statistics and discharge diagnosis）'
t.msg.statisticsCaution3 = 'Calculate the gender ratio of all patients'
t.msg.statisticsCaution4 = 'Calculate the age of all medical records at the time of visit'
t.msg.statisticsCaution5 = 'Count the admission dates of all medical records in the current search results'
t.msg.getStructInfoErr = 'Error in obtaining structured information'
t.msg.errorInfo = 'Error message'

// 首页
t.home = {}
t.home.userInfo = 'Account Information'
t.home.belongOrg = 'Organization'
t.home.diagnosisStatus = 'Diagnosis and treatment status'
t.home.patientCount = 'Number of cases'
t.home.outpatientVisits = 'Outpatient visits'
t.home.hospitalizationVisits = 'Number of inpatients'
t.home.emergencyVisits = 'Number of emergency patients'
t.home.noCharts = 'Sorry, no statistical results were obtained for this chart'
t.home.totalError = 'Error in obtaining the total number of statistics'
t.home.visitType = 'Visit Type'
t.home.diagnosticDistribution = 'Diagnostic distribution'
t.home.surgicalDistribution = 'Surgical distribution'
t.home.testDistribution = 'Test distribution'
t.home.inspectionDistribution = 'Check distribution'
t.home.visitTime = 'Visit time'
t.home.msgStatisticsAll = 'Calculate the number of visits per year for each type of visit'
t.home.msgStatistics1year = 'Calculate the number of visits per month for each type of visit in the past year'
t.home.msgCountByHospitalization = 'Statistics by hospitalization'
t.home.msgCountByHospitalization1 = 'Statistics of inpatient medical records and main diagnoses for discharge diagnosis'
t.home.msgCountByHospitalization2 = 'Statistical data of inpatient medical records and surgical records'
t.home.msgCountByHospitalization3 = 'Statistical data on inpatient medical records and medical orders'
t.home.msgCountByHospitalization4 = 'Statistical data of inpatient medical records and medical orders'
t.home.msgCountByOutpatient = 'According to outpatient statistics'
t.home.msgCountByOutpatient1 = 'Statistical Outpatient Medical Records: Main Diagnosis of Outpatient Diagnosis'
t.home.msgCountByOutpatient2 = 'Collect surgical data from outpatient medical records'
t.home.msgCountByOutpatient3 = 'Statistical data on outpatient medical records and check out medical orders'
t.home.msgCountByOutpatient4 = 'Statistical data on outpatient medical records and check on medical orders'
t.home.msgVisitDistribution = `The above data is based on the statistics of patient medical record data within the user's permission range for outpatient medical record examination and medical orders`
t.home.medicTendency = 'Diagnosis and treatment trends'
t.home.msgHospitalizationTop5 = 'Count the top 5 trends in the number of diagnoses, surgeries, tests, and examinations of inpatient medical records over a period of time'
t.home.msgVisitDistributionTop5 = 'Count the top 5 trends in the number of diagnoses, surgeries, tests, and examinations of outpatient medical records over a period of time'
t.home.caseDistribution3 = 'Count the visit time of all medical records'

// 专病
t.specialDisease = {}
t.specialDisease.visitInfo = 'Visit information'
t.specialDisease.heartFailure = 'Heart failure'
t.specialDisease.heartFailureResearch = 'Research on Heart Failure Diseases'
t.specialDisease.cardiovascular1 = 'Cardiovascular Medicine Ward 1'
t.specialDisease.cardiovascular2 = 'Cardiovascular Internal Medicine Clinic'
t.specialDisease.userCountTop5 = 'Top 5 User Project Quantity'
t.specialDisease.departmentPros = 'Department project quantity statistics'
t.specialDisease.baseSituation = 'Basic information'
t.specialDisease.caseHomeDetail = 'Characteristics of medical record homepage'
t.specialDisease.treatmentData = 'Diagnosis and treatment data'
t.specialDisease.homeStatisticsReport = 'Home page statistical report'
t.specialDisease.predictAlive = 'Survival and Death Prediction'
t.specialDisease.dataDetail = 'Data Details'
t.specialDisease.proportion = 'Proportion'
t.specialDisease.numOfHospitalizations = 'Number of hospitalizations'
t.specialDisease.peopleNum = 'Number of people'
t.specialDisease.visitAge = 'Age at Visit'
t.specialDisease.residentPhysician = 'Resident physician'
t.specialDisease.hospitalizationDays = 'Hospitalization days'
t.specialDisease.admissionPathway = 'Admission pathway'
t.specialDisease.chinesePatentDrug = 'Chinese patent drug'
t.specialDisease.chineseHerbalMedicine = 'Chinese herbal medicine'
t.specialDisease.dischargeStatus = 'Discharge status'
t.specialDisease.surgicalTrends = 'Surgical trends'
t.specialDisease.inspectionTrend = 'Inspection trend'
t.specialDisease.checkTrends = 'Check trends'
t.specialDisease.hypertension = 'Hypertension'
t.specialDisease.coronaryHeartDisease = 'Coronary heart disease'
t.specialDisease.patientID = 'Patient ID'
t.specialDisease.admissionDiagnosis = 'Admission diagnosis'
t.specialDisease.symptomsAndSigns = 'Symptoms and signs'

t.exportRecord = {}
t.exportRecord.createTimeOrder = 'Creation time (order)'
t.exportRecord.createTimeReverse = 'Creation time (in reverse order)'
t.exportRecord.authing = 'In review'
t.exportRecord.approved = 'Approved'
t.exportRecord.reviewFailed = 'Review failed'
t.exportRecord.audit = 'Audit'
t.exportRecord.allReviewStatus = 'All review status'
t.exportRecord.askClearRecords = 'Are you sure to clear all records? After clearing, you cannot view or operate again!'
t.exportRecord.getFilePwd = 'Obtain file password'
t.exportRecord.submit = 'Submit application'
t.exportRecord.fail = 'Export failed'
t.exportRecord.success = 'Export successful'
t.exportRecord.isExporting = 'Export in progress'
t.exportRecord.condition = 'Filter Criteria'
t.exportRecord.bringInto = 'Inclusion Criteria'
t.exportRecord.bringOut = 'Exclusion Criteria'
t.exportRecord.exportMetrics = 'Export metrics'
t.exportRecord.exportStatus = 'Export Status'
t.exportRecord.viewExportLog = 'Viewing Export Logs'
t.exportRecord.exportLines = 'Number of exported rows'
t.exportRecord.inputApplyReason = 'Please enter the application reason'
t.exportRecord.applyReasonCaution = 'Reason for application is a required field, please fill it out and submit!'
t.exportRecord.applyAgainCaution = 'Reason for reapplication is required. Please fill it out and submit!'
t.exportRecord.uploadCaution = 'You must upload an attachment before submitting!'
t.exportRecord.askConfirmApply = 'Are you sure you want to submit the application to the OA system for review?'
t.exportRecord.extraFile = 'Please upload additional materials below: (required)'
t.exportRecord.dragRemind = 'Drag files here, or'
t.exportRecord.clickUpload = 'Click to upload'
t.exportRecord.uploadLimit = 'Only jpg/png files can be uploaded, and no more than 10Mb'
t.exportRecord.applyAgain = 'Reapply'
t.exportRecord.verifyIdentity = 'Verify my identity'
t.exportRecord.filePwd = 'File password'
t.exportRecord.systemPwd = 'System Password'
t.exportRecord.exportLog = 'Export log'
t.exportRecord.reasonCaution = 'Please enter the reason for submitting the application'
t.exportRecord.reasonAgainCaution = 'Please enter the reason for resubmitting the application'
t.exportRecord.accountCaution = 'Please enter an account'
t.exportRecord.pwdCaution = 'Please enter the password'
t.exportRecord.applyOAExport = 'Submit OA export application'
t.exportRecord.clickDownload = 'Have clicked download'
t.exportRecord.checkRecordDetail = 'Audit Record Details'
t.exportRecord.exportInfoDetail = 'Export information details'
t.exportRecord.exportObservePeriod = 'Export observation stage'
t.exportRecord.reInputReason = 'Fill in the reason for reapplication'
t.exportRecord.noPreview = 'There is currently no preview for the current format data'

t.timeline = {}
t.timeline.allAdm = 'All visits'
t.timeline.getPatientInfoErr = 'Error in obtaining patient information'
t.timeline.conditionErr = 'Error in obtaining patient information'
t.timeline.getAdmListErr = 'Error in obtaining visit list data'
t.timeline.physicalExaminationDepartment = 'Physical examination department'
t.timeline.physicalExaminationDate = 'Date of physical examination'
t.timeline.mr_adm_dept = 'Inpatient department'
t.timeline.mr_adm_datetime = 'date of admission'
t.timeline.mr_dish_datetime = 'Discharge date'
t.timeline.item = 'Item'
t.timeline.msgBox1 = 'Inpatient display and discharge diagnosis'
t.timeline.msgBox2 = 'Outpatient (emergency) diagnosis display Outpatient (emergency) diagnosis'
t.timeline.msgBox3 = 'Note: The diagnosis name preceded by * is the main diagnosis'
t.timeline.msgBox4 = 'Display the surgical name of the surgical record'

// 审核记录
t.auditRecord = {}
t.auditRecord.pass = 'Pass'
t.auditRecord.reject = 'Reject'
t.auditRecord.applyer = 'Applicant'
t.auditRecord.applyReason = 'Reason for application'
t.auditRecord.viewReasonFile = 'View the application reason attachment'
t.auditRecord.currStatus = 'Current state'
t.auditRecord.auditor = 'Reviewer'
t.auditRecord.auditTime = 'Audit time'
t.auditRecord.rejectReason = 'Reason for failure to pass the audit'
t.auditRecord.viewFileFirst = 'Please review the attachment first!'
t.auditRecord.inputRejectReasonFirst = 'Please fill in the reason for rejection first'
t.auditRecord.auditRecordDetail = 'Audit Record Details'
t.auditRecord.exportMethod = 'Export method'

t.dataStore = {}
t.dataStore.fullTextSearch = 'Full-text searching'
t.dataStore.advancedSearch = 'Advanced Search'
t.dataStore.patientList = 'Patient List'
t.dataStore.dataList = 'Data list'
t.dataStore.theme = 'Theme'
t.dataStore.inputRegno = 'Please enter the registration number'
t.dataStore.admno = 'Visit number'
t.dataStore.inputAdmno = 'Please enter the visit number'
t.dataStore.clear = 'Clear screen'
t.dataStore.claim = 'Description: The purpose of the current function is to understand the data situation, only displaying partial patient data. If you need to view all data, the current function does not support it at the moment'
t.dataStore.selectModel1st = 'Please select a model first'
t.dataStore.admTime1st = 'First visit time'
t.dataStore.admAge1st = 'Age of initial diagnosis'
t.dataStore.admTimeLast = 'Last visit time'

t.dsl = {}
t.dsl.dslSearch = 'DSL Query Statement'
t.dsl.search = 'Query statement'
t.dsl.please = 'Please'
t.dsl.select = 'Select'
t.dsl.searchDSL = 'Query DSL statement'
t.dsl.startSearch = 'Starting Search'
t.dsl.inputConditionDesc = 'Please enter a description of the query criteria'
t.dsl.inputKey = 'Please enter query keywords'
t.dsl.application = 'Application'
t.dsl.dslStatement = 'DSL statement'

// 全文检索
t.fullText = {}
t.fullText.placeholder = 'Disease, medication, surgery, testing, examination, etc'
t.fullText.searchHistory = 'Search History'
t.fullText.unlimit = 'No limit'
t.fullText.positive = 'Positive'
t.fullText.negative = 'Negative'
t.fullText.filterWords = 'Filter search terms'
t.fullText.synonym = 'Synonym'
t.fullText.setRange = 'Specified range'
t.fullText.startDate = 'Start date'
t.fullText.endDate = 'End date'
t.fullText.to = 'to'
t.fullText.dischargeTime = 'Discharge time'
t.fullText.dataType = 'Data type'
t.fullText.searchErr = 'Query error'
t.fullText.getIndexIdErr = 'Failed to obtain indexId, unable to jump!'
t.fullText.checkGroupName = 'Inspection set name'
t.fullText.setCondition1st = 'There is currently no patient data available, please set the conditions'
t.fullText.noMatchPatient = 'No matching patient information found'
t.fullText.searching = 'Working hard to search'
t.fullText.handleErr = 'Execution error'

// 高级检索
t.advanced = {}
t.advanced.accordingTo = 'According to'
t.advanced.searchTemplate = 'Query template'
t.advanced.joinTemplate = 'Add Query Template'
t.advanced.joinProject = 'Join Project'
t.advanced.setSearchDesc = 'Set Query Description'
t.advanced.inputSearchDesc = 'Enter query description'
t.advanced.searchDescNotNull = 'Search description cannot be empty'
t.advanced.brintIntoNotNull = 'Inclusion criteria cannot be empty'
t.advanced.checkSearchField = 'Please check the query fields'
t.advanced.exportSuccess = 'Export successful, please go to the export record to view or download'
t.advanced.inputCondition1st = 'Please fill in the discharge standard first'
t.advanced.selectProject = 'Select project'
t.advanced.proCaution = 'Please select a project'
t.advanced.noProject = 'There are currently no projects available. Please contact the system administrator of the EDC system to create the project before joining the group。'
t.advanced.noProject1 = 'There is currently no project data available'
t.advanced.joinEDCMsg = 'Patients are being enrolled in the EDC, and this process may take some time. Please be patient and go to the EDC to check and confirm'
t.advanced.matchCase = 'Find relevant cases for you'
t.advanced.unit1 = ''
t.advanced.msgTips1 = 'Incorporate search results into the project for prospective research'
t.advanced.msgTips2 = 'Export search results to Excel'
t.advanced.msgTips3 = 'Visualization Chart Display of Search Results'
t.advanced.msgTips4 = 'Online analysis of search results'
t.advanced.dataExport = 'Data export'
t.advanced.statistics = 'Statistic'
t.advanced.analysis = 'Data analysis'
t.advanced.birthday = 'Date of birth'
t.advanced.allCase = 'All medical records'
t.advanced.unit2 = ''
t.advanced.matchCase = 'Eligible medical records'
t.advanced.diagnostic = 'Diagnostic Information'
t.advanced.viewMore = 'See more'
t.advanced.toDetailFail = 'No visit number or registration number found, unable to view case details'
t.advanced.batchFilterPatient = 'Batch screening of cases'
t.advanced.selectFilterField = 'Select Filter Fields'
t.advanced.uploadExcelFile = 'Upload Excel file'
t.advanced.clickUpload = 'Click to upload'
t.advanced.downloadTemplate = 'Download Template'
t.advanced.patientID = 'Patient ID number'
t.advanced.uploadTypeLimit = 'Please upload a file of type. xls/. xlsx'
t.advanced.please = 'Please'
t.advanced.add = 'Add'
t.advanced.orUse = 'or use'
t.advanced.helpSearch = 'Help Query'
t.advanced.viewDSL = 'View DSL'
t.advanced.viewJson = 'View JSON'
t.advanced.viewDesc = 'View Text Description'
t.advanced.copyJson = 'Copy JSON'
t.advanced.copySuccess = 'Successfully copied to clipboard!'
t.advanced.checkBringInto = 'Please check the inclusion criteria'
t.advanced.checkBringOut = 'Please check the exclusion criteria'
t.advanced.allCase = 'All cases'
t.advanced.collection = 'My Collection'
t.advanced.inputExportTempDesc = 'Please enter a description of the export template'
t.advanced.accurateExport = 'Precise Export'
t.advanced.sqlExport = 'Original Table Export'

// 数据分析
t.analysis = {}
t.analysis.addSet = 'New Dataset'
t.analysis.setObPeriod = 'Set observation stage'
t.analysis.setDataFormat = 'Set Data Format'
t.analysis.generateSet = 'Generate Data Set'
t.analysis.addObPeriod = 'Add Observation Phase'
t.analysis.lastStep = 'Back'
t.analysis.next = 'Next'
t.analysis.generate = 'Generate'
t.analysis.addObPeriod1st = 'Please add an observation stage first'
t.analysis.inputSetName = 'Dataset Name'
t.analysis.selectTable = 'Select Table'
t.analysis.batchSetSpecialValue = 'Batch Set Special Values'
t.analysis.selectSpecialValue = 'Select Special Values'
t.analysis.first = 'First'
t.analysis.last = 'Last'
t.analysis.obPeriod = 'Observation stage'
t.analysis.setExportItem = 'Set export items'
t.analysis.allPeriod = 'Full Stage'
t.analysis.comparisonCondition = 'Comparison Conditions'
t.analysis.timeRange = 'Time frame'
t.analysis.exporting = 'Exporting data for analysis, please go later'
t.analysis.analysisSystem = 'On-line Analysis System'
t.analysis.pleaseSelectObPeriod = 'Please select the observation stage'
t.analysis.getObPeriodErr = 'Failed to obtain observation stage,'
t.analysis.setObPeriod1st = 'Please set the observation stage first'
t.analysis.setObPeriod1st2 = 'There is an observation stage where no export item has been set. Please set the observation stage export item first'
t.analysis.exportingWaiting = 'Export is in progress, please be patient and wait'
t.analysis.filterNeedAdd = 'Filter conditions not added'
t.analysis.researchObject = 'Research object'
t.analysis.patient = 'Patient'
t.analysis.dataFormat = 'Data format'
t.analysis.custom = 'Custom'
t.analysis.selectAdm = 'Select visit'
t.analysis.firstAdm = 'First visit'
t.analysis.lastAdm = 'Last visit'
t.analysis.setSpecialValue = 'Set special values'
t.analysis.table = 'Table'

// 历史与收藏
t.history = {}
t.history.collection = 'Favorite'
t.history.removeCollection = 'Cancel Favorite'
t.history.medicalRecordNum = 'Medical record number'

// 查询模板
t.template = {}
t.template.inputSearchDesc = 'Please enter a query description'

// 统计信息
t.statistics = {}
t.statistics.statisticsInfo = 'Statistic'
t.statistics.basicSituation = 'Basic information of medical records'
t.statistics.relatedMRTop10 = 'Top 10 related medical record statistics'
t.statistics.patientMRDistribution = 'Patient medical record distribution'
t.statistics.number = 'Number'

// 组件
t.component = {}
t.component.formatError = 'Format code error:'
t.component.initCodeMirrorErr = 'Error initializing codemirror:'
t.component.getInputValErr = 'Failed to obtain the content of the edit box:'
t.component.updateInputValErr = 'Failed to modify the content of the edit box:'
t.component.inputStrLimit = 'The content of the edit box can only be a string'
t.component.selectIndex = 'Select metrics'
t.component.addSameLevelCond = 'Add sibling conditions'
t.component.addLevel2Cond = 'Add Secondary Condition'
t.component.turn2Level2 = 'Convert to secondary condition'
t.component.turn2Level1 = 'Convert to first level condition'
t.component.addChildCond = 'Add Sub Condition'
t.component.allField = 'All fields'
t.component.specialDiseaseField = 'Specialized disease field'
t.component.getSearchItemErr = 'Error in obtaining query items'
t.component.startDate = 'Start Date'
t.component.endDate = 'End Date'
t.component.to = 'To'
t.component.click2Edit = 'Click to modify'
t.component.enter2Save = 'Please enter the text and press Enter to confirm'
t.component.loading = 'Loading'
t.component.selectModel = 'Select Model'
t.component.onlyNotSpecial = 'Only load indexes for non specialized disease models'
t.component.configModel1st = 'There is currently no relevant model data available, relevant models need to be configured'
t.component.toEnableModel = 'Please contact the administrator to enable the model'
t.component.defineFilterCond = 'Custom Filter Criteria'
t.component.here = 'Here'
t.component.noSuitFilter = 'No suitable filtering conditions? click'
t.component.itemValue = 'Attribute item'
t.component.filterCond = 'Filter conditions'
t.component.editCond = 'Edit conditions'
t.component.least1NotNull = 'At least one filter item property cannot be empty'
t.component.any = 'Any'
t.component.setExportProp = 'Edit conditions'
t.component.noExportableProp = 'There are currently no attributes to export'
t.component.exportSetConfig = 'Export Settings Configuration'
t.component.separateByAdvice = 'Split columns by order item:'
t.component.separateByCheckItem = 'Split columns by check items:'
t.component.separateByItem = 'Split columns by inspection item:'
t.component.montageRecords = 'Symbols used to concatenate multiple records:'
t.component.dataSameTable = 'Used for concatenating multiple data symbols in the same table:'
t.component.compressExportFile = 'Do you want to compress the exported file:'
t.component.writeIntoSheet = 'Do you want to write a new sheet when the exported columns exceed Excel:'
t.component.exportMsg1 = 'There are currently no special values to export. Please select an export item with special values first'
t.component.exportMsg2 = 'Note: Special export value configuration needs to be configured in the background management - Special Value Configuration'
t.component.setExportSpecial = 'Set export special values'
t.component.inputExportFileName = 'Please enter the export file name'
t.component.fileName = 'File Name'
t.component.exportFormatClaim = 'Export format: one observation stage, one tab, one row of data for each observation stage'
t.component.name = 'Name'
t.component.total = 'Total'
t.component.percent = 'Percent'
t.component.exportModelType = 'Export Model Types:'
t.component.observePeriod = 'Observation stage'
t.component.first = 'Fist'
t.component.exportSuccess = 'Export task submission completed. Do you want to jump to the export record page and view the export record?'
t.component.filterRange = 'Filter Range'
t.component.filterRangeTime = 'Time range spanning multiple visits'
t.component.filterRangeHit = 'Hit visit'
t.component.customExport = 'Custom Export'
t.component.setExportContent = 'Free settings for exporting content'
t.component.setExportFilterCond = 'Set export filter conditions'
t.component.startExport = 'Starting Export'
t.component.useExportHistory = 'Reuse History Export'
t.component.useExportHistory1 = 'Direct Reuse History Export'
t.component.selectExportRecord = 'Select Export Record'
t.component.updateExportContent = 'Modify export content'
t.component.exportTemplate = 'Export Template'
t.component.frequentExport = "Recommended by administrator, commonly exported"
t.component.selectExportTemplate = "Select Export Template"
t.component.setExportDesc = 'Set export description'
t.component.checkCond = 'Please check the conditions'

export default t
