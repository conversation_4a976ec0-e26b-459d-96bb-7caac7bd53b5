<template>
  <div class="text-content">
    <div v-show="!editing" class="input-text" @dblclick="edit">
      {{ dataStr }}
    </div>
    <hos-input
              v-show="editing"
              ref="dataStr"
              v-model="inputStr"
              type="text"
              class="input-text"
              :placeholder="inputStr"
              size="mini"
              @keyup.enter.native="save"
              @blur="blurEvent"
    />
    <slot />
  </div>
</template>
<script>
export default {
  name:'EditText',
  props: {
    dataStr: {
      type: [Number, String],
      default () {
        return ''
      }
    },
  },
  data() {
    return {
      editing: false,
      inputStr: ''
    }
  },
  watch: {
    editing(val) {
      if(val) {
        this.inputStr = this.dataStr;
      }
    }
  },
  methods: {
    edit: function() {
      this.editing= true
      this.inputStr = this.dataStr
      this.$nextTick(() => {
        this.$refs['dataStr'].focus()
      })
    },
    save(e) {
      // 在回车事件中，手动触发input的blur事件，避免回车重复触发事件
      e.target.blur()
    },
    blurEvent(e) {
      if (this.inputStr === null || this.inputStr === undefined) {
        this.editing = false
      } else {
        if (this.inputStr === this.dataStr) {
          this.editing = false
        } else {
          this.editing = false
          this.$emit('afterSave', this.inputStr)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.text-content{
  /* text-align: center; */
  height: 28px;
}
.input-text{
  /* background-color: #fff; */
  // padding: 0 5px;
  cursor: pointer;
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;

  ::v-deep.el-input__inner {
    padding: 0;
  }
}

.span-check-result {
  color: #409eff;
  text-decoration: underline;
}

</style>

<style lang="scss">
  .edit-text--autocomplete-class {
    width: auto !important;

    // ::v-deep .el-autocomplete-suggestion li {
    //   padding: 0;
    // }
  }
</style>