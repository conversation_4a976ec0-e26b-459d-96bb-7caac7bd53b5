export const queryListApi = (params) => {
    return {
        url: 'quality/medical-data-standards/page',
        method: 'get',
        params,
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'quality/medical-data-standards/detail/' + id,
        method: 'get',
    }
}

export const addApi = (data) => {
    return {
        url: 'quality/medical-data-standards/insert',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'quality/medical-data-standards/update',
        method: 'post',
        data,
    }
}

export const deleteApi = (id) => {
    return {
        url: 'quality/medical-data-standards/delete/' + id,
        method: 'delete',
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'quality/medical-data-standards/batch-delete',
        method: 'post',
        data
    }
}

export const getAllStandard = () => {
    return {
        url: 'quality/medical-data-standards/list',
        method: 'get',
    }
}