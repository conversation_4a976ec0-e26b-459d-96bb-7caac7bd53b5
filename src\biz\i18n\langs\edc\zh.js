const t = {}

t.loading = '加载中...'

t.brand = {}
t.brand.lg = '科研数据采集系统'
t.brand.lg1 = '科研数据采集系统'
t.brand.mini = 'EDC'

t.add = '新增'
t.delete = '删除'
t.deleteBatch = '删除'
t.update = '修改'
t.auth = '授权'
t.query = '查询'
t.reset = '重置'
t.export = '导出'
t.handle = '操作'
t.confirm = '确定'
t.cancel = '取消'
t.logout = '退出'
t.downLoad = '下载'
t.select = '选择'
t.unfold = '展开'
t.fold = '收起'
t.save = '保存'
t.close = '关闭'
t.showDetail = '查看详情'
t.goBack = '返回'
t.backupData = '备份数据'
t.superUser = '超级用户'
t.insert = '插入'
t.move = '移动'
t.tishi = '提示'
t.cancelLogin = '取消登录'
t.keepLogin = '继续登录'
t.emptyData = '暂无数据'
t.all = '全部'
t.category = '分类'

t.more = '更多'

t.yes = '是'
t.no = '否'

t.day = '天'

t.prompt = {}
t.prompt.title = '提示'
t.prompt.info = '确定进行[{handle}]操作?'
t.prompt.deleteInfo = '确定删除[{content}]吗?'
t.prompt.success = '操作成功'
t.prompt.failed = '操作失败'
t.prompt.deleteBatch = '请选择删除项'
t.prompt.logout = '注销当前账户吗?'

t.message = {}
t.message.logoutCancel = '放弃注销用户'

t.validate = {}
t.validate.required = '必填项不能为空'
t.validate.format = '{attr}格式错误'
t.validate.correctIdCardNum = '请输入正确的身份证号码'

t.upload = {}
t.upload.text = '将文件拖到此处，或<em>点击上传</em>'
t.upload.tip = '只支持{format}格式文件！'
t.upload.button = '点击上传'

t.datePicker = {}
t.datePicker.range = '至'
t.datePicker.start = '开始日期'
t.datePicker.end = '结束日期'

t.fullscreen = {}
t.fullscreen.prompt = '您的浏览器不支持此操作'

t.updatePassword = {}
t.updatePassword.title = '修改密码'
t.updatePassword.username = '账号'
t.updatePassword.password = '原密码'
t.updatePassword.newPassword = '新密码'
t.updatePassword.comfirmPassword = '确认密码'
t.updatePassword.validate = {}
t.updatePassword.validate.comfirmPassword = '确认密码与新密码输入不一致'

t.theme = {}
t.theme.d2 = 'D2Admin 经典'
t.theme.violet = '紫罗兰'
t.theme.line = '简约线条'
t.theme.star = '繁星'
t.theme.tomorrowNightBlue = 'Tomorrow Night Blue'
t.theme.list = {}
t.theme.list.button = '使用'
t.theme.list.buttonActive = '已激活'

t.layout = {}
t.layout.hello = '你好'
t.layout.logout = '注销'
t.layout.updatePassword = '修改密码'
t.layout.tooltip = {}
t.layout.tooltip.fullscreen = '全屏'
t.layout.tooltip.fullscreenActive = '退出全屏'
t.layout.tooltip.theme = '主题'
t.layout.tooltip.todo = '待办'
t.layout.tooltip.search = '搜索'
t.layout.search = {}
t.layout.search.placeholder = '搜索页面'
t.layout.search.tip1 = '您可以使用快捷键'
t.layout.search.tip2 = '唤醒搜索面板，按'
t.layout.search.tip3 = '关闭。'
t.layout.backToIndex = '进入系统后台'
t.layout.backToUserIndex = '返回个人中心'
t.layout.changeProjectIndex = '切换其他项目'
t.layout.about = '关于'

t.layoutTab = {}
t.layoutTab.index = '首页'
t.layoutTab.noName = '未命名'

t.contentTabs = {}
t.contentTabs.closeCurrent = '关闭当前标签页'
t.contentTabs.closeOther = '关闭其它标签页'
t.contentTabs.closeAll = '关闭全部标签页'

/* 页面 */
t.notFound = {}
t.notFound.desc = '抱歉！您访问的页面<em>失联</em>啦...'
t.notFound.back = '上一页'
t.notFound.home = '首页'

t.login = {}
t.login.language = '语言'
t.login.motto = '时间是一切财富中最宝贵的财富'
t.login.form = {}
t.login.form.placeholderUsername = '登录名'
t.login.form.placeholderPassword = '密码'
t.login.form.placeholderCaptcha = '验证码'
t.login.form.textSubmitButton = '登录'
t.login.form.textForget = '忘记密码'
t.login.form.textSignUp = '注册用户'
t.login.footer = {}
t.login.footer.buttonHelp = '帮助'
t.login.footer.buttonPrivacy = '隐私'
t.login.footer.buttonClause = '条款'
t.login.copyright = {}
t.login.copyright.p1 = 'Copyright'
t.login.copyright.p2 = (new Date()).getFullYear() + ' EDC 东华医为科技有限公司'
t.login.copyright.p3 = '@dhcc'
t.login.ruleMessage = {}
t.login.ruleMessage.username = '请输入登录名'
t.login.ruleMessage.password = '请输入密码'
t.login.ruleMessage.captcha = '请输入验证码'

// 登录页许可证相关
t.login.appVersion = '版本'
t.login.description = '说明'
t.login.expirationDate = '有效期至'
t.login.left = '还剩'
t.login.renewLicense = '更新许可证'
t.login.getLicense = '获取许可证'
t.login.activateLicense = '激活许可证'
t.login.serviceMachineCode = '服务机器码'
t.login.enterMachineCode = '请输入机器码'
t.login.copy = '复制'
t.login.installationLicenseSerialNumber = '安装许可序列号'
t.login.enterSerialNumber = '请输入序列号'
t.login.activate = '激活'

t.home = {}
t.home.sysInfo = {}
t.home.sysInfo.name = '系统名称'
t.home.sysInfo.nameVal = 'boot-admin'
t.home.sysInfo.version = '版本信息'
t.home.sysInfo.versionVal = process.env.VUE_APP_VERSION
t.home.sysInfo.osName = '操作系统'
t.home.sysInfo.osVersion = '系统版本'
t.home.sysInfo.osArch = '系统架构'
t.home.sysInfo.processors = 'CPU核数'
t.home.sysInfo.totalPhysical = '系统内存'
t.home.sysInfo.freePhysical = '剩余内存'
t.home.sysInfo.memoryRate = '内存使用'
t.home.sysInfo.usageRate = '内存使用率'
t.home.sysInfo.userLanguage = '系统语言'
t.home.sysInfo.jvmName = 'JVM信息'
t.home.sysInfo.javaVersion = 'JVM版本'
t.home.sysInfo.javaHome = 'JAVA_HOME'
t.home.sysInfo.userDir = '工作目录'
t.home.sysInfo.javaTotalMemory = 'JVM占用内存'
t.home.sysInfo.javaFreeMemory = 'JVM空闲内存'
t.home.sysInfo.javaMaxMemory = 'JVM最大内存'
t.home.sysInfo.javaUsageRate = 'JVM内存使用率'
t.home.sysInfo.userName = '当前用户'
t.home.sysInfo.systemCpuLoad = 'CPU负载'
t.home.sysInfo.userTimezone = '系统时区'

/* 模块 */
t.model = {}
t.model.name = '名称'
t.model.key = '标识'
t.model.version = '版本'
t.model.createTime = '创建时间'
t.model.lastUpdateTime = '更新时间'
t.model.design = '在线设计'
t.model.deploy = '部署'
t.model.description = '描述'

t.process = {}
t.process.name = '名称'
t.process.key = '标识'
t.process.deployFile = '部署流程文件'
t.process.id = '流程ID'
t.process.deploymentId = '部署ID'
t.process.version = '版本'
t.process.resourceName = 'XML'
t.process.diagramResourceName = '图片'
t.process.deploymentTime = '部署时间'
t.process.active = '激活'
t.process.suspend = '挂起'
t.process.convertToModel = '转换为模型'

t.running = {}
t.running.id = '实例ID'
t.running.definitionKey = '定义Key'
t.running.processDefinitionId = '定义ID'
t.running.processDefinitionName = '定义名称'
t.running.activityId = '当前环节'
t.running.suspended = '是否挂起'
t.running.suspended0 = '否'
t.running.suspended1 = '是'

t.news = {}
t.news.title = '标题'
t.news.pubDate = '发布时间'
t.news.createDate = '创建时间'
t.news.content = '内容'

t.schedule = {}
t.schedule.beanName = 'bean名称'
t.schedule.beanNameTips = 'spring bean名称, 如: testTask'
t.schedule.pauseBatch = '暂停'
t.schedule.resumeBatch = '恢复'
t.schedule.runBatch = '执行'
t.schedule.log = '日志列表'
t.schedule.params = '参数'
t.schedule.cronExpression = 'cron表达式'
t.schedule.cronExpressionTips = '如: 0 0 12 * * ?'
t.schedule.remark = '备注'
t.schedule.status = '状态'
t.schedule.status0 = '暂停'
t.schedule.status1 = '正常'
t.schedule.statusLog0 = '失败'
t.schedule.statusLog1 = '成功'
t.schedule.pause = '暂停'
t.schedule.resume = '恢复'
t.schedule.run = '执行'
t.schedule.jobId = '任务ID'
t.schedule.times = '耗时(单位: 毫秒)'
t.schedule.createDate = '执行时间'

t.mail = {}
t.mail.name = '名称'
t.mail.config = '邮件配置'
t.mail.subject = '主题'
t.mail.createDate = '创建时间'
t.mail.send = '发送邮件'
t.mail.content = '内容'
t.mail.smtp = 'SMTP'
t.mail.port = '端口号'
t.mail.username = '邮箱账号'
t.mail.password = '邮箱密码'
t.mail.mailTo = '收件人'
t.mail.mailCc = '抄送'
t.mail.params = '模板参数'
t.mail.paramsTips = '如：{"code": "123456"}'
t.mail.templateId = '模版ID'
t.mail.status = '状态'
t.mail.status0 = '发送失败'
t.mail.status1 = '发送成功'
t.mail.mailFrom = '发送者'
t.mail.createDate = '发送时间'

t.sms = {}
t.sms.mobile = '手机号'
t.sms.status = '状态'
t.sms.status0 = '发送失败'
t.sms.status1 = '发送成功'
t.sms.config = '短信配置'
t.sms.send = '发送短信'
t.sms.platform = '平台类型'
t.sms.platform1 = '阿里云'
t.sms.platform2 = '腾讯云'
t.sms.params = '参数'
t.sms.paramsTips = '如：{"code": "123456"}'
t.sms.params1 = '参数1'
t.sms.params2 = '参数2'
t.sms.params3 = '参数3'
t.sms.params4 = '参数4'
t.sms.createDate = '发送时间'
t.sms.aliyunAccessKeyId = 'Key'
t.sms.aliyunAccessKeyIdTips = '阿里云AccessKeyId'
t.sms.aliyunAccessKeySecret = 'Secret'
t.sms.aliyunAccessKeySecretTips = '阿里云AccessKeySecret'
t.sms.aliyunSignName = '短信签名'
t.sms.aliyunTemplateCode = '短信模板'
t.sms.aliyunTemplateCodeTips = '短信模板CODE'
t.sms.qcloudAppId = 'AppId'
t.sms.qcloudAppIdTips = '腾讯云AppId'
t.sms.qcloudAppKey = 'AppKey'
t.sms.qcloudAppKeyTips = '腾讯云AppKey'
t.sms.qcloudSignName = '短信签名'
t.sms.qcloudTemplateId = '短信模板'
t.sms.qcloudTemplateIdTips = '短信模板ID'

t.oss = {}
t.oss.config = '云存储配置'
t.oss.upload = '上传文件'
t.oss.url = 'URL地址'
t.oss.createDate = '创建时间'
t.oss.type = '类型'
t.oss.type1 = '七牛'
t.oss.type2 = '阿里云'
t.oss.type3 = '腾讯云'
t.oss.type4 = 'FastDFS'
t.oss.type5 = '本地上传'
t.oss.qiniuDomain = '域名'
t.oss.qiniuDomainTips = '七牛绑定的域名'
t.oss.qiniuPrefix = '路径前缀'
t.oss.qiniuPrefixTips = '不设置默认为空'
t.oss.qiniuAccessKey = 'AccessKey'
t.oss.qiniuAccessKeyTips = '七牛AccessKey'
t.oss.qiniuSecretKey = 'SecretKey'
t.oss.qiniuSecretKeyTips = '七牛SecretKey'
t.oss.qiniuBucketName = '空间名'
t.oss.qiniuBucketNameTips = '七牛存储空间名'
t.oss.aliyunDomain = '域名'
t.oss.aliyunDomainTips = '阿里云绑定的域名，如：http://cdn.renren.io'
t.oss.aliyunPrefix = '路径前缀'
t.oss.aliyunPrefixTips = '不设置默认为空'
t.oss.aliyunEndPoint = 'EndPoint'
t.oss.aliyunEndPointTips = '阿里云EndPoint'
t.oss.aliyunAccessKeyId = 'AccessKeyId'
t.oss.aliyunAccessKeyIdTips = '阿里云AccessKeyId'
t.oss.aliyunAccessKeySecret = 'AccessKeySecret'
t.oss.aliyunAccessKeySecretTips = '阿里云AccessKeySecret'
t.oss.aliyunBucketName = 'BucketName'
t.oss.aliyunBucketNameTips = '阿里云BucketName'
t.oss.qcloudDomain = '域名'
t.oss.qcloudDomainTips = '腾讯云绑定的域名'
t.oss.qcloudPrefix = '路径前缀'
t.oss.qcloudPrefixTips = '不设置默认为空'
t.oss.qcloudAppId = 'AppId'
t.oss.qcloudAppIdTips = '腾讯云AppId'
t.oss.qcloudSecretId = 'SecretId'
t.oss.qcloudSecretIdTips = '腾讯云SecretId'
t.oss.qcloudSecretKey = 'SecretKey'
t.oss.qcloudSecretKeyTips = '腾讯云SecretKey'
t.oss.qcloudBucketName = 'BucketName'
t.oss.qcloudBucketNameTips = '腾讯云BucketName'
t.oss.qcloudRegion = '所属地区'
t.oss.qcloudRegionTips = '请选择'
t.oss.qcloudRegionBeijing1 = '北京一区（华北）'
t.oss.qcloudRegionBeijing = '北京'
t.oss.qcloudRegionShanghai = '上海（华东）'
t.oss.qcloudRegionGuangzhou = '广州（华南）'
t.oss.qcloudRegionChengdu = '成都（西南）'
t.oss.qcloudRegionChongqing = '重庆'
t.oss.qcloudRegionSingapore = '新加坡'
t.oss.qcloudRegionHongkong = '香港'
t.oss.qcloudRegionToronto = '多伦多'
t.oss.qcloudRegionFrankfurt = '法兰克福'
t.oss.localDomain = '域名'
t.oss.localDomainTips = '绑定的域名，如：http://cdn.renren.io'
t.oss.fastdfsDomain = '域名'
t.oss.fastdfsDomainTips = '绑定的域名，如：http://cdn.renren.io'
t.oss.localPrefix = '路径前缀'
t.oss.localPrefixTips = '不设置默认为空'
t.oss.localPath = '存储目录'
t.oss.localPathTips = '如：D:/upload'

t.dept = {}
t.dept.name = '名称'
t.dept.parentName = '上级部门'
t.dept.id = '部门id'
t.dept.isResearchCenter = '是否可设置为研究中心'
t.dept.sort = '排序'
t.dept.parentNameDefault = '一级部门'

t.dict = {}
t.dict.cateName = '分类名称'
t.dict.cateCode = '分类编码'
t.dict.dictValue = '值'
t.dict.sort = '排序'
t.dict.remark = '备注'
t.dict.createDate = '创建时间'
t.dict.detail = '查看字典项'
t.dict.item = '字典项'
t.dict.itemName = '字典名称'
t.dict.itemCode = '字典编码'
t.dict.itemValue = '字典值'
t.dict.pinyin = '字典拼音'
t.dict.excelImport = 'Excel导入'
t.dict.firstLetter = '字典拼音首字母'

t.resource = {}
t.resource.cateName = '分类名称'
t.resource.cateCode = '分类编码'
t.resource.dictValue = '值'
t.resource.sort = '排序'
t.resource.datatype = '数据类型'
t.resource.createDate = '创建时间'
t.resource.detail = '查看资源项'
t.resource.item = '资源项'
t.resource.itemName = '名称'
t.resource.itemCode = '编码'
t.resource.excelImport = 'Excel导入'

t.logError = {}
t.logError.requestUri = '请求URI'
t.logError.requestMethod = '请求方式'
t.logError.requestParams = '请求参数'
t.logError.ip = '操作IP'
t.logError.userAgent = '用户代理'
t.logError.createDate = '创建时间'
t.logError.errorInfo = '异常信息'

t.logLogin = {}
t.logLogin.creatorName = '登录名'
t.logLogin.status = '状态'
t.logLogin.status0 = '失败'
t.logLogin.status1 = '成功'
t.logLogin.status2 = '账号已锁定'
t.logLogin.operation = '操作类型'
t.logLogin.operation0 = '登录'
t.logLogin.operation1 = '退出'
t.logLogin.ip = '操作IP'
t.logLogin.userAgent = '用户代理'
t.logLogin.createDate = '创建时间'

t.logOperation = {}
t.logOperation.status = '状态'
t.logOperation.status0 = '失败'
t.logOperation.status1 = '成功'
t.logOperation.creatorName = '登录名'
t.logOperation.operation = '用户操作'
t.logOperation.requestUri = '请求URI'
t.logOperation.requestMethod = '请求方式'
t.logOperation.requestParams = '请求参数'
t.logOperation.requestTime = '请求时长'
t.logOperation.ip = '操作IP'
t.logOperation.userAgent = 'User-Agent'
t.logOperation.createDate = '创建时间'

t.menu = {}
t.menu.name = '名称'
t.menu.icon = '图标'
t.menu.type = '类型'
t.menu.type0 = '菜单'
t.menu.type1 = '按钮'
t.menu.enabled = '状态'
t.menu.enabled0 = '未启用'
t.menu.enabled1 = '启用'
t.menu.sort = '排序'
t.menu.url = '路由'
t.menu.permissions = '授权标识'
t.menu.permissionsTips = '多个用逗号分隔，如：sys:menu:save,sys:menu:update'
t.menu.parentName = '上级菜单'
t.menu.parentNameDefault = '一级菜单'
t.menu.resource = '授权资源'
t.menu.resourceUrl = '资源URL'
t.menu.resourceMethod = '请求方式'
t.menu.resourceAddItem = '添加一项'
t.menu.changeApp = '移动到其他应用'
t.menu.changeTo = '移动到...'
t.menu.changeAppTitle = '请选择应用'

t.params = {}
t.params.paramCode = '编码'
t.params.paramValue = '值'
t.params.remark = '备注'

t.app = {}
t.app.appName = '应用名称'
t.app.appCode = '应用编码'
t.app.appDesc = '应用描述'
t.app.isDefault = '是否默认'
t.app.isExternal = '是否外部应用'
t.app.externalUrl = '外部应用地址'
t.app.isProjectApp = '是否项目应用'
t.app.sort = '排序'
// t.app.isProjectApp = '打开方式'

t.role = {}
t.role.name = '名称'
t.role.remark = '备注'
t.role.roleLevel = '职能级别'
t.role.createDate = '创建时间'
t.role.menuList = '菜单授权'
t.role.deptList = '数据授权'
t.role.defaultUrl = '默认首页路由'
t.role.isProjectAdmin = '是否项目管理员'
t.role.isSysAdmin = '是否系统管理员'
t.role.scope = '使用范围'

t.user = {}
t.user.username = '登录名'
t.user.deptName = '所属机构'
t.user.email = '邮箱'
t.user.mobile = '手机号'
t.user.status = '状态'
t.user.status0 = '停用'
t.user.status1 = '正常'
t.user.createDate = '创建时间'
t.user.password = '密码'
t.user.comfirmPassword = '确认密码'
t.user.nickname = '姓名'
t.user.roleList = '角色'
t.user.gender = '性别'
t.user.gender0 = '男'
t.user.gender1 = '女'
t.user.gender2 = '保密'
t.user.gender3 = '不确定'
t.user.roleIdList = '设置系统角色'
t.user.validate = {}
t.user.validate.formatPassword = '密码必须包含字母，数字，字符中的任意两种，长度在6到16位'
t.user.validate.formatUsername = '登录名不能包含中文和特殊字符'
t.user.validate.comfirmPassword = '确认密码与密码输入不一致'
t.user.expiredDate = '过期时间'
t.user.center = '所属中心'
t.user.centerType = '中心类型'

t.form = {}
t.form.formName = '表单名称'
t.form.modelCode = '模型编码'
t.form.formGuid = '表单GUID'
t.form.copyFormGuid = '复制表单GUID'
t.form.createDate = '创建时间'
t.form.publish = '发布到模板'
t.form.formProject = '所属项目'
t.form.status = '状态'
t.form.status0 = '未发布'
t.form.status1 = '已发布'
t.form.publish = '发布'
t.form.stop = '停止'
t.form.design = '设计'
t.form.fill = '填写数据'
t.form.updateRecord = '历史版本'
t.form.copy = '复制'
t.form.updateTime = '修改时间'
t.form.updateUser = '修改人'
t.form.version = '版本号'
t.form.preview = '预览'
t.form.reuseVersion = '恢复此版本'
t.form.selectForm = '选择表单'
t.form.setTemp = '设为模板'
t.form.exportTemp = '导出模板'
t.form.associatedMedicineNum = '关联随访次数'
t.form.associatedMedicineNumTips = '设置多次关联后，可在随访中重复关联'
t.form.isPatientForm = '是否患者可填'
t.form.resubmit = '是否可重复提交'
t.form.resubmitTips = '设置可重复提交后，可在随访中重复提交表单'
t.form.sort = '排序'

t.tag = {}
t.tag.tagName = '标签名称'
t.tag.tagType = '标签类型'
t.tag.dictionary = '字典'

t.formTemp = {}

t.backup = {}
t.backup.name = '备份文件名'
t.backup.size = '备份文件大小'
t.backup.date = '备份时间'
t.backup.path = '备份位置'
t.backup.status = '备份任务执行状态'
t.backup.status0 = '备份成功'
t.backup.status1 = '备份失败'
t.backup.status2 = '备份成功,压缩失败'
t.backup.status3 = '其他异常,请查看日志'

t.project = {}
t.project.projectName = '项目名称'
t.project.description = '项目描述'
t.project.level = '项目级别'
t.project.level1 = '院级'
t.project.level2 = '科室级'
t.project.status = '项目状态'
t.project.status1 = '启用'
t.project.status0 = '新建'
t.project.status2 = '停用'
t.project.type = '项目类型'
t.project.startDate = '开始时间'
t.project.endDate = '结束时间'
t.project.abledDate = '有效时间'
t.project.createTime = '创建时间'
t.project.createUserId = '创建人ID'
t.project.projectUser = '项目管理员'
t.project.user = '设置项目成员'
t.project.setProjectManage = '指定项目管理员'
t.project.projectRole = '项目角色'
t.project.notProjectRole = '未设置'
t.project.dept = '科研中心'
t.project.researchTeam = '关联研究团队'
t.project.addProject = '创建项目'
t.project.toggleProject = '切换项目'
t.project.addSubProject = '创建子项目'
t.project.project = '项目'
t.project.subProject = '子项目'
t.project.changeProject = '选择项目'
t.project.noProject = '暂无项目'
t.project.projCategory = '项目类型'
t.project.projCategory1 = '单中心项目'
t.project.projCategory2 = '多中心项目'
t.project.studyCenter = '科研中心'
t.project.primaryCenter = '主中心'
t.project.branchCenter = '分中心'
t.project.subProjCount = '子项目数量'
t.project.dataExportAudit = '数据导出审核'
t.project.enableGroupMember = '启用入组员'
t.project.enableAudit = '启用审核'
t.project.dataDoubleExport = '数据双审核'
t.project.auditing = '待审核'
t.project.noSubPorject = '暂无子项目'
t.project.auditFail = '审核不通过'
t.project.auditSuccess = '审核通过'
t.project.selectProjectType = '请选择项目类型'
t.project.singleDisease = '单病种'
t.project.multiDisease = '多病种'
t.project.selectCenterType = '请选择研究中心类型'
t.project.singleCenter = '单中心'
t.project.multiCenter = '多中心'
t.project.projectStatus = '请选择项目状态'
t.project.singleDiseaseTag = '单病种'
t.project.multiDiseaseTag = '多病种'
t.project.singleCenterTag = '单中心'
t.project.multiCenterTag = '多中心'
t.project.thereAre = '已有'
t.project.auditTips = '个子项目，新建子项目将在管理员审批后生效'
t.project.patientUnit = '例'
t.project.subProjCountUnit = '子项目容量'
t.project.auditSubProject = '审核子项目'
t.project.auditFailReason = '审核不通过原因'
t.project.singleDiseaseProj = '单病种项目'
t.project.multiDiseaseProj = '多病种项目'
t.project.centerTips = '关联中心选择医院，则项目管理员能看到医院下所有用户，并选择作为项目成员；选择科室，则能够看到科室下所有用户'
t.project.cantBeMainAndSubCenter = '不可同时设为主中心和分中心'
t.project.enableAuditTips = '启用数据审核的流程，由审核员审核录入员录入的数据'
t.project.isEnableSaveTmpData = 'CRF表单是否保存临时数据'
t.project.isEnableSaveTmpDataTips = 'CRF表单保存是否先保存到临时表'
t.project.otherConfig = '其他配置'
t.project.addSuccessfully = '创建成功'
t.project.starting = '正在启动...'
t.project.startProject = '启动项目'
t.project.tips = '提示'
t.project.tipsOne = '1. 项目需要启动才能生效。'
t.project.tipsTwo = '2. 可在后端管理-》'
t.project.proList = '项目列表'
t.project.viewNewProject = '中查看新创建的项目，并进行启动。'
t.project.configWeChat = '配置公众号'
t.project.setAdminFirst = '请先设置项目管理员'
t.project.selectprojectCneterType = '请选择科研项目类型'
t.project.projCategory1Tips = '只能关联一个机构，项目成员为该机构中的用户。'
t.project.projCategory2Tips = '可以关联多个机构，其中一个机构作为主中心。'
t.project.projectTypeTips = '分为单病种和多病种。单病种项目无子项目，多病种项目可以创建指定个数的项目。'
t.project.editCategory1Project = '修改单中心项目'
t.project.editCategory2Project = '修改多中心项目'
t.project.addCategory1Project = '创建单中心项目'
t.project.addCategory2Project = '创建多中心项目'
t.project.projectCreating = '项目创建中'
t.project.exportAuditTips = '启用后,项目数据需要系统管理员审核后才能导出'
t.project.intoGroupTips = '启用，则由入组员负责患者入组；否则由录入员负责患者入组'
t.project.dateDoubtTips = '启用后，同一个患者数据需要由两位审核员分别审核。同时审核通过的数据即锁定，否则交由项目管理员本人或指派第三位审核员审核'
t.project.selectCenterFirst = '请先选择科研中心'
t.project.stepBackPre = '返回上一步'
t.project.backProjectList = '返回项目列表'

t.subjectRole = {}
t.subjectRole.name = '角色名'
t.subjectRole.nameRequired = '角色名必填'
t.subjectRole.remark = '角色描述'
t.subjectRole.createTime = '创建时间'
t.subjectRole.isProjectAdmin = '是否是项目管理员'
t.subjectRole.dataAuth = '数据权限值'
t.subjectRole.auditConfig = 'CRF表单数据权限'
t.subjectRole.isDesensitization = '是否需要脱敏'

t.member = {}
t.member.deptList = '机构列表'
t.member.username = '姓名'
t.member.projectRole = '项目角色'

t.projectSearch = {}
t.projectSearch.name = '姓名'
t.projectSearch.gerden = '性别'
t.projectSearch.regNo = '登记号'
t.projectSearch.visit = '随访名'
t.projectSearch.visitNo = '随访序号'
t.projectSearch.visitType = '随访类型'
t.projectSearch.visitDate = '随访时间'
t.projectSearch.search = '开始搜索'
t.projectSearch.reset = '重置'
t.projectSearch.saveQuery = '保存查询'
t.projectSearch.queryName = '查询名称'
t.projectSearch.queryRecord = '查询记录'
t.projectSearch.export = '导出'
t.projectSearch.exportRecord = '导出记录'
t.projectSearch.searchName = '查询名称'
t.projectSearch.searchCondition = '查询条件'
t.projectSearch.searchResult = '查询结果'
t.projectSearch.searchSaveTime = '保存时间'
t.projectSearch.userName = '操作用户'

t.exportRecord = {}
t.exportRecord.name = '文件名'
t.exportRecord.userName = '导出用户'
t.exportRecord.startTime = '开始时间'
t.exportRecord.endTime = '结束时间'
t.exportRecord.stage = '导出阶段'
t.exportRecord.filterCondition = '过滤条件'
t.exportRecord.field = '导出字段'
t.exportRecord.progress = '进度'
t.exportRecord.status = '导出状态'
t.exportRecord.status0 = '进行中'
t.exportRecord.status1 = '成功'
t.exportRecord.status2 = '失败'
t.exportRecord.auditStatus = '审核状态'
t.exportRecord.failMsg = '导出失败信息'
t.exportRecord.detail = '导出详情'
t.exportRecord.apply = '申请导出'
t.exportRecord.getFilePwd = '获取文件密码'

t.importRecord = {}
t.importRecord.userName = '导入用户'
t.importRecord.startTime = '开始时间'
t.importRecord.endTime = '结束时间'
t.importRecord.progress = '进度'
t.importRecord.status = '导入状态'
t.importRecord.status0 = '进行中'
t.importRecord.status1 = '成功'
t.importRecord.status2 = '失败'
t.importRecord.failMsg = '导入失败信息'
t.importRecord.detail = '导入详情'
t.importRecord.apply = '申请导出'

t.auditRecord = {}
t.auditRecord.applyUserName = '申请用户'
t.auditRecord.applyTime = '申请时间'
t.auditRecord.applyReason = '申请原因'
t.auditRecord.status = '审核状态'
t.auditRecord.approveMsg = '审批信息'
t.auditRecord.approveUserName = '审核人'
t.auditRecord.approveTime = '审核时间'
t.auditRecord.status0 = '待审核'
t.auditRecord.status1 = '通过'
t.auditRecord.status2 = '不通过'
t.auditRecord.status2Reason = '不通过原因'
t.auditRecord.handle = '审核'
t.auditRecord.handleLabel = '是否通过'

t.patient = {}
t.patient.empid = '患者唯一ID'
t.patient.singleImport = '单个入组'
t.patient.multImport = '批量导入'
t.patient.hisCase = '是否本院患者'
t.patient.uploadFileformat = '只能上传xls/xlsx文件'
t.patient.downLoadTemplate = '下载导入模板'
t.patient.uploadDrag = '将文件拖到此处，或'
t.patient.fileExtensionError = '文件格式不符合要求'
t.patient.fileUploadNetWorkError = '请检查网络是否连接正常'
t.patient.regno = '登记号'
t.patient.sourceDeptName = '所属中心'
t.patient.name = '姓名'
t.patient.initials = '姓名缩写'
t.patient.gender = '性别'
t.patient.recordId = '病案号'
t.patient.birthday = '出生日期'
t.patient.mobile = '手机号'
t.patient.IdCard = '身份证号'
t.patient.project = '项目'
// t.patient.joinDate = '入组日期'
// t.patient.intoTime = '入组时间'
t.patient.joinDate = '筛选日期'
t.patient.intoTime = '筛选日期'
t.patient.pointDate = '参考点时间'
t.patient.joinDoc = '入组医生'
t.patient.lastTime = '最后一次更新'
t.patient.status = '随访总状态'
t.patient.nextTime = '下次随访日期'
t.patient.tips = '备注'
t.patient.mark = '标注'
t.patient.business = '子项目'
t.patient.fillinUser = '填写人'
t.patient.fillinRate = '填写率'
t.patient.import = '患者导入'
t.patient.export = '模板导出'
t.patient.assign = '分配'
t.patient.source = '来源'
t.patient.folwManage = '随访流程管理'
t.patient.setFollowUpPlan = '配置随访计划'
t.patient.followUpPlan = '随访流程'
t.patient.followUpRange = '随访阶段'
t.patient.detail = '查看'
t.patient.search = '检索'

t.attachment = {}
t.attachment.regNo = '登记号'
t.attachment.name = '姓名'
t.attachment.visitDate = '随访日期'
t.attachment.visitName = '随访名称'
t.attachment.hasAttachment = '有无附件'
t.attachment.catalog = '我的目录'
t.attachment.detail = '附件详情'
t.attachment.upload = '上传附件'
t.attachment.downLoad = '下载附件'
t.attachment.prop = '属性'
t.attachment.desc = '说明'
t.attachment.setProp = '设置附件属性'
t.attachment.preview = '附件预览'
t.attachment.selectedProps = '已选属性'
t.attachment.commonProps = '常用属性'

t.temp = {}
t.temp.tableName = '表头'
t.temp.filterName = '筛选内容'

t.interfaceType = {}
t.interfaceType.name = '名称'
t.interfaceType.code = '代码'
t.interfaceType.className = '代码'
t.interfaceType.cateField = '查询项分类字段'
t.interfaceType.itemField = '查询项细项字段'
t.interfaceType.level = '表级别'
t.interfaceType.hisClassName = 'HIS接口类名'
t.interfaceType.esClassName = 'ES接口类名'
t.interfaceType.esUniqueField = 'ES唯一字段'
t.interfaceType.dataOrg = '数据组织方式'
t.interfaceType.itemCodeField = '查询项细项字段'
t.interfaceType.itemCatCodeField = '查询项分类字段'
t.interfaceType.isHasSpecialValue = '是否含有特殊值'
t.interfaceType.isUsed = '是否启用'
t.interfaceType.useNewFollowUp = '版本'
t.interfaceType.webServiceUrl = '接口地址'

t.queryItem = {}
t.queryItem.name = '名称'
t.queryItem.code = '代码'
t.queryItem.esCode = 'ES代码'
t.queryItem.hisCode = 'HIS代码'
t.queryItem.categoryID = '查询项目大类'
t.queryItem.parentID = '父级项目ID'
t.queryItem.prjNameList = '项目名称'
t.queryItem.orderNo = '排序字段'
t.queryItem.itemOrderDicCode = '医嘱字典表'

t.queryAttribute = {}
t.queryAttribute.name = '名称'
t.queryAttribute.code = '代码'
t.queryAttribute.esCode = 'ES代码'
t.queryAttribute.hisCode = 'HIS代码'
t.queryAttribute.isChecked = '是否默认选中'
t.queryAttribute.associateSpecialValue = '是否关联特殊值'
t.queryAttribute.disabled = '是否可用'
t.queryAttribute.type = '数据类型'
t.queryAttribute.displayInOutInterface = '在外部接口显示'
t.queryAttribute.displayInOutInterfaceWidth = '在外部接口宽度'

t.specialValue = {}
t.specialValue.name = '名称'
t.specialValue.code = '代码'
t.specialValue.fieldName = '排序字段'
t.specialValue.isAsc = '升/降序'
t.specialValue.isAll = '是否为所有值'
t.specialValue.startNo = '起始数'
t.specialValue.endNo = '截至数'
t.specialValue.isChecked = '是否默认选中'
t.specialValue.disabled = '是否不可操作'
t.specialValue.categoryID = '类别ID'
t.specialValue.resultIsList = '结果是否为列表'
t.specialValue.useNewFollowUp = '是否使用新版随访'

t.DEDataType = {}
t.DEDataType.name = '名称'
t.DEDataType.code = '代码'
t.DEDataType.orderNo = '排序字段'

t.followUpDesign = {}
t.followUpDesign.followUpType = '随访类型'
t.followUpDesign.pointEvent = '参考点事件'
t.followUpDesign.pointDate = '参考时间点'
t.followUpDesign.range = '间隔'
t.followUpDesign.category = '随访分类'
t.followUpDesign.followTotal = '随访次数'
t.followUpDesign.name = '组合套名称'
t.followUpDesign.remind = '随访提醒'

t.subProject = {}
t.subProject.name = '子项目名'
t.subProject.templateName = '模板名称'
t.subProject.describe = '描述'

t.researchTeam = {}
t.researchTeam.name = '研究团队名'
t.researchTeam.dept = '关联机构'
t.researchTeam.member = '团队成员'
t.researchTeam.setmember = '设置团队成员'
t.researchTeam.describe = '描述'

t.patientView = {}
t.patientView.regno = '登记号'
t.patientView.gender = '性别'
t.patientView.birthday = '出生日期'
t.patientView.joinDate = '入组时间'
t.patientView.joinDateRange = '已入组'
t.patientView.followUpStatus = '随访进行中'
t.patientView.followUpStatus1 = '随访已完成'
t.patientView.followUpStatus2 = '已超期未随访'
t.patientView.followUpStatus3 = '窗口内待随访'
t.patientView.followUpStatus4 = '窗口外'
t.patientView.before = '修改前'
t.patientView.after = '修改后'

t.filenameMapping = {}
t.filenameMapping.originFilename = '原始字段'
t.filenameMapping.targetFilename = '目标字段'
t.filenameMapping.sort = '排序'

t.todo = {}
t.todo.list = '待办事项'
t.todo.name = '名称'
t.todo.date = '时间'
t.todo.status = '状态'

t.recycle = {}
t.recycle.username = '操作用户'
t.recycle.data = '数据'
t.recycle.date = '操作日期'
t.recycle.reset = '恢复'
t.recycle.entityname = '业务域'

t.followUpTemplate = {}
t.followUpTemplate.name = '模板名'
t.followUpTemplate.date = '保存时间'

t.formTemplate = {}
t.formTemplate.name = '模板名'
t.formTemplate.date = '保存时间'

t.wxUser = {}
t.wxUser.userId = '用户id'
t.wxUser.wxNickname = '用户昵称'
t.wxUser.idCard = '身份证号'
t.wxUser.userType = '用户类型'
t.wxUser.openId = '用户open_id'
t.wxUser.tel = '手机号'
t.wxUser.attentionType = '关注状态'
t.wxUser.subProjectId = '子项目id'

t.crfData = {}
t.crfData.firstVisit = '首次随访'
t.crfData.month1 = '术后1个月'
t.crfData.month2 = '术后2个月'
t.crfData.month3 = '术后3个月'
t.crfData.month6 = '术后6个月'
t.crfData.year1 = '术后1年'
t.crfData.year2 = '术后2年'
t.crfData.year3 = '术后3年'

t.visitRemind = {}
t.visitRemind.nextVisitDate = '下次随访日期'
t.visitRemind.nextVisitDate1 = '下次随访开始日期'
t.visitRemind.nextVisitDate2 = '下次随访结束日期'
t.visitRemind.expiredDay = '超期时长'
t.visitRemind.todoVisitCount = '待完成随访数'

// 个人中心
t.userCenter = {}
t.userCenter.filter = '过滤'
t.userCenter.add = '新建'
t.userCenter.about = '关于'

// 关于弹窗(展示公司信息)
t.about = {}
t.about.aboutTitle = '产品服务支持'
t.about.productName = '产品名称'
t.about.productVersion = '产品版本'
t.about.softwareLicenseType = '软件许可授权类型'
t.about.softwareLicensingExpiration = '软件许可授权截止日期'
t.about.softwareLicensingAuthority = '软件许可授权使用单位'
t.about.softwareLicensePurpose = '软件许可授权用途'
t.about.softwareModuleLicensing = '软件模块许可授权'
t.about.companyAllRightsReserved = '东华医为科技有限公司版权所有'
t.about.machineCode = '机器码'

// 项目概览
t.dashboard = {}
t.dashboard.projInfo = '项目信息'
t.dashboard.projAdmin = '项目管理员'
t.dashboard.projStartTime = '项目开始时间'
t.dashboard.projEndTime = '项目结束时间'
t.dashboard.projAffCenter = '所属中心'
t.dashboard.projMainCenter = '主中心'
t.dashboard.projSubCenter = '分中心'
t.dashboard.none = '暂无'
t.dashboard.description = '项目描述'
t.dashboard.projStatus = '项目状态'
t.dashboard.normal = '正常'
t.dashboard.disabled = '停用'
t.dashboard.statusDesc = '状态描述'
t.dashboard.projEnabled = '项目处于启用状态'
t.dashboard.projDisabled = '项目处于停用状态'
t.dashboard.projCases = '患者数'

t.dashboard.projRole = '项目角色'
t.dashboard.members = '项目成员'
t.dashboard.noMembers = '暂无成员'
t.dashboard.viewAll = '查看全部'
t.dashboard.unprocessed = '未处理消息'
t.dashboard.viewMore = '查看更多'
t.dashboard.noUnprocessed = '暂无未处理消息'
t.dashboard.followUpCalendar = '随访日历'
t.dashboard.genderDistribution = '性别分布'
t.dashboard.ageDistribution = '年龄分布'

export default t