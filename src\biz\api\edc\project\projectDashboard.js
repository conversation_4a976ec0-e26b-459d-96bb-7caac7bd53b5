// 获取患者信息
export const getPatientCount = (params) => {
  return {
    url: 'edc/subject/overview/get-patient-count',
    method: 'GET',
    params
  }
}

// 获取性别信息
export const getSexInfo = (params) => {
  return {
    url: 'edc/subject/overview/get-sex-info',
    method: 'GET',
    params
  }
}

// 获取年龄信息
export const getAgeInfo = (params) => {
  return {
    url: 'edc/subject/overview/get-age-info',
    method: 'GET',
    params
  }
}

// 获取待办信息
export const getTodoInfo = (params) => {
  return {
    url: 'edc/subject/overview/get-todo-info',
    method: 'GET',
    params
  }
}

// 获取成员信息
export const getMemberInfo = (params) => {
  return {
    url: 'edc/subject/overview/get-member-info',
    method: 'GET',
    params
  }
}

// 获取随访日历信息
export const getCalendarInfo = (params) => {
  return {
    url: 'edc/visit/calender',
    method: 'GET',
    params
  }
}

// 获取近一年年入组量
export const getIntoCaseTrend = (params) => {
  return {
    url: 'edc/subject/overview/into-tendency/last-year',
    method: 'GET',
    params
  }
}

// 获取全部入组量统计
export const getAllIntoCaseTrend = (params) => {
  return {
    url: 'edc/subject/overview/into-tendency/total',
    method: 'GET',
    params
  }
}