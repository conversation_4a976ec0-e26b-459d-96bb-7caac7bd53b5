const dict = {
  state: {
    dictMap: {} // 缓存页面中请求过的字典
  },
  actions: {
    /**
     * @description 设置模型信息
     * @param {Object} state vuex state
     * @param {Object} modelData modelData
     */
    setDictItem({
      state,
      dispatch,
      rootState
    }, dictItem) {
      state.dictMap[dictItem.key] = dictItem.list
    },
    clearAllDict({
      state,
      dispatch,
      rootState
    }) {
      state.dictMap = {}
    },
    getDictItem({
      state,
      dispatch,
      rootState
    }, dictKey) {
      return state.dictMap[dictKey] || null
    },
  }
}

export default dict