export const queryListApi = (params) => {
    return {
        url: 'edc/elastic/subject/index/page',
        method: 'GET',
        params
    }
}

export const deleteApi = (params) => {
    return {
        url: '/edc/elastic/subject/index/delete',
        method: 'POST',
        params
    }
}

export const getSettingApi = (params) => {
    return {
        url: 'edc/elastic/select-setting-by-index',
        method: 'GET',
        params
    }
}

export const getMappingApi = (params) => {
    return {
        url: 'edc/elastic/select-mapping-by-index',
        method: 'GET',
        params
    }
}

export const getDataApi = (params) => {
    return {
        url: 'edc/elastic/subject/index/preview',
        method: 'GET',
        params
    }
}