import { getSubjectId,setSubjectId,getSubjectInfo,setSubjectInfo } from "@/utils/subject-util";
import axios from "@base/axios";
import { Loading } from 'hosui'
import i18n from '@base/i18n'
const subject = {
  state: {
    subjectInfoLock: false, // 增加锁，防止设置项目的函数被重复调用
    subjectId: getSubjectId() || null,
    subjectInfo: null, // getSubjectInfo() || null,
    subjectMenu:[] // 缓存当前项目角色的菜单
  },
  actions: {
    /**
     * @description 设置子项目信息
     * @param {Object} state vuex state
     * @param {Object} subjectId subjectId
     */
    async setSubjectInfo({
      state,
      dispatch,
      rootState
    }, subjectId) {
      if(!subjectId){
        state.subjectInfo = null
        setSubjectInfo(null)
        return
      }
      if(state.subjectInfoLock){
        return
      }else{
        state.subjectInfoLock = true
      }
      const loading = Loading.service({
        lock: true,
        text: i18n.t('正在打开项目...'),
        spinner: 'hos-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      setSubjectId(subjectId)
      state.subjectId = subjectId
      // 获取登录用户的项目缓冲信息
      const {data:subjectCacheInfo} = await axios.request('biz.edc.subProject.getSubjectCacheInfo',subjectId)
      // 计算出全部子项目机构
      subjectCacheInfo.allOrgList = [subjectCacheInfo.mainOrg,...subjectCacheInfo.otherOrgList]
      let orgId = null
      if(subjectCacheInfo.loginUserOrgId){
        orgId = subjectCacheInfo.loginUserOrgId
      }else{
        if(subjectCacheInfo.mainOrg && subjectCacheInfo.mainOrg.orgId){
          orgId = subjectCacheInfo.mainOrg.orgId
        }
      }
      // if(subjectCacheInfo.projectRoleId){
        const {data:subjectMenu} = await axios.request('biz.edc.subProject.getSubjectMenu',{roleId:subjectCacheInfo.projectRoleId,subjectId})
        state.subjectMenu = subjectMenu || []
      // }
      const {data:patientAttr} = await axios.request('biz.edc.subProject.queryOrgPatientListAttr',{subjectId:subjectId,orgId})
      let projectRoleInfo = {}
      if(subjectCacheInfo.projectRoleId && subjectCacheInfo.projectRoleId !== '0'){
        const res = await axios.request('biz.edc.project.subjectRole.queryDetailApi',subjectCacheInfo.projectRoleId)
        projectRoleInfo = res.data
      }

      const info = {...subjectCacheInfo,patientAttr,projectRoleInfo}
      setSubjectInfo(info)
      state.subjectInfo = info
      state.subjectInfoLock = false
      loading.close()
    },
    // 患者入组字段修改后更新缓存
    async updateCachePatientAttr({
      state,
      dispatch,
      rootState
    }){
      let orgId = null
      if(state.subjectInfo.loginUserOrgId){
        orgId = state.subjectInfo.loginUserOrgId
      }else{
        if(state.subjectInfo.mainOrg && state.subjectInfo.mainOrg.orgId){
          orgId = state.subjectInfo.mainOrg.orgId
        }
      }
      const {data:patientAttr} = await axios.request('biz.edc.subProject.queryOrgPatientListAttr',{subjectId:state.subjectInfo.id,orgId})
      const info = {...state.subjectInfo,patientAttr}
      setSubjectInfo(info)
      state.subjectInfo = info
    }
  }
}

export default subject