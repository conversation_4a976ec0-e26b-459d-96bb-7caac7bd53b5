<!-- 单个条件 -->
<template>
  <div class="rule-container">
    <div class="rule-header">
      <div class="rule-filter-container">

        <!-- :fetch-suggestions="querySearchAsyncItem" 关键字检索自动补全后续迭代 -->
        <hos-autocomplete ref="autoComItem" v-model="input_name" :disabled="queryBuilderObj.disabled"
          :class="{'is-error':valueError}" style="width:100%;" :fetch-suggestions="querySearchAsyncItem"
          @focus="openSearchItem" @select="handleSelectItem">
          <i slot="suffix" :title="$t('选择指标')" class="search-open-icon hos-input__icon icon hos-icon-search"
            @click="openSearchItem" />
        </hos-autocomplete>
      </div>
      <div class="rule-operator-container">
        <hos-select v-model="curQuery.relative" :disabled="queryBuilderObj.disabled"
          :class="{'is-error':relativeError}">
          <template v-if="query.fieldItemInfo && query.fieldItemInfo.isNegative">
            <hos-option v-for="operator in filterTypeList" :key="operator.code" :value="operator.code"
              :label="operator.name" />
          </template>
          <template v-else-if="query.fieldItemInfo && allTypeRelativesObj.idRelativesMap[query.fieldItemInfo.datatype]">
            <hos-option v-for="operator in allTypeRelativesObj.idRelativesMap[query.fieldItemInfo.datatype]"
              :key="operator.id" :value="relationMap2[operator.relativeType].code"
              :label="relationMap2[operator.relativeType].name" />
          </template>
        </hos-select>
      </div>
      <div class="rule-value-container">
        <!-- 关系词选 ‘多指匹配’ 或 ‘多指匹配（parent_all）时使用多选下拉控件’ -->
        <template v-if="curQuery.relative === 'in' || curQuery.relative === 'in_and_parent' || curQuery.relative === 'in_and_like' || curQuery.relative === 'in_and_parent_like'">
          <input-multi v-model="query.value" :class="{'is-error':valueError}" />
        </template>
        <template v-else-if="isInputTypeCustom">
          <!-- v-if="query.inputType === 'text' || query.inputType === 'keyword'" -->
          <hos-autocomplete
            v-if="query.inputType === 'keyword' || query.inputType === 'text' || bindDictAndIsFuzzyMatch"
            ref="autoComValue" v-model="query.value" :disabled="valueDisabled" :hide-loading="true"
            :placeholder="curQuery.relative == 'regexp' ? $t('.*表示任意数量任意字符,支持标准正则语法') : ''"
            :trigger-on-focus="false" :popper-class="popperClass" style="width:100%;" :class="{'is-error':valueError}"
            clearable :fetch-suggestions="querySearchAsync" @select="handleSelect" />

          <!--数值框-->
          <hos-input-number v-else-if="query.inputType === 'number'" v-model="query.value" :disabled="valueDisabled"
            style="width:100%" :class="{'is-error':valueError}" />
          <!--日期时间选择框-->
          <hos-date-picker v-else-if="query.inputType === 'date' || query.inputType === 'datetime'"
            v-model="query.value" :disabled="valueDisabled" value-format="yyyy-MM-dd" :editable="false"
            style="width:100%" :class="{'is-error':valueError}" type="date" />
          <!-- 下拉框 -->
          <hos-select v-else-if="query.inputType === 'select' || query.inputType === 'single_select'" v-model="query.value" style="width:100%"
            :disabled="valueDisabled" :placeholder="$t('请选择')" :class="{'is-error':valueError}">
            <hos-option v-for="(item, i) in query.fieldItemInfo.selectOptions" :key="i" :label="item.label" :value="item.data" />
          </hos-select>
          <!-- 文本输入框 -->
          <hos-input v-else v-model="query.value" :disabled="valueDisabled" type="text" style="width:100%"
            :class="{ 'is-error': valueError }" clearable />
        </template>
        <template v-else>
          <hos-input v-model="query.value" :disabled="valueDisabled" type="text" style="width:100%"
            :class="{'is-error':valueError}" clearable />
        </template>

      </div>

      <div class="btn-group pull-right rule-actions">
        <hos-button v-if="!isSubRule || index === 0" :disabled="queryBuilderObj.disabled" type="primary" size="mini"
          :title="$t('添加同级条件')" icon="hos-icon-plus" circle @click="add" />
        <template v-if="!isSimpleMode">
          <hos-button v-if="depth <= maxDepth " :disabled="queryBuilderObj.disabled" type="primary" size="mini"
            :title="$t('添加二级条件')" icon="hos-icom-batch-add" circle @click="addGroup" />
          <hos-button v-if="depth <= maxDepth && !(parentQuery.children && parentQuery.children.length === 1)"
            :disabled="queryBuilderObj.disabled" type="primary" size="mini" :title="$t('转为二级条件')" icon="hos-icon-d-arrow-right"
            circle @click="changeToGroup" />
          <hos-button v-if="depth > maxDepth && !isSubRule" :disabled="queryBuilderObj.disabled" type="primary"
            size="mini" :title="$t('转为一级条件')" icon="hos-icon-d-arrow-left" circle @click="changeToRule" />
          <hos-button v-if="!isSubRule || index === 0" :disabled="queryBuilderObj.disabled" type="success" size="mini"
            :title="$t('添加子条件')" icon="hos-icon-s-operation" circle @click="addSubGroup" />
        </template>
        <hos-button :disabled="queryBuilderObj.disabled" type="info" size="mini" icon="hos-icon-close"
          circle :title="$t('删除')" @click="remove" />
      </div>
    </div>
  </div>
</template>

<script>
  import {deepClone} from "@/utils/index.js"
  import {
    relationMap2,
    filterTypeList
  } from "./mapData.js"
  import {
    _debounce
  } from "@/utils/throttle.js"
  import InputMulti from '../input-multi.vue'

  export default {
    name: "QueryBuilderRule",
    components: {
      InputMulti
    },
    props: {
      query: {
        type: Object,
        default () {
          return {}
        }
      },
      parentQuery: {
        type: Object,
        default () {
          return {}
        }
      },
      parentType: {
        type: String
      },
      index: {
        type: Number,
        default () {
          return null
        }
      },
      parentIndex: {
        type: Number,
        default () {
          return null
        }
      },
      // 全部关系词类型
      rules: {
        type: Array,
        default () {
          return []
        }
      },
      maxDepth: {
        type: Number,
        default () {
          return 3
        }
      },
      depth: {
        type: Number,
        default () {
          return 0
        }
      },
      // 全部关联字段信息
      fieldData: {
        type: Array,
        default () {
          return []
        }
      },
      cateItems: {
        type: Array,
        default () {
          return []
        }
      },
      currentGroupIndex: {
        type: Number,
      },
      currentConditionIndex: {
        type: Number
      }
    },
    inject: {
      "queryBuilderObj": {
        from: 'queryBuilderObj'
      },
      "isSimpleMode": {
        from: 'isSimpleMode'
      },
      allTypeRelativesObj: {
        from: "allTypeRelativesObj",
        default: () => {}
      }
    },
    data() {
      return {
        popperClass: 'hide-popper', // 自动补全的下拉class，默认隐藏，只有在有数据返回时显示
        relationMap2,
        filterTypeList,
        isInputTypeCustom: true, // 输入值的控件是否根据类型判断
        // 当前查询条件的字段编码
        selectSearchItemFieldEName: "",
        fieldItemInfo: this.query.rule || this.rules[0],
        hasError: false,
        fieldError: false,
        relativeError: false,
        valueError: false,
        input_name: this.query.fieldItemInfo.name,
        queryOperatorOption: [],
        curQuery: this.query
      }
    },
    computed: {
      isSubRule() {
        return this.parentType === 'query-builder-sub-group'
      },
      valueDisabled() {
        return this.queryBuilderObj.disabled || this.query.relative == "not_null" || this.query.relative == "is_null"
      },
      cateItemsMap() {
        const ret = {}
        this.cateItems.forEach(cate => {
          if (cate.child && cate.child.length > 0) {
            cate.child.forEach(subCate => {
              if (subCate.item && subCate.item.length > 0) {
                subCate.item.forEach((item) => {
                  ret[item.searchItemId] = item
                })
              }
            })
          }
        })
        return ret
      },
      fieldDataMap() {
        const ret = {}
        this.fieldData.forEach((field) => {
          ret[field.id] = field
        })
        return ret
      },
      bindDictAndIsFuzzyMatch() {
        return this.query.fieldItemInfo.dictId && this.query.fieldItemInfo.isFuzzyMatch === 1
      },
      bindDictAndIsNotFuzzyMatch() {
        return this.query.fieldItemInfo.dictId && this.query.fieldItemInfo.isFuzzyMatch === 0
      },
    },
    watch: {
      // 监听当前条件查询字段的变化
      selectSearchItemFieldEName() {
        this.ruleChange()
      },
      "query.fieldItemInfo.name"(val) {
        this.fieldError = false
        this.relativeError = false
        this.valueError = false
        this.input_name = val
      },
      "query.value"(val) {
        // console.log('query.value')
        this.curQuery.value = val
        this.valueError = false
      },
      "query.relative"(val) {
        // console.log('query.relative')
        this.curQuery.relative = val
      },
      "query.inputType"(value) {
        this.$nextTick(() => {
          if (this.$refs.autoComValue) {
            this.$refs.autoComValue.handleChange = function (value) {
              // this.$emit("change",value)
            }
          }
        })
      },
      "curQuery.value"(val) {
        // console.log('curQuery.value')
        this.query.value = val
        this.$emit('change-query', this.query)
      },
      "curQuery.relative"(val) {
        // console.log('curQuery.relative')
        this.query.relative = val
        this.$emit('change-query', this.query)
      },
      "query.fieldItemInfo.selectOptions"(val) {

      },
    },
    mounted() {
      this.initValue()

      if (this.$refs.autoComItem) {
        this.$refs.autoComItem.handleChange = (value) => {
          this.$refs.autoComItem.$emit("change", value)
        }
      }

      // var _this = this
      // var selectedRuleCopy = _this.fieldItemInfo;
      // var splitIndex = selectedRuleCopy.indexOf("-");
      // if (splitIndex > -1) {
      //   _this.fieldItemInfo = selectedRuleCopy.substring(0, splitIndex);
      //   _this.selectedSubRule = selectedRuleCopy.substring(splitIndex + 1);
      //   this.rules.forEach(function (rule) {
      //     if (rule.id === _this.fieldItemInfo) {
      //       var isBreak = false;
      //       _this.subRules = rule.subRules;
      //       rule.subRules.forEach(function (subRule) {
      //         if (subRule.id === _this.selectedSubRule) {
      //           _this.selectedRuleObj = subRule;
      //           isBreak = true;
      //           return false;
      //         }
      //       });
      //       if (isBreak) {
      //         return false;
      //       }
      //     }
      //   });
      // } else {
      //   this.rules.forEach(function (rule) {
      //     if (rule.id === _this.fieldItemInfo) {
      //       _this.selectedRuleObj = rule;
      //       return false;
      //     }
      //   });
      // }
    },
    methods: {
      querySearchAsync: _debounce(async function (queryString, cb) {
        queryString = queryString || ''
        const dictId = this.query.fieldItemInfo.dictId
        // if(!queryString || queryString.trim()==="" || !fieldId){
        //   cb([])
        //   return
        // }
        const params = {
          // type: dictId,
          keyword: queryString.trim(),
          name: this.query.fieldItemInfo.fieldName,
          indexId: this.modelData.id,
        }
        this.popperClass = 'hide-popper'
        if (!params.keyword) {
          cb([])
          return
        }
        if (dictId) {
          params.type = dictId
        } else {
          params.category = this.query.fieldItemInfo.typeName
        }

        if (!this.query.fieldItemInfo.dictId) {
          const res = await this.$api('biz.search.web.dictionary.getESSuggest',params)
          let data = res.data
          const code = res.code
          if (code == 200) {
            data = data || []
            const list = data.map(item => {
              return {
                value: item
              }
            })
            cb(list)
            if (list.length > 0) {
              this.popperClass = ''
            } else {
              this.popperClass = 'hide-popper'
            }
          } else {
            cb([])
            this.popperClass = 'hide-popper'
          }
        } else if (this.query.fieldItemInfo.dictId && (this.query.inputType === 'text' || this.query.inputType === 'keyword')) {
          const p = {
            dictId: params.type,
            suggest: params.keyword,
            name: this.query.fieldItemInfo.fieldName,
            indexId: this.modelData.id,
          }
          const res = await this.$api('biz.search.web.dictionary.getSuggestionByKey',p)
          let data = res.data
          const code = res.code
          if (code == 200) {
            data = data || []
            const list = data.map(item => {
              return {
                value: item.dictItemName
              }
            })
            cb(list)
            if (list.length > 0) {
              this.popperClass = ''
            } else {
              this.popperClass = 'hide-popper'
            }
          } else {
            cb([])
            this.popperClass = 'hide-popper'
          }
        } else {
          // 最外层querybuilder对下拉所有字典统一请求处理了
        }
      }),
      handleSelect(item) {
        this.popperClass = 'hide-popper'
        this.query.value = item.value
      },
      querySearchAsyncItem(queryString, cb) {
        cb([])
        // if(!queryString || queryString.trim()===""){
        //   cb([])
        //   return
        // }
        // getApproveSearchItemLike({
        //   key:queryString.trim(),
        //   indexId: this.modelData.id
        // }).then(({data,msg,success})=>{
        //   if(success && data){
        //     cb(data.map(item=>{
        //      return {value:item.name,data:item}
        //     }))
        //   }else{
        //     cb([])
        //   }
        // })
      },
      handleSelectItem(item) {
        this.input_name = item.value
        const selectInfo = {
          ...this.cateItemsMap[item.data.id]
        }
        selectInfo.fieldInfo = {
          ...this.fieldDataMap[selectInfo.searchFieldId]
        }
        if (selectInfo.searchFieldOtherId && selectInfo.searchFieldOtherId > 0) {
          // 获取其他字段信息
          const otherFieldInfoArr = this.fieldData.filter(info => {
            return info.id === selectInfo.searchFieldOtherId
          })
          selectInfo.otherFieldInfo = (otherFieldInfoArr && otherFieldInfoArr.length > 0) ? otherFieldInfoArr[0] : null
        } else {
          selectInfo.fieldInfo.name = selectInfo.searchItemName
        }
        // console.log(this.query, 317);
        this.query.fieldItemInfo = selectInfo.fieldInfo
        // todo query.fieldItemInfo.name
        this.$emit("autoComSelect", this.index, null, selectInfo)
      },
      autoCompleteItemChange(value) {
        // console.log(value)
        this.input_name = this.query.fieldItemInfo.name
      },
      add() {
        if (this.isSubRule) {
          // 在当前子条件组（即父条件下）下面增加同级条件
          this.$emit("child-add-requested", this.parentIndex, true)
        } else {
          // 在当前条件下面增加同级条件
          this.$emit("child-add-requested", this.index)
        }
      },
      remove() {
        // console.log('remove', this.index)
        if (this.isSubRule) {
          // console.log('isSubRule')
          this.$emit("child-deletion-requested", this.index, true)
        } else {
          this.$emit("child-deletion-requested", this.index)
        }
      },
      addGroup() {
        // console.log('group-add-requested', this.index)
        this.$emit("group-add-requested", this.index)
      },
      changeToGroup() {
        this.$emit("change-to-group", this.index)
      },
      changeToRule() {
        this.$emit("change-to-rule", this.index)
      },
      addSubGroup() {
        if (this.isSubRule) {
          // console.log('isSubRule', this.isSubRule, 'addSubGroup')
          // 在当前条件下面增加子条件
          this.$emit("child-add-requested", this.index)
        } else {
          // console.log('isSubRule', this.isSubRule, 'addSubGroup')
          // 将当前条件变成子条件组
          this.$emit("sub-group-add-requested", this.index)
        }
      },
      initValue() {
        if (this.query.value === null) {
          const updated_query = deepClone(this.query)
          // 适配输入框类型的值
          // if (this.selectedRuleObj.inputType === "checkbox") {
          //   updated_query.value = [];
          // }
          // if (
          //   this.selectedRuleObj.inputType === "select" ||
          //   this.selectedRuleObj.inputType === "radio"
          // ) {
          //   updated_query.value = this.selectedRuleObj.choices[0];
          // }
          // if (
          //   this.selectedRuleObj.inputType === "time" ||
          //   this.selectedRuleObj.inputType === "date" ||
          //   this.selectedRuleObj.inputType === "datetime"
          // ) {
          //   updated_query.value = Math.round(new Date());
          // }
          this.$emit("update:query", deepClone(updated_query))
        }
      },
      // 打开查询项弹出框
      openSearchItem() {
        // console.log('openSearchItem', this.index)
        if (this.isSubRule) {
          let typeId = 0
          // 弹框查询字段过滤主条件typeId下的字段
          typeId = this.parentQuery.query ? this.parentQuery.query.fieldItemInfo.typeId : 0
          // console.log(typeId)
          this.$emit("open-search-dialog", this.index, typeId)
        } else {
          this.$emit("open-search-dialog", this.index)
        }
      },
      // rulex修改
      ruleChange() {

      },
      valid() {
        const b1 = this.validField()
        const b2 = this.validRelative()
        const b3 = this.validValue()
        return b1 || b2 || b3
      },
      validField() {
        const curFieldInfo = this.query.fieldItemInfo

        if (!curFieldInfo || !curFieldInfo.name) {
          this.fieldError = true
        } else {
          this.fieldError = false
        }

        return this.fieldError
      },
      validRelative() {
        const curOp = this.query.relative
        if (!curOp || curOp.length === 0) {
          this.relativeError = true
        } else {
          this.relativeError = false
        }

        return this.relativeError
      },
      validValue() {
        const curV = this.query.value

        if (this.isNull(curV)) {
          if (this.query.relative == "not_null" || this.query.relative == "is_null") {
            this.valueError = false
          } else {
            this.valueError = true
          }
        } else {
          this.valueError = false
        }

        return this.valueError
      }
    }
  }

</script>
