// 获取当前已订阅的订阅表单
export const queryAllDeFormList = (params) => {
    return {
        url: 'edc/subject/deform/list-by-subjectId',
        method: 'GET',
        params
    }
}

// 获取所有可用于自动采集的接口类型
export const getAllCates = (params) => {
    return {
        url: 'edc/external-category/deform',
        method: 'GET',
        params
    }
}

// 获取所有可用于自动采集的接口类型(tree)
export const getAllCatesTree = (params) => {
    return {
        url: `edc/external-category/deform/select-tree`,
        method: 'get',
        params
    }
}

// 获取指定分类ID下的查询项目
export const getPropByCategoryId = (params) => {
    return {
        url: `edc/external-item/select-item-tree`,
        method: 'get',
        params
    }
}

// 批量保存分类字段订阅配置
export const saveBatch = (data) => {
    return {
        url: `edc/subject/deform/batch`,
        method: 'POST',
        data
    }
}

// 更新订阅配置
export const updateDeForm = (data) => {
    return {
        url: `edc/subject/deform/update`,
        method: 'POST',
        data
    }
}

// 删除单个分类下的字段订阅配置
export const deleteField = (data) => {
    return {
        url: `edc/subject/deform/deletion`,
        method: 'POST',
        data
    }
}

// 新增订阅字段申请
export const addFieldApply = (data) => {
    return {
        url: `edc/deform/field/apply/insert-apply`,
        method: 'POST',
        data
    }
}
