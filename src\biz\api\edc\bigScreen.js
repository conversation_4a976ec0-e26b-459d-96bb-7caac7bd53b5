// 报表（大屏）

export const pageListApi = (params) => {
    return {
        url: 'edc/report/page',
        method: 'GET',
        params
    }
}

export const detailApi = (id) => {
    return {
        url: 'edc/report/info/' + id,
        method: 'GET'
    }
}

export const addApi = (data) => {
    return {
        url: 'edc/report/insert',
        method: 'POST',
        data
    }
}

// 更新报表接口
export const editApi = (data) => {
    return {
        url: 'edc/report/update',
        method: 'POST',
        data
    }
}

export const deleteApi = (data) => {
    return {
        url: 'edc/report/deletion',
        method: 'POST',
        data
    }
}

// 保存大屏设计
export const insertDashboard = (data) => {
    return {
        url: 'edc/report-dashboard/save',
        method: 'POST',
        data
    }
}

// 预览、查询大屏详情
export const detailDashboard = (data) => {
    return {
        url: 'edc/report-dashboard/view/' + data,
        method: 'GET'
    }
}

// 获取动态数据
export const getData = (data) => {
    return {
        url: 'edc/report-dashboard/getData',
        method: 'post',
        data
    }
}

// 导出大屏
export const exportDashboard = (params) => {
    return {
        url: 'edc/report/export',
        method: 'GET',
        params,
        responseType: 'blob'
    }
}

// 导入大屏
export const importDashboard = (data) => {
    return {
        url: 'edc/report/import/',
        method: 'post',
        data
    }
}

// 上传大屏背景图片或者图片控件图片
export const uploadBackImage = ({reportId,data}) => {
    return {
        url: 'edc/report-dashboard/upload-back-image/' + reportId,
        method: 'post',
        data
    }
}

// 查看大屏背景图片或者图片控件图片
export const readBackImage = ({reportId,fileName}) => {
    return {
        url: `edc/report-dashboard/read-back-image/${fileName}/${reportId}`,
        method: 'get',
        responseType: 'blob'
    }
}

// 查看大屏图片控件图片列表
export const getImgList = (params) => {
    return {
        url: `edc/report-dashboard/image-list`,
        method: 'get',
        params
    }
}