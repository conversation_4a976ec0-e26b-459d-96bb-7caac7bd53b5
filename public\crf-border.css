/*
覆盖表单设计器单选多选框的样式
加粗加深crf中单选多选和输入框的边框
方便项目上需要使用加粗样式的直接引用
*/
.gf-form .gf-form-item .el-radio__inner:hover,
.gf-form .gf-form-item .el-checkbox__inner:hover {
  border-color: #339eff;
}

.gf-form .gf-form-item .el-radio__inner,
.gf-form .gf-form-item .el-checkbox__inner {
  border: 3px solid #555;
  background-color: #fff;
}

.gf-form .gf-form-item .el-radio__input.is-checked .el-radio__inner,
.gf-form .gf-form-item .el-checkbox__input.is-checked .el-checkbox__inner {
  border-color: #409EFF;
  background-color: #409EFF;
}

.gf-form .gf-form-item .el-input__inner,
.gf-form .gf-form-item .el-textarea__inner {
  border: 2px solid #555;
}