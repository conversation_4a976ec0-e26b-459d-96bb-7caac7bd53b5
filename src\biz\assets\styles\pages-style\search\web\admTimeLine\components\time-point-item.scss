.wide {
  font-weight: 900;
  margin-right: 10px;
}

.s20 {
  font-size: 20px;
}

.time-point-wrapper {

  // margin-top: 60px;
  .head {
    width: 100%;
    height: 60px;
    position: relative;
    // border: 1px solid #ccc;

    .point-title {
      font-size: 28px;
      color: #409eff;
      position: absolute;
      left: 146px;
      bottom: 0;
      cursor: pointer;
    }

    .expand {
      color: #409eff;
      cursor: pointer;
      position: absolute;
      left: 206px;
      bottom: 5px;
    }

    .history-icon {
      font-size: 60px;
      color: #67C23A;
      position: absolute;
      left: 221px;
      bottom: -10px;

      .hos-icon-time {
        background-color: #fff;
      }
    }
  }

  .event-items-wrapper {
    width: 100%;

    .item {
      width: 100%;
      height: 150px;
      display: flex;

      // border: 1px solid #ccc;
      .left {
        width: 250px;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #67C23A;
        position: relative;

        .time {
          position: absolute;
          right: 20px;
          top: 80px;
          color: #606266;
          font-size: 14px;
        }

        .circle {
          width: 10px;
          height: 10px;
          border: 1px solid #088;
          border-radius: 10px;
          // background-color: #0FF;
          background-color: #fff;
          // opacity: 0.5;
          position: absolute;
          top: 84px;
          right: -7px;

        }
      }

      .right {
        width: calc(100% - 250px);
        // min-width: 750px;
        height: 100%;
        position: relative;

        .content {
          position: absolute;
          top: 70px;
          left: 10px;
          display: flex;
          flex-direction: column;

          .type-wrapper {
            display: flex;

            .name {
              height: 36px;
              line-height: 36px;
              text-align: center;
              margin-left: 15px;
              margin-right: 20px;
              font-size: 14px;
              font-weight: bold;
            }

            .btn-detail {
              height: 34px;
              border: unset;
            }
          }

          .content-right-relative {
            position: relative;
          }
        }

        .content-right {
          height: 100%;
          width: calc(100% - 310px);
          position: absolute;
          top: 70px;
          left: 310px;

          .c-r-card {
            width: 100%;
            float: left;
            position: absolute;
            bottom: 33px;
            font-size: 14px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .card-title {
              height: 25px;
              line-height: 25px;
              padding: 0 10px;
              box-sizing: border-box;
              background-color: rgb(247, 247, 247);
              width: 100%;
              color: rgb(121, 121, 121);
              font-size: 12px;

              .icon {
                float: right;
                cursor: pointer;
              }

              .title {
                float: left;
                margin-right: 10px;
                color: rgb(60, 157, 26);
              }

              .sub-title {
                float: left;
              }
            }

            .card-body {
              height: 75px;
              width: 100%;

              .p {
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                height: 25px;
                line-height: 25px;
                color: rgb(121, 121, 121);
                font-size: 12px;
                padding-left: 10px;
                box-sizing: border-box;
              }

            }
          }
        }
      }
    }
  }

  .hos-divider--horizontal {
    width: 900px !important;
    margin-top: 30px;
  }
}
