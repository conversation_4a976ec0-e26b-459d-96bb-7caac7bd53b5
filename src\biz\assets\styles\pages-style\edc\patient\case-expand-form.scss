.hos-form.case-expand-form-container {
    .hos-form-item__label{
        line-height: 16px;
        font-size: 16px;
        min-height: 30px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
    .hos-form-item {
        .hos-radio-group {
            .hos-radio {
                margin-top: 5px;
            }
        }
    }

    .hos-form-item__label:hover {
        cursor: move;
    }

    .has-external-config {
        position: relative;
        .external-btn {
            position: absolute;
            right: 0;
            top: 0;
            transform: translateX(100%);
        }
        .external-btn:focus {
            color: #fff;
            border-color: #DCDFE6;
        }
        .external-btn:hover {
            border-color: #409EFF;
            color: #409EFF;
        }
    }
}
.expand-field-popper {
    width: 600px;
    max-height: 500px;
    // min-height: 300px;

    .top-search {
        display: flex;
        padding: 5px 10px;
    }

    .table-container {
        padding: 0px 0px 10px;
        width: auto;
        // position: absolute;
        top: 75px;
        left: 0;
        bottom: 0;
        right: 0;
        overflow-y: auto;

        .el-table.has-data {
            min-width: 2000px;
            overflow: hidden;

            // 隐藏表格底部自带的滚动条
            .el-table__body-wrapper {
                overflow-x: hidden !important;
            }
        }
    }
}