<template>
  <div :style="styleObj">
    <v-chart ref="myVChart" :options="options" autoresize />
  </div>
</template>

<script>
import { targetWidgetLinkageLogic } from "../../../designer/linkageLogic"

import widgetMixin from "../../../designer/widget/widget-mixin.js"
export default {
  name: "WidgetFunnel",
  components: {},
  mixins: [widgetMixin],
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {
        color: [],
        title: {
          text: "",
          textStyle: {
            color: "#fff"
          }
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c}"
        },
        legend: {
          x: "center",
          y: "92%",
          textStyle: {
            color: "#fff"
          }
        },
        series: [
          {
            name: "",
            type: "funnel",
            left: "center",
            width: "80%",
            // maxSize: '80%',
            sort: "descending",
            label: {
              normal: {
                show: true,
                position: "inside",
                formatter: "{c}",
                textStyle: {
                  color: "#fff",
                  fontSize: 14
                }
              },
              emphasis: {
                position: "inside",
                formatter: "{b}: {c}"
              }
            },
            itemStyle: {
              normal: {
                opacity: 0.8,
                borderColor: "rgba(12, 13, 43, .9)",
                borderWidth: 1,
                shadowBlur: 4,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                shadowColor: "rgba(0, 0, 0, .6)"
              }
            },
            data: []
          }
        ]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsCollapse: {}, // 图标属性
      optionsSetup: {}
    }
  },
  computed: {
    styleObj() {
      return {
        position: this.ispreview ? "absolute" : "static",
        width: this.optionsStyle.width + "px",
        height: this.optionsStyle.height + "px",
        left: this.optionsStyle.left + "px",
        top: this.optionsStyle.top + "px",
        background: this.optionsSetup.background
      }
    },
    allComponentLinkage() {
      return this.$store.state.designer.allComponentLinkage
    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position
        this.optionsData = val.data
        this.optionsCollapse = val.collapse
        this.optionsSetup = val.setup
        this.editorOptions()
      },
      deep: true
    }
  },
  created() {
    this.optionsStyle = this.value.position
    this.optionsData = this.value.data
    this.optionsCollapse = this.value.collapse
    this.optionsSetup = this.value.setup
    this.editorOptions()
    targetWidgetLinkageLogic(this) // 联动-目标组件逻辑
  },
  methods: {
    // 修改图标options属性
    editorOptions() {
      this.setEnding()
      this.setOptionsText()
      this.setOptionsTitle()
      this.setOptionsTooltip()
      this.setOptionsLegend()
      this.setOptionsColor()
      this.setOptionsData()
    },
    // 翻转
    setEnding() {
      const optionsSetup = this.optionsSetup
      const series = this.options.series
      if (optionsSetup.ending) {
        series[0].sort = "ascending"
      } else {
        series[0].sort = "descending"
      }
    },
    // 数值设置
    setOptionsText() {
      const optionsSetup = this.optionsSetup
      const normal = {
        show: optionsSetup.isShow,
        position: "inside",
        formatter: "{c}",
        textStyle: {
          color: optionsSetup.color,
          fontSize: optionsSetup.fontSize,
          fontWeight: optionsSetup.fontWeight
        }
      }
      this.options.series[0].label["normal"] = normal
    },
    // 标题修改
    setOptionsTitle() {
      const optionsSetup = this.optionsSetup
      const title = {}
      title.text = optionsSetup.titleText
      title.show = optionsSetup.isNoTitle
      title.left = optionsSetup.textAlign
      title.textStyle = {
        color: optionsSetup.textColor,
        fontSize: optionsSetup.textFontSize,
        fontWeight: optionsSetup.textFontWeight,
        fontStyle: optionsSetup.textFontStyle
      }
      title.subtext = optionsSetup.subText
      title.subtextStyle = {
        color: optionsSetup.subTextColor,
        fontWeight: optionsSetup.subTextFontWeight,
        fontSize: optionsSetup.subTextFontSize,
        fontStyle: optionsSetup.subTextFontStyle
      }
      this.options.title = title
    },
    // 提示语设置 tooltip
    setOptionsTooltip() {
      const optionsSetup = this.optionsSetup
      const tooltip = {
        trigger: "item",
        show: true,
        textStyle: {
          color: optionsSetup.tipsColor,
          fontSize: optionsSetup.tipsFontSize
        }
      }
      this.options.tooltip = tooltip
    },
    // 图例操作 legend
    setOptionsLegend() {
      const optionsSetup = this.optionsSetup
      const legend = this.options.legend
      legend.show = optionsSetup.isShowLegend
      legend.left = optionsSetup.lateralPosition
      legend.right = optionsSetup.lateralPosition
      legend.top = optionsSetup.longitudinalPosition
      legend.bottom = optionsSetup.longitudinalPosition
      legend.orient = optionsSetup.layoutFront
      legend.textStyle = {
        color: optionsSetup.legendColor,
        fontSize: optionsSetup.legendFontSize
      }
      legend.itemWidth = optionsSetup.legendWidth
    },
    // 图例颜色修改
    setOptionsColor() {
      const optionsSetup = this.optionsSetup
      const customColor = optionsSetup.customColor
      if (!customColor) return
      const arrColor = []
      for (let i = 0; i < customColor.length; i++) {
        arrColor.push(customColor[i].color)
      }
      this.options.color = arrColor
      this.options = Object.assign({}, this.options)
    },
    staticDataFn(val) {
      const staticData = typeof val === "string" ? JSON.parse(val) : val
      for (const key in this.options.series) {
        if (this.options.series[key].type == "funnel") {
          this.options.series[key].data = staticData
        }
      }
    },
    renderingFn(val) {
      for (const key in this.options.series) {
        if (this.options.series[key].type == "funnel") {
          this.options.series[key].data = val
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
