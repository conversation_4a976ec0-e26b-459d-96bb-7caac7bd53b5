var doctorName = '';
var userId = '';
var orgId = '';
// 当前页面地址baseUrl路径
var origin = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
// 定义 options 数组
var options = [
  { title: '受试者推荐', icon:'icon2.png', description: '受试者入组系统，为IIT和GCP研究项目快速推荐受试者。', url: 'http://hxdmc.cn/research-web/#/his-recommend-detail' }, // 配置受试者推荐url
  { title: '科研患者入组', icon:'icon3.png', description: '科室和医生在信息中心申请建设的临床科研项目', url: 'edc' }
];
function getUrl(){
  // 获取受试者入组页面跳转的地址
  return options[0].url + '?medCode=' + orgCode + '&deptId=' + deptId + '&foreignId=' + foreignId + '&regNo=' + regNo;
}
// 链接跳转函数
function link(title, url) {
  if(url == 'edc'){
    toEdc()
  }else{
    if (isIE()) {
        goChrome();
    } else {
        console.log("不是IE浏览器");
        window.open(getUrl(), title);
    }
  }
}
// 跳转edc的方法
function toEdc() {
  var href = origin + '/his-into-group/index.html?orgCode=' + orgCode + '&foreignId=' + foreignId + '&regNo=' + regNo;
  window.open(href, "csm_window");
}
$(function () {

  // 动态渲染选项
  var $container = $('#options-container');
  $.each(options, function (index, item) {
    var optionHtml = '<div class="t-option" onclick="link(\'' + item.title + '\', \'' + item.url + '\')">' +
        '  <div class="t-icon">' +
        '    <img src="./image/' + item.icon + '" alt="' + item.title + '" class="t-icon-img">' +
        '  </div>' +
        '  <div class="t-title">' + item.title + '</div>' +
        '  <div class="t-description">' +
        '    <span class="t-tag">适用</span>' +
        '    ' + item.description +
        '  </div>' +
        '</div>';
    $container.append(optionHtml);
  });
});

