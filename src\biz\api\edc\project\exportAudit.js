export const queryListApi = (params) => {
    return {
        url: 'edc/export-audit/all/page',
        method: 'get',
        params,
    }
}


export const queryAllListApi = (params) => {
    return {
        url: `edc/export-audit/all/page`,
        method: 'GET',
        params
    }
}

export const detailApi = (params) => {
    return {
        url: 'edc/export-audit/detail/' + params.id,
        method: 'get',
        params,
    }
}

export const deleteApi = (data) => {
    return {
        url: 'edc/export-audit/deletion',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'edc/export-audit/update',
        method: 'post',
        data,
    }
}