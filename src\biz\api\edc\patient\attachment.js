export const getPatientAttachmentTree = (params) => {
  return {
      url: `edc/attachment/get-tree-by-patient`,
      method: 'GET',
      params
  }
}

export const deleteAttachmentApi = (data) => {
  return {
      url: `edc/attachment/relation/deletion`,
      method: 'post',
      data
  }
}

// 获取所有自定义属性
export const getAttachmentProps = (params) => {
  return {
      url: `edc/attachment/attrs`,
      method: 'get',
      params
  }
}

// 新增自定义属性
export const addAttachmentProp = (data) => {
  return {
      url: `edc/attachment/attrs/insert`,
      method: 'post',
      data
  }
}

// 删除自定义属性
export const deleteAttachmentProp = (data) => {
  return {
      url: `edc/attachment/attrs/deletion`,
      method: 'post',
      data
  }
}

// 上传附件
export const uploadAttachmentApi = (data) => {
  return {
      url: `edc/attachment/relation/upload-by-patient`,
      method: 'post',
      data
  }
}

export const downloadApi = (params) => {
  return {
      url: `edc/attachment/file/download`,
      method: 'GET',
      params,
      responseType: 'blob'
  }
}

export const downloadZipApi = (data) => {
  return {
      url: `edc/attachment/file/downloadZip`,
      method: 'post',
      data,
      responseType: 'blob'
  }
}

// 预览图片
export const getFilesPreview = (id) => {
  return {
      url: `edc/attachment/file-preview/${id}`,
      method: 'get',
  }
}

// 获取指定访视所有的附件
export const getVisitAttachment = (params) => {
  return {
    url: `edc/attachment/file/get-by-visit`,
    method: 'GET',
    params
  }
}

// 删除
export const deleteVisitAttachment = (data) => {
  return {
    url: `edc/attachment/file/deletion`,
    method: 'post',
    data
  }
}

// 获取指定患者id的随访列表
export const getPatVisitList = (params) => {
  return {
    url: `edc/patient/visit`,
    method: 'GET',
    params
  }
}

// 获取下载记录
export const getDownloadRecord = (params) => {
  return {
    url: `edc/attachment/op/page`,
    method: 'GET',
    params
  }
}

// 通知云端开始下载文件
export const notifyCloudDownload = (data) => {
  return {
    url: `edc/attachment/file/patient/download`,
    method: 'POST',
    data
  }
}

// 根据任务id查询下载进度
export const getDownloadProgress = (id) => {
  return {
    url: `edc/attachment/op/detail/${id}`,
    method: 'GET'
  }
}

// 获取系统隐私规则配置
export const getAttachPrivacyRule = (params) => {
  return {
    url: `edc/attachment/file/config`,
    method: 'GET',
    params
  }
}

// 获取附件管理业患者分页列表
export const getPatientAttachmentPage = (params) => {
  return {
    url: `edc/patient/attachment/page`,
    method: 'GET',
    params
  }
}

// 通知云端开始下载单个访视下多个附件
export const downloadVisitAttachments = (data) => {
  return {
    url: `edc/attachment/file/download/batch`,
    method: 'POST',
    data
  }
}