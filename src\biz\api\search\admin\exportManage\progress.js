
export const queryDetailApi = (id) => {
    return {
        url: 'search/observe-period/detail/' + id,
        method: 'GET'
    }
}

// 分页数据
export const queryListApi = (params) => {
    return {
        url: 'search/observe-period/page',
        method: 'GET',
        params
    }
}

// 新增
export const addApi = (data) => {
    return {
        url: 'search/observe-period/insert',
        method: 'POST',
        data
    }
}
// 修改
export const updateApi = (data) => {
    return {
        url: 'search/observe-period/update',
        method: 'POST',
        data
    }
}

// 删除
export const deleteBatchApi = (data) => {
    return {
        url: 'search/observe-period/deletion',
        method: 'POST',
        data
    }
}
