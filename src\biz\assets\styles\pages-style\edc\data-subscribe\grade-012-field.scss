.grade012-box {
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;

  .outer-container {
    height: calc(100% - 40px);
    position: relative;


    .title {
      width: 100%;
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #d9d9d9;
      background-color: #f5f7fa;
      display: flex;
      justify-content: space-between;
    }

    .search {
      width: 250px;
      height: 48px;
      padding: 10px;
      box-sizing: border-box;
    }

    .field-content {
      height: calc(100% - 88px);
      overflow-y: auto;
      padding: 10px;
      overflow-y: scroll;

      .hos-tag {
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;
      }

      .hos-tag.is-selected {
        color: white;
        background-color: #589cfc;
      }

      .hos-tag.disable {
        color: white;
        border-color: #909399;
        background-color: #909399;
        cursor: not-allowed;
      }
    }
  }

  .garde012-btn-container {
    height: 40px;
    padding-right: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}