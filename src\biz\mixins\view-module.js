import { getToken } from '@base/utils/base/token-util';
import qs from 'qs'
// import { sysDictService } from '@/common/api'
import {deepClone} from "@/utils/index.js"
export default {
  data() {
    /* eslint-disable */
    return {
      // 设置属性
      mixinViewModuleOptions: {
        activatedIsNeed: true, // 此页面是否在激活（进入）时，调用查询数据列表接口？
        getDataListURL: '', // 数据列表接口，API地址
        getDataListIsPage: false, // 数据列表接口，是否需要分页？
        deleteURL: '', // 删除接口，API地址
        deleteIsBatch: false, // 删除接口，是否需要批量？
        deleteIsBatchKey: 'id', // 删除接口，批量状态下由那个key进行标记操作？比如：pid，uid...
        exportURL: '', // 导出接口，API地址
        isExportWithQueryParams: false // 导出时携带所有查询的参数
      },
      tableHeight: '80%',
      // 默认属性
      firstDataForm: null, // 缓存首次查询时的查询条件，以便在点击重置按钮后还原回去
      dataForm: {}, // 查询条件
      dataList: [], // 数据列表
      dataListTotal: [], // 累计列表
      requestResult: {},
      order: '', // 排序，asc／desc
      orderField: '', // 排序，字段
      page: 1, // 当前页码
      limit: 10, // 每页数
      total: 0, // 总条数
      totalPage: 0, // 总页数
      dataListLoading: false, // 数据列表，loading状态
      dataListSelections: [], // 数据列表，多选项
      addOrUpdateVisible: false // 新增／更新，弹窗visible状态
    }
    /* eslint-enable */
  },
  computed:{
    
  },
  // activated() {
  //   if (this.mixinViewModuleOptions.activatedIsNeed && this.mixinViewModuleOptions.getDataListURL) {
  //     this.getDataList()
  //   }
  // },
  created() {
    if (this.mixinViewModuleOptions.activatedIsNeed && this.mixinViewModuleOptions.getDataListURL) {
      this.getDataList()
    }
  },
  methods: {
    getQueryParams() {
      let timeRangeObj = {}
      if (this.dataForm.timerange && this.dataForm.timerange.length > 0) {
        timeRangeObj = {
          startTime: this.dataForm.timerange[0],
          endTime: this.dataForm.timerange[1]
        }
      }
      return {
        subjectId:this.subjectId,
        order: this.order,
        sort: this.orderField,
        current: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
        size: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
        ...this.dataForm,
        ...timeRangeObj
      }
    },
    // 获取数据列表
    getDataList(resetPage = false) {
      if (resetPage) {
        this.page = 1
        this.dataListTotal = []
      }
      this.dataListLoading = true
      let timeRangeObj = {}
      if (this.dataForm.timerange && this.dataForm.timerange.length > 0) {
        timeRangeObj = {
          startTime: this.dataForm.timerange[0],
          endTime: this.dataForm.timerange[1]
        }
      }
      if (!this.firstDataForm) {
        this.firstDataForm = deepClone(this.dataForm)
      }
      console.log()
      const params = {
        order: this.order,
        sort: this.orderField,
        subjectId:this.subjectId,
        current: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
        size: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
        ...this.dataForm,
        ...timeRangeObj
      }
      return this.mixinViewModuleOptions.getDataListURL(params)
        .then(_res => {
          const res = _res.data || []
          this.dataListLoading = false
          this.requestResult = res
          this.dataList = this.mixinViewModuleOptions.getDataListIsPage ? res.records : res
          // 在初始化一下dataList
          this.dataList = this.dataList == null ? [] : this.dataList
          if (this.dataListTotal.length === 0 && this.dataList.length > 0) {
            this.dataListTotal = [...this.dataList]
          }
          this.requestResult = this.requestResult == null ? {} : this.requestResult
          this.total = this.mixinViewModuleOptions.getDataListIsPage ? res.total : 0
          this.totalPage = res.totalPage ? res.totalPage : Math.ceil(this.total / this.limit)
        })
        .catch(() => {
          this.dataList = []
          this.dataListTotal = []
          this.requestResult = {}
          this.total = 0
          this.totalPage = 0
          this.dataListLoading = false
        })
    },
    loadMore() {
      if (this.totalPage <= this.page) {
        return
      }
      this.page++
      this.getDataList().then(() => {
        this.dataListTotal = this.dataListTotal.concat(this.dataList)
      })
    },
    refreshLoadMore() {
      let scrollTop = 0
      const scrollContainer = document.querySelector('.d2-container-full__body')
      if (scrollContainer) {
        scrollTop = scrollContainer.scrollTop
      }
      const curPage = this.page
      const curLimit = this.limit
      this.page = 1
      this.limit = curPage * curLimit
      this.dataListTotal = []
      this.getDataList().then(() => {
        this.page = curPage
        this.limit = curLimit
        this.totalPage = Math.ceil(this.total / this.limit)
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollTop
        }
      })
    },
    // 多选
    dataListSelectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 排序
    dataListSortChangeHandle(data) {
      if (!data.order || !data.prop) {
        this.order = ''
        this.orderField = ''
        return false
      }
      this.order = data.order.replace(/ending$/, '')
      this.orderField = data.prop.replace(/([A-Z])/g, '_$1').toLowerCase()
      this.getDataList()
    },
    // 分页, 每页条数
    pageSizeChangeHandle(val) {
      this.page = 1
      this.limit = val
      this.getDataList()
    },
    // 分页, 当前页
    pageCurrentChangeHandle(val) {
      this.page = val
      this.getDataList()
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.init()
      })
    },
    // 删除
    deleteHandle(id, content) {
      if (this.mixinViewModuleOptions.deleteIsBatch && !id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('biz.edc.prompt.deleteBatch'),
          type: 'warning',
          duration: 500
        })
      }
      // 自定义删除提醒
      let msgInfo = ''
      if (content) {
        msgInfo = this.$t('biz.edc.prompt.deleteInfo', {
          content: content
        })
      } else {
        msgInfo = this.$t('biz.edc.prompt.info', {
          handle: this.$t('biz.edc.delete')
        })
      }
      this.$confirm(msgInfo, this.$t('biz.edc.prompt.title'), {
        loading: true,
        confirmButtonText: this.$t('biz.edc.confirm'),
        cancelButtonText: this.$t('biz.edc.cancel'),
        type: 'warning'
      })
        .then(() => {
            this.mixinViewModuleOptions.deleteURL({
              data: id ? [id] : this.dataListSelections.map(item => item[this.mixinViewModuleOptions.deleteIsBatchKey])
            }).then(res => {
              this.$message({
                message: this.$t('biz.edc.prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  // 需要将页码减1以解决删除最后一页所有数据后页面无数据的情况
                  const deleteNum = id ? 1 : this.dataListSelections.length // 计算本次删除的数据条数
                  if (this.dataList.length === deleteNum && this.page > 1 && this.page === this.totalPage) {
                    this.page--
                  }
                  this.getDataList()
                }
              })
            })
            .catch(() => { })
        })
        .catch(() => { })
    },
    // 导出
    exportHandle() {
      const query = this.mixinViewModuleOptions.isExportWithQueryParams ? this.getQueryParams() : this.dataForm
      var params = qs.stringify({
        token: getToken(),
        ...query
      })
      window.location.href = `${this.$baseUrl}${this.mixinViewModuleOptions.exportURL}?${params}`
    },
    // 刷新
    refreshHandle() {
      this.page = 1
      this.getDataList()
    },
    // 重置查询内容
    resetQueryField() {
      if (this.firstDataForm) {
        this.dataForm = deepClone(this.firstDataForm)
      } else {
        Object.keys(this.dataForm).forEach(key => {
          this.dataForm[key] = ''
        })
      }
      this.dataListTotal = []

      this.refreshHandle()
    }
  }
}
