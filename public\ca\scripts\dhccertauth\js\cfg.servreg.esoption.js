﻿//页面选中数据
var editIndex = "";
var newIndex = "";

$(function() {
    initBTN();
    setOption(parent.esOptionValue);
});

//初始化按钮事件
function initBTN() {
    $("#btnSaveOption").click(function() { saveOptionCheck();});
    $("#btnCancel").click(function() { cancelOption();});

    $("#btnNew").click(function(){newDataGrid();});
    $("#btnModify").click(function(){modifyDataGrid();});
    $("#btnSave").click(function(){saveDataGrid();});
    $("#btnDelete").click(function(){deleteDataGrid();});
    $("#btnCancelModify").click(function(){cancelModifyDataGrid();});
}

//保存全部签章数据
function saveOptionCheck() {
    if (editIndex !== "") {
        $.messager.confirm("提示", "编辑还未完成，未保存的修改将会丢失，是否继续保存？", function (r) {
            if (r) {
                if (editIndex === newIndex) {
                    $("#dgES").datagrid("deleteRow",editIndex);
                } else {
                    $("#dgES").datagrid("cancelEdit",editIndex);
                }
                saveOption();
            }
        });
    } else {
        saveOption();
    }
}

function saveOption() {
    var optionValue = {};
    var result = $("#dgES").datagrid("getData").rows
    if (result.length !== 0) {
        optionValue = {
            data: result
        };
    }
    parent.esOptionValue = optionValue;
    parent.setOptionHtmlInfo("esOption",optionValue),
    parent.closeDialog("esOptionDiv");
}

//取消当前全部修改
function cancelOption() {
    parent.closeDialog("esOptionDiv");
}

//新增一行
function newDataGrid() {
    if (editIndex !== "") {
        $.messager.alert("提示","请先保存或者取消当前修改的数据，再进行新增操作！");
        return ;
    }

    $("#dgES").datagrid("appendRow",{
        orgCode: "",
        orgName:"",
        orgCert:"",
        sealID: "",
        sealCode: "",
        sealHeight: "",
        sealWidth: "",
        sealCert:""
    });

    var curInd =$("#dgES").datagrid("getData").rows.length -1;
    $("#dgES").datagrid("beginEdit",curInd);
    editIndex = curInd;
    newIndex = curInd;
    $("#dgES").datagrid("selectRow",editIndex);
}

//修改数据
function modifyDataGrid() {
    var current = $("#dgES").datagrid("getSelected");
    if (current == null) return;

    var curInd =$("#dgES").datagrid("getRowIndex",current);
    $("#dgES").datagrid("beginEdit",curInd);
    editIndex = curInd;
}

//取消修改
function cancelModifyDataGrid() {
    var current = $("#dgES").datagrid("getSelected");
    if (current == null) return;

    var curInd =$("#dgES").datagrid("getRowIndex",current);
    $("#dgES").datagrid("cancelEdit",curInd);
    if (newIndex === curInd) {
        $("#dgES").datagrid("deleteRow",curInd);
    }
    editIndex = "";
    newIndex = "";
}

//保存数据
function saveDataGrid() {
    var current = $("#dgES").datagrid("getSelected");
    if (current == null) return;

    var curInd =$("#dgES").datagrid("getRowIndex",current);
    $("#dgES").datagrid("endEdit",curInd);
    if ($("#dgES").datagrid("getData").rows[curInd].sealCode == "") {
        $.messager.popover({msg: "存在必填项未填写，不允许保存",type: "alert",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
        return;
    }
    editIndex = "";
    newIndex = "";
}

//删除数据
function deleteDataGrid() {
    var current = $("#dgES").datagrid("getSelected");
    if (current == null) return;

    var curInd =$("#dgES").datagrid("getRowIndex",current);
    $("#dgES").datagrid("deleteRow",curInd);
    editIndex = "";
    newIndex = "";
}

//初始化表格数据
function setOption(esOption) {
    //这里转一下字符串，对象属性，直接操作会修改父页面的数据
    var optionStr = JSON.stringify(esOption);
    var gridData = JSON.parse(optionStr).data;
    if (optionStr == "{}") {
        gridData = [];
    }

    $("#dgES").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbES",
        data:gridData,
        singleSelect:true,
        pagination:false,
        rownumbers:true,
        columns:[[
            {field:"sealID",width:100,title:"签章ID",editor:"text",options:{required:true}},
            {field:"sealCode",width:150,title:"签章类型",
                formatter:function(value,row){
                    return row.sealCode;
                },
                editor:{
                    type:"combobox",
                    options:{
                        required:true,
                        blurValidValue:true,
                        valueField: "sealCode",
                        textField: "sealValue",
                        data:
                            [{ sealCode: "IPDIAG", sealValue: "住院诊断章"},
                            { sealCode: "OPDIAG",sealValue: "门诊诊断章"},
                            { sealCode: "IPFEE",sealValue: "住院费用章"},
                            { sealCode: "OPFEE",sealValue: "门诊费用章"},
                            { sealCode: "IPFEEDETAIL",sealValue: "住院费用明细章"},
                            { sealCode: "OPFEEDETAIL",sealValue: "门诊费用明细章"}]
                    }
                }
            },
            {field:"sealHeight",width:80,align:"left", title:"签章高度",editor:"numberbox"},
            {field:"sealWidth",width:80,align:"left", title:"签章宽度",editor:"numberbox"},
            {field:"sealCert",width:300,align:"left", title:"签章证书",editor:{type:"textarea",options:{height:"60px"}}},
            {field:"orgCode",width:150,align:"left",title:"组织机构代码",editor:"text"},
            {field:"orgName",width:150,align:"left",title:"组织机构名称",editor:"text"},
            {field:"orgCert",width:300,align:"left",title:"组织机构证书",editor:{type:"textarea",options:{height:"60px"}}}
        ]],
        onLoadError:function() {
        },
        onSelect:function(rowIndex,row){
        },
        onLoadSuccess:function(data){
        },
        onBeforeSelect:function(index, row){
            if ((editIndex !== "")&&(editIndex !== index)) {
                $.messager.alert("提示","请先保存或者取消当前修改的数据，再选择其他数据！");
                $("#dgES").datagrid("selectRow",editIndex);
                return false;
            }
        }
    });
}