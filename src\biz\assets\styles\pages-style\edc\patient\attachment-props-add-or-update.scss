.attachment-prop-row {
    margin-top: 10px;
    margin-left: 5px !important;
    margin-right: 0 !important;

    .flex-space-between {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .mover {
        cursor: move;
    }

    .selected-props {
        border-right: 1px solid #e4e7ed;
    }

    .selected-props-item {
        padding: 10px;
        border-bottom: 1px solid #ddd;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item-rank,
        .item-close {
            margin-right: 10px;
        }

        .item-close {
            margin-left: 10px;
            cursor: pointer;
        }
    }

    .common-item-box {
        padding: 10px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;

        .common-item {
            padding: 6px 10px;
            margin-right: 8px;
            margin-bottom: 8px;
            background-color: #e6effb;
            color: #4e4e4e;

            &.actived,
            &:hover {
                background-color: #409EFF;
                color: #fff;
            }

            img {
                width: 15px;
                height: 15px;
                position: relative;
                top: 3px;
                margin-right: 5px;
            }

            span {
                cursor: pointer;
            }

            .delete-prop {
                margin-left: 10px;

                &:hover {
                    cursor: pointer;
                    color: #F56C6C;
                }
            }
        }

        .button-item {
            margin-bottom: 8px
        }
    }
}