<template>
  <div class="select-tree-wrapper h-fit" :class="{'is-cancelable':!!ultimateTreeOptions.treeConf.isClickCancel}">
    <div v-if="ultimateTreeOptions.type === 'input'" style="flex:none;">
      <hos-input clearable :placeholder="$t('请输入检索内容')" suffix-icon="hos-icom-find" v-model.trim="keyWord"></hos-input>
    </div>
    <div v-if="ultimateTreeOptions.type === 'select'" style="flex:none;">
        <hos-select
        v-model="value"
        :placeholder="$t('请选择')"
        @change="handleChange"
      >
        <hos-option
          v-for="item in options"
          :key="optionKey(item)+'option'"
          :label="item[ultimateTreeOptions.selectConf.labelName]"
          :value="optionKey(item)" />
      </hos-select>
    </div>

    <!-- 树结构上方的插槽 -->
    <div style="flex:none;padding:0 0 5px 10px;">
      <slot name="treeTopHandler"></slot>
    </div>

    <hos-tree
      ref="tree"
      :data="treeData"
      :props="ultimateTreeOptions.treeConf.defaultProps ? ultimateTreeOptions.treeConf.defaultProps : nodeProps"
      :node-key="ultimateTreeOptions.treeConf.nodeKey"
      :default-expand-all="ultimateTreeOptions.treeConf.isExpandAll"
      :filter-node-method="filterNode"
      :highlight-current="true"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script>
import { _debounce } from "@/utils/throttle.js"
export default {
  name: "SelectTree",
  props: {
    ultimateTreeOptions: {
      type: Object,
      default: () => {
        return {
          type: "select",
          selectConf: {
            url: "",
            labelName: "label",
            valueName: "id",
            options: []
          },
          treeConf: {
            url: "",
            defaultProps: {
              children: "children",
              label: "label",
            },
            nodeKey: "id",
            isExpandAll: true,
            treeData: [],
            isFirstSelect: true,
            isClickCancel: false,
            defaultCurrentKey: null // 自定义默认选中项
          },
        }
      },
    },
  },
  data() {
    return {
      keyWord: "",
      options: [],
      value: "",

      treeData: [],

      nodeProps: {
        children: "children",
        label: "label",
      },
      curTreeNode: null, // 当前选中的tree node
      // lastTreeNode: null // 上次选中的tree node
    }
  },
  computed: {
  },
  watch: {
    keyWord(val) {
      this.$refs.tree.filter(val)
    },
    ultimateTreeOptions: {
      deep: true,
      handler: function(val) {
        this.watchConfigs(val)
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.watchConfigs(this.ultimateTreeOptions)
      if (this.ultimateTreeOptions.type === "select") {
        this.queryOptions()
      } else {
        this.queryTreeData()
      }
    },
    watchConfigs(val) {
      if (val.selectConf && val.selectConf.options) {
        if (val.selectConf.options.length !== 0) {
          this.options = val.selectConf.options
        }
      }
      if (val.treeConf && val.treeConf.treeData) {
        this.treeData = val.treeConf.treeData
        this.defaultSelect1stNode()
      }
    },
    queryOptions() {
      if (
        this.ultimateTreeOptions.selectConf.url &&
        this.ultimateTreeOptions.selectConf.url !== ""
      ) {
        this.ultimateTreeOptions.selectConf.url().then((res) => {
          if (res.code == 200) {
            this.options = res.data || []
            if (this.options.length > 0) {
              if(this.ultimateTreeOptions.selectConf  && this.ultimateTreeOptions.selectConf.defaultValue) {
                this.value = this.ultimateTreeOptions.selectConf.defaultValue
              } else {
                if (this.options[0].data) {
                  this.value = this.options[0].data[this.ultimateTreeOptions.selectConf.valueName]
                } else {
                  this.value = this.options[0][this.ultimateTreeOptions.selectConf.valueName]
                }
              }
              this.handleChange(this.value)
            }
          }
        })
      }
    },
    queryTreeData() {
      if (
        this.ultimateTreeOptions.treeConf.url === "" ||
        this.ultimateTreeOptions.treeConf.url === "null" ||
        this.ultimateTreeOptions.treeConf.url === undefined
      ) {
        return
      }
      this.ultimateTreeOptions.treeConf.url(this.getUrlParams()).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data || []
          if(this.ultimateTreeOptions.treeConf.defaultCurrentKey) {
            const currentNode = this.treeData.filter(i => {
              const id = i.data ? i.data.id : i.id
              if (id == this.ultimateTreeOptions.treeConf.defaultCurrentKey) {
                return i
              }
            })[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(currentNode.id)
              this.handleNodeClick(currentNode)
            })
          } else {
            this.defaultSelect1stNode()
          }
        }
      })
    },
    defaultSelect1stNode() {
      if (this.treeData.length > 0 && this.ultimateTreeOptions.treeConf.isFirstSelect !== false) {
        // 'nextTick()' 下次dom更新时触发回调函数
        // 默认点击
        this.$nextTick().then(() => {
          const firstNode = document.querySelector(".hos-tree-node")
          firstNode.click()
        })
      }
    },
    getUrlParams() {
      let params = {}
      if (this.ultimateTreeOptions.type === "select") {
        if (
          this.value === "" ||
          this.value === "null" ||
          this.value === undefined
        ) {
          //
        } else {
          params.value = this.value
        }
      } else {
        if (
          this.keyWord === "" ||
          this.keyWord === "null" ||
          this.keyWord === undefined
        ) {
          //
        } else {
          params.keyWord = this.keyWord
        }
      }
      return params
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.ultimateTreeOptions.treeConf.defaultProps.label].indexOf(value) !== -1
    },

    handleChange: _debounce(function(val) {
      this.$emit("selectOption", val)
      this.queryTreeData() // 成员权限管理接口没有参数，暂时以全局查询
    }, 200),
    handleNodeClick: _debounce(function(node) {
      if (this.ultimateTreeOptions.treeConf.isClickCancel) {
        if (this.curTreeNode && this.curTreeNode.id === node.id) {
          // 取消选中
          this.curTreeNode = null
          this.$refs.tree.setCurrentKey(null)
        } else {
          this.curTreeNode = node
        }
      } else {
        this.curTreeNode = node
      }
      this.$emit("selectNode", this.curTreeNode)
    }, 200),
    optionKey(item) {
      if (item.data) {
        return item.data[this.ultimateTreeOptions.selectConf.valueName]
      } else {
        return item[this.ultimateTreeOptions.selectConf.valueName]
      }
    },
  },
}
</script>