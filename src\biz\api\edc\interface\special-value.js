// 分页查询
export const pageListApi = (params) => {
    return {
        url: `edc/external-special-val/page`,
        method: 'GET',
        params
    }
}

// 查询所有
export const queryAllApi = (params) => {
    return {
        url: `edc/external-special-val/all`,
        method: 'GET',
        params
    }
}

// 根据id查询详情
export const queryDetailApi = (id) => {
    return {
        url: `edc/external-special-val/detail/${id}`,
        method: 'GET',
    }
}

// 新增接口
export const addApi = (data) => {
    return {
        url: 'edc/external-special-val/insert',
        method: 'POST',
        data
    }
}

// 修改接口
export const editApi = (data) => {
    return {
        url: 'edc/external-special-val/update',
        method: 'post',
        data
    }
}

// 批量删除
export const deleteApi = (data) => {
    return {
        url: `edc/external-special-val/deletion`,
        method: 'POST',
        data
    }
}

// 根据接口类别id获取字段列表
export const getFieldsByCategoryId = (categoryId) => {
    return {
        url: `edc/external-special-val/list/${categoryId}`,
        method: 'GET'
    }
}