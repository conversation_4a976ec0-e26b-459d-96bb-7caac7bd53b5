﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//获取页面传参信息
var urlParams = ca_common_tools.getParams();
var globalInfo = {
    ViewCertID: urlParams.ViewCertID || "",
    OrgID: urlParams.OrgID || "",
    VenderCode: urlParams.VenderCode || "",
    SignTypeCode: urlParams.SignTypeCode || "",
    UserCode: urlParams.UserCode || "",
    IsLimitImageNull: urlParams.IsLimitImageNull || "",
    IsLimitImageSize: urlParams.IsLimitImageSize || "",
    IsConvertCareImage: urlParams.IsConvertCareImage || ""
}

if ((globalInfo.VenderCode != "")&&(globalInfo.SignTypeCode != "")) {
    document.write("<script src='../scripts/dhccertauth/ics/ca_common_"+ globalInfo.VenderCode.toLowerCase() +"_"+globalInfo.SignTypeCode.toLowerCase()+".js'><\/script>");

    /*var script = document.createElement("script");
    script.src = "http://localhost/his/base/scripts/ca/scripts/dhccertauth/imanageduser/ca_common_fake_ukey.js";
    var head = document.getElementsByTagName('head')[0];
    head.appendChild(script);
    */
}

$(function() {
    $(".textbox").attr("disabled","true");
    $(".hisui-textbox").attr("disabled","true");

    //查看证书时页面，隐藏相应按钮
    if (globalInfo.ViewCertID != "") {
        $("#btnViewCert").hide();
        $("#btnUsrCertReg").hide();
        $("#ukeyListTr").hide();
        $("#caUserCodeTr").hide();
        initCertInfo(globalInfo.ViewCertID);
        return;
    }

    if (globalInfo.UserCode === "") {
        $("#userCode").removeAttr("disabled");
    }

    $("#bindCertDateTime").hide();
    $("#signTypeCode").val(globalInfo.SignTypeCode);
    $("#venderCode").val(globalInfo.VenderCode);
    $("#userCode").val(globalInfo.UserCode);

    if (globalInfo.SignTypeCode == "UKEY") {
        $("#caUserCodeTr").hide();
        initUKey();
    } else if ((globalInfo.SignTypeCode == "PHONE")||(globalInfo.SignTypeCode == "FACE")) {
        ///人脸证书关联与手机证书关联用同一逻辑
        $("#caUserCode").removeAttr("disabled");
        $("#ukeyListTr").hide();
        initPhone();
    }

    $("#btnUsrCertReg").bind("click",function() {
        var userCode = $("#userCode").val();
        if ((userCode === null)||(userCode === "")) {
            $.messager.alert("提示","请先输入要绑定的HIS用户工号");
            return;
        }

        var certContainer = "";
        if (globalInfo.SignTypeCode == "UKEY") {
            certContainer = $HUI.combobox("#ukeyList").getValue();
            if ((certContainer === null) || (certContainer === "")) {
                $.messager.alert("提示","请先选择需要绑定的证书");
                return;
            }
        } else if ((globalInfo.SignTypeCode == "PHONE")||(globalInfo.SignTypeCode == "FACE")) {
            certContainer = $("#caUserCode").val();
            if ((certContainer === null) || (certContainer === "")) {
                $.messager.alert("提示","请先录入要绑定的CA-ID标识");
                return;
            }
        }

        var certObj = {
            signTypeCode:globalInfo.SignTypeCode,
            venderCode:globalInfo.VenderCode,
            userCertCode:$("#userCertCode").val(),
            certNo:$("#certNo").val(),
            certSn:$("#certSn").val(),
            certName:$("#certName").val(),
            identityID:$("#identityID").val(),
            signCert:$("#cert").val(),
            uKeyNo:$("#ukeyNo").val(),
            signImage:$("#signImgBase64").val(),
            userCode:userCode
        };

        if (certObj.userCertCode == "") {
            $.messager.alert("提示","CA用户唯一标识不能为空");
            return;
        }
        if (certObj.certNo == "") {
            $.messager.alert("提示","CA证书唯一标识不能为空");
            return;
        }
        if (certObj.signCert == "") {
            $.messager.alert("提示","CA证书不能为空");
            return;
        }

        var imgBase64 = certObj.signImage;
        var imageSize = getIamgeSize(imgBase64);

        if ((globalInfo.IsLimitImageNull == "1")&&(imgBase64.length < 50)) {
            $.messager.alert("提示","签名图片不能为空");
            return;
        }

        if ((globalInfo.IsLimitImageSize == "1")&&(imageSize > 15)) {
            $.messager.alert("提示","签名图片超长");
            return;
        }

        var data = {
            action: "BIND_USERCERT",
            params: {
                organizationID: globalInfo.OrgID,
                langID: logonInfo.LangID,
                bindCertInfo: certObj
            }
        };
    
        var json = ajaxPOSTCommon(data,"",false);
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.alert("提示", "证书关联成功！","success");
            } else {
                $.messager.alert("提示", "证书关联失败！", "error");
            }
        } else {
            $.messager.alert("提示", "关联证书数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
});


function initUKey() {
    $("#ukeyList").combobox({
        onLoadSuccess:function(d){
            if (d.length !== 0) {
                $("#ukeyList").combobox("select",d[0].id);
            }
        },
        onSelect : function(rec){
            var certContainer = rec.id;
            var callBackFunc = function(certResult) {
                if ("0" !== certResult.code) {
                    $.messager.alert("提示", "获取证书信息失败:"+certResult.msg, "error");
                    return;
                }
                var cert = certResult.data;
                var newImageBase64 = getConvertedImage(cert.signImage);
                $("#userCertCode").val(cert.usrCertCode).validatebox("validate");
                $("#certNo").val(cert.certificateNo).validatebox("validate");
                $("#certSn").val(cert.certificateSN).validatebox("validate");
                $("#certName").val(cert.certName).validatebox("validate");
                $("#ukeyNo").val(cert.uKeyNo);
                $("#identityID").val(cert.identityID);
                $("#cert").val(cert.certificate);
                $("#signImgBase64").val(newImageBase64);

                var objImg = document.getElementById("signImg");
                if (objImg != "") {
                    var imgdata = "data:image/jpeg;base64," + newImageBase64;
                      objImg.src = imgdata;
                }
            }
            ca_key.getUsrSignatureInfo({certContainer:certContainer,callBackFunc:callBackFunc});
        }
    });
    loadUKeyCertList();
}

function loadUKeyCertList() {
    var callBackFunc = function(certListResult) {
        if ("0" !== certListResult.code) {
            $.messager.alert("提示", "获取证书列表失败:"+certListResult.msg ,"error");
            return;
        }

        var list = certListResult.data.list;
        if ("" == list || 0 == list.length) {
            $("#ukeyList").combobox("loadData",[]);
            return "";
        }

        var arr = new Array();
        for (var i = 0; i < list.length; i++) {
            arr.push({"id":list[i].certContainer,"text":list[i].name});
        }
        $("#ukeyList").combobox("loadData",arr);
    }
    ca_key.getUserList(callBackFunc);
}

function initPhone() {
    $("#caUserCode").val(globalInfo.UserCode);
    $("#btnViewCert").bind("click",function() {
        var certContainer = $("#caUserCode").val();
        if ((certContainer === null)||(certContainer === "")) {
            $.messager.alert("提示","请先录入要查看的CA-ID标识");
            return;
        }
        
        var callBackFunc = function(certResult) {
            if ("0" !== certResult.code) {
                $.messager.alert("提示", "获取证书信息失败:"+certResult.msg, "error");
                return;
            }
            var cert = certResult.data;
            var newImageBase64 = getConvertedImage(cert.signImage);
            $("#userCertCode").val(cert.usrCertCode).validatebox("validate");
            $("#certNo").val(cert.certificateNo).validatebox("validate");
            $("#certSn").val(cert.certificateSN);
            $("#certName").val(cert.certName);
            $("#ukeyNo").val(cert.uKeyNo);
            $("#identityID").val(cert.identityID);
            $("#cert").val(cert.certificate);
            $("#signImgBase64").val(newImageBase64);

            var objImg = document.getElementById("signImg");
            if (objImg != "") {
                var imgdata = "data:image/jpeg;base64," + newImageBase64;
                  objImg.src = imgdata;
            }
        }
        ca_key.getCertInfoByUserCode({certContainer:certContainer,organizationID: globalInfo.OrgID,callBackFunc:callBackFunc});
    });
}

//考虑后台转换签名图
function getConvertedImage(base64) {
    return base64;
}

//根据证书ID展示对应证书数据
function initCertInfo(viewCertID) {
    var data = {
        action: "GET_CERTDETAIL",
        params: {
            organizationID: globalInfo.OrgID,
            langID: logonInfo.LangID,
            certID: viewCertID
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            showCertInfo(json.data);
        } else {
            $.messager.alert("提示", "获取证书详细数据错误，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function showCertInfo(data) {
    $("#userCode").val(data["userCode"]);
    $("#signTypeCode").val(data["signTypeCode"]);
    $("#venderCode").val(data["venderCode"]);
    $("#userCertCode").val(data["userCertCode"]);
    $("#certNo").val(data["certNo"]);
    $("#certSn").val(data["certSn"]);
    $("#certName").val(data["certName"]);
    $("#ukeyNo").val(data["ukeyNo"]);
    $("#identityID").val(data["identityID"]);
    $("#cert").val(data["cert"]);
    $("#signImgBase64").val(data["signImage"]);
    $("#bindDate").val(data["bindDate"]);
    $("#bindTime").val(data["bindTime"]);

    var objImg = document.getElementById("signImg");
    if (objImg != "") {
        var imgdata = "data:image/jpeg;base64," + data["signImage"];
          objImg.src = imgdata;
    }
}

function getIamgeSize(image) {
    // 找到等号，把等号去掉
    var index = image.indexOf("=");
    if (image.indexOf("=") > 0) {
        image = image.substring(0, index);
    }
    // 原来的字符流大小，单位为字节
    var length = image.length;
    // 计算后得到的文件流大小，单位为字节
    var fileLength = parseInt(length-(length/8)*2);
    // 由字节转换为kb
    var size = "";
    size = (fileLength / 1024).toFixed();
    return size;
}

