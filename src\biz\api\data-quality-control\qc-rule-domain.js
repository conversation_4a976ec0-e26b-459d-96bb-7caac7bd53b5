export const queryListApi = (params) => {
    return {
        url: `medical-data-domain/list/${params.current}/${params.size}`,
        method: 'get',
        params,
    }
}

export const detailApi = (params) => {
    return {
        url: 'medical-data-domain/query-recursion',
        method: 'get',
        params
    }
}

export const addOrUpdateApi = (data) => {
    return {
        url: 'medical-data-domain/save-or-update',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'medical-data-domain/update',
        method: 'post',
        data,
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'medical-data-domain/del',
        method: 'post',
        data
    }
}