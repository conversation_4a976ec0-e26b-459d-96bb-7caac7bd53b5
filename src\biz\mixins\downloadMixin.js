import axios from 'axios'
import Vue from 'vue'
import { getCurrentLocale } from '@base/utils/i18n/i18n-util'
import UserConstant from '@base/constant/user-constant'
import { getToken } from '@base/utils/base/token-util'
export default {
  methods: {
    doExport(config) {
      axios({
        url: config.url,
        method: config.method || 'post',
        responseType: 'blob',
        headers: {
          'access-token': getToken(),
          'client-ip': Vue.ls.get(UserConstant.IP),
          'client-mac': Vue.ls.get(UserConstant.Mac),
          language: getCurrentLocale()
        },
        data: config.data,
        ...config
      }).then(
        async (res) => {
          const response = await this.dealErrorByReader(res)
          this.BlobDownLoad(response)
        },
        (error) => {
          console.log(error)
        }
      )
    },
    BlobDownLoad(res, type) {
      let blob = new Blob([res.data], {
        type: type
      })
      const fileName = decodeURIComponent(res.headers['content-disposition'].split('filename=')[1])
      const href = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = href
      a.download = fileName
      a.click()
      a.remove()
      URL.revokeObjectURL(a.href)
    },
    dealErrorByReader(response) {
      return new Promise((resolve, reject) => {
        if (response.headers['content-type'] && response.headers['content-type'].includes('json')) {
          // 此处拿到的data才是blob
          const { data } = response
          const reader = new FileReader()
          reader.onload = () => {
            const { result } = reader
            const errorInfos = JSON.parse(result)
            const { msg } = errorInfos
            this.$message.error(msg)
            reject(msg)
            this.loading = false
          }
          reader.onerror = (err) => {
            reject(err)
            this.loading = false
          }
          reader.readAsText(data)
        } else {
          resolve(response)
          this.loading = false
        }
      })
    }
  }
}
