import { Message } from 'hosui'
import AuthConstant from '@base/constant/auth-constant'
import Constant from '@base/constant/common-constant'
import { checkModelRedirect } from "@/router/util"
// 请求成功时
const requestSuccessHandler = function (config) {
  // config.headers['TEST2'] = 'test2'
  return config
}

// 请求失败时
const requestFailHandler = function (error) {
  return Promise.reject(error)
}

// 响应成功时
const responseSuccessHandler = function (response) {
  if(response.data.code && (
    response.data.code == AuthConstant.tokenErrorCode ||
    response.data.code == '302' ||
    response.data.code == AuthConstant.tokenNotFondCode ||
    response.data.code == AuthConstant.captchaNeedOpenCode ||
    response.data.code == AuthConstant.passwordErrorCode ||
    response.data.code == '101-002-004-005' ||
    response.data.code == Constant.httpCode.UNAUTHORIZED ||
    response.data.code == AuthConstant.twoAuthErrorCode ||
    response.data.code.toString().includes(AuthConstant.forcedJumpSetPassword)
  )){
    // 系统级别的错误拦截
    if (response.data.code === AuthConstant.tokenErrorCode) {
      // 如果登陆模式是第三方，则需要重定向到第三方登录网址
      checkModelRedirect()
    }
  }else{
    // biz业务的错误拦截
    if (response.data.success === false || response.data.code == 500) {
      Message.error(response.data.msg || response.data.message)
      return Promise.reject(response)
    }
  }
  return response
}

// 响应失败时
const responseFailHandler = function (error) {
  if(error.config && error.config.ignoreError){
    // 支持接口传参配置不提示系统异常问题
    return Promise.reject(error.response.data)
  }
  return Promise.reject(error)
}

export default {
  requestSuccessHandler,
  requestFailHandler,
  responseSuccessHandler,
  responseFailHandler
}
