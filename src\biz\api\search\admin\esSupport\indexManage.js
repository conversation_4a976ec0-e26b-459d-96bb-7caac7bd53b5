
export const queryListApi = (params) => {
  return {
    url: 'search/elastic/index/list',
    method: 'GET',
    // params
  }
}

export const addApi = (data) => {
  return {
    url: 'search/elastic/index/create',
    method: 'PUT',
    data
  }
}

export const querySettingByIndex = (params) => {
  return {
    url: 'search/elastic/select-setting-by-index',
    method: 'GET',
    params
  }
}

export const queryMappingByIndex = (params) => {
  return {
    url: 'search/elastic/select-mapping-by-index',
    method: 'GET',
    params
  }
}

export const getHeadUrl = (params) => {
  return {
    url: 'search/api/get-head-url',
    method: 'GET',
    params
  }
}

export const getSenseUrl = (params) => {
  return {
    url: 'search/api/get-sense-url',
    method: 'GET',
    params
  }
}

export const getAllModels = () => {
  return {
    url: 'search/es-index-metadata/list',
    method: 'GET',
  }
}

export const getModalList = () => {
  return {
    url: 'search/elastic/select-index-model-map',
    method: 'GET',
  }
}

export const getTableField = (params) => {
  return {
    url: 'search/elastic/index/theme/property',
    method: 'GET',
    params
  }
}

export const putEditIndexModel = (data) => {
  return {
    url: 'search/elastic/index-model-map/update',
    method: 'post',
    data
  }
}

export const deleteIndexModel = (data) => {
  return {
    url: 'search/elastic/index-model-map/deletion-by-id',
    method: 'post',
    data,
    emulateJSON:true
  }
}

export const queryListReindex = (params) => {
  return {
    url: 'search/es/reindex/page',
    method: 'get',
    params
  }
}

export const postCreateReindex = (data) => {
  return {
    url: 'search/es/reindex/create',
    method: 'post',
    data
  }
}

export const PutStopConfirm = (taskId) => {
  return {
    url: `search/es/reindex/cancel/${taskId}`,
    method: 'put'
  }
}

// 添加表
export const postAddTable = (params) => {
  return {
    url: `search/elastic/insert-elastic-theme/${params.indexName}/${params.tableName}`,
    method: 'POST'
  }
}

// 添加字段
export const postAddField = (params) => {
  return {
    url: `search/elastic/insert-elastic-field/${params.indexName}`,
    method: 'POST',
    data: params.data
  }
}

// 创建指定的建议词索引
export const creatTipsIndex = (params) => {
  return {
    url: 'search/elastic-api/create-suggest-index',
    method: 'get',
    params
  }
}
