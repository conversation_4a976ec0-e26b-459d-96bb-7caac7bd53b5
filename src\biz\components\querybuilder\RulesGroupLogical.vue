<template>
  <div v-show="showLogical" class="rules-group-logical" :style="logicalStyleObject" @click="toggleLogical">
    <div class="flip" :style="transformStyleObject">
      <div class="front">
        AND
      </div>
      <div class="back">
        OR
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      showLogical: {
        type: <PERSON><PERSON>an,
        default() {
          return false
        }
      },
      logicalOperator: {
        type: String,
        default() {
          return "AND"
        }
      },
      logicalStyleObject: {
        type: Object,
        default() {
          return {
            top: "5px",
            left: "-10px"
          }
        }
      }
    },
    inject: ["queryBuilderObj"],
    data() {
      return {
        transformStyleObject: {
          transform: null
        }
      }
    },
    watch: {
      "logicalOperator": {
        immediate: true,
        handler() {
          this.watchLogical()
        }
      }
    },
    methods: {
      // 切换逻辑连接词
      toggleLogical() {
        if (!this.queryBuilderObj.disabled) {
          this.$emit("change-logical", this.logicalOperator)
        }
      },
      watchLogical() {
        if (this.logicalOperator === "AND") {
          this.transformStyleObject.transform = "rotateY(0deg)"
        } else {
          this.transformStyleObject.transform = "rotateY(180deg)"
        }
      }
    }
  }
</script>
