
// 获取左侧树的数据
export const getTreeByIndex = (val) => {
    return {
        url: `search/es-theme-metadata/tree-by-indexid/${val}`,
        method: 'get',

    }
}

// 分页获取表单数据
export const queryTableById = (params) => {
    return {
        url: 'search/es-property-metadata/page',
        method: 'GET',
        params
    }
}

// 更新数据
export const updateApi = (data) => {
    return {
        url: 'search/es-property-metadata/update',
        method: 'post',
        data
    }
}

// 删除数据
export const deleteBatchApi = (data) => {
    return {
        url: 'search/es-property-metadata/deletion',
        method: 'POST',
        data
    }
}

// 获取模型列表
export const getModelList = (params) => {
    return {
        url: 'search/es-index-metadata/page',
        method: 'get',
        params,
    }
}

export const queryListApi = (params) => {
    return {
        url: 'search/es-theme-metadata/page',
        method: 'GET',
        params,
    }
}

export const addApi = (data) => {
    return {
        url: 'search/es-property-metadata/insert',
        method: 'POST',
        data
    }
}

// 获取类型
export const getTypeList = () => {
    return {
        url: 'search/field-type/list',
        method: 'GET',

    }
}

// 同步字段的ES推荐词
export const syncFieldESSuggestion = (params) => {
  return {
    url: `search/elastic-api/bulk-insert-suggest`,
    method: 'GET',
    params
  }
}

// 获取标签
export const getTagList = (params) => {
    return {
        url: 'search/tag/config/list/by-tag-type',
        method: 'GET',
        params
    }
}

export const dataSyncInit = (data) => {
    return {
        url: '/search/es-theme-metadata/init-data',
        method: 'post',
        data
    }
}

export const getSyncStatus = (data) => {
    return {
        url: '/search/es-theme-metadata/init-data-status',
        method: 'get',
        data
    }
}
