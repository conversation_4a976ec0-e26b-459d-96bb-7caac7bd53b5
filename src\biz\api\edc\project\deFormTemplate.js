// 订阅表单回显的模板管理相关接口

export const getAllDeFormTemplate = (params) => {
  return {
    url: 'edc/deform/view/template/all',
    method: 'get',
    params,
  }
}

export const getDeFormTemplatePage = (params) => {
  return {
    url: 'edc/deform/view/template/page',
    method: 'get',
    params,
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/deform/view/template/insert',
    method: 'post',
    data,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/deform/view/template/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/deform/view/template/detail/' + id,
    method: 'get',
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/deform/view/template/update',
    method: 'post',
    data
  }
}