.rules-group-logical {
  position: absolute;
  font-size: 14px;
  color: #fff;
  font-weight: 600;
  perspective: 1000;
  transform-style: preserve-3d;
  text-align: center;
  z-index: 2;
  cursor: pointer;
  &,
  .front,
  .back {
    width: 42px;
    height: 24px;
    line-height: 20px;
    border-radius: 2px;
  }
  .flip {
    position: relative;
    transition: 0.6s;
    transform-style: preserve-3d;
  }

  .front,
  .back {
    position: absolute;
    top: 0px;
    left: 0px;
    padding-top: 4px;
    backface-visibility: hidden;
  }

  .front {
    background-color: #409eff;
    z-index: 3;
  }

  .back {
    background-color: #ffd54f;
    transform: rotateY(-180deg);
  }

  /* .rules-group-logical:hover .flip {
  transform: rotateY(180deg);
} */
}
