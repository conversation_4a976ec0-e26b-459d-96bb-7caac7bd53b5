﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_usercert";
//页面选中数据
var selectedCertID = "";

//获取页面传参信息
var urlParams = ca_common_tools.getParams();
var globalInfo = {
    ViewCertUserCode: urlParams.UserCode || ""
}

$(function() {
    initBTN();

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue();
        initCertGrid(selOrgID);
        initCTLoc(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue();
        initCertGrid(selOrgID);
        initCTLoc(selOrgID);
    }

    //查看指定用户证书时页面隐藏部分布局及部分按钮
    if (globalInfo.ViewCertUserCode != "") {
        $("#btnStop").hide();
        $("#btnUpdateLis").hide();

        $("#queryCriteriaDetail").hide();
        $('#queryCriteria').panel('resize', {height: 51});
        $("body").layout("resize");
        return;
    }
    initVender();
    document.onkeydown = documentOnKeyDown;
});

function initBTN() {
    $("#btnQuery").click(function(){queryCert();});
    $("#btnReset").click(function(){resetCondition();});
    $("#btnStop").click(function(){stopCert();});
    $("#btnViewCertInfo").click(function(){btnViewCertInfo();});
}

//初始化科室
function initCTLoc(selOrgID) {
    var data = {
        action: "GET_ALLCTLOC",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        $("#userDept").combobox({
            data:json.data,
            valueField:"ctLocID",
            textField:"ctLocDesc",
            width:160,
            panelWidth:200,
            panelHeight:350,
            filter: function(q, row){
                return ((row["ctLocDesc"].toUpperCase().indexOf(q.toUpperCase()) >= 0)||(row["ctLocAlias"].toUpperCase().indexOf(q.toUpperCase()) >= 0));
            }
        });
    } else {
        $.messager.alert("提示", "获取科室数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//初始化厂商
function initVender() {
    var data = {
        action: "GET_ALLVENDER",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("venderCode",json.data,false,true,"venderCode","venderDesc");
    } else {
        $.messager.alert("提示", "获取厂商数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//初始化证书列表
function initCertGrid(selOrgID) {
    var param = {
        action: "GET_CERTLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: true,
            ctLocID: "",
            userCode: globalInfo.ViewCertUserCode,
            userName: "",
            userCertCode: "",
            certNo: "",
            venderCode: ""
        }
    };

    $("#dgCert").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbCert",
        url: ca_common_tools.getAppPath("GET_CERTLIST"),
        queryParams: param,
        idField:"certID",
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:50,
        pageList:[20,50,100],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"certID",title:"ID"},
            {field:"userCode",title:"用户工号"},
            {field:"userName",title:"用户姓名"},
            {field:"venderCode",title:"厂商代码",align:"center",editor:"text"},
            {field:"signTypeCode",title:"签名类型代码",align:"center",editor:"text"},
            {field:"userCertCode",title:"CA用户唯一标识",width:400,align:"center",editor:"text"},
            {field:"certNo",title:"CA证书唯一标识",width:450,align:"center",editor:"text"},
            {field:"certName",title:"CA证书名",width:100,align:"center",editor:"text"},
            {field:"bindDateTime",title:"关联日期时间",width:200,align:"center",editor:"text"},
            {
                field:"haveImage",
                title:"有无签名图",
                align: "center",
                formatter: function(value,row,index)
                {
                    if (value == "1") {
                        return "<span style='color:green'>有签名图</span>";
                    } else {
                        return "<span style='color:red'>无签名图</span>";
                    }
                }
            }

        ]],
        onLoadError:function() {
            $.messager.alert("提示","证书数据加载失败");
        },
        onSelect:function(rowIndex,row){
            selectedCertID = row.certID;
        },
        onLoadSuccess:function(data){
            $("#dgCert").datagrid("clearSelections");
            selectedCertID = "";
        }
    });
}

//查询证书数据
function queryCert() {
    var userCode = $("#userCode").val();
    var userName = $("#userName").val();
    var userCertCode = $("#userCertCode").val();
    var certNo = $("#certNo").val();

    var venderCode = $("#venderCode").combobox("getValue");
    if (venderCode == "undefined") {
        venderCode = "";
    }
    var ctLocID = $("#userDept").combobox("getValue");
    if (ctLocID == "undefined") {
        ctLocID = "";
    }

    var queryParams = {
        action: "GET_CERTLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: false,
            ctLocID: ctLocID,
            userCode: userCode,
            userName: userName,
            userCertCode: userCertCode,
            certNo: certNo,
            venderCode: venderCode
        }
    };

    $("#dgCert").datagrid("load",queryParams);
}

//重置查询条件
function resetCondition() {
    $("#userCode").val("");
    $("#userName").val("");
    $("#userCertCode").val("");
    $("#certNo").val("");
    $("#userDept").combobox("setValue","");
    $("#venderCode").combobox("setValue","");
}

//停用证书关联
function stopCert() {
    selectedCertID = selectedCertID || "";
    if (selectedCertID == "") {
        $.messager.alert("提示","请选中要停用的证书");
        return;
    }

    var data = {
        action: "SET_CERTUNBIND",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            certID    : selectedCertID
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "证书停用成功",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
                queryCert();
            } else {
                $.messager.alert("提示", "证书停用失败！", "error")
            }
        } else {
            $.messager.alert("提示", "停用证书关联失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

//查看证书详细信息
function btnViewCertInfo() {
    selectedCertID = selectedCertID || "";
    if (selectedCertID == "") {
        $.messager.alert("提示","请选中要查看详细信息的证书数据");
        return;
    }

    var content = '<iframe id="ViewCertInfoFrame" scrolling="auto" frameborder="0" src="dhc.certauth.cfg.certreg.bindcert.html?ViewCertID='+ selectedCertID +'&MWToken='+ ca_common_tools.getMWToken() +'&OrgID='+ selOrgID +'" style="width:100%; height:99%;"></iframe>';
    createModalDialog("ViewCertInfo","查看证书详细信息",860,580,'ViewCertInfoFrame',content,'','');
}

function documentOnKeyDown(e) {
    if (window.event) {
        var keyCode=window.event.keyCode;
    } else {
        var keyCode=e.which;
    }

    if (keyCode==13) {
        queryCert();
    }
}