﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数维护</title>
    <!--引用HISUI-->
    <link rel="stylesheet" type="text/css" href="../../scripts_lib//hisui-0.1.0/dist/css/hisui.lite.min.css">
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.min.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.hisui.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/locale/hisui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../scripts/websys.jquery.bsp.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/websys.css">

    <!--公共js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/common.data.js"></script>
    <script type="text/javascript" src="../scripts/dhccertauth/lib/json2.js"></script>

    <!--业务js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/cfg.servmgr.js"></script>
    <link rel="stylesheet" type="text/css" href="../scripts/dhccertauth/css/servmgr.css">
</head>
<body class="hisui-layout">
    <div data-options="region:'center',title:'',border:false,collapsible:false,split:true,headerCls:'panel-header-gray'" style="height:auto;" >
        <div class="hisui-panel" style="width:auto;height:auto" data-options="title:'系统配置',headerCls:'panel-header-gray',iconCls:'icon-paper'">
            <table cellspacing="5" cellpadding="0">
                <tr>
                    <td id="_OrgListLabel" style="color:red;margin:0 10">选择组织机构：</td>
                    <td><input id="_OrgList"></td>

                    <td><a class="hisui-linkbutton" data-options="iconCls:'icon-save',plain:true" id="btnSaveCommon" style="margin-left:20px;width:90px">保存</a></td>
                </tr>
            </table>
            <div id="sysOption">
                <div class="hisui-panel " style="width:auto;height:auto;padding-left:20px;" data-options="title:'医护签名相关配置',headerCls:'panel-header-gray'">
                    <table cellpadding="5" cellspacing="10">
                        <tr>
                            <td class="optname-td">是否支持免密签名</td>
                            <td><select id="careLoginOnce" class="hisui-combobox sysOption whether" customAttr="是否支持免密签名"></select></td>
                            <td></td>
                            <td class="optname-td">默认认证方式</td>
                            <td><select id="defaultCareAuthType" class="hisui-combobox sysOption authType" customAttr="默认认证方式"></select></td>
                            <td></td>
                            <td class="optname-td">可用认证方式列表</td>
                            <td><select id="authTypeList" class="hisui-combobox sysOption authType" customAttr="可用认证方式列表"></select></td>
                        </tr>
                        <tr>
                            <td class="optname-td">UKey签名厂商</td>
                            <td><select id="defaultUKEYVenderCode" class="hisui-combobox sysOption vender" customAttr="默认UKEY签名厂商代码"></select></td>
                            <td></td>
                            <td class="optname-td">手机签名厂商</td>
                            <td><select id="defaultPHONEVenderCode" class="hisui-combobox sysOption vender" customAttr="默认手机扫码厂商代码"></select></td>
                            <td></td>
                            <td class="optname-td">人脸签名厂商</td>
                            <td><select id="defaultFACEVenderCode" class="hisui-combobox sysOption vender" customAttr="默认人脸识别厂商代码"></select></td>
                        </tr>
                        <tr>
                            <td class="optname-td">是否限制图片不能为空</td>
                            <td><select id="isLimitImageNull" class="hisui-combobox sysOption whether" customAttr="关联证书时是否允许图片为空"></select></td>
                            <td></td>
                            <td class="optname-td">是否限制图片大小</td>
                            <td><select id="isLimitImageSize" class="hisui-combobox sysOption whether" customAttr="关联证书时是否限制图片大小"></select></td>
                            <td></td>
                            <td class="optname-td">关联证书是否转换签名图</td>
                            <td><select id="isConvertCareImage" class="hisui-combobox sysOption whether" customAttr="关联证书时是否转换签名图格式"></select></td>
                        </tr>
                        <tr>
                            <td class="optname-td"><a id="autoReloadImage" href="#" title="可能导致程序异常,不建议开启" class="hisui-tooltip">是否自动更新签名图</a></td>
                            <td><select id="isAutoReloadImage" class="hisui-combobox sysOption whether" customAttr="是否自动更新签名图"></select></td>
                            <td></td>
                            <td class="optname-td">登录页显示CA登录按钮</td>
                            <td><select id="isShowCALogonLinks" class="hisui-combobox sysOption whether" customAttr="登录页面是否显示CA登录按钮"></select></td>
                            <td></td>
                            <td class="optname-td"><a id="defaultLoadList" href="#" title="关闭参数后，第一次打开签名证书管理、签名证书关联等页面时默认不查询列表" class="hisui-tooltip">是否默认加载列表数据</a></td>
                            <td><select id="isDefaultLoadList" class="hisui-combobox sysOption whether" customAttr="是否默认加载列表数据"></select></td>
                        </tr>
                    </table>
                </div>
                <div class="hisui-panel" style="width:auto;height:auto;padding-left:20px;" data-options="title:'患者签名相关配置',headerCls:'panel-header-gray'">
                    <table cellpadding="5" cellspacing="10">
                        <tr>
                            <td class="optname-td">默认患者签名厂商</td>
                            <td><select id="defaultPATVenderCode" class="hisui-combobox sysOption vender" customAttr="默认患者签名厂商代码"></select></td>
                            <td></td>
                            <td class="optname-td">是否支持PDF文件签署</td>
                            <td><select id="isPatSupportPDFSign" class="hisui-combobox sysOption whether" customAttr="患者签名是否支持PDF签署"></select></td>
                            <td></td>
                            <td class="optname-td">签署需要展示二维码</td>
                            <td><select id="isPatNeedQrCode" class="hisui-combobox sysOption whether" customAttr="患者签署是否需要展示二维码"></select></td>
                        </tr>
                    </table>
                </div>
                <div class="hisui-panel" style="width:auto;height:auto;padding-left:20px;" data-options="title:'医院签章相关配置',headerCls:'panel-header-gray'">
                    <table cellpadding="5" cellspacing="10">
                        <tr>
                            <td class="optname-td">默认医院签章厂商</td>
                            <td><select id="defaultESVenderCode" class="hisui-combobox sysOption vender" customAttr="默认医院签章厂商代码"></select></td>
                            <td></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div id = "sysStatus">
                <div class="hisui-panel" style="width:auto;height:auto;padding-left:20px;" data-options="title:'医护签名相关配置',headerCls:'panel-header-gray'">
                    <table cellpadding="5" cellspacing="10">
                        <tr>
                            <td class="optname-td">是否开启医护签名</td>
                            <td><select id="isCareCAOn" class="hisui-combobox sysStatus whether" customAttr="是否开启医护CA签名"></select></td>
                            <td></td>
                            <td class="optname-td">全院开启医护签名</td>
                            <td><select id="allCareCAOn" class="hisui-combobox sysStatus whether" customAttr="是否全院开启医护CA签名"></select></td>
                            <td></td>
                            <td class="optname-td">不开启CA的人员类型</td>
                            <td><select id="disabledCarPrvTp" class="hisui-combobox sysStatus" customAttr="不开启CA的人员类型"></select></td>
                        </tr>
                        <tr>
                            <td class="optname-td"><a id="careCAOnByRole" href="#" title="开启后如需开启CA，需要同时开启角色和科室" class="hisui-tooltip">是否开启角色管理</a></td>
                            <td><select id="isCareCAOnByRole" class="hisui-combobox sysStatus whether" customAttr="是否按照角色开启医护CA签名"></select></td>
                            <td></td>
                            <td class="optname-td"><a id="disabledNoCert" href="#" title="开启后当用户未关联证书时，不启用CA" class="hisui-tooltip">关联证书的用户才启用CA</a></td>
                            <td><select id="isDisabledNoCert" class="hisui-combobox sysStatus whether" customAttr="未关联证书的用户不启用CA"></select></td>
                            <td></td>
                        </tr>
                    </table>
                </div>
                <div class="hisui-panel" style="width:auto;height:auto;padding-left:20px;" data-options="title:'患者签名相关配置',headerCls:'panel-header-gray'">
                    <table cellpadding="5" cellspacing="10">
                        <tr>
                            <td class="optname-td">是否开启患者签名</td>
                            <td><select id="isPatCAOn" class="hisui-combobox sysStatus whether" customAttr="是否开启患者签名"></select></td>
                            <td></td>
                            <td class="optname-td">全院开启患者签名</td>
                            <td><select id="allPatCAOn" class="hisui-combobox sysStatus whether" customAttr="是否全院开启患者签名"></select></td>
                        </tr>
                    </table>
                </div>
                <div class="hisui-panel" style="width:auto;height:auto;padding-left:20px;" data-options="title:'医院签章相关配置',headerCls:'panel-header-gray'">
                    <table cellpadding="5" cellspacing="10">
                        <tr>
                            <td class="optname-td">是否开启医院签章</td>
                            <td><select id="isESCAOn" class="hisui-combobox sysStatus whether" customAttr="是否开启医院签章"></select></td>
                            <td></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>