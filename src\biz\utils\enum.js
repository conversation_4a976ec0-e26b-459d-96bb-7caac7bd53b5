import i18n from '@base/i18n'
export const visitStatusMap = {
  1: {
    'path': require('@/assets/icons/svg/more.svg'),
    'label': i18n.t('未录入'),
    'otherField': ['unInputCount'],
    'tagType': 'info',
    'bgColor': '#91939A'
  },
  2: {
    'path': require('@/assets/icons/svg/pencil16.svg'),
    'label': i18n.t('录入中'),
    'otherField': ['inputtingCount'],
    'tagType': '',
    'bgColor': '#409EFF'
  },
  3: {
    'path': require('@/assets/icons/svg/share.svg'),
    'label': i18n.t('待审核'),
    'otherField': ['unAuditCount'],
    'tagType': '',
    'bgColor': '#409EFF'
  },
  4: {
    'path': require('@/assets/icons/svg/auditing.svg'),
    'label': i18n.t('审核中'),
    'otherField': ['auditingCount'],
    'tagType': 'warning',
    'bgColor': '#e6a23c'
  },
  5: {
    'path': require('@/assets/icons/svg/more.svg'),
    'label': i18n.t('未提交'),
    'otherField': ['unCommitCount'],
    'tagType': 'danger',
    'bgColor': '#f56c6c'
  },
  6: {
    'path': require('@/assets/icons/svg/checked.svg'),
    'label': i18n.t('通过'),
    'otherField': ['finishCount'],
    'tagType': 'success',
    'bgColor': '#67c23a'
  }
}

export const disauditableVisitStatusMap = {
  1: {
    'path': require('@/assets/icons/svg/more.svg'),
    'label': i18n.t('未录入'),
    'otherField': ['unInputCount'],
    'tagType': 'info',
    'bgColor': '#91939A'
  },
  2: {
    'path': require('@/assets/icons/svg/pencil16.svg'),
    'label': i18n.t('录入中'),
    'otherField': ['inputtingCount'],
    'tagType': '',
    'bgColor': '#409EFF'
  },
  3: {
    'path': require('@/assets/icons/svg/checked.svg'),
    'label': i18n.t('完成'),
    'otherField': ['finishCount'],
    'tagType': 'success',
    'bgColor': '#67c23a'
  },
  6: {
    'path': require('@/assets/icons/svg/checked.svg'),
    'label': i18n.t('完成'),
    'otherField': ['finishCount'],
    'tagType': 'success',
    'bgColor': '#67c23a'
  }
}

export const visitWindowStatusMap = {
  0: {
    'color': '#f4f4f5',
    'deepColor': '#909399',
    'className': 'label-info',
    'label': i18n.t('窗口外随访')
  },
  1: {
    'color': '#f0f9eb',
    'deepColor': '#67c23a',
    'className': 'label-success',
    'label': i18n.t('已完成随访')
  },
  2: {
    'color': '#ecf5ff',
    'deepColor': '#409eff',
    'className': 'label-primary',
    'label': i18n.t('窗口期随访')
  },
  3: {
    'color': '#fef0f0',
    'deepColor': '#F56C6C',
    'className': 'label-danger',
    'label': i18n.t('超期未随访')
  },
  4: {
    'color': '#f4f4f5',
    'deepColor': '#909399',
    'className': 'label-info',
    'label': i18n.t('窗口外随访')
  }
}

export const periodVisitStatusMap = {
  1: {
    'color': '#ecf5ff',
    'deepColor': '#409eff',
    'className': 'label-primary',
    'label': i18n.t('进行中/未完成'),
    'circleColor': '#20A0FF',
    'circlePercent': 40,
    'circleText': i18n.t('进行中')
  },
  2: {
    'color': '#f0f9eb',
    'deepColor': '#67c23a',
    'className': 'label-success',
    'label': i18n.t('已完成随访'),
    'circleColor': '#67C23A',
    'circlePercent': 100,
    'circleText': i18n.t('已完成')
  },
  3: {
    'color': '#fef0f0',
    'deepColor': '#F56C6C',
    'className': 'label-danger',
    'label': i18n.t('未开始'),
    'circleColor': '#999999',
    'circlePercent': 0,
    'circleText': i18n.t('未开始')
  },
  4: {
    'color': '#f4f4f5',
    'deepColor': '#909399',
    'className': 'label-info',
    'label': i18n.t('已停止'),
    'circleColor': '#F56C6C',
    'circlePercent': 0,
    'circleText': i18n.t('已停止')
  }
}
// 定义患者入组的时候基本信息字段，作为和自定义字段的一个区分
export const basicInfoFieldList = ['isHisCase', 'regno', 'name', 'gender', 'idCard', 'recordId', 'birthday', 'intoDate', 'filterNo', 'initials', 'sysUniqueId', 'projUniqueId', 'tel']
export const basicInfoFieldObj = { 'regno': 'input', 'name': 'input', 'initials': 'input', 'gender': 'input', 'idCard': 'input', 'recordId': 'input',
 'birthday': 'date', 'intoDate': 'date', 'tel': 'input', 'filterNo': 'input', 'isHisCase': 'input', 'sysUniqueId': 'input', 'projUniqueId': 'input' }

export function getMapItemByOtherField(fieldName) {
  for (const key in visitStatusMap) {
    if (visitStatusMap[key].otherField.includes(fieldName)) {
      return visitStatusMap[key]
    }
  }
}

export function getDisauditableMapItemByOtherField(fieldName) {
  for (const key in disauditableVisitStatusMap) {
    if (disauditableVisitStatusMap[key].otherField.includes(fieldName)) {
      return disauditableVisitStatusMap[key]
    }
  }
}

export function getMapByAuditFlag(flag) {
  let statusArr = [1,2,3,4,5,6]
  // if (flag) {
  //   // 过滤出审核的五种状态
  //   statusArr = [5, 3, 4, 6]
  // } else {
  //   // 过滤出录入的五种状态
  //   statusArr = [1, 2, 4, 6]
  // }
  const obj = {}
  statusArr.map(item => {
    obj[item] = visitStatusMap[item]
  })

  return obj
}

export function getDisauditableStatus() {
  return {
    1: {
      'path': require('@/assets/icons/svg/more.svg'),
      'label': i18n.t('录入中'),
      'otherField': ['unCommitCount']
    },
    3: {
      'path': require('@/assets/icons/svg/checked.svg'),
      'label': i18n.t('通过'),
      'otherField': ['finishCount']
    }
  }
}

export const admTypes = {
  'I': i18n.t('住院'),
  'O': i18n.t('门诊'),
  'E': i18n.t('急诊'),
  'H': i18n.t('体检')
}

// 详情弹窗补充字段翻译映射
export const detailFieldOtherMap = {
  'subjectId': i18n.t('子项目ID'),
  'createTime': i18n.t('创建时间'),
  'auditTime': i18n.t('审核时间'),
  'auditUserId': i18n.t('审核人ID'),
  'subjectName': i18n.t('子项目名称'),
  'subjectDesc': i18n.t('子项目描述'),
  'subjectEnableAudit': i18n.t('子项目是否启用审核'),
  'projectId': i18n.t('项目ID'),
  'projectName': i18n.t('项目名称'),
  'projectAdminId': i18n.t('项目管理员ID')
}

export default { visitStatusMap, visitWindowStatusMap, periodVisitStatusMap, basicInfoFieldList, basicInfoFieldObj, disauditableVisitStatusMap, getMapItemByOtherField, getMapByAuditFlag, getDisauditableStatus, detailFieldOtherMap }