<template>
  <div class="scrollMenu">
    <div v-if="cateItems.length > 0" style="display:flex;">
      <div class="sibarmenu">
        <hos-menu class="hos-menu-vertical-demo" :default-active="activeIndex" @select="selectMenu">
          <template v-for="(item, index) in cateItems" >
            <hos-menu-item :key="index" :index="id + type + item.cateId">
              <span slot="title" :title="item.cateName">{{ item.cateName }}</span>
            </hos-menu-item>
          </template>
        </hos-menu>
      </div>
      <div ref="itemBox" class="search-item-box" style="width:100%;" @scroll="scrollEvent">
        <div v-for="(item, index) in cateItems" :key="index" class="search-parent-cate">
          <!-- 添加锚点 -->
          <span :id="id + type + item.cateId" class="acontent" />
          <!-- <p :id="type + item.cateId + '_' + random" class="search-parent-title acontent">{{ item.cateName }}</p> -->
          <div v-for="(sub, subIndex) in item.child" :key="subIndex" class="search-sub-cate">
            <checkbox-all v-if="!singleSelect" v-slot="scope" :selected="selectItems" :all="sub.item">
              <p class="search-sub-title">
                <hos-checkbox
                  :value="scope.checked"
                  :indeterminate="scope.indeterminate"
                  @change="(val) => checkAll(val, selectItems, sub.item, sub)"
                  >{{ sub.childName }}</hos-checkbox
                >
              </p>
            </checkbox-all>
            <p v-else class="search-sub-title">{{ sub.childName }}</p>
            <div class="search-item-content">
              <hos-tag
                v-for="(sItem, sIndex) in sub.item" :key="sIndex"
                type="info"
                :class="{ 'is-selected': selectItems.findIndex(value => isItemEq(value,sItem)) > -1 }"
                @click="clickItem(sItem, sub)"
                v-html="getHeightLightItem(sItem.fieldName)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <empty-box v-else />
  </div>
</template>

<script>
import CheckboxAll from './CheckboxAll'
import { getUUID } from "@/utils/index.js"
export default {
  name: 'ScrollMenu',
  components: { CheckboxAll },
  props: {
    heightLight: {
      // 需要高亮的字段
      type: String,
      default() {
        return ''
      }
    },
    // 类型
    type: {
      type: String,
      default() {
        return ''
      }
    },
    // 是否单选，默认是
    singleSelect: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 查询项大分类
    cateItems: {
      type: Array,
      default() {
        return []
      }
    },
    // 全部关联字段信息
    fieldData: {
      type: Array,
      default() {
        return []
      }
    },
    // 公共的指标项
    commonSearchItems: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      cacheRestore:[],
      id: getUUID(),
      activeIndex: null,
      selectId: '',
      keyword: '',
      selectItems: [],
      random: Math.floor(Math.random() * 10000) // 防止id重复，拼接一个随机数字
    }
  },
  watch: {
    selectId() {
      this.activeIndex = this.selectId
    },
    cateItems(val,old){
      if(JSON.stringify(val) != JSON.stringify(old)){
        this.restoreSelectItems(this.cacheRestore)
      }
    }
  },
  methods: {
    getHeightLightItem(str = '') {
      if (this.heightLight) {
        return str.replaceAll(this.heightLight, `<span style="color:red;">${this.heightLight}</span>`)
      } else {
        return str
      }
    },
    isItemEq(a,b){
      return a.fieldType == b.fieldType &&
             a.id == b.id &&
             a.cateName == b.cateName &&
             a.cateId == b.cateId &&
             a.fieldName == b.fieldName &&
             a.fieldKey == b.fieldKey
    },
    // 查询项点击事件
    clickItem(item, sub) {
      console.log(item, sub, "查询项点击事件");
      if (this.singleSelect) {
        this.selectItems = []
      }
      const arr = this.selectItems.filter((i) => this.isItemEq(i,item))
      if (arr && arr.length > 0) {
        if(item.isRequired){
          this.$message.warning('必填字段不允许取消')
          return
        }
        // 从选中的列表中移除
        this.selectItems = this.selectItems.filter((i) => !this.isItemEq(i,item))
      } else {
        // this.selectItems.push(this.checkItem(item, sub))
        this.selectItems.push(item)
      }

      if (this.singleSelect) {
        // this.closeSearchDialog()
        this.$emit('closeSearchDialog')
      }
    },
    // 获取全部选中的查询项
    getSelectItems() {
      const activeItem = this.selectItems ? this.selectItems : []

      if (this.singleSelect) {
        return activeItem.length > 0 ? activeItem[0] : null
      } else {
        return activeItem
      }
    },
    // 回显全部选中的查询项
    restoreSelectItems(all){
      this.cacheRestore = all
      if(!all || all.length == 0){
        this.selectItems = []
        return
      }
      const res = []
      this.cateItems.forEach(item=>{
        item.child.forEach(sub=>{
          sub.item.forEach(sItem=>{
            if(all.findIndex(value=> this.isItemEq(sItem,value))>-1){
              res.push(sItem)
            }
          })
        })
      })
      this.selectItems = res
    },
    selectMenu(key) {
      this.toParentid(key)
    },
    // 跳转到锚点
    toParentid(key) {
      var el = document.getElementById(key)
      el.scrollIntoView()
    },
    scrollEvent() {
      const navContents = document.querySelectorAll('.acontent')
      // 所有锚点元素的 offsetTop
      const offsetTopArr = []
      navContents.forEach((item) => {
        const temp = {
          offsetTop: item.offsetTop,
          id: item.id
        }
        offsetTopArr.push(temp)
      })
      // 获取当前文档流的 scrollTop
      const offsetTop = this.$refs.itemBox.offsetTop
      const scrollTop = this.$refs.itemBox.scrollTop + offsetTop + 25

      let navIndex = ''
      for (let n = 0; n < offsetTopArr.length; n++) {
        // 如果 scrollTop 大于等于第 n 个元素的 offsetTop 则说明 n-1 的内容已经完全不可见
        // 那么此时导航索引就应该是 n 了
        if (scrollTop >= offsetTopArr[n].offsetTop) {
          navIndex = offsetTopArr[n].id
        }
      }
      // 把下标赋值给 vue 的 data
      this.selectId = navIndex
    },
    // checkItem(item, sub) {
    //   // 获取关联的字段信息
    //   const fieldInfoArr = this.fieldData.filter((info) => {
    //     return info.id === item.searchFieldId
    //   })

    //   if (fieldInfoArr && fieldInfoArr.length > 0) {
    //     item['fieldInfo'] = fieldInfoArr[0]
    //   } else if (!item.fieldInfo) {
    //     item['fieldInfo'] = {}
    //   }
    //   // 获取字段的父字段
    //   if (sub) {
    //     item['pFieldInfo'] = {
    //       id: sub.childId,
    //       name: sub.childName
    //     }
    //   } else {
    //     item['pFieldInfo'] = {}
    //   }
    //   // 特殊处理检验、检查等
    //   if (item.searchFieldOtherId && item.searchFieldOtherId > 0) {
    //     // item.fieldInfo.name = item.searchItemName + "_" + item.fieldInfo.name
    //     // todo 检验检查生成的时候在查询项中已经带上名称
    //     item.fieldInfo.name = item.searchItemName
    //     // 获取其他字段信息
    //     const otherFieldInfoArr = this.fieldData.filter((info) => {
    //       return info.id === item.searchFieldOtherId
    //     })

    //     item.otherFieldInfo = otherFieldInfoArr && otherFieldInfoArr.length > 0 ? otherFieldInfoArr[0] : null
    //   } else {
    //     // 展示查询项名称
    //     item.fieldInfo.name = item.searchItemName
    //   }
    //   return item
    // },
    checkAll(value, selected, all, sub) {
      if (value) {
        // 全选
        all.forEach((a_item) => {
          if (selected.indexOf(a_item) < 0) {
            // selected.push(this.checkItem(a_item, sub))
            selected.push(a_item)
          }
        })
      } else {
        // 取消全选
        let hasRequiredField = false
        all.forEach((a_item) => {
          if (selected.indexOf(a_item) >= 0) {
            if(a_item.isRequired){
              hasRequiredField = true
              return
            }
            selected.splice(selected.indexOf(a_item), 1)
          }
        })
        if (hasRequiredField) {
          this.$message.warning('必填字段不允许取消')
        }
      }
    },
    resetSearchItems() {
      this.selectItems = []
    }
  }
}
</script>
<style lang="scss" scoped>
  .hos-tag.hos-tag--info {
    cursor: pointer;
    margin-right: 5px;
    margin-bottom: 3px;
    background-color: #fefeff;
    color: #303133;
    border-color: #c0c4cc;
  }
  .hos-tag.hos-tag--info:hover {
    background-color: #409eff;
    color: white;
    border-color: #409eff;
  }
  .hos-tag.hos-tag--info.is-selected {
    background-color: #409eff;
    color: white;
    border-color: #409eff;
  }
</style>