// 患者浏览获取患者的随访时间轴
export const getPatientVisitList = (params) => {
  return {
    url: `edc/visit/select-by-patient`,
    method: 'get',
    params,
  }
}

// 患者浏览获取全程项目患者随访时间轴(projectTyp3)
export const getQcPatientVisitList = (params) => {
  return {
    url: `edc/visit/disease/select-by-patient`,
    method: 'get',
    params,
  }
}

// 终止随访阶段
export const endVisitPeriod = (data) => {
  return {
    url: `edc/visit/end-visit-period`,
    method: 'post',
    data,
  }
}

// 完成随访
export const finishVisit = (params) => {
  return {
    url: `edc/visit/finish`,
    method: 'post',
    params,
  }
}

// 删除随访
export const deleteVisit = (data) => {
  return {
    url: 'edc/visit/deletion',
    method: 'post',
    data,
  }
}

// 新增随访
export const addVisit = (data) => {
  return {
    url: 'edc/visit/insert',
    method: 'post',
    data,
  }
}

// 修改随访
export const updateVisit = (data) => {
  return {
    url: 'edc/visit/update',
    method: 'post',
    data,
  }
}

// 修改随访名称
export const updateVisitName = (params) => {
  return {
    url: 'edc/visit/update-visit-name',
    method: 'post',
    params,
  }
}

// 获取未绑定到随访的组合套
export const getVisitFormGroup = (params) => {
  return {
    url: 'edc/visit/form-group/un-bind',
    method: 'get',
    params,
  }
}

// 绑定组合套的表单到随访
export const bindVisitForm = ({params,data}) => {
  return {
    url: 'edc/visit-form/update/bind-form',
    method: 'post',
    params,
    data,
  }
}

// 获取指定患者指定随访的CRF表单
export const getVisitForm = (params) => {
  return {
    url: 'edc/visit-form/select-crf-form',
    method: 'get',
    params,
  }
}

// 获取患者随访表单填写数据
export const getPatientFormFillData = (params) => {
  return {
    url: 'edc/form-maker/form-fill/get-data',
    method: 'get',
    params,
  }
}

// 保存患者随访表单填写数据
export const savePatientFormFillData = (data) => {
  return {
    url: 'edc/form-maker/form-fill/save-tmp',
    method: 'post',
    data,
  }
}

// 保存并提交患者随访表单填写数据
export const saveAndSubmitPatientFormFillData = (data) => {
  return {
    url: 'edc/form-maker/form-fill/submit',
    method: 'post',
    data,
  }
}

// 获取患者随访指定表单中的字段的全部数据
export const getPatientFormFillDataAllByField = ({subjectId,patientId,fieldKeys}) => {
  return {
    url: `edc/form-maker/form-fill/data/all/${subjectId}/${patientId}`,
    method: 'post',
    data:fieldKeys,
  }
}

// 新增表单数据修改记录
export const addFormFillChange = (data) => {
  return {
    url: 'edc/form-fill-data-mark/insert',
    method: 'post',
    data,
  }
}

// 获取表单数据修改记录
export const getFormFillChangeHistory = (params) => {
  return {
    url: params.fieldKey ? `edc/form-fill-data-mark/select-fill-id/${params.subjectId}/${params.formFillId}/${params.fieldKey}` :
       `edc/form-fill-data-mark/select-visit-form/${params.subjectId}/${params.visitId}/${params.formId}`,
    method: 'get',
    params,
  }
}

// 设置随访参考点时间
export const setPeriodReferDate = (data) => {
  return {
    url: 'edc/patient/set-refer',
    method: 'post',
    data,
  }
}

// 修改计划访视时间
export const setPlanVisitDate = (params) => {
  return {
    url: 'edc/visit/update/plan',
    method: 'post',
    params,
  }
}

// 获取可关联的就诊数据
export const getAdmInfo = (params) => {
  return {
    url: 'edc/external/data/get-adm-info',
    method: 'get',
    params,
  }
}

// 保存关联就诊
export const admRelation = ({data,visitId}) => {
  return {
    url: 'edc/visit-adm-relation/bind',
    method: 'post',
    data,
    params:{
      visitId
    }
  }
}

// 同步患者所有已关联的就诊的订阅表单数据
export const refreshDeForm = (data) => {
  return {
    url: 'edc/external/data/sync-patient-deform-data',
    method: 'post',
    data,
  }
}

// 获取患者随访订阅表单的填写数据
export const getPatientDeformFillData = (params) => {
  return {
    url: 'edc/deform-fill-data/info',
    method: 'get',
    params,
  }
}

// 通知获取表单的外部接口填充数据
export const noticeExternalData = (params) => {
  return {
    url: 'edc/external/data/crf/notice',
    method: 'post',
    data:params,
  }
}

// 通过任务uid查询外部接口数据状态
export const getExternalDataStatusByUid = (uid) => {
  return {
    url: `edc/external/data/crf/status/${uid}`,
    method: 'get',
  }
}

// 通过任务uid查询外部接口数据内容
export const getExternalDataValueByUid = (uid) => {
  return {
    url: `edc/external/data/crf/value/${uid}`,
    method: 'get',
  }
}

// 以下是【审核模式】相关接口=============================================

// 保存并提交患者随访表单填写数据(有审核模式)
export const saveAndSubmitPatientFormFillDataAuditMode = (data) => {
  return {
    url: 'edc/form-maker/form-fill/audit/submit',
    method: 'post',
    data,
  }
}

// 保存患者随访表单填写数据(有审核模式)
export const savePatientFormFillDataAuditMode = (data) => {
  return {
    url: 'edc/form-maker/form-fill/audit/save-tmp',
    method: 'post',
    data,
  }
}

// 获取有审核记录的字段
export const getAuditJson = (params) => {
  return {
    url: `edc/form-field-doubt/getkeys`,
    method: 'get',
    params
  }
}

// 获取指定字段的审核记录
export const getAuditFieldRecord = (params) => {
  return {
    url: 'edc/form-field-doubt/querydoubt',
    method: 'get',
    params
  }
}

// 新增字段的审核记录
export const addAuditFieldRecord = (data) => {
  return {
    url: 'edc/form-field-doubt/insert',
    method: 'post',
    data
  }
}

// 锁定字段(字段审核通过)
export const lockField = (data) => {
  return {
    url: 'edc/form-field/audit/lock',
    method: 'post',
    data
  }
}

// 解锁字段
export const unlockField = (params) => {
  return {
    url: 'edc/form-field/audit/unlock',
    method: 'post',
    params
  }
}

// 一键锁定(一键通过)
export const lockAll = (data) => {
  return {
    url: `edc/form-field/audit/batch/lock/form`,
    method: 'post',
    data
  }
}

// 一键解锁
export const unlockAll = (data) => {
  return {
    url: `edc/form-field/audit/batch/unlock/form`,
    method: 'post',
    data
  }
}

// 质疑记录
export const getDoubtList = (params) => {
  return {
    url: `edc/form-field-doubt/list`,
    method: 'get',
    params
  }
}

// 质疑记录消息已读
export const readDoubtMsg = (data) => {
  return {
    url: `edc/form-field-doubt/update`,
    method: 'post',
    data
  }
}