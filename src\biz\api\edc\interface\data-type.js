// 分页查询
export const pageListApi = (params) => {
    return {
        url: `edc/external-data-type/page`,
        method: 'GET',
        params
    }
}

// 根据id查询详情
export const queryDetailApi = (id) => {
    return {
        url: `edc/external-data-type/detail/${id}`,
        method: 'GET',
    }
}

// 新增接口
export const addApi = (data) => {
    return {
        url: 'edc/external-data-type/insert',
        method: 'POST',
        data
    }
}

// 修改接口
export const editApi = (data) => {
    return {
        url: 'edc/external-data-type/update',
        method: 'post',
        data
    }
}

// 批量删除
export const deleteApi = (data) => {
    return {
        url: `edc/external-data-type/deletion`,
        method: 'POST',
        data
    }
}