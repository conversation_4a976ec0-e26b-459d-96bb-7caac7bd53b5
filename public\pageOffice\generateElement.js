const mainThead =
  '<tr><th>名称</th><th>编码</th><th style="width:70px">数据类型</th><th class="coordinate" style="width:140px">坐标</th><th style="width:70px">操作</th></tr>'
const subThead =
  '<tr><th>名称</th><th>编码</th><th style="width:70px">数据类型</th><th style="width:80px">是否分组</th><th class="coordinate" style="width:136px">坐标</th><th style="width:70px">操作</th></tr>'

function generateTables(obj) {
  let finalHtmlStr = ''

  // 生成obj.mainData主表对应的table节点
  if (obj.mainData && obj.mainData.length) {
    finalHtmlStr += generateMainTable(obj.mainData)
  }

  // 生成从表listData对应的table节点
  if (obj.listData && Array.isArray(obj.listData)) {
    finalHtmlStr += generateSubTable(obj.listData)
  }

  return finalHtmlStr
}

// 生成主表
function generateMainTable(mainData) {
  // const rightContent =
  //   '<button data-type="main" onclick="openCreateTableField(this)">新增</button><button class="button-ml edit-table" data-type="main">编辑</button><button class="button-ml" onclick="deleteMainTable(this)" data-type="main">删除</button>' // 固定右侧内容
  const rightContent =
    '<a href="#" data-type="main" onclick="openCreateTableField(this); return false;"><img src="../static/hisui/css/icons/mtpaper_add.png" alt=""></a><a class="button-ml edit-table" data-type="main" href="#"><img src="../static/hisui/css/icons/big/book_edit.png" alt=""></a><a href="#" class="button-ml" onclick="deleteMainTable(this); return false;"><img src="../static/hisui/css/icons/big/delete_table.png" alt=""></a>'

  let finalStr = ''

  for (var i = 0; i < mainData.length; i++) {
    const item = mainData[i].dataAttribute
    const dataCode = mainData[i].dataCode ? ' - ' + mainData[i].dataCode : ''
    const leftText = mainData[i].dataName ? '表单数据 : ' + mainData[i].dataName + dataCode : '表单数据'
    let str =
      '<div class="mainTable"><div class="table-header row">' +
      '<div class="table-header__left">' +
      leftText +
      '</div>' +
      '<div class="table-header__right">' +
      rightContent +
      '</div>' +
      '</div>'

    str += '<table>'
    str += '<thead>' + mainThead + '</thead>'
    str += '<tbody>' + formatAttributeConfigDom(item, 'main', i) + '</tbody>'
    str += '</table></div>'

    finalStr += str
  }

  return finalStr
}

// 生成从表
function generateSubTable(listData) {
  // const rightContent =
  //   '<button onclick="openCreateTableField(this)" data-type="sub">新增</button><button class="button-ml edit-table" data-type="sub">编辑</button><button class="subTableDelete button-ml">删除</button>' // 固定右侧内容
  const rightContent =
    '<a href="#" data-type="sub" onclick="openCreateTableField(this); return false;"><img src="../static/hisui/css/icons/mtpaper_add.png" alt=""></a><a class="button-ml edit-table" data-type="sub" href="#"><img src="../static/hisui/css/icons/big/book_edit.png" alt=""></a><a href="#" class="subTableDelete button-ml"><img src="../static/hisui/css/icons/big/delete_table.png" alt=""></a>'
  let subStr = ''

  for (var i = 0; i < listData.length; i++) {
    const item = listData[i].dataAttribute
    const dataCode = listData[i].dataCode ? ' - ' + listData[i].dataCode : ''
    const leftText = listData[i].dataName ? '列表数据 : ' + listData[i].dataName + dataCode : '列表数据'
    const detailStartRow = listData[i].detailStartRow ? listData[i].detailStartRow : ''
    const detailEndRow = listData[i].detailEndRow ? listData[i].detailEndRow : ''
    let str =
      '<div class="subTable"  data-index=' +
      i +
      '><div class="table-header row">' +
      '<div class="table-header__left">' +
      leftText +
      '</div>' +
      '<div class="table-header__right">' +
      rightContent +
      '</div>' +
      '</div>'

    str +=
      '<div class="subTable-header row">' +
      '<div class="row"><span class="red">* </span>明细开始行：<input type="text" class="textbox restrictedInput" value="' +
      detailStartRow +
      '"><button class="button-ml" data-index=' +
      i +
      ' onclick="getDetailRowValue(this)">取值</button></div>' +
      '<div class="row">明细结束行：<input type="text" class="textbox restrictedInput" value="' +
      detailEndRow +
      '"><button class="button-ml" data-index=' +
      i +
      ' onclick="getDetailRowValue(this)">取值</button></div>' +
      '</div>'
    str += '<table data-index=' + i + ' >'
    str += '<thead>' + subThead + '</thead>'
    if (item && item.length) {
      str += '<tbody>' + formatAttributeConfigDom(item, 'sub', i) + '</tbody>'
    } else {
      str += '<tbody></tbody>'
    }
    str += '</table></div>'
    subStr += str
  }

  return subStr
}

// 生成属性配置dom字符串
function formatAttributeConfigDom(data, type, tableIndex) {
  let tableHTML = ''

  try {
    for (var i = 0; i < data.length; i++) {
      tableHTML += generateAttributeTrDom(data[i], type, tableIndex, i)
    }
  } catch (error) {
    alert('生字符串error:' + JSON.stringify(error))
  }

  return tableHTML
}

// 生成单条属性配置dom字符串
function generateAttributeTrDom(item, type, tableIndex, trIndex) {
  let isGroupStr = ''
  let typeText = ''
  let tableHTML = ''
  const position = item.position || ''
  const fieldName = item.fieldName ? item.fieldName : '-'
  const fieldCode = item.fieldCode ? item.fieldCode : '-'
  let positionHtml =
    '<td><input style="width:50px" class="textbox restrictedInput" type="text" value="' +
    position +
    '" /><button onclick="getCoordinateValue(this, \'' +
    type +
    '\')" class="button-ml">取值</button></td>'

  if (item.fieldType == '1' || item.fieldType == 'data') {
    // 数据
    isGroupStr +=
      '<td><div class="row"><div style="margin-right:6px;">' +
      '<input type="radio" class="radioYes" name="' +
      type +
      '-' +
      tableIndex +
      '-' +
      trIndex +
      '-isGroup" value="true"' +
      (item.isGroup ? ' checked' : '') + // 使用三元运算符设置选中属性
      '>' +
      '<label for="' +
      tableIndex +
      '-' +
      trIndex +
      '-radioYes">是</label></div>' +
      '<input type="radio" class="radioNo" name="' +
      type +
      '-' +
      tableIndex +
      '-' +
      trIndex +
      '-isGroup" value="false"' +
      (item.isGroup ? '' : ' checked') + // 使用三元运算符设置选中属性
      '>' +
      '<label for="' +
      tableIndex +
      '-' +
      trIndex +
      '-radioNo">否</label></div></td>'

    typeText = '数据'
  } else {
    // 图片
    isGroupStr += '<td>-</td>'
    typeText = '图片'
    positionHtml = '<td><button onclick="picSettingDialog(this)">设置</button></td>'
  }

  tableHTML += '<tr>'
  tableHTML += '<td>' + fieldName + '</td>'
  tableHTML += '<td>' + fieldCode + '</td>'
  tableHTML += '<td>' + typeText + '</td>'
  if (type === 'sub') {
    tableHTML += isGroupStr
  }
  tableHTML += positionHtml
  tableHTML +=
    '<td><a href="#" class="icon-ml" onclick="editTableField(this); return false;"><img src="../static/hisui/css/icons/paper_pen_blue.png" alt=""></a><a href="#" class="icon-ml deleteAttrBtn"><img src="../static/hisui/css/icons/no.png" alt=""></a></td>'
  tableHTML += '</tr>'

  return tableHTML
}

// 生成唯一ID的函数
function generateUniqueId() {
  return 'table-' + Math.random().toString(36).substr(2, 10)
}
