﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <!--引用HISUI-->
    <link rel="stylesheet" type="text/css" href="../../scripts_lib//hisui-0.1.0/dist/css/hisui.lite.min.css">
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.min.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.hisui.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/locale/hisui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../scripts/websys.jquery.bsp.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/websys.css">

    <!--公共js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/common.data.js"></script>
    <script type="text/javascript" src="../scripts/dhccertauth/lib/json2.js"></script>

    <!--业务js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/cfg.usermgr.js"></script>
</head>
<body class="hisui-layout">
    <div data-options="region:'north',title:'',border:false,collapsible:false,split:true,headerCls:'panel-header-gray'" style="height:48px">
        <div class="hisui-panel" style="height:41px;">
            <table cellspacing="5" cellpadding="0">
                <tr>
                    <td id="_OrgListLabel" style="color:red;margin:0 10">选择组织机构：</td>
                    <td><input id="_OrgList"></td>
                </tr>
            </table>
        </div>
    </div>
    <div data-options="region:'east',title:'',border:false,collapsible:false,split:true,headerCls:'panel-header-gray'" style="width:760px" >
        <div class="hisui-panel" data-options="title:'未临时关闭用户列表',fit:true,headerCls:'panel-header-gray',iconCls:'icon-paper'">
            <table id="dgOnUser" ></table>
        </div>
        <div id="tbOnUser">
            <table cellpadding="5">
                <tr>
                    <td>用户工号</td>
                    <td><input id="userCode" type="text" class="textbox tdtext" style="width:155px"/></td>
                    <td>用户姓名</td>
                    <td><input id="userName" type="text" class="textbox tdtext"/></td>
                    <td><a class="hisui-linkbutton" data-options="iconCls:'icon-search',plain:true" id="btnQueryOnUser" style="width:165px">查询</a></td>
                </tr>
                <tr>
                    <td>截止日期</td>
                    <td><input id="offCAToDate" class="hisui-datebox datebox-f combo-f"></td>
                    <td>备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td><input id="offCAMark" type="text" class="textbox tdtext"/></td>
                    <td><a class="hisui-linkbutton" data-options="iconCls:'icon-unuse',plain:true" id="btnOffUserCA" style="width:165px">临时关闭个人CA</a></td>
                </tr>
            </table>
        </div>
    </div>
    <div data-options="region:'center',title:'',border:false,collapsible:false,split:true,headerCls:'panel-header-gray'" >
        <div class="hisui-panel" data-options="title:'已临时关闭用户列表',fit:true,headerCls:'panel-header-gray',iconCls:'icon-paper'">
            <table id="dgOffUser" class="hisui-datagrid"></table>
        </div>
        <div id="tbOffUser">
            <table cellpadding="5">
                <tr>
                    <td>用户工号</td>
                    <td><input id="offUserCode" type="text" class="textbox tdtext"/></td>
                    <td>用户姓名</td>
                    <td><input id="offUserName" type="text" class="textbox tdtext"/></td>
                    <td>用户关闭状态</td>
                    <td>
                        <select id="offType" class="hisui-combobox" editable="false" style="width:155px">
                            <option value="ALLOFF" selected>所有</option>
                            <option value="TMPOFF">临时关闭</option>
                            <option value="OFF">永久关闭</option>
                        </select>
                    </td>
                    <td><a class="hisui-linkbutton" data-options="iconCls:'icon-search',plain:true" id="btnQueryOffUser" style="width:135px">查询</a></td>
                </tr>
                <tr>
                    <td>备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="4"><input id="onCAMark" type="text" class="textbox tdtext" style="width:388px"/></td>
                    <td></td>
                    <td><a class="hisui-linkbutton" data-options="iconCls:'icon-add',plain:true" id="btnOnUserCA" style="width:135px">开启个人CA</a></td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
