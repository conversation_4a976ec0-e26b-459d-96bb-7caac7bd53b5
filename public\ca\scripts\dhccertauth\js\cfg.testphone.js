﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//获取页面传参信息
var urlParams = ca_common_tools.getParams();
var globalInfo = {
    VenderCode: urlParams.VenderCode || "",
    SignTypeCode: urlParams.SignTypeCode || "",
    OrgID: urlParams.OrgID || "",
    VenderSignTypeConfig: {}     //厂商配置服务信息
}

//所有页签数据
var tabs = [{id:"qrcode",title:"扫码签名",src:"dhc.certauth.cfg.testphone.qrcode.html"}]

//页面数据
var toSignData = "测试待签数据123ABCabc";
var hisUserName = "";
var hisUserCode = "";
var hisUserID = "";
var hashData = "";
var hashType = "";
var signedData = "";
var signCert = "";
var certNo = "";
var userCertCode = "";
var signImage = "";

if ((globalInfo.VenderCode != "")&&(globalInfo.SignTypeCode != "")) {
    document.write("<script src='../scripts/dhccertauth/ics/ca_common_"+ globalInfo.VenderCode.toLowerCase() +"_"+globalInfo.SignTypeCode.toLowerCase()+".js'><\/script>");
}

$(function() {
    initBTN();
    getVenderSignTypeConfig();
});

//url转换
function formatSrc(src) {
    if (src.indexOf("?") > -1) {
        src = src + "&MWToken=" + ca_common_tools.getMWToken();
    } else {
        src = src + "?MWToken=" + ca_common_tools.getMWToken();
    }
    return src;
}

//获取厂商配置服务信息
function getVenderSignTypeConfig() {
    var params = {
        organizationID: globalInfo.OrgID,
        langID: logonInfo.LangID
    };
    var configInfo = ca_key.getConfig(params);
    if ("0" !== configInfo.code) {
        $.messager.alert("提示", configInfo.msg, "error");
        return;
    }

    globalInfo.VenderSignTypeConfig = configInfo.data;
    var enablePinLogon = globalInfo.VenderSignTypeConfig.signOption.enablePinLogon || "0";
    if (enablePinLogon == "1")
        tabs.push({id:"pin",title:"PIN码签名",src:"dhc.certauth.cfg.testphone.pin.html"});

    var enablePushSign = globalInfo.VenderSignTypeConfig.signOption.enablePushSign || "0";
    if (enablePushSign == "1")
        tabs.push({id:"statusmgr",title:"推送签名",src:"dhc.certauth.cfg.testphone.push.html"});
    
    initTabs();
}

//初始化页签
function initTabs() {
    for(i=0;i<tabs.length;i++ ) {
        var src = formatSrc(tabs[i].src);
        var content = "<iframe id='fram" + tabs[i].id + "' frameborder='0' src='"+ src +"' style='width:100%; height:99%;scrolling:no;margin:0px;'></iframe>"
        $("#tab").tabs("add",{
            id:       "tab"+tabs[i].id ,
            title:    ca_common_tools.trans(tabs[i].title),
            content:  content,
            closable: false,
            selected: false
       });
    }
    $("#tab").tabs("select",0);
}

//展示错误信息
function displayError(errorInfo) {
    $("#caTip").show();
    $("#signInfo").hide();
    document.getElementById("caTip").innerHTML = ca_common_tools.trans(errorInfo);
    document.getElementById("signInfo").innerHTML = "";
}

//子页面认证过以后，证书信息传到父页面开始签名流程
function caSign(certInfo,userInfo) {
    hisUserName = userInfo.userName;
    hisUserCode = userInfo.userCode
    hisUserID = userInfo.userID;
    signImage = userInfo.signImage;

	console.time("签名耗时");
    signedDataResult = ca_key.hashAndSignedData({
        toSignData:toSignData,
        hashType:"SM3Hex",
        certContainer:certInfo.certContainer,
        signToken:certInfo.signToken,
        organizationID:globalInfo.OrgID,
        actionCode:"Test",
        episodeID:"",
        businessID:"",
        getSignDetail:true
    });
    console.timeEnd("签名耗时");
    if ("0" !== signedDataResult.code) {
        displayError("数据签名失败:"+signedDataResult.msg);
        return;
    }
    console.log("签名数据成功："+getCurrentTime());//输出到控制台
    
    hashData = signedDataResult.data.hashData;
    hashType = signedDataResult.data.hashType;
    signedData = signedDataResult.data.signedData;
    signCert = signedDataResult.data.signCert;
    certNo = signedDataResult.data.certNo;
    userCertCode = signedDataResult.data.userCertCode;
    var digitalSignID = signedDataResult.data.digitalSignID;
    var timeStampData = signedDataResult.data.timeStampData;
    var signDataTime = signedDataResult.data.signDateTime;
    //签名成功，展示相关数据
    ShowSucessInfo(timeStampData,signDataTime,digitalSignID);
}

//展示成功信息
function displayOK(info) {
    $("#caTip").hide();
    $("#signInfo").show();
    document.getElementById("caTip").innerHTML = "";
    document.getElementById("signInfo").innerHTML = ca_common_tools.trans(info);
}

function initBTN() {
    $("#brnReloadPage").click(function(){reload();});
}

//签名成功后展示相关数据
function ShowSucessInfo(timeStampData,signDateTime,digitalSignID) {
    if (digitalSignID == "") {
        displayError("签名数据存储失败");
    } else {
        $("#cadiv").hide();

        var signInfo = "【Hash前签名数据】：" + "\n&#09;" + toSignData;
        signInfo = signInfo + "\n"+ "【Hash算法】：" + "\n&#09;" + hashType;
        signInfo = signInfo + "\n"+ "【Hash算法生成值】：" + "\n&#09;" + hashData + "\n";
        signInfo = signInfo + "\n"+ "【CA用户签名证书】：" + "\n&#09;" + signCert;
        signInfo = signInfo + "\n"+ "【CA证书唯一标识】：" + "\n&#09;" + certNo;
        signInfo = signInfo + "\n"+ "【CA用户唯一标识】：" + "\n&#09;" + userCertCode + "\n";
        signInfo = signInfo + "\n"+ "【签名数据存储表ID】：" + "\n&#09;" + digitalSignID;
        signInfo = signInfo + "\n"+ "【CA签名值】：" + "\n&#09;" + signedData.signValue;
        signInfo = signInfo + "\n"+ "【CA签名格式】：" + "\n&#09;" + signedData.pkcsType;
        signInfo = signInfo + "\n"+ "【CA时间戳】：" + "\n&#09;" + timeStampData;
        signInfo = signInfo + "\n"+ "【时间戳解析时间】：" + "\n&#09;" + signDateTime + "\n";
        signInfo = signInfo + "\n"+ "【签名图片base64】：" + "\n&#09;" + signImage;
        signInfo = signInfo + "\n"+ "【关联HIS用户ID】：" + "\n&#09;" + hisUserID;
        signInfo = signInfo + "\n"+ "【关联HIS用户工号】：" + "\n&#09;" + hisUserCode;
        signInfo = signInfo + "\n"+ "【关联HIS用户名】：" + "\n&#09;" + hisUserName;
        signInfo = signInfo.replace(/\n/g, "<br>");
        signInfo = signInfo.replace(/&#09;/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
        displayOK(signInfo);

        if ((signImage !="")&&(signImage != null))
            document.getElementById("image").src ="data:image/jpg;base64,"+signImage;
    }
}

//重置页面
function reload() {
    location.reload();
}