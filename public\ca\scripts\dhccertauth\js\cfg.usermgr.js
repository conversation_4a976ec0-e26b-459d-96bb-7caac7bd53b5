﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_userstatus";

//页面选中数据
var selClosedUserID = "";
var selOpenedUserID = "";

$(function() {
    initBTN();
    document.onkeydown = documentOnKeyDown;

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        initOffUserGrid(selOrgID);
        initOnUserGrid(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        initOffUserGrid(selOrgID);
        initOnUserGrid(selOrgID);
    }
})

function initBTN() {
    $("#btnQueryOnUser").click(function(){queryOnUser();});
    $("#btnQueryOffUser").click(function(){queryOffUser();});
    $("#btnOffUserCA").click(function(){ offUserCA(); });
    $("#btnOnUserCA").click(function(){ onUserCA(); });
}

function queryOffUser() {
    var userCode = $("#offUserCode").val();
    var userName = $("#offUserName").val();
    var offType = $("#offType").combobox("getValue");
    var param = {
        action: "GET_OFFCAUSERLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: false,
            userCode: userCode,
            userName: userName,
            offType: offType
        }
    };
    $("#dgOffUser").datagrid("load",param);
}

function queryOnUser() {
    var userCode = $("#userCode").val();
    var userName = $("#userName").val();
    var param = {
        action: "GET_USERLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: false,
            userCode: userCode,
            userName: userName,
            userStatus: "NOOFF"
        }
    };
    $("#dgOnUser").datagrid("load",param);
}

//关闭用户CA
function offUserCA() {
    selOpenedUserID = selOpenedUserID || "";
    if (selOpenedUserID == "") {
        $.messager.alert("提示","请选中要临时关闭的用户");
        return;
    }
    var offCAToDate = $("#offCAToDate").datebox("getText");
    var mark = $("#offCAMark").val();
    var data = {
        action: "SET_USERCAOFF",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            operaUserID: logonInfo.UserID,
            offCAToDate: offCAToDate,
            offCAUserID: selOpenedUserID,
            offCAMark: mark
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "用户CA关闭成功",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)+220}});
                queryOffUser();
                queryOnUser();
            } else {
                $.messager.alert("提示", "用户CA关闭失败！", "error")
            }
        } else {
            $.messager.alert("提示", "关闭用户CA失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

//开启用户CA
function onUserCA() {
    selClosedUserID = selClosedUserID || "";
    if (selClosedUserID == "") {
        $.messager.alert("提示","请选中要开启CA的用户");
        return;
    }

    var mark = $("#onCAMark").val();
    var data = {
        action: "SET_USERCAON",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            operaUserID: logonInfo.UserID,
            onCAMark: mark,
            onCAUserID: selClosedUserID
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "用户CA开启成功",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
                queryOffUser();
                queryOnUser();
            } else {
                $.messager.alert("提示", "用户CA开启失败！", "error")
            }
        } else {
            $.messager.alert("提示", "开启用户CA失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function initOffUserGrid(selOrgID) {
    var param = {
        action: "GET_OFFCAUSERLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: true,
            userCode: "",
            userName: "",
            offType: "ALLOFF"
        }
    };
    $("#dgOffUser").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbOffUser",
        url: ca_common_tools.getAppPath("GET_OFFCAUSERLIST"),
        queryParams: param,
        idField:"offCAUserID",
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:50,
        pageList:[10,30,50],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"offCAUserID",title:"用户ID",hidden:true},
            {field:"offCAUserCode",title:"用户工号"},
            {field:"offCAUserName",title:"用户姓名"},
            {field:"offCAUserInfo",title:"关闭时效",width:220},
            {field:"operaUserCode",title:"操作人工号"},
            {field:"operaUserName",title:"操作人姓名"},
            {field:"operaDateTime",title:"操作日期时间",width:200},
            {field:"offCAUserMark",title:"备注",width:250}
        ]],
        onLoadError:function() {
            $.messager.alert("提示","已关闭CA用户列表加载失败");
        },
        onSelect:function(rowIndex,row){
            selClosedUserID = row.offCAUserID;
        },
        onLoadSuccess:function(data){
            $("#dgOffUser").datagrid("clearSelections");
            selClosedUserID = "";
        }
    });
}

function initOnUserGrid(selOrgID) {
    var param = {
        action: "GET_USERLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: true,
            userCode: "",
            userName: "",
            userStatus: "NOOFF"
        }
    };
    $("#dgOnUser").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbOnUser",
        url: ca_common_tools.getAppPath("GET_USERLIST"),
        queryParams: param,
        idField:"userID",
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:50,
        pageList:[10,30,50],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"userID",title:"用户ID",hidden:true},
            {field:"userCode",title:"用户工号"},
            {field:"userName",title:"用户姓名"}
        ]],
        onLoadError:function() {
            $.messager.alert("提示","用户列表加载失败");
        },
        onSelect:function(rowIndex,row){
            selOpenedUserID = row.userID;
        },
        onLoadSuccess:function(data){
            $("#dgOnUser").datagrid("clearSelections");
            selOpenedUserID = "";
        }
    });
}

function documentOnKeyDown(e) {
    if (window.event) {
        var keyCode=window.event.keyCode;
    } else {
        var keyCode=e.which;
    }

    if (keyCode==13) {
        if ((document.activeElement.id === "offUserCode")||(document.activeElement.id === "offUserName")) {
            queryOffUser();
        }
        else if ((document.activeElement.id === "userCode")||(document.activeElement.id === "userName")) {
            queryOnUser();
        }
    }
}