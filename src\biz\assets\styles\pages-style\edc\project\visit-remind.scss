.visit-remind__list {

    .visit-status-popover {
        display: flex;
        align-items: center;
        height: 20px;
    }

    .statusLabel {
        height: 20px;
        line-height: 20px;
        width: 80px;
        display: inline-block;
        margin-right: 1px;
        color: #fff;
        cursor: pointer;
        text-align: center;
    }

    .label-success {
        background-color: #67c23a;
    }

    .label-primary {
        background-color: #409eff;
    }

    .label-warning {
        background-color: #e6a23c;
    }

    .label-danger {
        background-color: #ff0000;
    }

    .label-info {
        background-color: #999;
    }

    .status-point {
        display: inline-block;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 5px;
        vertical-align: text-bottom;
    }

    .label-item {
        line-height: 14px;
        margin-right: 10px;
        display: flex;
        align-items: center;
    }

    .visitRemindTable .cell {
        padding-left: 0px !important;
        padding-right: 0;
    }

    .multiple-visit {
        display: flex;
    }
}

.custom-tips-popover.hos-popover {
    font-size: 14px;
    background: rgb(34, 41, 55, 0.8);
    border: 1px solid #3d4750;
    color: #ffffff;
    padding: 10px;
}