/**
 * 格式化去掉时间字符串尾部的.0
 * 例如：2009-02-20 15:35:27.0 ==> 2009-02-20 15:35:27
 * @param {String} dateTimeStr --格式化时间字符串
 */
export function time_substr(dateTimeStr) {
  if (!dateTimeStr || typeof dateTimeStr !== 'string') {
    return dateTimeStr
  }
  const reg = /^\d{4}-\d{2}-\d{2}\s{1}\d{2}:\d{2}:\d{2}\.0$/
  if (reg.test(dateTimeStr)) {
    return dateTimeStr.substring(0, dateTimeStr.length - 2)
  } else {
    return dateTimeStr
  }
}