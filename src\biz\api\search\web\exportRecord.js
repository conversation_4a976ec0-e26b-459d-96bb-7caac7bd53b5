
// 获取分页数据
export const getPageData = (params) => {
  return {
    url: 'search/export-record/page',
    method: 'GET',
    params,
  }
}

// 获取文件详情
export const getFileByIds = (data) => {
  return {
    url: `file/getFileByIds`,
    method: 'POST',
    data: data
  }
}

export const previewOrDownloadFile = (params) => {
  return {
    url: `file/downloadById`,
    method: 'get',
    params
  }
}

// 获取审核被拒原因
export const getRefused = (id) => {
  return {
    url: `search/approve-record/fail-by-export-record-id/${id}`,
    method: 'GET',
  }
}

//  重新请求审核

export const submitCheckAgain = (params) => {
  return {
    url: `search/approve-record/once/submit-by-export-record-id`,
    method: 'GET',
    params,
  }
}

//  第一次请求审核
export const submitCheckFirst = (data) => {
  return {
    url: `search/export-record/apply/download`,
    method: 'POST',
    data: data
  }
}

//  清空数据

export const clearAllData = (params) => {
  return {
    url: `search/export-record/delete/all/${params.indexId}/${params.userId}`,
    method: 'POST',
  }
}

//  删除数据

export const handleDelete = (id) => {
  return {
    url: `search/export-record/delete/${id}`,
    method: 'POST',
  }
}

// 获取文件加密密码
export const getProfilePassword = (params) => {
  return {
    url: `search/export-record/encrypt-password`,
    method: 'GET',
    params,
  }
}

export const uploadFileFirst = (data) => {
  return {
    url: "search/export-record/upload/attachment",
    method: "POST",
    data
  }
}

export const previewImgByName = (params) => {
  return {
    url: 'search/file/preview/img/' + params,
    method: 'get',
    responseType: 'blob'
  }
}

// 获取导出excel的预览数据
export const getXlsxPreviewData = (params) => {
  return {
    url: `search/file/preview/excel/${params.fileGuid}`,
    method: 'get',
    params
  }
}

export const askIsOpenOA = () => {
  return {
    url: '/search/export-record/select-oa-status',
    method: 'get'
  }
}
