
// 获取当前用户是项目管理员且启用了的项目列表
export const getAdminProjectList = () => {
  return {
    url: `/edc/subject/edc-pro-list`,
    method: "GET",
  }
}

// 根据当前高级检索查询到的患者数,获取该数量患者是否可以批量入组
export const getIntoProjectEnable = (params) => {
  return {
    url: `search/advanced/over-num-by-case-num`,
    method: "get",
    params
  }
}

// 高级检索加入项目
export const joinEDCProject = (data) => {
  return {
    url: `open/edc-join-pro`,
    method: "post",
    data
  }
}

