
export const queryListApi = (params) => {
  return {
    url: 'edc/subject-form/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/subject-form/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/subject-form/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/subject-form/insert',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/subject-form/update',
    method: 'post',
    data
  }
}

// 获取子项目下所发布的表单
export const queryAllListApi = (params) => {
  return {
    url: 'edc/subject-form/info-list',
    method: 'get',
    params,
  }
}

export const publishAll = ({params,data}) => {
  return {
    url: 'edc/subject-form/publish',
    method: 'post',
    params,
    data,
  }
}

// 分页查询表单设计历史
export const queryFormHistory = (params) => {
  return {
    url: 'edc/form-history/page',
    method: 'get',
    params,
  }
}

// 查询表单设计历史详情
export const queryFormHistoryDetail = (params) => {
  return {
    url: `edc/form-history/select-version-form/${params.formId}/${params.version}`,
    method: 'get',
    // params,
  }
}

// 恢复此版本
export const resetFormHistory = (data) => {
  return {
    url: `edc/form-history/revert/${data.id}/${data.version}`,
    method: 'post',
    // data,
  }
}

// 获取项目列表
export const getAllProject = (params) => {
  return {
    url: 'edc/project/list',
    method: 'get',
    params
  }
}

// 应用项目下所有表单到本项目
export const applyAllForm = (params) => {
  return {
    url: 'edc/subject-form/apply-all',
    method: 'post',
    params
  }
}

// 修改表单时获取模型列表
export const getFormTemplateList = (params) => {
  return {
    url: '/edc/subject-form/modelList',
    method: 'get',
    params
  }
}
