<template>
  <div style="height: calc(100vh - 320px);">
    <div v-if="menuArr.length > 0" class="content">
      <div class="left-custom-menu">
        <template v-for="(item, index1) in menuArr">
          <div v-if="item.children && item.children.length > 0" :key="index1" class="submenu">
            <div class="submeunItem" @click="changeCurCateId(item)">
              {{ item.categoryName }}<i class="hos-icon-arrow-down" />
            </div>
            <hos-collapse-transition :key="index1">
              <div v-show="curCateId === item.categoryId" class="son-menu-box">
                <div
                  v-for="(form, index2) in item.children"
                  :key="index2"
                  :class="{ 'is-active': curFormId === form.id }"
                  class="transition-box"
                  @click="changeCurForm(form)"
                >
                  {{ form.name }}
                </div>
              </div>
            </hos-collapse-transition>
          </div>
          <div
            v-if="!item.children"
            :key="index1"
            :class="{ 'is-active': curFormId === item.id }"
            class="munu-item"
            @click="changeCurForm(item)"
          >
            {{ item.name }}
          </div>
        </template>
      </div>
      <template v-if="!isRefresh">
        <div v-if="singleSelect" id="scroll-area-deform_1" class="right-form-field">
          <!-- 设置查询条件的时候是单选 -->
          <div class="single-select">
            <div v-for="(form, index3) in formArr" :id="'item_1_' + form.id" :key="index3" class="form-div acontent_1">
              <div class="form-name">{{ form.name }}</div>
              <div
                v-if="form.type == 1"
                class="tag-box"
              >
                <hos-tag
                  v-for="field in form.metadata.itemProp"
                  :key="field.id"
                  type="info"
                  :class="{ 'is-selected': selectQueryFieldArr.find((item) => item.id === field.id) }"
                  @click="clickTag_condition(form, field)"
                  v-html="getHeightLightItem(field.name)"
                />
              </div>
              <div v-if="form.type == 2" class="tag-box">
                <template v-for="field in form.metadata.itemCode">
                  <hos-tag
                      v-if="!field.popoverShow"
                      :key="field.id"
                      @click="showPopover(field)"
                      type="info"
                      class="with-prop"
                      :class="{ 'pre-selected':field.visible,'is-selected': selectQueryFieldArr.find((item) => item.id === field.id) }"
                      v-html="getHeightLightItem(`${field.name}${(selectQueryFieldArr.find((item) => item.id === field.id) || field.visible) && field.conditionQueryAttr?('-'+field.conditionQueryAttr.name):''}`)"
                    />
                  <hos-popover
                    v-else
                    :key="field.id"
                    @show="popoverShow(form,field)"
                    placement="top"
                    width="600"
                    :title="$t('查询属性') + ' - ' + field.name"
                    v-model="field.visible">
                    <p v-if="field.visible" class="hos-popover__content" style='padding-bottom: 0;'>
                      <hos-radio-group v-model="field.conditionQueryAttr">
                        <hos-radio class="field-prop-item" v-for="(item4, index4) in form.metadata.itemProp" :key="index4" :label="item4">{{
                          item4.name
                        }}</hos-radio>
                      </hos-radio-group>
                    </p>
                    <div style="text-align: center; padding-bottom: 15px;">
                      <hos-button size="small" @click="cancelPopover(form,field)">取消</hos-button>
                      <hos-button size="small" type="success" @click="clickTag_condition(form, field)">确定</hos-button>
                    </div>
                    <hos-tag
                      slot="reference"
                      type="info"
                      class="with-prop"
                      :class="{ 'pre-selected':field.visible,'is-selected': selectQueryFieldArr.find((item) => item.id === field.id) }"
                      v-html="getHeightLightItem(`${field.name}${(selectQueryFieldArr.find((item) => item.id === field.id) || field.visible) && field.conditionQueryAttr?('-'+field.conditionQueryAttr.name):''}`)"
                    />
                  </hos-popover>
                </template>
                
              </div>
            </div>
          </div>
        </div>

        <div v-else id="scroll-area-deform_2" class="right-form-field">
          <!-- 设置查询字段的时候是多选 -->
          <div class="multple-select">
            <div v-for="(form, index3) in formArr" :id="'item_2_' + form.id" :key="index3" class="form-div acontent_2">
              <div class="form-name">
                <span class="checkbox-span">
                  <hos-checkbox
                    :value="checkAllSelect(form)"
                    :indeterminate="checkIndeterminate(form)"
                    @change="changeFormSelected($event, form)"
                  />
                </span>
                {{ form.name }}
              </div>
              <div
                v-if="form.type == 1"
                class="tag-box"
              >
                <hos-tag
                  v-for="field in form.metadata.itemProp"
                  :key="field.id"
                  type="info"
                  :class="{ 'is-selected': selectQueryFieldArr.find((item) => item.id === field.id) }"
                  @click="clickTag_queryField(form, field)"
                  v-html="getHeightLightItem(field.name)"
                />
              </div>
              <div v-if="form.type == 2" class="tag-box">
                <hos-tag
                  v-for="field in form.metadata.itemCode"
                  :key="field.id"
                  type="info"
                  :class="{ 'is-selected': selectQueryFieldArr.find((item) => item.id === field.id) }"
                  @click="clickTag_queryField(form, field)"
                  v-html="getHeightLightItem(field.name)"
                />
                <div>
                  <span style="display: inline-block; margin: 10px 5px">{{ $t('查询属性(必填项)') }}</span>
                  <hos-checkbox-group v-model="form.deformItemInfos" @change="queryAttrChange($event, form)">
                    <hos-checkbox class="field-prop-item" style="width:18%;" v-for="(item5, index5) in form.metadata.itemProp" :key="index5" :label="item5">{{
                      item5.name
                    }}</hos-checkbox>
                  </hos-checkbox-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      
    </div>
    <empty-box v-else />
  </div>
</template>

<script>
import { getFullInputName_edc } from '../../utils/utils'
export default {
  name: 'SubscribeFieldSelect',
  components: {},
  props: {
    heightLight: {
      // 需要高亮的字段
      type: String,
      default() {
        return ''
      }
    },
    // 是否单选，默认是
    singleSelect: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 查询项大分类
    menuArr: {
      type: Array,
      default() {
        return []
      }
    },
    // 全部关联字段信息
    formArr: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      activePopoverField: null,
      cacheRestore: [],
      isRefresh:false,
      activeIndex: null,
      selectId: '',
      keyword: '',
      selectItems: [],
      random: Math.floor(Math.random() * 10000), // 防止id重复，拼接一个随机数字
      curCateId: 0,
      curFormId: 0,
      checked: false,
      selectQueryFieldArr: []
    }
  },
  watch: {
    selectId() {
      this.activeIndex = this.selectId
    },
    formArr(){
      this.restoreSelectItems(this.cacheRestore)
    }
  },
  methods: {
    showPopover(field){
      this.$set(field,'popoverShow',true)
      setTimeout(() => {
        this.$set(field,'visible',true)
      }, 0);
    },
    getHeightLightItem(str = '') {
      if (this.heightLight) {
        return str.replaceAll(this.heightLight, `<span style="color:red;">${this.heightLight}</span>`)
      } else {
        return str
      }
    },
    popoverShow(form,field){
      if(form.metadata.itemProp.length>0){
        if(!field.conditionQueryAttr){
          // 选中配置的默认选中的那个属性
          const tmp = form.metadata.itemProp.filter(item=>item.code == form.checkedProp)
          const checkedProp = tmp.length>0 ? tmp[0] : form.metadata.itemProp[0]
          this.$set(field,'conditionQueryAttr',checkedProp)
        }
        // 缓存一下打开之前选中项，以便取消时回显原来的
        this.$set(field,'conditionQueryAttrPrev',field.conditionQueryAttr)
      }
      this.activePopoverField = field
    },
    cancelPopover(form,field){
      if(form.type == 2){
        this.$set(field,'conditionQueryAttr',field.conditionQueryAttrPrev)
      }
      field.visible = false
    },
    closePopover(){
      if(this.activePopoverField){
        this.$set(this.activePopoverField,'visible',false)
      }
    },
    // 回显全部选中的查询项
    restoreSelectItems(all){
      this.cacheRestore = all
      if(!all || all.length == 0){
        this.selectQueryFieldArr = []
        return
      }
      const res = []
      this.formArr.forEach(form=>{
        let fields = []
        let props = []
        if(form.type == 1){
          fields = form.metadata.itemProp
        }
        if(form.type == 2){
          fields = form.metadata.itemCode
          props = form.metadata.itemProp
        }
        fields.forEach(field=>{
          const index = all.findIndex(value=>value.searchItemId == field.id)
          if(index > -1){
            const value = all[index]
            res.push(field)
            if(props.length>0 && value.prodList && value.prodList.length>0){
              if(this.singleSelect){
                const prod = value.prodList[0]
                const propIndex = props.findIndex(p=>p.id == prod.prodId)
                if(propIndex > -1){
                  this.$set(field,'conditionQueryAttr',props[propIndex])
                }
              }else{
                const prodList = value.prodList
                form.deformItemInfos = props.filter(p=>prodList.findIndex(prod=>prod.prodId == p.id)>-1)
                field.deformItemInfos = form.deformItemInfos
              }
            }
          }
        })
      })
      this.selectQueryFieldArr = res
      console.log('refreshing...')
      this.isRefresh = true
      this.$nextTick(()=>{
        this.isRefresh = false
      })
    },
    // 查询项点击事件
    clickItem(item, sub) {
      if (this.singleSelect) {
        this.selectItems = []
      }
      const arr = this.selectItems.filter((i) => {
        return i.searchItemId === item.searchItemId
      })
      if (arr && arr.length > 0) {
        // 从选中的列表中移除
        this.selectItems = this.selectItems.filter((i) => {
          return i.searchItemId !== item.searchItemId
        })
      } else {
        this.selectItems.push(this.checkItem(item, sub))
      }

      if (this.singleSelect) {
        // this.closeSearchDialog()
        this.$emit('closeSearchDialog')
      }
    },
    // 获取全部选中的查询项
    getSelectItems() {
      const activeItem = this.selectItems ? this.selectItems : []

      if (this.singleSelect) {
        return activeItem.length > 0 ? activeItem[0] : null
      } else {
        return activeItem
      }
    },
    checkItem(item, sub) {
      // 获取关联的字段信息
      const fieldInfoArr = this.fieldData.filter((info) => {
        return info.id === item.searchFieldId
      })

      if (fieldInfoArr && fieldInfoArr.length > 0) {
        item['fieldInfo'] = fieldInfoArr[0]
      } else if (!item.fieldInfo) {
        item['fieldInfo'] = {}
      }
      // 获取字段的父字段
      if (sub) {
        item['pFieldInfo'] = {
          id: sub.childId,
          name: sub.childName
        }
      } else {
        item['pFieldInfo'] = {}
      }
      // 特殊处理检验、检查等
      if (item.searchFieldOtherId && item.searchFieldOtherId > 0) {
        // item.fieldInfo.name = item.searchItemName + "_" + item.fieldInfo.name
        // todo 检验检查生成的时候在查询项中已经带上名称
        item.fieldInfo.name = item.searchItemName
        // 获取其他字段信息
        const otherFieldInfoArr = this.fieldData.filter((info) => {
          return info.id === item.searchFieldOtherId
        })

        item.otherFieldInfo = otherFieldInfoArr && otherFieldInfoArr.length > 0 ? otherFieldInfoArr[0] : null
      } else {
        // 展示查询项名称
        item.fieldInfo.name = item.searchItemName
      }
      return item
    },
    checkAll(value, selected, all, sub) {
      if (value) {
        // 全选
        all.forEach((a_item) => {
          if (selected.indexOf(a_item) < 0) {
            selected.push(this.checkItem(a_item, sub))
          }
        })
      } else {
        // 取消全选
        all.forEach((a_item) => {
          if (selected.indexOf(a_item) >= 0) {
            selected.splice(selected.indexOf(a_item), 1)
          }
        })
      }
    },
    resetSearchItems() {
      this.selectItems = []
    },
    changeCurCateId(category) {
      if (this.curCateId === category.categoryId) {
        this.curCateId = 0
      } else {
        this.curCateId = category.categoryId
      }
    },
    // 转换vue组件中保存的字段变量格式为对外保存的变量格式
    transformFieldToOut(field){
      const res = {
          fieldType: 'deform',
          searchItemName: '',
          searchItemId: field.id,
          fieldName: field.name,
          fieldKey: field.code, // 查询项目编码
          cateName: field.cateName, // 分类名称
          cateCode: field.cateCode, // 分类名称
          cateId: field.cateId,
          externalCateId: field.categoryId, // 分类ID
          id: field.tableId,
          prodList: field.deformItemInfos ? field.deformItemInfos.map(it=>({
            prodId: it.id, // 查询属性id （可选）
            prodName: it.name, // 查询属性名称 （可选）
            prodKey: it.code, // 查询属性编码 （可选）
          })) : []
      }
      res.searchItemName = getFullInputName_edc(res)
      return res
    },
    changeCurForm(form) {
      this.curFormId = form.id

      // 滚动
      // 为了解决document元素id重复问题,在html区分开,针对单选多选分开处理
      if (this.singleSelect) {
        const baseTop = document.querySelectorAll('.acontent_1')[0].offsetTop
        const offsetTop = document.getElementById('item_1_' + form.id).offsetTop
        document.getElementById('scroll-area-deform_1').scrollTop = offsetTop - baseTop
      } else {
        const baseTop_2 = document.querySelectorAll('.acontent_2')[0].offsetTop
        const offsetTop_2 = document.getElementById('item_2_' + form.id).offsetTop
        document.getElementById('scroll-area-deform_2').scrollTop = offsetTop_2 - baseTop_2
      }
    },
    // Condition单选订阅字段作为查询字段相关的函数
    // Condition.1 设置查询条件时点击某一个字段
    clickTag_condition(form, field) {
      field.visible = false
      // 将点击的字段所属表单信息和字段信息传给父组件
      this.$emit('singleSelectDone', form, field)
    },
    // Sub.多选订阅字段作为查询字段相关的函数
    // Sub.1 根据传过来的form信息返回表单下字段数组,与categoryGrade有关
    getFormFieldArr(form) {
      if (!form) return []
      let temArr = []
      if (form.type == 1) {
        temArr = form.metadata.itemProp
      }
      if (form.type == 2) {
        temArr = form.metadata.itemCode
      }
      return temArr
    },
    // Sub.2 获取当前表单有多少个字段被选中
    getCurFormSelectedNum(form) {
      if (!form) return 0
      const temArr = this.getFormFieldArr(form)
      let length = 0
      temArr.map((item) => {
        const index = this.selectQueryFieldArr.findIndex((field) => field.id === item.id)
        if (index > -1) {
          length++
        }
      })
      return length
    },
    // Sub.3 判断当前表单字段是否被全部选中
    checkAllSelect(form) {
      const temArr = this.getFormFieldArr(form)
      const length = this.getCurFormSelectedNum(form)
      if (length === temArr.length) {
        return true
      } else {
        return false
      }
    },
    // Sub.4 判断当前表单字段是否被部分选中
    checkIndeterminate(form) {
      const temArr = this.getFormFieldArr(form)
      const length = this.getCurFormSelectedNum(form)
      // 部分选中时显示-
      if (length > 0 && length < temArr.length) {
        return true
      } else {
        // 否则不显示-
        return false
      }
    },
    // Sub.5 设置查询字段时点击某一个字段
    clickTag_queryField(form, field) {
      if (this.selectQueryFieldArr.includes(field)) {
        const index = this.selectQueryFieldArr.findIndex((item) => item.id === field.id)
        this.selectQueryFieldArr.splice(index, 1)
      } else {
        this.selectQueryFieldArr.push(field)
      }
      console.log(this.selectQueryFieldArr)
    },
    // Sub.6 点击全单前全选框时
    changeFormSelected(val, form) {
      // 全选
      if (val) {
        // 先删除掉原来有的,然后全部存入
        const temArr = this.getFormFieldArr(form)
        // 删掉原来有的
        temArr.map((item) => {
          const index = this.selectQueryFieldArr.findIndex((field) => field.id === item.id)
          if (index > -1) {
            this.selectQueryFieldArr.splice(index, 1)
          }
        })
        // 全部存入
        temArr.map((item) => {
          this.selectQueryFieldArr.push(item)
        })
      } else {
        // 全不选
        const deleArr = this.getFormFieldArr(form)
        // 找到并删除这一项
        deleArr.map((item) => {
          const index = this.selectQueryFieldArr.findIndex((field) => item.id === field.id)
          if (index > -1) {
            this.selectQueryFieldArr.splice(index, 1)
          }
        })
      }
    },
    // Sub.7 多选查询属性时,要将这个查询属性值绑定到每个字段上
    queryAttrChange(val, form) {
      // FIXME:这里拼一个字段
      console.log(val, form, '362')
      form.metadata.itemCode.map((code) => {
        code.deformItemInfos = form.deformItemInfos
        code.key = form.itemCodeField
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: calc(100vh - 350px);
  display: flex;
  .left-custom-menu {
    min-width: 150px;
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    border-right: solid 1px #e6e6e6;
    .submenu,
    .munu-item {
      cursor: pointer;
      position: relative;
      .submeunItem {
        min-height: 45px;
        padding-left: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 149px;
        // white-space: nowrap;
        &:hover {
          background-color: #c6e2ff;
        }
        i {
          margin-right: 5px;
        }
      }
      .son-menu-box {
        .transition-box {
          padding: 5px 0;
          padding-left: 15px;
          background-color: white;
          &:hover {
            background-color: #c6e2ff;
          }
        }
        .transition-box.is-active {
          color: white;
          background-color: #409eff;
        }
      }
    }
    .munu-item {
      min-height: 45px;
      padding-left: 5px;
      display: flex;
      align-items: center;
      &:hover {
        background-color: #c6e2ff;
      }
    }
    .munu-item.is-active {
      color: white;
      background-color: #409eff;
    }
  }
  .right-form-field {
    flex-grow: 1;
    height: 100%;
    overflow-y: auto;
    padding: 10px;
    .single-select,
    .multple-select {
      .form-div {
        margin-bottom: 15px;
        .form-name {
          margin-bottom: 5px;
        }
        .tag-box {
          padding: 10px;
          background-color: #f2f8ff;
        }
      }
      .hos-tag {
        margin-right: 5px;
        margin-bottom: 3px;
        cursor: pointer;
      }
    }
    .single-select {
      .hos-tag.hos-tag--info {
        background-color: #fefeff;
        color: #303133;
        border-color: #c0c4cc;
      }
      .hos-tag.hos-tag--info:hover {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
      }
      .hos-tag.with-prop:hover,.hos-tag.with-prop.pre-selected{
        background-color: #f93;
        color: white;
        border-color: #f93;
      }
      .hos-tag.with-prop.is-selected,.hos-tag.hos-tag--info.is-selected {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
      }
    }
    .multple-select {
      .checkbox-span {
        margin-right: 5px !important;
      }
      .hos-tag.hos-tag--info {
        background-color: #fefeff;
        color: #303133;
        border-color: #c0c4cc;
      }
      .hos-tag.hos-tag--info:hover {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
      }
      .hos-tag.hos-tag--info.is-selected {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
      }
    }
  }
}
.field-prop-item{
  margin: 0 5px 8px;
  width: 31%;
  min-width: 150px;
  white-space: wrap;
}
.empty-box {
  height: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
