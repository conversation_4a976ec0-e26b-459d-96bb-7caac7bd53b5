<template>
  <div class="input-multi-wrapper">
    <hos-select v-model="curValue" class="input-multi" :class="{ 'is-focus': isFocus, 'not-focus': !isFocus }" multiple
      filterable allow-create default-first-option :style="style" :no-data-text="placeholder" :placeholder="placeholder" @visible-change="visibleChange">
      <hos-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
    </hos-select>
    <hos-button type="primary" class="hos-icon-upload btn-upload" :title="$t('添加')" circle style="margin-left: 13px;"
      @click="dialogVisible = true" />

    <hos-dialog :title="$t('上传')" :visible.sync="dialogVisible" width="50%">
      <div class="dialog-content">
        <hos-upload :action="'#'" :show-file-list="false" :on-change="fileChange" accept=".xlsx,.xls,.csv,.txt"
          :auto-upload="false" :file-list="fileList" style="width: 100%;">
          <div class="upload-content">
            <i class="hos-icom-upload-cloud" />
            <div class="hos-upload__text">{{ $t('将文件拖到此处，或') }}<em style="color: #409eff;">{{ $t('点击上传') }}</em></div>
          </div>
        </hos-upload>
        <div class="tips">{{ $t('支持上传excel， csv，txt文件') }}</div>
        <div class="img-wrapper">
          <img src="@/assets/images/input-multi-excel.jpeg" alt="">{{ $t('或') }}<img src="@/assets/images/input-multi-txt.jpeg" alt="">
        </div>
      </div>

      <div slot="footer" class="dialog-footer" style="padding-bottom: 10px;">
        <hos-button @click="dialogVisible = false">{{ $t('关闭') }}</hos-button>
        <!-- <hos-button type="success" style="margin-left: 4px;" @click="dialogVisible = false">{{ $t('确 定') }}</hos-button> -->
      </div>
    </hos-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx/dist/xlsx.full.min.js'
export default {
  props: {
    separator: { // 拼接符
      type: String,
      default() {
        return ','
      }
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('请输入文本后按回车确定')
      }
    },
    // eslint-disable-next-line
    value: {},
    options: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    const _val = typeof this.value === 'string' ? this.value.split(this.separator) : []
    return {
      curValue: _val.filter(item => item),
      isFocus: false,

      fileList: [],
      dialogVisible: false
    }
  },
  computed: {
    style() {
      return this.isFocus ? { 'z-index': 2 } : { height: '28px', 'overflow': 'hidden' }
    }
  },
  watch: {
    value(val) {
      const _val = typeof val === 'string' ? val.split(this.separator) : []
      this.curValue = _val.filter(item => item)
    },
    curValue(val) {
      let _str
      if (typeof val === 'string') {
        _str = val
      } else {
        _str = val.map(item => item.trim()).join(this.separator)
      }
      this.$emit('input', _str)
    }
  },
  methods: {
    visibleChange(val) {
      if (val) {
        this.isFocus = true
      } else {
        this.isFocus = false
      }
    },
    fileChange(file, fileList) {
      this.curValue = ''
      this.fileList = [fileList[fileList.length - 1]]
      const type = file.name.split('.').pop()
      switch (type) {
        case 'xlsx':
        case 'xls':
          this.analysisExcel(file.raw).then(jsonString => {
            this.curValue = jsonString
          })
          break
        case 'csv':
          this.analysisCsv(file.raw).then(jsonString => {
            this.curValue = jsonString
          })
          break
        case 'txt':
          this.analysisTxt(file.raw).then(jsonString => {
            this.curValue = jsonString
          })
          break
        default:
          break
      }
      this.dialogVisible = false
    },
    analysisExcel(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader()
        reader.onload = function (e) {
          const data = e.target.result
          // const datajson = XLSX.read(data, {
          //   type: 'binary'
          // })
          // const result = datajson.Strings.map(i => {
          //   if (i.t.length > 0) return i.t
          // })
          // 因为纯数字的excel在直接通过datajson.Strings有时候读取不到数据
          // 所以调整读取excel内容的方法，直接从具体单元格中读取
          const workbook = XLSX.read(data, {type: 'binary'})

          // 只处理第一个工作表
          let firstSheetName = workbook.SheetNames
          if(Array.isArray(firstSheetName)){
            firstSheetName = firstSheetName[0]
          }
          const worksheet = workbook.Sheets[firstSheetName]

          // 获取第一列的所有值
          const result = [];
          if (worksheet) {
              const range = XLSX.utils.decode_range(worksheet['!ref']);
              for (let R = range.s.r; R <= range.e.r; ++R) {
                  const address = XLSX.utils.encode_cell({r: R, c: 0}); // c: 0 表示第一列
                  if (worksheet[address]) {
                      result.push(worksheet[address].v);
                  }
              }
          }
          resolve(result.join(','))
        }
        reader.readAsBinaryString(file)
      })
    },
    analysisCsv(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = function (evt) {
          // 读取文件内容 csv格式，txt格式
          const fileString = evt.target.result
          const replaceArr = fileString.split('\n').map(item => item.replace('\r', ''))
          resolve(replaceArr.join(',').slice(0, -1))
        }
        reader.readAsText(file, "UTF-8")
      })
    },
    analysisTxt(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = function (evt) {
          // 读取文件内容 txt格式
          const fileString = evt.target.result
          resolve(fileString)
        }
        reader.readAsText(file, "UTF-8")
      })
    }
  }
}
</script>

<style scoped lang="scss">
.input-multi-wrapper {
  position: relative;

  .input-multi {
    position: absolute;
    top: 0;
    width: calc(100% - 50px);

    ::v-deep .hos-select__tags {
      max-height: 100px;
      overflow-y: auto;
    }
  }

  .btn-upload {
    position: absolute;
    top: 0;
    right: 10px;
  }
}

::v-deep .hos-upload {
  width: 100%;
  display: flex;
  justify-content: center;
}

.dialog-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .upload-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 240px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;

    i {
      font-size: 50px;
      color: #409eff;
    }
  }

  .tips {
    margin: 10px 0;
  }

  .img-wrapper {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 25%;
        margin: 0 50px;
      }
    }
}
</style>
