// 数据类型
// export const datatypeMap = {
//     1: "数字",
//     2: "文本",
//     3: "日期",
//     4: "时间",
//     5: "日期时间",
//     6: "单选下拉框",
//     7: "字典",
//     8: "关键词",
// };

// 关系词类型
export const relationMap2 = {
  1: { name: '等于', code: 'eq' },
  2: { name: '大于', code: 'gt' },
  3: { name: '小于', code: 'lt' },
  4: { name: '大于等于', code: 'gte' },
  5: { name: '小于等于', code: 'lte' },
  6: { name: '不等于', code: 'ne' },
  7: { name: '包含', code: 'include' },
  8: { name: '不包含', code: 'not_include' },
  9: { name: '语义包含', code: 'include2' },
  10: { name: '多值匹配', code: 'in' },
  11: { name: '为空', code: 'is_null' },
  12: { name: '非空', code: 'not_null' }
}
// 阴阳性关系词
export const filterTypeList = [
  { name: '阳性包含', code: 'positive_include' },
  { name: '阴性包含', code: 'negative_include' }
]

// 数据类型下拉选项
// export const typeOptions = [
//     { label: '数字', value: 1 },
//     { label: '文本', value: 2 },
//     { label: '日期', value: 3 },
//     { label: '时间', value: 4 },
//     { label: '日期时间', value: 5 },
//     { label: '单选下拉框', value: 6 },
//     { label: '字典', value: 7 },
//     { label: '关键词', value: 8 },
// ];

// 关系词下拉选项
export const relatedOptions = [
    { label: '等于', value: 'eq' },
    { label: '大于', value: 'gt' },
    { label: '小于', value: 'lt' },
    { label: '大于等于', value: 'gte' },
    { label: '小于等于', value: 'lte' },
    { label: '不等于', value: 'ne' },
    { label: '包含', value: 'include' },
    { label: '不包含', value: 'not_include' },
    { label: '开区间', value: 'between_open' }
]
export default { relatedOptions }
