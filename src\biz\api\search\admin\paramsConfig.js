
export const queryListByModel = (params) => {
    return {
        url: 'search/sys-config/page',
        method: 'GET',
        params
    }
}

export const configAdd = (data) => {
    return {
        url: 'search/sys-config/insert',
        method: 'POST',
        data
    }
}

export const configDetail = (id) => {
    return {
        url: 'search/sys-config/detail/' + id,
        method: 'GET'
    }
}

export const configUpdate = (data) => {
    return {
        url: 'search/sys-config/update',
        method: 'post',
        data
    }
}

export const configDeleteBatch = (data) => {
    return {
        url: 'search/sys-config/deletion',
        method: 'POST',
        data
    }
}
