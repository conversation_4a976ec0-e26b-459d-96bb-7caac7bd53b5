.filter-box {
  min-width: 300px;

  .hos-collapse-item__content {
    max-height: 300px !important;
    overflow-y: scroll !important;
  }

  .hos-range-separator {
    width: 10% !important;
  }

  .box-title {
    margin: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-bottom: 1px dashed #e4e4e4;
    // height: 40px;
  }

  dl,
  dd {
    padding: 0;
    margin: 0;
  }

  dl {
    margin-bottom: 20px;
    border-right: 1px solid #e4e4e4;
  }

  .title {
    padding: 10px 20px;
    font-weight: 500;
    font-size: 16px;
    background-color: #ebeef5;
  }

  .hos-collapse {
    border-top: none;
    border-bottom: none;
  }

  .hos-collapse-item__header {
    background-color: #ebeef5 !important;
    padding-left: 10px;
    height: 40px;
  }

  .hos-collapse-item__wrap {
    border: 1px solid #ebeef5;
  }

  .hos-collapse-item__content {
    padding: 10px;
  }

  .filed--append-btn {
    float: right;
    width: 40%;
    display: block;
  }

  .date-content {
    margin: 10px;

    .date-item {
      padding: 3px 5px 2px 5px;
      font-size: 14px;
      cursor: pointer;
      border-radius: 2px;
      margin-left: 5px;
      display: inline-block;
    }

    span.item:hover {
      color: #fff;
      background-color: #a4c8ec;
    }

    span.active {
      color: #fff;
      background-color: #409eff;
    }

    .t-a-center {
      text-align: center;
    }
  }

  .content {
    margin: 10px;
    overflow: hidden;

    .titleStyle {
      height: 100%;
      width: 250px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    span {
      padding: 3px 5px 2px 5px;
      font-size: 14px;
      cursor: pointer;
      border-radius: 2px;
      margin-left: 5px;
      display: inline-block;
    }

    span:hover,
    span.item:hover {
      color: #fff;
      background-color: #a4c8ec;
    }

    span.active {
      color: #fff;
      background-color: #409eff;
    }

    .other {
      font-size: 14px;
      padding-left: 10px;
      margin-top: 10px;

      i.hos-icon-date {
        display: none;
      }
    }

    .t-a-center {
      text-align: center;
    }
  }

  .field-type-content {
    margin: 10px;
  }
}
