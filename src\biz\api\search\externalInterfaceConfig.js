export const pageListApi = (params) => {
    return {
        url: 'search/ext-net-work-data/page',
        method:'get',
        params
    }
}

// 数据不在数据库中，删除就是禁用
export const deleteApi = (data) => {
    return {
        url: 'search/ext-net-work-data/deletion',
        method: 'post',
        data
    }
}

// 数据不在数据库中，新增就是启用
export const addApi = (data) => {
    return {
        url: 'search/ext-net-work-data/insert',
        method: 'post',
        data
    }
}