<template>
  <div class="query-builder form-inline" style="padding: 0 50px">

    <query-builder-group ref="queryBuilderGroup" :index="-1" :query.sync="query" :rule-types="ruleTypes" :cate-items="searchItemData.cateItems"
      :rules="mergedRules" :max-depth="maxDepth" :depth="depth" :allow-del-first="allowDelFirst" :field-data="searchItemData.fieldData"
      type="query-builder-group" :single-select-cate="singleSelectCate" :current-group-index="currentGroupIndex"
      :current-condition-index="currentConditionIndex" @open-search-dialog="openSearchDialog" @delFirst="$emit('delFirst')"
    />

    <search-item-select ref="searchItemDialog" type="search" :cate-items="searchItemData.cateItems" :loading="loading" :filter-type-id="filterTypeId"
      :field-data="searchItemData.fieldData" @close-search-dialog="fillInput" />

  </div>
</template>

<script>
  import { _debounce } from "@/utils/throttle.js"
  import QueryBuilderGroup from "./QueryBuilderGroup.vue"
  import SearchItemSelect from "./SearchItemSelect.vue"
  import {deepClone} from "@/utils/index.js"
  import {
    queryToTextDesc, getAllSearchWords
  } from "@/utils/utils.js"
  import { EventBus } from "./event-bus"

  const defaultRuleType = {}
  const defaultQuery = {
    logical: "AND",
    children: [{
      type: "query-builder-rule",
      query: {
        rule: null,
        fieldItemInfo: {},
        relative: null,
        value: null
      }
    }]
  }

  export default {
    name: "QueryBuilder",

    components: {
      QueryBuilderGroup,
      SearchItemSelect
    },
    provide() {
      return {
        queryBuilderObj: this.provideObj,
        isSimpleMode: this.isSimpleMode,
      }
    },

    props: {
      loading: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 默认的条件
      rules: {
        type: Array,
        default() {
          return []
        }
      },
      disabled: { // 是否禁用
        type: Boolean,
        default() {
          return false
        }
      },
      // 条件树深度， 默认2层
      maxDepth: {
        type: Number,
        default: 2,
        validator: function(value) {
          return value >= 1
        }
      },
      value: Object,
      // 查询项相关数据
      searchItemData: {
        type: Object,
        default() {
          return {
            cateItems: [],
            fieldData: [],
            commonSearchItems: []
          }
        }
      },
      singleSelectCate: {
        type: Object,
        default() {
          return {}
        }
      },
      // 是否允许删除第一个条件
      allowDelFirst: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 默认初始化是空条件还是初始化一个条件
      isDefaultEmpty: {
        type: Boolean,
        default() {
          return true
        }
      },
      actived: {
        type: Boolean,
        default: true
      },
      filterTypeId: { // 过滤指定某个表id的字段
        type: Number,
        default() {
          return 0
        }
      },
      isSimpleMode: { // 简单模式(仅一层条件)
        type: Boolean,
        default() {
          return false
        }
      }
    },

    data() {
      return {
        isDev: true,
        jsonVisible: false,
        queryJsonTest: '',
        depth: 1,
        query: {},
        ruleTypes: defaultRuleType,
        currentGroupIndex: null,
        currentConditionIndex: null,
        currentSubIndex: undefined,
        provideObj: { // 向所有嵌套的子组件注入的全局对象
          disabled: this.disabled
        },
        fieldsIsNegative: new Map(), // 存储所有支持阴阳性的检索词

        isQuickUseFlag: false, // 避免watch监听query进入死循环；凡是快速使用条件查询，需要手动置为true，参考检索历史与使用模板
      }
    },

    computed: {
      // 合并条件
      mergedRules() {
        var mergedRules = []
        var vm = this

        vm.rules.forEach(function(rule) {
          if (rule.subRules !== undefined) {
            var mergedSubRules = []
            rule.subRules.forEach(function(subRule) {
              if (typeof vm.ruleTypes[subRule.type] !== "undefined") {
                mergedSubRules.push(
                  Object.assign({}, vm.ruleTypes[subRule.type], subRule)
                )
              } else {
                mergedSubRules.push(subRule)
              }
            })
            rule.subRules = mergedSubRules
          }
          if (typeof vm.ruleTypes[rule.type] !== "undefined") {
            mergedRules.push(Object.assign({}, vm.ruleTypes[rule.type], rule))
          } else {
            mergedRules.push(rule)
          }
        })

        // 如果为空， 则默认加入一个空的子条件，防止报错
        if (mergedRules.length == 0) {
          const defaultChild = {
            // operators: [],
            operators: [
              {
                label: this.$t('等于'),
                value: 'eq'
              }, {
                label: this.$t('不等于'),
                value: 'ne'
              }, {
                label: this.$t('大于'),
                value: 'gt'
              }, {
                label: this.$t('小于'),
                value: 'lt'
              }, {
                label: this.$t('大于等于'),
                value: 'gte'
              }, {
                label: this.$t('小于等于'),
                value: 'lte'
              }, {
                label: this.$t('包含'),
                value: 'include'
              }
            ],
            inputType: "",
            id: "",
            dataType: ""
          }
          mergedRules.push(defaultChild)
        }
        return mergedRules
      }
    },
    created() {
      this.init()
    },
    mounted() {
      this.$watch(
        "query",
        newQuery => {
          // this.recombination(newQuery)
          // 重新组装
          // 1.0 如果有子节点没有条件的， 需要移除子节点
          // 2.0 如果删除跟节点的需要将yuan
          this.$emit("input", deepClone(newQuery))
          this.syncAllSelectOptions()
          // this.$refs.queryBuilderGroup.calculateOffset(newQuery)
        }, {
          deep: true
        }
      )

      if (typeof this.$options.propsData.value !== "undefined") {
        this.query = Object.assign(this.query, this.$options.propsData.value)
      }
      EventBus.$on("updateFieldsIsNegative", (p) => { this.updateFieldsIsNegative(p) })
    },
    // 在watch中调用时ruleTypes为空对象
    beforeUpdate() {
      if (this.actived && Object.keys(this.ruleTypes).length === 0) {
        this.getAllRelative()
      }
    },
    methods: {
      // 判断条件是否为空， 如果没有条件， 或者有一个条件且条件为空
      checkQueryEmpty(query) {
        if (!query) {
          return true
        }
        const curType = query.type
        if (curType === "query-builder-rule") {
          const curQuery = query.query
          if (!curQuery) {
            return true
          }

          if (this.isNull(curQuery.value) && curQuery.relative != "is_null" && curQuery.relative != "not_null") {
            return true
          }
        }
        if (query.children && query.children.length == 0) {
          return true
        }

        if (query.children && query.children.length == 1) {
          const curChild = query.children[0]
          return this.checkQueryEmpty(curChild)
        }

        return false
      },
      syncAllSelectOptions:_debounce(function() {
        const query = this.deepClone(this.query)
        if (Object.keys(query).length <= 0 || this.isQuickUseFlag === false) {
          return
        }
        const dictIds = []
        query.children.forEach(child => {
          if (child.type === 'query-builder-rule') {
            if (child.query.fieldItemInfo.dictId) {
              dictIds.push(child.query.fieldItemInfo.dictId)
            }
          }
          if (child.type === 'query-builder-group') {
            for (const child2 of child.query.children) {
              if (child2.query.fieldItemInfo.dictId) {
                dictIds.push(child2.query.fieldItemInfo.dictId)
              }
            }
          }
        })
        if (dictIds.length <= 0) {
          return
        }
        // let dictOptionMap = {
        //   '235': [
        //     { dictItemName: '男' },
        //     { dictItemName: '女' },
        //     { dictItemName: '未知' },
        //   ]
        // }
        this.$api('biz.search.web.dictionary.getSubDictItem',dictIds).then(res => {
          const dictOptionMap = res.data || {}
          this.$nextTick(() => {
            query.children.forEach(child => {
              if (child.type === 'query-builder-rule') {
                if (child.query.fieldItemInfo.dictId) {
                  const options = dictOptionMap[child.query.fieldItemInfo.dictId]
                  let selectOptions = []
                  if (options) {
                    selectOptions = options.map(option => {
                      return {
                        label: option.dictItemName,
                        data: option.dictItemName
                      }
                    })
                  }

                  this.$set(child.query.fieldItemInfo, "selectOptions", selectOptions)
                }
              }

              if (child.type === 'query-builder-group') {
                for (const child2 of child.query.children) {
                  if (child2.query.fieldItemInfo.dictId) {
                    child2.query.fieldItemInfo.selectOptions = dictOptionMap[child2.query.fieldItemInfo.dictId]
                    const options = dictOptionMap[child2.query.fieldItemInfo.dictId]
                    const selectOptions = options.map(option => {
                      return {
                        label: option.dictItemName,
                        data: option.dictItemName
                      }
                    })
                    this.$set(child2.query.fieldItemInfo, "selectOptions", selectOptions)
                  }
                }
              }
            })
            this.query = this.deepClone(query)
            this.isQuickUseFlag = false
          })
        })
      }),
      init() {
        // 双向绑定导致了父组件的query被修改，用深拷贝解除引用
        if (this.isDefaultEmpty) {
          this.query = {}
        } else {
          this.query = this.deepClone(defaultQuery)
        }
        this.ruleTypes = defaultRuleType
        this.currentGroupIndex = null
        this.currentConditionIndex = null
        this.depth = 1
      },
      showJson() {
        this.queryJsonTest = JSON.stringify(this.query, null, '\t')
        this.jsonVisible = true
      },
      showDesc() {
        this.queryJsonTest = queryToTextDesc(this.query, true)
        this.jsonVisible = true
      },

      // 异步获取关系词
      async getAllRelative() {
        const ruleType = {
          'text': {
            id: 1,
            options: [{
              label: this.$t('等于'),
              value: 'eq'
            }],
            dataType: 'text'
          }
        }
        // let ruleType = {}
        // let _result = await getAll()
        // if (_result.success) {
        //   if (_result.data && _result.data instanceof Array) {
        //     let groupData = groupBy(_result.data, (item) => item.datatypeCode)
        //     Object.keys(groupData).forEach((key, index) => {

        //       let curOptions = groupData[key].map((item) => ({
        //         label: item.relativeDesc,
        //         value: item.relativeCode
        //       }))

        //       ruleType[key] = {
        //         id: index + 1,
        //         options: curOptions,
        //         dataType: key
        //       }
        //     })
        //   }
        // }
        this.ruleTypes = ruleType
      },
      openSearchDialog(conditionIndex, groupIndex, subIndex, typeId) {
        this.currentGroupIndex = groupIndex
        this.currentConditionIndex = conditionIndex
        this.currentSubIndex = subIndex
        this.$refs.searchItemDialog.openSearchDialog(true, groupIndex, conditionIndex, subIndex, typeId)
      },
      // 填充查询项
      fillInput(activeItems) {
        let curItem = {}
        if (activeItems) {
          if (activeItems instanceof Object) {
            curItem = activeItems
          } else if (activeItems instanceof Array) {
            curItem = activeItems[0]
          }
        }
        // console.log('asdlfj;afsdk')
        this.$refs.queryBuilderGroup.updateChild(this.currentGroupIndex, this.currentConditionIndex, curItem, this.currentSubIndex)
      },
      // 获取条件
      getQuery() {
        if (this.checkQueryEmpty(this.query)) {
          return null
        }
        // 判断条件是否未空
        return this.query
      },
      // 获取所有条件的检索词
      getAllSearchWords() {
        const curQuery = this.query
        return getAllSearchWords(curQuery)
      },
      // 获取条件描述
      getQueryDesc() {
        const curQuery = this.query
        return queryToTextDesc(curQuery, true)
      },
      // 转换中文描述
      getQueryHtml() {
        const curQuery = this.query
        return queryToTextDesc(curQuery, false)
      },
      toQueryHtml(query) {
        return queryToTextDesc(query, false)
      },
      setQuery(query) {
        this.query = query || this.deepClone(defaultQuery)
      },
      // 重置
      resetQuery() {
        this.init()
        this.$refs.queryBuilderGroup.clearValid()
        this.$refs.queryBuilderGroup.calculateOffset(this.query)
      },
      // 校验条件
      vaildQuery() {
        return this.$refs.queryBuilderGroup.valid()
      },
      // 清空条件
      clearQuery() {
        this.query = defaultQuery
      },
      // 更新记录支持阴阳性的字段map
      updateFieldsIsNegative(p) {
         if (this.fieldsIsNegative.has(p.searchFieldId)) {
          return
         } else {
          this.fieldsIsNegative.set(p.searchFieldId, p)
         }
      },
      // 清空记录支持阴阳性的字段map
      clearFieldsIsNegative() {
        this.fieldsIsNegative.clear()
      },
      // 获取支持阴阳性的z字段列表
      getFieldsIsNegative() {
        const output = []
        this.fieldsIsNegative.forEach((item, key) => {
          output.push(item)
        })
        return output
      }
    }
  }
</script>

