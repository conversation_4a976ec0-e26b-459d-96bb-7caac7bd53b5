.export-item-container {
  margin: 10px;
  padding: 10px;
  border: 1px solid #ddd;

  &.no-editable {
    border: 0;
    padding: 0;

    .export-item-line {
      margin-bottom: 10px !important;
    }
  }

  .m-b-10 {
    margin-bottom: 10px;
  }

  .export-item-line {
    margin-bottom: 10px;
    padding-left: 10px;
    display: flex;
    align-items: flex-start;

    .line-title {
      padding: 5px 0;
      display: inline-block;
      flex: 80px 0 0;
    }

    .filter-cond {
      position: relative;
      &::before {
        content: "*";
        color: #F56C6C;
        position: absolute;
        left: -10px; /* 调整星号的位置 */
        top: 0;
      }
    }

    .line-content {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }

    .hos-tag--dark.line-item .hos-tag__close:hover {
      background-color: #007dff;
    }

    .line-item.hos-button:hover {
      background-color: #159cff;
    }

    .line-item {
      background-color: #409effe6;
      color: #fff;
      padding: 3px 8px;
      border-radius: 2px;
      font-size: 14px;
      margin: 3px;
      line-height: 16px;
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;

      // height: 22px;
      .line-item-close {
        display: inline-block;
        cursor: pointer;
        text-align: center;
        font-size: 12px;
        padding: 1px;
        margin-left: 3px;
        border-radius: 50%;

        // background: #31beff;
        &:hover {
          background: #096dd1;
        }
      }
    }
  }
}
