<template>
  <div class="query-builder form-inline" style="padding: 15px 50px">

    <query-builder-group ref="queryBuilderGroup" :index="0" :is-root="true" :query.sync="query" :rule-types="ruleTypes"
                         :rules="mergedRules" :max-depth="maxDepth" :depth="depth" :allow-del-first="allowDelFirst"
                         type="query-builder-group" @open-search-dialog="openSearchDialog"
    />

    <search-item-select ref="searchItemDialog" :cate-items="searchItemData.cateItems" :loading="loading"
                        :field-data="searchItemData.fieldData" @close-search-dialog="fillInput" @opened="searchItemOpened"
    />

  </div>
</template>

<script>
  import QueryBuilderGroup from './QueryBuilderGroup.vue'
  import SearchItemSelect from './SearchItemSelect.vue'
  import {
    queryToTextDesc
} from '../../utils/utils.js'

  const defaultRuleType = {}
  const defaultQuery = {
    logical: 'AND',
    children: [
      // {
      //   type: 'query-builder-rule',
      //   query: {
      //     rule: null,
      //     fieldItemInfo: {},
      //     relative: null,
      //     value: null
      //   }
      // }
    ]
  }

  // const groupBy = (list, fn) => {
  //   const groups = {}
  //   list.forEach(function(o) {
  //     const group = fn(o)
  //     groups[group] = groups[group] || []
  //     groups[group].push(o)
  //   })

  //   return groups
  // }

  export default {
    name: 'QueryBuilder',

    components: {
      QueryBuilderGroup,
      SearchItemSelect
    },
    provide() {
      return {
        queryBuilderObj: this.provideObj
      }
    },

    props: {
      loading: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 默认的条件
      rules: {
        type: Array,
        default() {
          return []
        }
      },
      disabled: { // 是否禁用
        type: Boolean,
        default() {
          return false
        }
      },
      // 条件树深度， 默认2层
      maxDepth: {
        type: Number,
        default: 2,
        validator: function(value) {
          return value >= 1
        }
      },
      value: Object,
      // 查询项相关数据
      searchItemData: {
        type: Object,
        default() {
          return {
            cateItems: [],
            fieldData: [],
            commonSearchItems: []
          }
        }
      },
      // 是否允许删除第一个条件
      allowDelFirst: {
        type: Boolean,
        default() {
          return false
        }
      },
      actived: {
        type: Boolean,
        default: true
      }
    },

    data() {
      return {
        depth: 1,
        query: {},
        ruleTypes: defaultRuleType,
        currentGroupIndex: null,
        currentConditionIndex: null,
        provideObj: { // 向所有嵌套的子组件注入的全局对象
          disabled: this.disabled
        }
      }
    },

    computed: {
      // 合并条件
      mergedRules() {
        var mergedRules = []
        var vm = this

        vm.rules.forEach(function(rule) {
          if (rule.subRules !== undefined) {
            var mergedSubRules = []
            rule.subRules.forEach(function(subRule) {
              if (typeof vm.ruleTypes[subRule.type] !== 'undefined') {
                mergedSubRules.push(
                  Object.assign({}, vm.ruleTypes[subRule.type], subRule)
                )
              } else {
                mergedSubRules.push(subRule)
              }
            })
            rule.subRules = mergedSubRules
          }
          if (typeof vm.ruleTypes[rule.type] !== 'undefined') {
            mergedRules.push(Object.assign({}, vm.ruleTypes[rule.type], rule))
          } else {
            mergedRules.push(rule)
          }
        })

        // 如果为空， 则默认加入一个空的子条件，防止报错
        if (mergedRules.length === 0) {
          const defaultChild = {
            operators: [],
            inputType: '',
            id: '',
            dataType: ''
          }
          mergedRules.push(defaultChild)
        }
        return mergedRules
      }
    },
    created() {
      this.init()
    },
    mounted() {
      this.$watch(
        'query',
        newQuery => {
          if(!newQuery){
            newQuery = defaultQuery
          }
          // this.recombination(newQuery)
          // 重新组装
          // 1.0 如果有子节点没有条件的， 需要移除子节点
          // 2.0 如果删除跟节点的需要将yuan
          this.$emit('input', this.deepClone(newQuery))
          this.$refs.queryBuilderGroup.calculateOffset(newQuery)
        }, {
          deep: true
        }
      )

      if (typeof this.$options.propsData.value !== 'undefined') {
        this.query = Object.assign(this.query, this.$options.propsData.value)
      }
    },
    // 在watch中调用时ruleTypes为空对象
    beforeUpdate() {
      if (this.actived && Object.keys(this.ruleTypes).length === 0) {
        this.getAllRelative()
      }
    },
    methods: {
      // 判断条件是否为空， 如果没有条件， 或者有一个条件且条件为空
      checkQueryEmpty(query) {
        if (!query) {
          return true
        }
        const curType = query.type
        if (curType === 'query-builder-rule') {
          const curQuery = query.query
          if (!curQuery) {
            return true
          }

          if (this.isNull(curQuery.value)) {
            return true
          }
        }
        if (query.children && query.children.length === 0) {
          return true
        }

        if (query.children && query.children.length === 1) {
          const curChild = query.children[0]
          return this.checkQueryEmpty(curChild)
        }

        return false
      },
      init() {
        // 双向绑定导致了父组件的query被修改，用深拷贝解除引用
        this.query = this.deepClone(defaultQuery)
        this.ruleTypes = defaultRuleType
        this.currentGroupIndex = null
        this.currentConditionIndex = null
        this.depth = 1
      },

      // 异步获取关系词
      getAllRelative() {
        const ruleType = {
          'text': {
            id: 1,
            options: [{
              label: this.$t('等于'),
              value: 'eq'
            }],
            dataType: 'text'
          }
        }
        // let _result = await getAll()
        // if (_result.success) {
        //   if (_result.data && _result.data instanceof Array) {
        //     let groupData = groupBy(_result.data, (item) => item.datatypeCode)
        //     Object.keys(groupData).forEach((key, index) => {

        //       let curOptions = groupData[key].map((item) => ({
        //         label: item.relativeDesc,
        //         value: item.relativeCode
        //       }))

        //       ruleType[key] = {
        //         id: index + 1,
        //         options: curOptions,
        //         dataType: key
        //       }
        //     })
        //   }
        // }
        this.ruleTypes = ruleType
      },
      openSearchDialog(groupIndex, conditionIndex) {
        this.currentGroupIndex = groupIndex
        this.currentConditionIndex = conditionIndex
        this.$refs.searchItemDialog.openSearchDialog(true)
      },
      // 填充查询项
      fillInput(activeItems) {
        let curItem = {}
        if (activeItems) {
          if (activeItems instanceof Object) {
            curItem = activeItems
          } else if (activeItems instanceof Array) {
            curItem = activeItems[0]
          }
        }

        this.$refs.queryBuilderGroup.updateChild(this.currentGroupIndex, this.currentConditionIndex, curItem)
        this.$refs.searchItemDialog.clearSearchItems()
      },
      searchItemOpened(){
        const groupIndex = this.currentGroupIndex
        const conditionIndex = this.currentConditionIndex
        const query = this.query
        let curField = []
        if (conditionIndex === 0) {
          curField = [query.children[groupIndex].query.fieldItemInfo]
        } else if(conditionIndex > 0 && query.children[conditionIndex]) {
          const tmp = query.children[conditionIndex].query.children[groupIndex]
          if(tmp){
            curField = [tmp.query.fieldItemInfo]
          }
        }
        if(curField.length>0 && curField[0] && curField[0].fieldType){
          // 回显选中项
          this.$refs.searchItemDialog.restoreSelectItems(curField)
        }else{
          this.$refs.searchItemDialog.restoreSelectItems(null)
        }
      },
      // 获取条件
      getQuery() {
        if (this.checkQueryEmpty(this.query)) {
          return null
        }
        // 判断条件是否未空
        return this.query
      },
      // 获取条件描述
      getQueryDesc() {
        const curQuery = this.query

        return queryToTextDesc(curQuery, true)
      },
      // 转换中文描述
      getQueryHtml() {
        const curQuery = this.query
        return queryToTextDesc(curQuery, false)
      },
      toQueryHtml(query) {
        return queryToTextDesc(query, false)
      },
      setQuery(query) {
        this.query = query || this.deepClone(defaultQuery)
      },
      // 重置
      resetQuery() {
        this.init()
        this.$refs.queryBuilderGroup.calculateOffset(this.query)
      },
      // 校验条件
      vaildQuery() {
        return this.$refs.queryBuilderGroup.valid()
      },
      // 清空条件
      clearQuery() {
        this.query = defaultQuery
      }
    }
  }

</script>
