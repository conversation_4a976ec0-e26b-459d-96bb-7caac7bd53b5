﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_venderservice";   

//页面选中数据
var selRowSrvID = "";
var selRowIndex = "";

//企业签章拓展数据
var esOptionValue = {};
var signOptionValue = {};
var patSignOptionValue = {};

$(function() {
    initBTN(); 
    initSignType();
    initVenderCode();
    initVersionSelect();
    initOptionValueStatus();

    var orgComp = genOrgComp(logonInfo);  
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        initSrvGrid(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        initSrvGrid(selOrgID);
    }
});

//通过签名方式设置拓展参数点击事件
function initOptionValueStatus() {
    $("#signOption").off("click").on("click", function(){$HUI.tooltip("#signOption",{position:"bottom"}).show();});
    $("#patSignOption").off("click").on("click", function(){$HUI.tooltip("#patSignOption",{position:"bottom"}).show();});
    $("#esOption").off("click").on("click", function(){$HUI.tooltip("#esOption",{position:"bottom"}).show();});

    $("#signOption").attr("title","签名方式选为医护手机扫码签名时可用");
    $("#patSignOption").attr("title","签名方式选为患者移动签名时可用");
    $("#esOption").attr("title","签名方式选为医院签章时可用");

    var currentSignType = $("#signTypeCode").combobox("getValue");
    if (currentSignType == "PHONE")
    {
        $("#signOption").attr("title","");
        $("#signOption").off("click").on("click", function(){setOption("signOption");});
    }
    
    if (currentSignType == "PATPDF")
    {
        $("#patSignOption").attr("title","");
        $("#patSignOption").off("click").on("click", function(){setOption("patSignOption");});
    }

    if (currentSignType == "ES")
    {
        $("#esOption").attr("title","");
        $("#esOption").off("click").on("click", function(){setOption("esOption");});
    }
}

//初始化按钮功能
function initBTN() {
    $("#btnSave").click(function(){saveCheck();});
    $("#btnReset").click(function(){reSet();});
    $("#btnDelSrv").click(function(){delSrv();});
    $("#btnTestSrv").click(function(){testSrv();});
    $("#btnRefresh").click(function(){refreshSrv();});
    
    $("#signOption").click(function(){$HUI.tooltip("#signOption",{position:"bottom"}).show();});
    $("#patSignOption").click(function(){$HUI.tooltip("#patSignOption",{position:"bottom"}).show();});
    $("#esOption").click(function(){$HUI.tooltip("#esOption",{position:"bottom"}).show();});
}

//初始化版本选择
function initVersionSelect() {
    var data = [{"code":"1","value":"v1"},{"code":"2","value":"v2"},{"code":"3","value":"v3"},{"code":"4","value":"v4"},{"code":"5","value":"v5"},
                {"code":"6","value":"v6"},{"code":"7","value":"v7"},{"code":"8","value":"v8"},{"code":"9","value":"v9"},{"code":"10","value":"v10"}]; 
    initCombobox("caVersion",data,false,true,"code","value");
}

//初始化签名方式
function initSignType() {
    var data = {
        action: "GET_ALLSIGNTYPE",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("signTypeCode",json.data,false,false,"signTypeCode","signTypeDesc");
    } else {
        $.messager.alert("提示", "获取签名方式数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//初始化CA厂商代码
function initVenderCode() {
    var data = {
        action: "GET_ALLVENDER",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("venderCode",json.data,false,true,"venderCode","venderDesc");
    } else {
        $.messager.alert("提示", "获取厂商数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//重置按钮触发
function reSet() {
    resetServiceInfo();
    refreshSrv();
}

//刷新服务列表
function refreshSrv() {
    var queryParams = {
        action: "GET_VENDERSERVICELIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };
    $("#dgSrv").datagrid("load",queryParams);
}

//刷新详细信息填报页
function resetServiceInfo() {
    $("#venderCode").combobox("clear");
    $("#signTypeCode").combobox("clear");
    $("#caVersion").combobox("clear");
    $("#caLocation").val("");
    $("#tsLocation").val("");
    $("#ciLocation").val("");
    $("#vfLocation").val("");
    $("#siLocation").val("");
    $("#appID").val("");
    $("#appKey").val("");
    $("#appProjectID").val("");
    $("#appOrgCode").val("");
    $("#callBackUrl").val("");
    
    reSetEsOption();
    reSetSignOption();
    reSetPatOption();
}

function reSetEsOption() {
    esOptionValue = {};
    $("#esOption").html("{}");
}

function reSetSignOption() {
    signOptionValue = {};
    $("#signOption").html("{}");
}

function reSetPatOption() {
    patSignOptionValue = {};
    $("#patSignOption").html("{}");
}

//删除按钮触发
function delSrv() {
    if (selRowSrvID == "") {
        $.messager.alert("提示","请先选择一条数据");
        return;
    }

    $.messager.confirm("删除", "注意，删除后数据无法找回，是否确认删除数据？", function (r) {
        if (r) {
            var data = {
                action: "DELETE_VENDERSERVICE",
                params: {
                    organizationID: selOrgID,
                    langID: logonInfo.LangID,
                    venderServiceID: selRowSrvID
                }
            };
        
            var json = ajaxPOSTCommon(data,"",false);
            if (json.code == 200) {
                if (json.data.success) {
                    $.messager.alert("提示", "删除服务配置成功！","success");
                    reSet();
                } else {
                    $.messager.alert("提示", "删除服务配置失败！", "error");
                }
            } else {
                $.messager.alert("提示", "删除服务配置数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
            }  
        }
    });
}

//保存按钮触发
function saveCheck() {
    if (selOrgID == "") {
        $.messager.alert("提示","未选择组织机构，无法保存对应组织机构配置数据!");    
        return;
    }
    
    var venderCode = $("#venderCode").combobox("getValue");
    var signTypeCode = $("#signTypeCode").combobox("getValue");
    var caLocation = $("#caLocation").val();
    
    if (venderCode == "") {
        $.messager.alert("提示","厂商代码必填");
        return;
    }
    
    if (signTypeCode == "") {
        $.messager.alert("提示","签名方式必填");
        return;
    }
    
    if (caLocation == "") {
        $.messager.alert("提示","签名服务地址必填");
        return;
    }
    
    if (selRowSrvID != "") {
        $.messager.confirm("提示", "保存后会覆盖之前的配置数据，是否确认保存？", function (r) {
            if (r) {
                reSaveConfirm(venderCode,signTypeCode);
            }
        });
    } else {
        reSaveConfirm(venderCode,signTypeCode);
    }
}

//同一厂商同一签名方式校验
function reSaveConfirm(venderCode,signTypeCode) {
    var data = {
        action: "GET_VENDERSIGNTYPEISREG",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            venderCode: venderCode,
            signTypeCode: signTypeCode
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        if (json.data.isReg) {
            if (selRowSrvID !== json.data.id) { 
                $.messager.confirm("提示", "已存在同厂商同签名类型的配置数据，继续保存会覆盖之前的配置数据，是否继续保存？", function (r) {
                    if (r) {
                        CheckBeforeSave();
                    }
                });
            } else {
                CheckBeforeSave();
            }
        } else {
            CheckBeforeSave();
        }
    } else {
        $.messager.alert("提示", "获取厂商签名方式是否注册数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//保存前校验拓展参数配置
function CheckBeforeSave() {
    var flag = false;
    var signType = $("#signTypeCode").combobox("getValue");
    var tmpFunc = "";
    
    var isSetPatOption = (JSON.stringify(patSignOptionValue) != "{}")
    var isSetEsOption = (JSON.stringify(esOptionValue) != "{}")
    var isSetSignOptio = (JSON.stringify(signOptionValue) != "{}")

    if (signType == "PHONE") {
        if (isSetPatOption||isSetEsOption) {
            flag = true;
            tmpFunc = function() {
                reSetPatOption();
                reSetEsOption();
            }
        }
    } else if (signType == "ES") {
        if (isSetPatOption||isSetSignOptio) {
            flag = true;
            tmpFunc = function() {
                reSetPatOption();
                reSetSignOption();
            }
        }
    } else if (signType == "PATPDF") {
        if (isSetSignOptio||isSetEsOption) {
            flag = true;
            tmpFunc = function() {
                reSetSignOption();
                reSetEsOption();
            }
        }
    } else {
        if (isSetSignOptio||isSetEsOption||isSetPatOption) {
            flag = true;
            tmpFunc = function() {
                reSetEsOption();
                reSetSignOption();
                reSetPatOption();
            }
        }
    }
    if (flag) {
        $.messager.confirm("提示", "当前维护的拓展参数与选择的签名方式不符，相关拓展参数配置将会还原默认设置，是否继续保存？", function (r) {
            if (r) {
                tmpFunc();
                Save();
            }
        });
    } else {
        Save();
    }
}

//实际保存方法
function Save() {
    var obj = {
        organizationID:selOrgID,
        id: selRowSrvID,
        venderCode: $("#venderCode").combobox("getValue"),
        signTypeCode: $("#signTypeCode").combobox("getValue"),
        caVersion: $("#caVersion").combobox("getValue"),
        caLocation: $("#caLocation").val(),
        tsLocation: $("#tsLocation").val(),
        ciLocation: $("#ciLocation").val(), 
        vfLocation : $("#vfLocation").val(),
        siLocation : $("#siLocation").val(),
        appID: $("#appID").val(), 
        appKey: $("#appKey").val(), 
        appProjectID: $("#appProjectID").val(), 
        appOrgCode: $("#appOrgCode").val(),
        callBackUrl:$("#callBackUrl").val(),
        signOption:signOptionValue, 
        patSignOption: patSignOptionValue,
        esOption: esOptionValue
    };

    var data = {
        action: "SAVE_VENDERSERVICE",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            venderService: obj
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        if (json.data.success) {
            $.messager.alert("提示", "服务配置保存成功！","success");
            reSet();
        } else {
            $.messager.alert("提示", "服务配置保存失败！", "error");
        }
    } else {
        $.messager.alert("提示", "保存服务配置数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//加载已维护厂商信息
function initSrvGrid(selOrgID) {
    var param = {
        action: "GET_VENDERSERVICELIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };
    $("#dgSrv").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbSrv",
        url: ca_common_tools.getAppPath("GET_VENDERSERVICELIST"),
        queryParams: param,
        singleSelect:true,
        columns:[[
            {field:"id",title:"id"},
            {field:"venderCode",title:"签名厂商代码",align:"center",editor:"text"},
            {field:"signTypeCode",title:"签名类型代码",align:"center",editor:"text"},
            {field:"caLocation",title:"签名服务地址",width:300,align:"left",editor:"text"},
            {field:"tsLocation",title:"时间戳服务地址",width:300,align:"left",editor:"text"},
            {field:"ciLocation",title:"证书服务地址",width:280,align:"left",editor:"text"},
            {field:"vfLocation",title:"验签服务地址",width:300,align:"left",editor:"text"},
            {field:"siLocation",title:"签章服务地址",width:280,align:"left",editor:"text"}
        ]],        
        onDblClickRow:function(rowIndex,row){
        },
        onSelect:function(rowIndex,row){
            if (selRowIndex === rowIndex)
            {
                $("#dgSrv").datagrid("unselectRow",rowIndex);
                selRowSrvID = "";
                selRowIndex = "";
                resetServiceInfo();
            }
            else
            {
                selRowIndex = rowIndex; 
                selRowSrvID = row.id;
                setSrvInfo(row);
            }
        },
        onLoadError:function() {
            $.messager.alert("提示","厂商服务列表加载失败");
        },
        onLoadSuccess:function(data){
            $("#dgSrv").datagrid("clearSelections");
            selRowSrvID = "";
            selRowIndex = "";
        }
    });
}

//填充内容
function setSrvInfo(data) {
    $("#venderCode").combobox("select",data["venderCode"]);
    $("#signTypeCode").combobox("select",data["signTypeCode"]);
    $("#caVersion").combobox("select",data["caVersion"]);
    
    $("#caLocation").val(data["caLocation"]);
    $("#tsLocation").val(data["tsLocation"]);
    $("#ciLocation").val(data["ciLocation"]);
    $("#vfLocation").val(data["vfLocation"]);    
    $("#siLocation").val(data["siLocation"]);
    $("#callBackUrl").val(data["callBackUrl"]);

    $("#appID").val(data["appID"]);
    $("#appKey").val(data["appKey"]);
    $("#appProjectID").val(data["appProjectID"]);
    $("#appOrgCode").val(data["appOrgCode"]);
    
    signOptionValue = data["signOption"];
    setOptionHtmlInfo("signOption",signOptionValue);
    patSignOptionValue = data["patSignOption"];
    setOptionHtmlInfo("patSignOption",patSignOptionValue);
    esOptionValue = data["esOption"];
    setOptionHtmlInfo("esOption",esOptionValue);
}

//填充内容拓展项内容
function setOptionHtmlInfo(id,value) {
    var ret = JSON.stringify(value);
    if (ret.length > 60) {
        ret = ret.slice(0,60) + "......";
    }
    $("#"+id).html(ret);    
}

//拓展项维护页面
function setOption(input) {
    var url = "dhc.certauth.cfg.servreg."+input.toLowerCase()+".html?MWToken="+ca_common_tools.getMWToken();
    var content = '<iframe id="'+input+'Frame" scrolling="auto" frameborder="0" src="'+url+'" style="width:99%; height:99%;"></iframe>';
    var width = 1000;
    var height = 462;
    var name = "配置患者签名详细参数";
    var maximi = false;
    if (input == "signOption") {
        name = "配置医护签名详细参数";
        width = 600;
        height = 530;
    } else if (input == "esOption") {
        name = "配置医院签章详细参数";
        width = 1400;
        height = 600;
        maximi = true;
        var content = '<iframe id="'+input+'Frame" scrolling="auto" frameborder="0" src="'+url+'" style="width:100%; height:99%;"></iframe>';
    }
    createModalDialog(input+"Div",name,width,height,input+"Frame",content,"","",maximi,false);
}

//测试按钮触发-测试服务暂不实现
function testSrv() {
    if (selRowSrvID == "")         
    {
        $.messager.alert("提示","请先选择一条数据");
        return;
    }
   
    var venderCode = $("#venderCode").combobox("getValue");
    var signTypeCode = $("#signTypeCode").combobox("getValue");
    if ((signTypeCode == "UKEY")||(signTypeCode == "FACE"))
    {
        var url = "dhc.certauth.cfg.testukey.html?VenderCode="+ venderCode +"&SignTypeCode="+ signTypeCode +"&MWToken="+ ca_common_tools.getMWToken() +"&OrgID="+ selOrgID;
    }
    else if (signTypeCode = "PHONE")
    {
        var url = "dhc.certauth.cfg.testphone.html?VenderCode="+venderCode+"&SignTypeCode=PHONE&MWToken="+ca_common_tools.getMWToken()+"&OrgID="+selOrgID;
    }
    else
    {
        $.messager.alert("提示","所选签名方式暂不支持测试服务:"+SignTypeCode);
        return;
    }
    
    var xpwidth=window.screen.width-600;
    var xpheight=window.screen.height-100;
    var content = "<iframe id='testSrvFrame' scrolling='auto' frameborder='0' src='"+url+"' style='width:100%; height:99%;'></iframe>";
    createModalDialog("testSrv","测试CA服务",xpwidth,xpheight,"testSrvFrame",content,"","",true);
}