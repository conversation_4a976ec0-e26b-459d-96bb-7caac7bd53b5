<template>
  <svg aria-hidden="true" class="icon-svg">
    <use :xlink:href="icon" />
  </svg>
</template>

<script>
  export default {
    name: 'IconSvg',
    props: {
      name: {
        type: String,
        required: true
      }
    },
    computed: {
      icon() {
        return `#${this.name}`
      }
    }
  }
</script>
<style lang="scss">
  .icon-svg{
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }
</style>