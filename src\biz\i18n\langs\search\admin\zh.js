const t = {}

t.common = {}
t.common.belongMenu = '所属菜单'
t.common.enableStatus = '启用状态'
t.common.menuCode = '菜单代码'
t.common.menuName = '菜单名称'
t.common.btnCode = '按钮代码'
t.common.btnName = '按钮名称'
t.common.sort = '排序'
t.common.creator = '创建者'
t.common.createTime = '创建时间'
t.common.modifier = '修改人'
t.common.modifyTime = '修改时间'
t.common.modelName = '模型名称'
t.common.fieldCode = '字段编码'
t.common.fieldDataType = '字段数据类型'
t.common.isAnalyzer = '能否分析'
t.common.yes = '是'
t.common.no = '否'
t.common.count = '数量'
t.common.fieldName = '字段名称'
t.common.notDesensitized = '不脱敏'
t.common.nameDesensitized = '姓名脱敏'
t.common.idDesensitized = '身份证脱敏'
t.common.all = '全部'
t.common.maximum = '最大值'
t.common.minimum = '最小值'
t.common.firstTime = '首次'
t.common.lastTime = '末次'
t.common.comparison = '比较符'
t.common.equal = '等于'
t.common.contain = '包含'
t.common.containLike = '包含(like)'
t.common.regexp = '正则匹配'
t.common.greaterThan = '大于'
t.common.lessThan = '小于'
t.common.greaterEqual = '大于等于'
t.common.lessEqual = '小于等于'
t.common.notEqual = '不等于'
t.common.notContain = '不包含'
t.common.semanticInclusion = '语义包含'
t.common.multiple = '多值精确匹配(满足任意)'
t.common.multipleParentAll = '多值精确匹配(同时包含)'
t.common.multipleLike = '多值模糊匹配(满足任意)'
t.common.multipleParentAllLike = '多值模糊匹配(同时包含)'
t.common.null = '为空'
t.common.isNull = '非空'
t.common.enable = '是否启用'
t.common.welcome = '欢迎进入探索系统管理后台~'

t.common.save = '保存'
t.common.close = '关闭'
t.common.clearCache = '点击清理缓存'
t.common.add = '新增'
t.common.submit = '提交'
t.common.confirm = '确定'
t.common.cancel = '取消'
t.common.handle = '操作'
t.common.edit = '编辑'
t.common.delete = '删除'
t.common.query = '查询'
t.common.clear = '重置'
t.common.authBatch = '批量授权'
t.common.oneKeyAuth = '一键全部授权'
t.common.cancelAuthBatch = '批量取消授权'
t.common.cancelAuthAll = '全部取消授权'
t.common.searchAuth = '查询授权'
t.common.exportAuth = '导出授权'

// 路由
t.route = {}
t.route.home = '首页'
t.route.ESSupport = 'ES运维'
t.route.indexManage = '索引管理'
t.route.viewData = '数据查看'
t.route.dslSearch = 'DSL查询'
t.route.modelManage = '模型管理'
t.route.modelList = '模型列表'
t.route.tableManage = '主题管理'
t.route.fieldManage = '字段管理'
t.route.fieldRelation = '字段关系词映射'
t.route.exportConfig = '导出配置'
t.route.exportModelConfig = '导出模型类型配置'
t.route.authorManage = '授权管理'
t.route.paramsConfig = '检索参数配置'
t.route.caseDetailManage = '病历详情管理'
t.route.dictManage = '字典管理'
t.route.tag = '标签管理'
t.route.dslConfig = 'DSL查询配置'
t.route.terminologyManage = '术语集'
t.route.cache = '清理缓存'

// 提示消息
t.msg = {}
t.msg.needEnableStatus = '启用状态必填'
t.msg.remind = '提示'
t.msg.updateSuccess = '更新成功'
t.msg.cancelUpdate = '已取消更新'
t.msg.deleteSuccess = '删除成功'
t.msg.cancelDelete = '已取消删除'
t.msg.nothing = '暂无数据'
t.msg.selectPlease = '请选择'
t.msg.inputEsIndexName = '请输入ES索引名称'
t.msg.limitSpecialChar = '不允许输入空格等特殊符号'
t.msg.inputAliaName = '请输入ES索引别名'
t.msg.inputReplaceNum = '请输入副本数量'
t.msg.inputShardNumber = '请输入分片数量'
t.msg.saveSuccess = '保存成功!'
t.msg.inputFieldName = '请输入字段名称'
t.msg.cancelHandle = '取消操作'
t.msg.askUpdate = '是否确认修改'
t.msg.uploadSuccess = '上传成功'
t.msg.uploadFail = '上传失败,请稍后重试'

// 限制提示
t.limit = {}
t.limit.less16 = '不超过16个字符'
t.limit.less30 = '不超过30个字符'
t.limit.less32 = '不超过32个字符'
t.limit.less60 = '不超过60个字符'
t.limit.less64 = '不超过64个字符'
t.limit.less100 = '不超过100个字符'
t.limit.less128 = '不超过128个字符'
t.limit.less200 = '不超过200个字符'
t.limit.less512 = '不超过512个字符'

t.limit.lessNumber99 = '不超过99'

// 权限管理
t.authority = {}
t.authority.title = '权限管理'
t.authority.needTargetMenu = '目标菜单必填'
t.authority.needMenuName = '目标菜单名称必填'
t.authority.needBtnName = '目标按钮名称必填'
t.authority.assignPermission = '分配权限'
t.authority.assignForRole = '为角色分配权限'

// 角色管理
t.roleManage = {}
t.roleManage.title = '角色管理'
t.roleManage.roleCode = '角色编码'
t.roleManage.needRoleCode = '角色编码必填'
t.roleManage.roleName = '角色名称'
t.roleManage.needRoleName = '角色名称必填'
t.roleManage.assignForUser = '为用户分配角色'

// 用户管理
t.userManage = {}
t.userManage.title = '用户管理'
t.userManage.assignRole = '分配角色'
t.userManage.loginName = '登录名'
t.userManage.needLoginName = '登录名必填'
t.userManage.realName = '真实姓名'
t.userManage.needRealName = '真实姓名必填'
t.userManage.tel = '手机号'
t.userManage.pwd = '密码'
t.userManage.needPwd = '密码必填'
t.userManage.email = '用户邮箱'
t.userManage.remark = '备注'
t.userManage.lastLoginTime = '最后一次登陆时间'
t.userManage.lastLoginIP = '最后一次登陆IP'
t.userManage.pwdRemind = '初始密码：123456，请用户及时登录修改'
t.userManage.reFillLoginName = '登录名已存在,请重新输入!'
t.userManage.handleFail = '操作失败!'

// DSL查询
t.dslConfig = {}
t.dslConfig.title = 'DSL查询'
t.dslConfig.conditionDesc = '条件描述'
t.dslConfig.keyword = '关键字'
t.dslConfig.addDSLSearch = '新增DSL查询'
t.dslConfig.updateDSLSearch = '修改DSL查询'
t.dslConfig.searchName = '查询名称'
t.dslConfig.needSearchName = '查询名称不能为空'
t.dslConfig.dslQuery = 'DSL语句'
t.dslConfig.needDslQuery = 'DSL语句不能为空'

// ES运维
t.ESSupport = {}
t.ESSupport.queryMethod = '请求方式'
t.ESSupport.esIndex = 'ES索引'
t.ESSupport.indexMethod = '索引方法'
t.ESSupport.resetJson = '重置json'
t.ESSupport.checkJson = '验证json'
t.ESSupport.checkIndexMethod = '请检查ES索引方式'
t.ESSupport.checkSuccess = '验证成功'
t.ESSupport.checkJsonFormat = '请检查json格式'
t.ESSupport.updateDataCaution1 = '此操作将更新数据库中的值, 是否继续?'
t.ESSupport.getTemplateErr = '获取模板出错'
t.ESSupport.runDslErr = '执行dsl错误'
t.ESSupport.createIndex = '创建索引'
t.ESSupport.headPlugin = 'Head插件'
t.ESSupport.sensePlugin = 'Sense插件'
t.ESSupport.indexModelMap = '查看索引模型映射'
t.ESSupport.viewIndexModelMap = '查看索引模型映射'
t.ESSupport.createReIndexTask = '创建Reindex任务'
t.ESSupport.viewReIndexTaskList = '查看Reindex任务列表'
t.ESSupport.addTable = '新增数据表'
t.ESSupport.addField = '新增字段'
t.ESSupport.viewSetting = '查看setting'
t.ESSupport.viewMapping = '查看mapping'
t.ESSupport.indexName = '索引名称'
t.ESSupport.updateIndexModelInfo = '修改索引模型信息'
t.ESSupport.mainPieceNum = '主分片数量'
t.ESSupport.replicas = '副本数量'
t.ESSupport.countDocs = '文档数量'
t.ESSupport.deletedDocs = '删除文档数'
t.ESSupport.totalStoreSize = '存储大小'
t.ESSupport.primaryStoreSize = '主分片存储大小'
t.ESSupport.health = '索引状态'
t.ESSupport.caution1 = '您确定要解除索引该绑定关系吗？这样会导致该索引无法获取对应模型信息'
t.ESSupport.caution2 = '您确定要进行索引编辑吗？这样可能会导致该索引无法获取对应模型信息'
t.ESSupport.claim = '说明'
t.ESSupport.caution3 = '必须先在模型中进行表和字段的维护才能进行选择'
t.ESSupport.selectTable = '选择数据表'
t.ESSupport.selectField = '选择字段'
t.ESSupport.caution4 = 'ElasticSearch新增表后无法修改与删除，请确认字段名是否正确!'
t.ESSupport.caution5 = '当前暂无可选模型，请先创建模型'
t.ESSupport.selectDataModel = '选择数据模型'
t.ESSupport.esIndexName = 'ES索引名称'
t.ESSupport.aliaName = 'ES索引别名'
t.ESSupport.shardNumber = '分片数量'
t.ESSupport.toCreate = '去创建'
t.ESSupport.viewConfig = '查看配置'
t.ESSupport.viewMapping = '查看映射'
t.ESSupport.caution6 = '副本数量最小为0,最大为2'
t.ESSupport.caution7 = '分片数量最小为1,最大为21'
t.ESSupport.attention = '*注意'
t.ESSupport.caution8 = '必须先在模型中进行表的维护才能进行选择'
t.ESSupport.selectTablePlease = '请选择表'
t.ESSupport.caution9 = 'ElasticSearch新增表后无法修改与删除，请确认表名是否正确！'
t.ESSupport.reIndexList = 'Reindex列表'
t.ESSupport.viewDetail = '查看详情'
t.ESSupport.stopTask = '终止任务'
t.ESSupport.inputFieldCode = '请输入字段编码'
t.ESSupport.selectFieldType = '请选择字段类型'
t.ESSupport.selectIsAnalyzer = '请选择能否分析'
t.ESSupport.taskDDocTotal = '任务文档总数'
t.ESSupport.refreshDocCnt = "更新文档条数"
t.ESSupport.addDocCnt = "新增文档条数"
t.ESSupport.deleteDocCnt = "删除文档条数"
t.ESSupport.esTaskStartTime = "Elastic任务开始时间"
t.ESSupport.esTaskRunTime = "Elastic任务运行时间"
t.ESSupport.esTaskFinishFlag = "Elastic任务执行完成标识"
t.ESSupport.sourceDataIndex = '源数据索引'
t.ESSupport.targetIndex = '目标索引'
t.ESSupport.theme = '主题'
t.ESSupport.toAdmList = '前往就诊列表'
t.ESSupport.regno = '登记号'
t.ESSupport.inputRegno = '请输入登记号'
t.ESSupport.admNo = '就诊号'
t.ESSupport.inputAdmNo = '请输入就诊号'
t.ESSupport.claim2 = '说明：当前功能的目的是为了了解数据情况，最多只能部分患者数据, 如果需要查看全部数据，当前功能暂不支持'

// 模型管理
t.modelManage = {}
t.modelManage.admDate = '就诊日期字段'
t.modelManage.admDateTime = '就诊日期时间段'
t.modelManage.startDate = '开始日期'
t.modelManage.endDate = '结束日期'
t.modelManage.admDepart = '就诊科室字段'
t.modelManage.admDepartName = '就诊科室名称'
t.modelManage.enterConfirm = '请输入文本后按<回车>确认'
t.modelManage.selectField = '请先选择字段'
t.modelManage.askAuth1 = '此操作将授权全部字段, 是否继续?'
t.modelManage.authSuccess = '全部授权成功'
t.modelManage.askCancelAuth = '您确定要批量取消授权吗？'
t.modelManage.askCancelAuth2 = '此操作将取消已授权的所有表, 是否继续？'
t.modelManage.allCancelSuccess = '全部取消成功'
t.modelManage.tableName = '表名称'
t.modelManage.inputTableName = '请输入表名称'
t.modelManage.tableCode = '表编码'
t.modelManage.inputTableCode = '请输入表编码'
t.modelManage.selectTable = '请先选择表'
t.modelManage.askAuth2 = '此操作将授权所有表, 是否继续?'
t.modelManage.tableLevelAuth = '表级别授权'
t.modelManage.fieldLevelAuth = '字段级别授权'
t.modelManage.dataRangeAuth = '数据范围授权'
t.modelManage.selectRoleModel1st = '请先选择模型与角色'
t.modelManage.dictCateName = '字典分类名称'
t.modelManage.dictCateCode = '字典分类编码'
t.modelManage.isFuzzyMatch = '是否模糊匹配'
t.modelManage.selectDictFather = '请选择字典父类'
t.modelManage.inputName = '请填写名称'
t.modelManage.inputCode = '请填写编码'
t.modelManage.inputSort = '请填写排序'
t.modelManage.inputNumber = '请输入正整数'
t.modelManage.selectIsFuzzyMatch = '请选择是否支持模糊匹配'
t.modelManage.dictCateInfo = '字典分类信息'
t.modelManage.dictName = '字典名称'
t.modelManage.dictCode = '字典编码'
t.modelManage.selectDictCate1st = '请先选择左侧字典项'
t.modelManage.caution1 = '删除后会导致已绑定删除项字典的字段无法使用字典项匹配，删除后需要重新绑定字典，您确定要执行删除操作吗?'
t.modelManage.caution2 = '请先选择要删除的字典分类'
t.modelManage.dictItemManage = '字典项管理'
t.modelManage.name = '名称'
t.modelManage.needDictItemName = '字典项名称必填'
t.modelManage.itemCode = '项编码'
t.modelManage.needItemCode = '字典项编码必填'
t.modelManage.pyCapital = '首字母拼音'
t.modelManage.dictCateManage = '字典分类管理'
t.modelManage.syncDict = ' 同步字典'
t.modelManage.addDictCate1st = '请先新增字典分类'
t.modelManage.dictCate = '字典分类'
t.modelManage.paramsConfig = '参数配置'
t.modelManage.configName = '参数名称'
t.modelManage.configNameRequired = '参数名称必填'
t.modelManage.configKey = '参数编码'
t.modelManage.configKeyRequired = '参数编码必填'
t.modelManage.configDesc = '参数描述'
t.modelManage.configVal = '参数值'
t.modelManage.configValRequired = '参数值必填'

// 导出配置
t.exportConfig = {}
t.exportConfig.relateField = '关联字段'
t.exportConfig.isDefault = '是否默认'
t.exportConfig.selectRelateField = '请选择关联字段'
t.exportConfig.filterPropConfig = '过滤项属性配置'
t.exportConfig.selectTheme = '请选择主题'
t.exportConfig.filterProp = '过滤属性'
t.exportConfig.needFilterProp = '过滤属性必填'
t.exportConfig.filterCode = '过滤编码'
t.exportConfig.needFilterCode = '过滤编码必填'
t.exportConfig.typeCode = '类型编码'
t.exportConfig.exportTypeCode = '导出类型编码'
t.exportConfig.modelDesc = '模型描述'
t.exportConfig.inputModelName = '请输入模型名称'
t.exportConfig.inputExportTypeCode = '请输入导出类型编码'
t.exportConfig.inputModelDesc = '请输入模型描述'
t.exportConfig.exportSpecialVal = '导出特殊值'
t.exportConfig.exportSpecialValName = '导出特殊值名称'
t.exportConfig.relateFieldName = '关联字段名称'
t.exportConfig.comparisonValue = '比较值'
t.exportConfig.expression = '表达式'
t.exportConfig.timeField = '时间字段'
t.exportConfig.conditionField = '条件字段'
t.exportConfig.periodDesc = '阶段描述'
t.exportConfig.selectLeftTreeNode = '请先选择左侧树节点!'
t.exportConfig.obPeriodDesc = '观测阶段描述'
t.exportConfig.inputObPeriodDesc = '请输入观测阶段描述'
t.exportConfig.caution1 = '观测阶段时间范围的比较字段，例如，导出入院后24小时内的数据则时间字段可设置为“入院开始时间”'
t.exportConfig.caution2 = '观测阶段事件的比较字段，例如，导出服用复方甘草片后的数据则条件字段可设置为“药品通用名称”'
t.exportConfig.inputRelateField = '请输入关联字段'
t.exportConfig.inputSpecialValName = '请输入导出特殊值名称'
t.exportConfig.inputComparison = '请输入比较符'
t.exportConfig.obPeriodConfig = '观测阶段配置'
t.exportConfig.obFieldConfig = '观测字段配置'
t.exportConfig.exportFilterConfig = '导出项过滤配置'
t.exportConfig.exportSpecialValConf = '导出特殊值配置'
t.exportConfig.tabClaim = '页签说明'
t.exportConfig.obPeriodConfClaim = '观测阶段配置说明'
t.exportConfig.claim1 = '配置导出观测阶段的条件过滤字段和时间过滤字段'
t.exportConfig.obFieldConfConfClaim = '观测字段配置说明'
t.exportConfig.claim2 = '配置导出观测阶段相关主题用于作为时间范围过滤的字段'
t.exportConfig.specialValConfConfClaim = '特殊值配置说明'
t.exportConfig.claim3 = '配置导出阶段的相关主题特殊值选项维护'

// 字段管理
t.fieldManage = {}
t.fieldManage.themeId = '所属主题ID'
t.fieldManage.indexId = '索引ID'
t.fieldManage.enUSCode = '字段英文编码'
t.fieldManage.tmType = '脱敏类型'
t.fieldManage.tmTypeCode = '脱敏类型编码'
t.fieldManage.exportable = '是否可导出'
t.fieldManage.analyzable = '能否分析'
t.fieldManage.syncFieldSuggestion = '同步字段建议词'
t.fieldManage.selectFieldOnlyOne = '仅可选择一个字段'
t.fieldManage.syncSuccess = '同步成功'
t.fieldManage.isMultiple = '是否支持多值匹配'
t.fieldManage.isNegative = '是否支持阴阳性搜索'
t.fieldManage.searchable = '是否可检索'
t.fieldManage.domainName = '主体域名称'
t.fieldManage.cnNameRequired = '字段中文名称不能为空'
t.fieldManage.dataTypeRequired = '字段数据类型不能为空'
t.fieldManage.usCodeRequired = '字段英文编码不能为空'
t.fieldManage.fieldAttr = '字段属性'
t.fieldManage.participleType = '分词类型'
t.fieldManage.isDefaultExport = '是否默认导出'
t.fieldManage.allowQueryString = '是否支持全文检索'
t.fieldManage.boost = '权重'
t.fieldManage.bindDict = '绑定的字典'
t.fieldManage.bindTag = '绑定的标签'
t.fieldManage.selectField = '请选择字典'
t.fieldManage.undividedWord = '不分词'
t.fieldManage.structuredParticiple = '结构化分词'
t.fieldManage.selectParticipleType = '请选择分词类型'
t.fieldManage.selectIsNegative = '请选择是否支持阴阳性'
t.fieldManage.selectIsMultiple = '请选择是否多值匹配'
t.fieldManage.selectIsDefaultExport = '请选择是否默认导出'
t.fieldManage.selectAllowQueryString = '请选择是否支持全文检索'
t.fieldManage.selectTmType = '请选择脱敏类型'
t.fieldManage.inputBoost = '请输入权重'
t.fieldManage.regnoTm = '登记号脱敏'
t.fieldManage.phoneTm = '手机号脱敏'
t.fieldManage.emailTm = '电子邮箱脱敏'
t.fieldManage.addressTm = '地址脱敏'
t.fieldManage.allTm = '全部脱敏'
t.fieldManage.cnNameTm = '中文姓名脱敏'
t.fieldManage.fieldType = '字段类型'
t.fieldManage.selectTreeNode1st = '请先选择树节点!'
t.fieldManage.caution1 = '您确定要修改字段类型吗？修改过后阴阳性将会失效'
t.fieldManage.caution2 = '只有字段类型为【文本】时，才可以支持阴阳性'
t.fieldManage.caution3 = '请先选择数据'
t.fieldManage.caution4 = '此操作将永久删除该文件, 是否继续?'
t.fieldManage.caution5 = '必须大于0'
t.fieldManage.caution6 = '必须是整数'
t.fieldManage.isToAnalyzer = '请选择是否可用于在线分析'
t.fieldManage.selectDictPlease = '请选择字典'

// 字段关系
t.fieldRelation = {}
t.fieldRelation.relationType = '关系词类型'
t.fieldRelation.caution = '最大为100'
t.fieldRelation.inputNumber = '请输入数字！'

// 模型列表
t.modelList = {}
t.modelList.createModel = '创建模型'
t.modelList.editModel = '修改模型'
t.modelList.selectModelName = '请选择模型名称'
t.modelList.switchSelect = '切换选择'
t.modelList.switchInput = '切换输入'
t.modelList.modelCode = '模型编码'
t.modelList.indexAlias = '索引别名'
t.modelList.indexPrefix = 'ES索引前缀'
t.modelList.isSplitIndex = '采用分索引策略'
t.modelList.isSpecialDisease = '是否专病库模型'
t.modelList.isDefaultIndex = '是否默认模型'
t.modelList.onlyNumber = '仅限数字'
t.modelList.esIndexCodeRequired = 'ES索引编码不能为空'
t.modelList.modelNameRequired = '模型名称不能为空'
t.modelList.modelCodeRequired = '模型编码不能为空'
t.modelList.indexAliasRequired = '索引别名不能为空'
t.modelList.selectEnable = '请选择是否启用'
t.modelList.exportModel = '导出模型'
t.modelList.uploadModel = '上传模型'
t.modelList.syncData = '同步数据'
t.modelList.selectExportModel = '选择导出的模型'
t.modelList.confirmExport = '确认导出'
t.modelList.indexReplicasNum = '索引副本数量'
t.modelList.indexShardsNum = '索引分片数量'
t.modelList.isSplitIndex = '是否启用索引分片'
t.modelList.isSpecialDisease = '是否专病模型'
t.modelList.uploadCaution = '上传的文件只能是xls或者xlsx格式!'
t.modelList.selectModel1st = '请先选择模型再点击下载!'
t.modelList.title = '模型列表'
t.modelList.exportSuccess = '导出成功'
t.modelList.syncSuccess = '同步成功'
t.modelList.confirmDeleteModel = '确认删除该模型?'

// 表管理
t.tableManage = {}
t.tableManage.title = '表管理'
t.tableManage.themeName = '主题名称'
t.tableManage.inputThemeName = '请输入主题名称'
t.tableManage.themeCode = '主题编码'
t.tableManage.inputThemeCode = '请输入主题编码'
t.tableManage.themeBusinessCode = '主题域名称'
t.tableManage.inputThemeBusCode = '请输入主题域名称'
t.tableManage.tableLevel = '主题级别'
t.tableManage.selectTbLevel = '请选择表级别'
t.tableManage.level1 = '一级'
t.tableManage.level2 = '二级'
t.tableManage.level3 = '三级'
t.tableManage.themeParentName = '父主题'
t.tableManage.selectTbParent = '请选择父主题'
t.tableManage.noThemeParent = '无父主题'
t.tableManage.enable = '已启用'
t.tableManage.disable = '已禁用'

// 标签管理
t.tagManage = {}
t.tagManage.title = '标签管理'
t.tagManage.tagName = '标签名称'
t.tagManage.tagNameRequired = '标签名称必填'
t.tagManage.searchItemTag = '查询项标签'
t.tagManage.searchTempTag = '查询模板标签'
t.tagManage.exportTempTag = '导出模板标签'
t.tagManage.tagType = '标签类型'
t.tagManage.tagTypeRequired = '标签类型必填'

// 术语管理
t.terminology = {}
t.terminology.concept = '概念表'
t.terminology.semanticName = '语义化名称'
t.terminology.semanticNameRequired = '语义化名称必填'
t.terminology.semanticTag = '语义化标签'
t.terminology.semanticTagRequired = '语义化标签必填'
t.terminology.status = '状态'
t.terminology.statusRequired = '状态必填'
t.terminology.invalid = '无效'
t.terminology.effective = '有效'
t.terminology.termTable = '术语表'
t.terminology.relationTable = '关系表'
t.terminology.relationshipName = '关系词名称'
t.terminology.relationshipNameRequire = '关系词名称必填'
t.terminology.term = '术语名'
t.terminology.termRequired = '术语必填'
t.terminology.conceptId = '概念ID'
t.terminology.firstTerm = '首选术语'
t.terminology.pend1stTerm = '待定首选术语'
t.terminology.commonTerm = '普通术语'
t.terminology.termType = '术语类型'
t.terminology.termTypeRequired = '术语类型必填'
t.terminology.semanticDesc = '语义化描述'
t.terminology.releaseDate = '发布日期'

// 授权管理
t.authorManage = {}
t.authorManage.selectAdmDate = '请选择就诊日期字段'
t.authorManage.selectAdmDept = '请选择就诊科室字段'

// 患者详情管理
t.caseDetailManage = {}
t.caseDetailManage.title = '患者详情管理'
t.caseDetailManage.configName = '配置名称'
t.caseDetailManage.configCode = '配置编码'
t.caseDetailManage.configData = '患者详情配置器'
t.caseDetailManage.configType = '配置类型'
t.caseDetailManage.indexId = '模型'
t.caseDetailManage.indexIdRequire = '请选择模型'
t.caseDetailManage.configNameRequire = '请输入模型名称'
t.caseDetailManage.configCodeRequire = '请输入配置编码'
t.caseDetailManage.configTypeRequire = '请选择模型类型'

export default t
