/**
 * Created by ji<PERSON><PERSON><PERSON> on 16/11/18.
 */

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (('' + time).length === 10) time = parseInt(time) * 1000
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

export function formatTime(time, option) {
  time = +time * 1000
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

export function deepClone(source) {
  if (!source || typeof source !== 'object') {
    throw new Error('error arguments', 'shallowClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

// function padLeftZero(str) {
//   return ('00' + str).substr(str.length)
// }

/**
 *  生成uuid
 */
export function getUUID() {
  var d = new Date().getTime()
  if (window.performance && typeof window.performance.now === 'function') {
    d += performance.now() // use high-precision timer if available
  }
  var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = (d + Math.random() * 16) % 16 | 0
    d = Math.floor(d / 16)
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
  return uuid
}
// crypto 加解密
const CryptoJS = require('crypto-js') // 引用AES源码js

const key = CryptoJS.enc.Utf8.parse('1234123412ABCDEF') // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412') // 十六位十六进制数作为密钥偏移量

// 解密方法
export function Decrypt(word) {
  const encryptedHexStr = CryptoJS.enc.Hex.parse(word)
  const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
  const decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  const inside = decryptedStr.toString()
  const i = inside.substr(-32)
  return inside.split(i)[0]
}

// 加密方法
export function Encrypt(word) {
  const srcs = CryptoJS.enc.Utf8.parse(word + getUUID())
  const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
  return encrypted.ciphertext.toString().toUpperCase()
}
// 加密url
export function EncryptUrl(url) {
  if (!url) { return '' }
  if (url.indexOf('?') < 0) { return url }
  const [base_str, query_str] = url.split('?')
  const query = parseQuery(query_str)
  const new_query_str = Encrypt(query_str)
  let other = ''
  // 加密需要保留的字段
  const keepFields = ['isFromDataDetail', 'isFromAdvancedSearch', 'indexId']
  keepFields.forEach(field => {
    if (Object.keys(query).indexOf(field) >= 0) {
      other += `&${field}=${query[field]}`
    }
  })
  return `${base_str}?p=${new_query_str}${other}`
}
// 解密url参数
export function DecryptUrl(query_str) {
  const new_query_str = Decrypt(query_str)
  return parseQuery(new_query_str)
}
// 传入goreignID生成his入组加密参数的方法
export function makeHisEncryptParams(foreignId) {
  // 生成1-9的随机数
  const num = Math.floor(Math.random() * 9) + 1
  // 生成时间戳
  const timeStamp = new Date().getTime()

  // 生成num位的随机数
  const randomNumber = Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, num - 1))
  // 拼接出加密前的字符串 随机数位数 + 随机数 + 工号 + 时间戳 + 随机数
  const Str = `` + num + randomNumber + foreignId + timeStamp + randomNumber
  // 进行加密
  const sign = Encrypt(Str)

  return {
    foreignId: foreignId,
    timeStamp: timeStamp,
    sign: sign
  }
}
export function stringifyQuery(query) {
  return Object.keys(query).forEach.map(key => `${key}=${query[key]}`).join('&')
}
export function parseQuery(str) {
  const query = {}
  str.split('&').forEach(item => {
    const [key, value] = item.split('=')
    query[key] = value
  })
  return query
}

/**
 * 导出文件接口请求后的的公用方法
 * res 请求后返回的response
 * fileName 文件名
 */
export function downloadBlob(res, fileName) {
  const blob = new Blob([res.data], {
    type: 'application/vnd.ms-excel;charset=utf-8',
  })
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    // ie
    navigator.msSaveBlob(blob, fileName || '文件' + '.xlsx')
  } else {
    const link = document.createElement('a')
    // let blob = new Blob([res.data], {type: 'application/vnd.ms-excel'})
    link.style.display = 'none'
    link.href = URL.createObjectURL(blob)
    fileName && (link.download = fileName + '.xlsx') // 下载的文件名
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 下载文件
export function downloadFile(obj, name, suffix) {
  const url = window.URL.createObjectURL(new Blob([obj]))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  const fileName = parseTime(new Date()) + '-' + name + '.' + suffix
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export function isObjectFn(value) {
  return Object.prototype.toString.call(value) === '[object Object]'
}
export function isArrayFn(value) {
  if (typeof Array.isArray === 'function') {
    return Array.isArray(value)
  } else {
    return Object.prototype.toString.call(value) === '[object Array]'
  }
}
export function objToOne(obj) {
  // console.log(obj)
  const tmpData = {}
  for (const index in obj) {
    if (typeof obj[index] === 'object' && !isArrayFn(obj[index])) {
      const resObj = objToOne(obj[index])
      Object.assign(tmpData, resObj) // 这里使用对象合并
    } else {
      tmpData[index] = obj[index]
    }
  }
  return tmpData
}
// 保留两位小数
export function fixed(value) {
  var intVal = null
  if (typeof value === 'string' && /^[0-9.]+$/.test(value)) {
    intVal = parseInt(value)
  }
  if (typeof value === 'number') {
    intVal = value
  }
  if (intVal == null) {
    return value
  } else {
    return (Math.round(value * 100) / 100).toFixed(2)
  }
  }

/**
 * 检查下载接口返回内容是否错误
 * @param {*} response
 * @returns
 */
export function dealErrorByReader(response) {
  return new Promise((resolve, reject) => {
    if (response.headers['content-type'] && response.headers['content-type'].includes('json')) {
      // 此处拿到的data才是blob
      const { data } = response
      const reader = new FileReader()
      reader.onload = () => {
        const { result } = reader
        const errorInfos = JSON.parse(result)
        const { msg } = errorInfos
        reject(msg)
      }
      reader.onerror = (err) => {
        reject(err)
      }
      reader.readAsText(data)
    } else {
      resolve(response)
    }
  })
}

/**
 * 结构并格式化下载接口返回数据文件
 * @param {*} res
 * @param {*} type 接受处理的文件响应类型
 */
export function BlobDownLoad(res, type) {
  let blob = new Blob([res.data], {
    type: type
  })
  const fileName = decodeURIComponent(res.headers['content-disposition'].split('filename=')[1])
  const href = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.style.display = 'none'
  a.href = href
  a.download = fileName
  a.click()
  a.remove()
  URL.revokeObjectURL(a.href)
}
