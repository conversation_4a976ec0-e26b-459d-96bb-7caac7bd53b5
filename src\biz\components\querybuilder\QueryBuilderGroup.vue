<template>
  <div class="rules-group-container">
    <div class="rules-group-body">
      <rules-group-logical :show-logical="logicalStyleObject.showLogical" :logical-operator="curLogical"
        :logical-style-object="logicalStyleObject" @change-logical="toggleLogical" />
      <hos-button v-if="!logicalStyleObject.showLogical" :disabled="queryBuilderObj.disabled" @click="addRule">
        {{ $t('添加') }}
      </hos-button>
    </div>
    <div class="rules-list">

      <component :is="child.subRules && child.subRules.length>0 ? 'query-builder-sub-group' : child.type" v-for="(child, i) in query.children" :key="i" ref="ruleItem" :type="child.type"
        :allow-del-first="allowDelFirst" parent-type="query-builder-group" :query.sync="child.query"
        :parent-query.sync="computedParentQuery" :rule-types="ruleTypes" :rules="rules" :index="i" :parent-index="index"
:max-depth="maxDepth"
        :depth="depth + 1" :field-data="fieldData" :cate-items="cateItems"
        :current-group-index="currentGroupIndex" :current-condition-index="currentConditionIndex" @child-add-requested="addChildAfter"
        @child-deletion-requested="removeChild" @sub-group-add-requested="addSubGroupAfter" @group-add-requested="addGroupAfter"
        @calculateOffset="calculateOffset" @autoComSelect="autoComSelect"
        @change-to-group="changeToGroup" @change-to-rule="changeToRule"
        @open-search-dialog="openSearchDialog"
        @delFirst="$emit('delFirst')" @update-parent-query="updateParentQueryBySub" />
    </div>
  </div>
</template>

<script>
  import QueryBuilderRule from "./QueryBuilderRule.vue"
  import QueryBuilderSubGroup from "./QueryBuilderSubGroup.vue"
  import RulesGroupLogical from "./RulesGroupLogical.vue"
  import {deepClone} from "@/utils/index.js"
  import {
    relationMap2, filterTypeList
  } from "./mapData"

  const defaultQuery = {
    logical: "AND",
    children: [{
      type: "query-builder-rule",
      query: {
        rule: null,
        fieldItemInfo: {},
        relative: null,
        value: null
      }
    }]
  }

  const defaultChildQuery = {
    type: "query-builder-rule",
    query: {
      rule: null,
      fieldItemInfo: {},
      relative: null,
      value: null
    }
  }

  export default {
    name: "QueryBuilderGroup",

    components: {
      QueryBuilderRule,
      QueryBuilderSubGroup,
      RulesGroupLogical
    },
    /* eslint-disable */
    props: [
      "ruleTypes",
      "type", "parentType", "query", "parentQuery",
      "rules", "index", "parentIndex", "maxDepth",
      "depth", "allowDelFirst", "singleSelectCate", "fieldData", "cateItems",
      "currentGroupIndex", "currentConditionIndex"
    ],
    /* eslint-enable */
    inject: {
      "queryBuilderObj": {
        from: 'queryBuilderObj'
      },
      allTypeRelativesObj: {
          from: "allTypeRelativesObj",
          default: () => {}
      }
    },
    data() {
      return {
        relationMap2,
        filterTypeList,
        // showLogical: true,
        showAddBtn: false,
        // logicalStyleObject: {
        //   top: "5px",
        //   left: this.index < 0 ? "-10px" : '0px'
        // },
        children: [],
        computedParentQuery: this.query,
      }
    },
    computed: {
      hasMultipleRule() {
        if (this.query) {
          return this.query.children ? this.query.children.length > 1 : false
        } else {
          return false
        }
      },
      curLogical() {
        return this.query ? this.query.logical : "AND"
      },
      logicalStyleObject() {
        const res = {
          showLogical: true,
          top: "5px",
          left: this.index < 0 ? "-10px" : '0px'
        }
        const query = this.query
        if (query && query.children && query.children.length > 0) {
          res.showLogical = true
          res.top = this.getTopOffset(query) + "px"
        } else {
          res.showLogical = false
        }
        return res
      }
    },
    watch: {
      query: {
        deep: true,
        handler: function(val) {
          this.computedParentQuery = val
        }
      },
      computedParentQuery(val) {
        this.$emit("update:query", val)
      }
    },
    created() {
      // 初始化时计算topOffset
      // console.log('created-index', this.index)
      // console.log('created-calculateOffset', JSON.stringify(this.query, null, '\t'))
      // this.calculateOffset(this.query)
    },
    methods: {
      updateParentQueryBySub(updatedquery) {
        this.computedParentQuery = updatedquery
        this.$emit("update:query", updatedquery)
      },
      toggleLogical() {
        const updated_query = deepClone(this.query)
        updated_query.logical = updated_query.logical === "AND" ? "OR" : "AND"
        this.$emit("update:query", updated_query)
      },
      addRule() {
        this.addChildAfter()
      },
      getTopOffset(query) {
        // console.log('getTopOffset', query)
        let topH = 5 - 20
        if (query && query.children && query.children.length > 0) {
          // 单个条件相加top
          const oneH = 20
          // 循环子节点获取总top
          query.children.forEach(c => {
            if (c.query.children && c.query.children.length > 0) {
              topH += this.getTopOffset(c.query)
              topH += 20
            } else if (c.subRules && c.subRules.length > 0) {
              topH += 20 * (c.subRules.length + 1)
            } else {
              topH += oneH
            }
          })
        }
        return topH
      },
      // 计算逻辑词的偏移量
      calculateOffset(query) {
        // console.log('calculateOffset',query)
        // if (query && query.children && query.children.length > 0) {
        //   this.showLogical = true
        //   this.logicalStyleObject.top = this.getTopOffset(query) + "px"
        // } else {
        //   this.showLogical = false
        // }
        // console.log(this.logicalStyleObject.top,'top')
      },
      addGroup() {
        this.addGroupAfter()
      },
      addChildAfter(index) {
        // console.log('group-addChildAfter')
        let updated_query = deepClone(this.query)
        const child = deepClone(defaultChildQuery)
        if (this.isNotBlank(index)) {
          // 在指定index后面追加
          updated_query.children.splice(index + 1, 0, child)
        } else {
          // 如果没有条件，初始化
          if (!updated_query || this.isBlank(updated_query)) {
            updated_query = defaultQuery
            updated_query.children = []
          }

          // 在当前组最后一个条件后面追加一个
          updated_query.children.push(child)
        }

        this.$emit("update:query", updated_query)
        // 计算逻辑连接词的位置
        this.calculateOffset(updated_query)
      },
      addGroupAfter(index) {
        // console.log('addGroupAfter',index, JSON.stringify(this.query,null,'\t'))
        let updated_query = deepClone(this.query)
        if (this.depth < this.maxDepth) {
          const group = {
            type: "query-builder-group",
            query: {
              logical: "AND",
              // 默认添加分组时插入一个值
              children: [{
                type: "query-builder-rule",
                maxDepth: this.maxDepth,
                depth: this.depth,
                query: {
                  rule: null,
                  fieldItemInfo: {},
                  relative: null,
                  value: null
                }
              }]
            }
          }
          if (index >= 0) {
            // 在指定index后面追加
            updated_query.children.splice(index + 1, 0, group)
          } else {
            // 如果没有条件，初始化
            if (!updated_query) {
              updated_query = defaultQuery
              updated_query.children = []
            }

            updated_query.children.push(group)
          }
          this.$emit("update:query", updated_query)
          // 计算逻辑连接词的位置
          // this.calculateOffset(updated_query)
        }
      },
      changeToGroup(index) {
        const updated_query = deepClone(this.query)
        if (this.depth < this.maxDepth && index >= 0) {
          const group = {
            type: "query-builder-group",
            query: {
              logical: "AND",
              // 默认添加分组时插入一个值
              children: [updated_query.children[index]]
            }
          }
          // 将指定index替换成group
          updated_query.children.splice(index, 1, group)
          this.$emit("update:query", updated_query)
        }
      },
      changeToRule(index) {
        const updated_query = deepClone(this.query)
        // 将当前组按index的rule进行拆分，小于index和大于index两组，当前rule提升一级，与前面两组平级
        const beforeChildren = []
        const afterChildren = []
        let currentChildren = null
        updated_query.children.forEach((item, i) => {
          if (i < index) {
            beforeChildren.push(item)
          } else if (i === index) {
            currentChildren = item
          } else if (i > index) {
            afterChildren.push(item)
          }
        })

        const new_groups = []
        if (beforeChildren.length > 0) {
          new_groups.push({
            type: "query-builder-group",
            query: {
              logical: updated_query.logical,
              children: beforeChildren
            }
          })
        }
        new_groups.push(currentChildren)
        if (afterChildren.length > 0) {
          new_groups.push({
            type: "query-builder-group",
            query: {
              logical: updated_query.logical,
              children: afterChildren
            }
          })
        }
        // 更新父query
        const updated_parent_query = deepClone(this.parentQuery)
        updated_parent_query.children.splice(this.index, 1, ...new_groups)
        this.$emit("update:parent-query", updated_parent_query)
      },
      addSubGroupAfter(index) {
        // console.log('addSubGroupAfter')
        const updated_query = deepClone(this.query)
        const subRules = [deepClone(defaultChildQuery)]
        const cur_child_query = deepClone(updated_query.children[index])
        cur_child_query.subRules = subRules
        updated_query.children.splice(index, 1, cur_child_query)
        this.$emit("update:query", updated_query)
        // 计算逻辑连接词的位置
        this.calculateOffset(updated_query)
      },
      // 删除分组
      removeGroup() {
        // console.log('removeGroup',this.index)
        this.$emit("child-deletion-requested", this.index)
      },

      removeChild(index) {
        // console.log('removeChild',index,JSON.stringify(this.query,null,'\t'))
        const updated_query = deepClone(this.query)
        if (!updated_query) {
          return
        }
        // 判断是否可以删除，最后一个条件不允许删除
        if (updated_query.children &&
          updated_query.children.length === 1 &&
          this.depth === 1) {
          const curC = updated_query.children[0]
          if (!curC.query.children || curC.query.children.length === 0) {
            if (!this.allowDelFirst) {
              return
            } else {
              this.$emit('delFirst')
            }
          }
        }
        updated_query.children.splice(index, 1)
        // 如果剩下的children仅有一个item，且item的类型是query-builder-group，则将此item的层级提高一级
        if (updated_query.children.length === 1 && updated_query.children[0].type === 'query-builder-group') {
          updated_query.children = updated_query.children[0].query.children
          updated_query.logical = 'AND'
        }

        // 如果条件组没有子条件，则删除条件组
        if (updated_query.children.length === 0) {
          if (this.depth === 1) {
            if (this.allowDelFirst) {
              // 移出最后一个
              // this.showLogical = false
              this.$emit("update:query", updated_query)
              this.$emit('delFirst')
            } else {
              return
            }
          } else {
            this.removeGroup()
          }
          this.removeGroup()
        } else {
          if (updated_query.children.length === 1) {
            updated_query.logical = "AND"
          }
          // console.log('111')
          this.calculateOffset(updated_query)
          this.$emit("update:query", updated_query)
        }
      },
      // 查询项自动补全下拉选中
      autoComSelect(index, gIndex, curItem) {
        const cgIndex = gIndex == null ? this.index : gIndex
        this.updateChild(index, cgIndex, curItem)
      },
      // 打开查询项弹出框
      openSearchDialog(index, gIndex, subIndex, typeId) {
        // console.log('openSearchDialog',index,gIndex,this.index)
        // 注意： 这里如果有多层条件， 会执行多次
        const curGroupIndex = gIndex === undefined ? this.index : gIndex

        // // 第一个参数为当前条件在自己分组中的index, 第二个参数为当前条件组的index
        // if (this.query.children[gIndex] && this.query.children[gIndex].type === 'query-builder-sub-group') {
        //   this.$emit("open-search-dialog", index, curGroupIndex)
        //   return
        // }

        // if (this.query.children[index] && this.query.children[index].type === 'query-builder-rule') {
        //   curGroupIndex = index;
        // }
        this.$emit("open-search-dialog", index, curGroupIndex, subIndex, typeId)
      },
      /**
       * @param groupIndex 当前条件在所在条件组中的index
       * @param conditionIndex 条件index, 如果有条件组，则条件组的index
       */
      async updateChild(groupIndex, conditionIndex, curItem, subIndex) {
        const curFieldInfo = curItem.fieldInfo ? curItem.fieldInfo : {}
        curFieldInfo.searchItemId = curItem.searchItemId
        const dataTypeMap = this.allTypeRelativesObj.idDataMap
        let curDataType = dataTypeMap[curFieldInfo.datatype] ? dataTypeMap[curFieldInfo.datatype].code : "string"
        // 是否绑定字典
        let selectOptions = []
        if (curItem.fieldInfo && curItem.fieldInfo.dictId) {
          // curDataType = "select"
          const dictId = curItem.fieldInfo.dictId
          // 绑定下拉的值
          this.$api('biz.search.web.dictionary.getSubDictItem',[dictId]).then(res => {
            const options = res.data[dictId]
            if (options) {
                selectOptions = options.map(item => {
                return {
                  label: item.dictItemName,
                  data: item.dictItemName
                }
              })
            } else {
              selectOptions = []
            }
            curFieldInfo["selectOptions"] = selectOptions
            this.doUpdateChild(curItem, curFieldInfo, conditionIndex, groupIndex, curDataType, subIndex)
          })
        }
        this.doUpdateChild(curItem, curFieldInfo, conditionIndex, groupIndex, curDataType, subIndex)
      },
      doUpdateChild(curItem, curFieldInfo, conditionIndex, groupIndex, curDataType, subIndex) {
        const updated_query = deepClone(this.query)
        let curOhterFieldInfo = {}
        let otherValue = null
        if (curItem.searchFieldOtherId && curItem.searchFieldOtherId > 0) {
          curOhterFieldInfo = curItem.otherFieldInfo
          otherValue = curItem.searchFieldOtherValue
        }

        // } else {
        if (groupIndex === -1) {
            updated_query.children.forEach((citem, ci) => {
              if (ci === conditionIndex) {
                // let query = citem.query
                this.setItem(citem, curFieldInfo, curDataType, curOhterFieldInfo, otherValue, subIndex)
              }
            })
        } else {
          updated_query.children.forEach((item, index) => {
            if (index === groupIndex) {
              if (item.type === 'query-builder-rule') {
                this.setItem(item, curFieldInfo, curDataType, curOhterFieldInfo, otherValue, subIndex)
              }

              if (item.query.children) {
                item.query.children.forEach((citem, ci) => {
                  if (ci === conditionIndex) {
                    // let query = citem.query
                    this.setItem(citem, curFieldInfo, curDataType, curOhterFieldInfo, otherValue, subIndex)
                  }
                })
              }
            }
          })
        }
        this.$emit("update:query", updated_query)
      },
      setItem(item, curFieldInfo, curDataType, curOhterFieldInfo, otherValue, subIndex) {
        if (subIndex > 0) {
          item = item.subRules[subIndex - 1]
        }
        item.query.fieldItemInfo = curFieldInfo
        item.query.inputType = curDataType
        if (curFieldInfo.isNegative) {
          const operators = this.filterTypeList || []
          item.query.relative = operators.length > 0 ? operators[0].code : ""
        } else {
          const operators = this.allTypeRelativesObj.idRelativesMap[curFieldInfo.datatype] || []
          item.query.relative = operators.length > 0 ? relationMap2[operators[0].relativeType].code : ""
        }
        item.query.value = ""
        item.otherFieldItemInfo = curOhterFieldInfo
        item.otherValue = otherValue
      },
      valid() {
        let flg = false
        const ruleArr = this.$refs.ruleItem
        if (ruleArr && ruleArr instanceof Array) {
          ruleArr.forEach(item => {
            if (item.valid()) {
              flg = true
            }
          })
        }

        return flg
      },
      clearValid() {
        const ruleArr = this.$refs.ruleItem
        if (ruleArr && ruleArr instanceof Array) {
          ruleArr.forEach(item => {
            if (item.clearValid) {
              item.clearValid()
            } else {
              item.hasError = false
              item.fieldError = false
              item.relativeError = false
              item.valueError = false
            }
          })
        }
      }
    }
  }

</script>
