
export const queryListApi = (params) => {
  return {
    url: 'edc/form-group/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/form-group/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/form-group/info/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/form-group/insert',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/form-group/update',
    method: 'post',
    data
  }
}

// 分页获取指定组合id下表单列表
export const queryListByGroupId = (params) => {
  return {
    url: 'edc/form-group/get-relate-form',
    method: 'get',
    params
  }
}

// 应用组合模板到项目
export const applyAllGroupForm = (params) => {
  return {
    url: `edc/subject-form/apply-all-in-group`,
    method: 'post',
    params,
  }
}

