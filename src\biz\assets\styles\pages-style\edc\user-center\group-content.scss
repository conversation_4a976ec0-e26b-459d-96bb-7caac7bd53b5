.user-center-group-content {
	// border: 1px solid #EBEEF5;
	border-radius: 4px;
	.border-W {
		border-right: 3px solid #ebeef5;
	}

	.m-b-10 {
		margin-bottom: 10px;
	}

	.f-w-b {
		color: #666666;
		font-size: 16px;
		font-weight: bolder;
	}

	.role-text {
		color: #666666;
		font-size: 13px;
	}

	.p-l-30 {
		padding-left: 30px;
	}

	.pointer {
		cursor: pointer;
	}

	.w-50 {
		width: 50%;
	}
	.card-content {
		position: relative;
		overflow: visible;
	}
	.card-content :hover {
		background-color: rgba(242, 242, 242, 1);
		.item-lock {
			color: #666666;
		}
		.item-close {
			color: #f56c6c;
		}
		.item-edit {
			color: #409eff;
		}
		.item-audit {
			color: #67c23a;
		}
	}
	.enabled-content {
		cursor: pointer;
	}
	.name-label {
		display: flex;
		justify-content: space-between;
		.name-label-content {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}

	.float-l {
		float: left;
	}

	.audit-label {
		min-width: 50px;
		color: #00cc66;
		font-size: 14px;
		float: right;
		margin-left: 10px;
		cursor: pointer !important;
	}
	.fail-label {
		min-width: 50px;
		color: #cc0000;
		font-size: 14px;
		float: right;
		margin-left: 10px;
	}
	.diasble-content {
		.f-w-b {
			color: #999;
		}
		cursor: not-allowed;
		background-color: #f2f2f2;
	}
	.patcount {
		font-size: 20px;
		color: #409eff;
		margin-right: 5px;
	}
	.item-lock {
		position: absolute;
		right: 60px;
		top: -9px;
		color: transparent;
		background-color: transparent !important;
		z-index: 50;
		font-size: 18px;
		cursor: pointer;
	}
	.item-close {
		position: absolute;
		right: 10px;
		top: -9px;
		color: transparent;
		background-color: transparent !important;
		z-index: 50;
		font-size: 18px;
		cursor: pointer;
	}
	.item-edit {
		position: absolute;
		right: 35px;
		top: -9px;
		color: transparent;
		background-color: transparent !important;
		z-index: 50;
		font-size: 18px;
		cursor: pointer;
	}
	.item-audit {
		position: absolute;
		right: 60px;
		top: -9px;
		color: transparent;
		background-color: transparent !important;
		z-index: 50;
		font-size: 18px;
		cursor: pointer;
	}
}
