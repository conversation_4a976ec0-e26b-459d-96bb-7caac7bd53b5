
export const queryListApi = (params) => {
    return {
        url: 'search/es-theme-metadata/page',
        method: 'GET',
        params,
    }
}

export const queryDetailApi = (params) => {
    return {
        url: 'search/es-theme-metadata/detail',
        method: 'GET',
        params
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'search/es-theme-metadata/deletion',
        method: 'POST',
        data
    }
}

export const updateApi = (data) => {
    return {
        url: 'search/es-theme-metadata/update',
        method: 'post',
        data
    }
}

export const getThemeTree = () => {
    return {
        url: 'search/es-theme-metadata/tree',
        method: 'GET',
    }
}

export const getIndexTree = () => {
  return {
      url: 'search/es-index-metadata/index-tree',
      method: 'GET',
  }
}

export const addApi = (data) => {
    return {
        url: 'search/es-theme-metadata/insert',
        method: 'POST',
        data
    }
}

export const getThemePropertyTree = (params) => {
    return {
        url: 'search/es-index-metadata/index-property-tree',
        method: 'get',
        params
    }
}

export const dataSyncInit = (data) => {
    return {
        url: '/search/es-index-metadata/init-data',
        method: 'post',
        data
    }
}

export const getSyncStatus = (data) => {
    return {
        url: '/search/es-index-metadata/init-data-status',
        method: 'get',
        data
    }
}


