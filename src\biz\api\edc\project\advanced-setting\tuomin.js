export const pageListApi = (params) => {
    return {
        url: 'edc/project/tuo-min/page',
        method: 'GET',
        params
    }
}

export const addApi = (data) => {
    return {
        url: 'edc/project/tuo-min/insert',
        method: 'POST',
        data
    }
}

export const addBatchApi = (data) => {
    return {
        url: 'edc/project/tuo-min/insert/batch',
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: 'edc/project/tuo-min/update',
        method: 'POST',
        data
    }
}

export const deleteApi = (data) => {
    return {
        url: `edc/project/tuo-min/deletion`,
        method: 'POST',
        data
    }
}

export const getDisCateItems = (params) => {
    return {
        url: `edc/subject-form/tree`,
        method: 'GET',
        params
    }
}