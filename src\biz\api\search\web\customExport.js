export const fileSearchApi = (data) => {
    return {
        // url: 'search/export-custom/search',
        url: '/search/export-custom/search-by-file',
        method: 'post',
        data
    }
}

export const searchApi = (params) => {
    return {
        url: 'search/export-custom/search',
        method: 'get',
        params
    }
}

export const addToTemplate = (data) => {
    return {
        url: '/search/export-custom/insert-upload',
        method: 'post',
        data
    }
}

export const getSelectFields = (params) => {
    return {
        url: '/search/export-custom/read-by-id',
        method: 'get',
        params
    }
}