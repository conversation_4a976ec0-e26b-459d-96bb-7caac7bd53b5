// 数据概览
@import "./homePage/index.scss";

// 全文检索
@import "./full-text-search/index.scss";
@import "./full-text-search/FullSearchPage.scss";
@import "./full-text-search/SearchResult.scss";
@import "./full-text-search/FilterBox.scss";

// 高级检索
@import "./advanced-search/index.scss";
@import "./advanced-search/AdvancedFilterBox.scss";
@import "./advanced-search/query-cond-view.scss";
@import "./advanced-search/AdvancedResultList.scss";
@import "./advanced-search/PatFilterDialog.scss";
@import "./advanced-search/SearchHistory.scss";
@import "./advanced-search/SearchTemplate.scss";
@import "./advanced-search/statistics/index.scss";

// components
@import "./components/Export.scss";
@import "./components/ExportPeriodItem.scss";
@import "./components/ExportFilterDialog.scss";
@import "./components/ExportSettingDialog.scss";

// 就诊时间轴
@import "./admTimeLine/index.scss";
.app-container-tabs.hos-tabs{
  .hos-tabs__content {
      overflow: inherit;
      padding:0;
  }
}
.app-container-tabs-btn {
  position: relative;

  >.righticon {
    position: absolute;
    right: 10px;
    top: 0px;
    line-height: 40px;
  }
}

.q-logical {
  color: #409eff;

  &.or {
    color: #ffd54f;
  }

  // color: #fff;
  // font-weight: 900;
  padding: 0 2px;
  display: inline-block;
  margin: 0 5px;
}

.q-field,
.q-relative,
.q-value {
  margin: 0 2px;
}

.q-relative {
  font-weight: 900;
}
