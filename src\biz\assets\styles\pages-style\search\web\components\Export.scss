// @import "@/assets/styles/variables.scss";
// @import "@/assets/styles/mixins/mixins.scss";
.export-dialog {
  .hos-dialog__body {
    padding: 0;
    padding-bottom: 10px;
  }
  .box {
    margin-top: 20px;
    padding: 5px 10px;
    background: #fff;
  }
  .m-b-10 {
    margin-bottom: 10px;
  }
  .m-l-10 {
    margin-left: 10px;
  }
  // @include res(xs) {
  //   .box {
  //     margin: $container-margin-xs;
  //   }
  // }

  // @include res(sm) {
  //   .box {
  //     margin: $container-margin-sm;
  //   }
  // }

  // @include res(md) {
  //   .box {
  //     margin: $container-margin-md;
  //   }
  // }

  .option-wrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    .options-item {
      width: 18%;
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      cursor: pointer;
      // box-shadow: 1px 2px 2px 0 rgb(217, 212, 212);
      box-shadow: 8px 8px 25px 0px #f2f2f2,
                  -8px -8px 25px 0px #f2f2f2;
      // 鼠标悬浮放大
      transform: scale(1);
      transition: transform 300ms ease-in-out;
      padding-bottom: 10px;
      &:hover {
        // box-shadow: 5px 5px 5px 0 rgb(199, 196, 196);
        box-shadow: 8px 8px 25px 0px rgba(199, 196, 196, 0.8),
                    -8px -8px 25px 0px rgba(255, 255, 255, 0.5);
        // 鼠标悬浮放大
        transform: scale(1.05);
      }

      .item-head {
        width: 100%;
        height: 160px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: linear-gradient(to right, #64b9fa, #6497fa);
        color: #fff;
        margin-bottom: 10px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        .item-head-title {
          margin: 5px 0;
        }
        i {
          font-size: 40px;
        }
        span {
          font-size: 14px;
        }
      }

      .hos-step__title {
        font-size: 14px;
        color: #666666;
      }
      .hos-step__head {
        border-color: #409eff;
      }
      .hos-step.is-vertical .hos-step__title {
        margin-top: -4px;
      }
      .hos-step__head.is-finish {
        color: #409eff;
        border-color: #409eff;
      }
      .hos-step__icon {
        background-color: #409eff;
        border: 2px solid #409eff;
        width: 25px;
        height: 25px;
      }
      .hos-step.is-horizontal .hos-step__head.is-finish>.hos-step__icon.is-text, .hos-step.is-horizontal .hos-step__head.is-success>.hos-step__icon.is-text, .hos-step.is-vertical .hos-step__head.is-finish>.hos-step__icon.is-text {
        // background-color: #409eff;
        background: linear-gradient(to right, #64b9fa, #6497fa);
      }
      .hos-step__head.is-finish .hos-step__line {
        background-color: #dae2ee;
        height: calc(100% - 15px);
        left: 3px;
        top: 15px;

        .hos-step__line-inner  {
          border-color: #dae2ee;
        }
      }
    }
  }

  .export-record {
    width: 100%;
  }
}
