import Vue from "vue"

const dropVar = {
    state: {
        allDropVarMap: {}, // 当前页面所有拖入的变量
    },
    mutations: {
        UPDATE_DROP_VAR: (state, data) => {
          Vue.set(state.allDropVarMap, data.uid, data.varList)
        },
        DELETE_DROP_VAR: (state, data) => {
          if (state.allDropVarMap[data]) {
            delete state.allDropVarMap[data]
          }
        },
        RESET_DROP_VAR: (state) => {
          state.allDropVarMap = {}
        },
    },
    actions: {
        updateDropVar({ commit }, data) {
          commit('UPDATE_DROP_VAR', data)
        },
        deleteDropVar({ commit }, data) {
          commit('DELETE_DROP_VAR', data)
        },
        resetDropVar({ commit }) {
          commit('RESET_DROP_VAR')
        },
    }
}

export default dropVar
