﻿$(function() {
    $("#btnSaveOption").click(function() {
        //var qrcodeTypeValue = $("#qrcodeType").combobox("getValue");
        var qrcodeNeedUserCodeValue = $("#qrcodeNeedUserCode")[0].checked == true?"1":"0";
        var enablePinLogonValue = $("#enablePinLogon")[0].checked == true?"1":"0";
        var enablePushSignValue = $("#enablePushSign")[0].checked == true?"1":"0";
        var scanQRRollIntervalValue = $("#scanQRRollInterval").val();
        var scanQRRollMaxValue = $("#scanQRRollMax").val();
        var appNameValue = $("#appName").val();

        var optionValue = {
            appName: appNameValue,
            enablePinLogon :enablePinLogonValue,
            enablePushSign: enablePushSignValue,
            //qrcodeType : qrcodeTypeValue,
            qrcodeNeedUserCode : qrcodeNeedUserCodeValue,
            scanQRRollInterval: scanQRRollIntervalValue,
            scanQRRollMax: scanQRRollMaxValue
        }

        parent.signOptionValue = optionValue;
        parent.setOptionHtmlInfo("signOption",optionValue),
        parent.closeDialog("signOptionDiv");
    });

    $("#btnCancel").click(function() {
        parent.closeDialog("signOptionDiv");
    });
    setOption(parent.signOptionValue);
});

function setOption(signOption) {
    //$("#qrcodeType").combobox("clear");
    if (JSON.stringify(signOption) == "{}") return;

    //var qrcodeTypeSet = signOption.qrcodeType || "";
    var qrcodeNeedUserCodeSet = signOption.qrcodeNeedUserCode || "";
    var enablePinLogonSet = signOption.enablePinLogon || "";
    var enablePushSignSet = signOption.enablePushSign || "";
    var appNameSet = signOption.appName || "";
    var scanQRRollIntervalSet = signOption.scanQRRollInterval || "";
    var scanQRRollMaxSet = signOption.scanQRRollMax || "";

    //$("#qrcodeType").combobox("select",qrcodeTypeSet);

    if (qrcodeNeedUserCodeSet == "1")
        $("#qrcodeNeedUserCode").checkbox("check");

    if (enablePinLogonSet == "1")
        $("#enablePinLogon").checkbox("check");

    if (enablePushSignSet == "1")
        $("#enablePushSign").checkbox("check");

    $("#appName").val(appNameSet);
    $("#scanQRRollInterval").val(scanQRRollIntervalSet);
    $("#scanQRRollMax").val(scanQRRollMaxSet);
}