// EDC同步ES——获取配置
export const edc_sync_es_getConfig = (params) => {
    return {
        url: `/hos/config/map-by-code`,
        method: 'get',
        params
    }
}

// EDC同步ES——保存配置
export const edc_sync_es_saveConfig = (data) => {
    return {
        url: `/hos/config/update-values`,
        method: 'post',
        data
    }
}

// 敏感患者白名单-获取白名单作用范围
export const sensitive_func_range = () => {
    return {
        url: `/search-white-list/scope`,
        method: 'get'
    }
}

// 分页查询敏感患者报名单过滤配置
export const sensitive_pageList = (params) => {
    return {
        url: `/search-white-list/page`,
        method: 'get',
        params
    }
}

export const sensitive_detailApi = (id) => {
    return {
        url: `/search-white-list/detail/${id}`,
        method: 'get',
        params
    }
}

export const sensitive_addApi = (data) => {
    return {
        url: `/search-white-list/insert`,
        method: 'post',
        data
    }
}

export const sensitive_deleteApi = (data) => {
    return {
        url: `/search-white-list/deletion`,
        method: 'post',
        data
    }
}