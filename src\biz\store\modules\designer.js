// 大屏设计器缓存
const designer = {
    state: {
        allComponentLinkage: []
    },
    mutations: {
        SET_ALL_COMPONENT_LINKAGE: ( state, params ) => {
            var { index = -1, widgetId = '', linkageArr } = params
            try {
                console.log( 'params---', params )
                linkageArr = linkageArr.map( item => {
                    const arr = item.widgetValue.split( '-$-' )
                    return {
                        originId: widgetId,
                        targetId: arr[0],
                        targetName: arr[1],
                        paramsConfig: item.paramsConfig
                    }
                } )
            } catch ( error ) {
                linkageArr = [] // 兼容异常错误导致页面加载不出来
            }
            state.allComponentLinkage[index] = {
                index: +index,
                widgetId,
                linkageArr
            }
        }
    },
    actions: {
        /**
         * @description 设置
         * @param {Object} state vuex state
         * @param {Object} modelData modelData
         */
        setAllComponentLinkage( {
            state,
            dispatch,
            rootState
        }, dictItem ) {
            state.dictMap[dictItem.key] = dictItem.list
        },
        clearAllComponentLinkage( {
            state,
            dispatch,
            rootState
        } ) {
            state.allComponentLinkage = []
        },
        getAllComponentLinkage( {
            state,
            dispatch,
            rootState
        } ) {
            return state.allComponentLinkage || null
        },
    }
}

export default designer