<!--  不支持箭头函数和模版字符串 -->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" type="text/css" href="../static/hisui/css/hisui.css" />
  <link rel="stylesheet" type="text/css" href="../static/table.css" />
  <script src="../static/hisui/js/jquery-1.8.0.min.js"></script>
  <script type="text/javascript" src="../static/hisui/js/jquery.hisui.min.js"></script>
</head>

<body>
  <!-- <div>log-table: <strong class="log"></strong></div> -->
  <div id="tt2" class="hisui-tabs">
    <!-- 数据集管理 -->
    <div title="数据集管理" style="padding:20px;">
      <form>
        <table class="dataTable">
          <tr>
            <td class="dataTd">数据集来源</td>
            <td>
              <div style="margin-left: 6px;">
                <select id="dataSourceSelect" class="hisui-combobox" style="width: 300px;" name="data_source"
                  data-options="enterNullValueClear:false,blurValidValue:true">
                  <option value="reflect">反射调用数据集</option>
                  <!-- <option value="api">API接口数据集</option>
                <option value="3">查询管理数据集</option>
                <option value="4">JavaBean数据集</option>
                <option value="5">json数据集</option> -->
                </select>
              </div>
            </td>
          </tr>
          <tr>
            <td class="dataTd required-label">类名</td>
            <td><input id="dataSourceClassName" type="text" name="class_name" class="textbox hisui-validatebox"
                style="width: 294px; margin-left: 6px;" />
            </td>
          </tr>
          <tr>
            <td class="dataTd required-label">方法名</td>
            <td><input id="dataSourceMethodName" type="text" class="textbox hisui-validatebox"
                style="width: 294px; margin-left: 6px;" />
            </td>
          </tr>
        </table>
      </form>
    </div>

    <!-- 属性配置 -->
    <div title="属性配置" style="padding:20px;">
      <div id="attributeHead" class="row">
        <a href="javascript:void(0)" class="hisui-linkbutton" data-options="iconCls:'icon-add'"
          onclick="openCreateDataSource(); return false;">新增数据对象</a>
        <a href="javascript:void(0)" class="hisui-linkbutton" data-options="iconCls:'icon-w-import'"
          onclick="importDataSource(); return false;" style="margin-left: 6px;">函数导入</a>
      </div>
      <!-- <span class="icon-big-insert-table"
        style="display:inline-block;width: 30px;height: 30px;background-size: 100% 100%;"></span> -->
      <div id="tableContent"></div>
    </div>

    <!-- 打印配置 -->
    <div title="打印配置" style="padding:20px;">
      <table id="printConfig">
        <tbody class="printTable">
          <tr>
            <td class="l-label">
              <span style="color: red;">*</span>是否取打印机默认纸张
            </td>
            <td class="fs0 row">
              <div style="margin-right: 20px; margin-left: 8px;"><input class="hisui-radio" type="radio" label="是"
                  name="useDefault" value="true"></div>
              <input class="hisui-radio" type="radio" label="否" name="useDefault" value="false">
            </td>
          </tr>

          <tr>
            <td class="l-label">
              <span style="color: red;">*</span>是否套打
            </td>
            <td class="fs0 row" style="margin-left: 8px;">
              <div style="margin-right: 20px;">
                <input class="hisui-radio" type="radio" label="是" name="Overlay" value="true">
              </div>
              <input class="hisui-radio" type="radio" label="否" name="Overlay" value="false">
            </td>
          </tr>

          <tr>
            <td class="l-label"><span style="color: red;">*</span>是否清除空行
            </td>
            <td class="fs0 row" style="margin-left: 8px;">
              <div style="margin-right: 20px;"><input class="hisui-radio" type="radio" label="是" name="clearBlank"
                  value="true"></div>
              <input class="hisui-radio" type="radio" label="否" name="clearBlank" value="false">
            </td>
          </tr>

          <tr id="totalPagesTr">
            <td class="l-label">总页数</td>
            <td class="row" style="margin-left: 8px;"><input id="totalPages" type="text" style="width: 94px;"
                class="textbox restrictedInput" /><button class="get-value-btn button-ml">取值</button></td>
          </tr>

          <tr id="currentPagesTr">
            <td class="l-label">当前页数</td>
            <td class="row" style="margin-left: 8px;"><input id="currentPages" type="text"
                class="textbox restrictedInput" style="width: 94px;" /><button
                class="get-value-btn button-ml">取值</button></td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- <div title="调试" style="padding:20px;">
      <div>
        <input type="text" name="" id="debuggerText" style="width: 400px;">
        <button id="debuggerBtn">调试log</button>
      </div>
      <span class="logText"></span>
    </div> -->
  </div>

  <!-- 弹窗--新增数据对象 -->
  <div id="createDataDialog" class="hisui-dialog" style="width:400px;height:350px;padding:20px;" data-options="modal:true,closed:true,closable:false,buttons:[{
      text:'取消',
      handler:function(){$HUI.dialog('#createDataDialog').close();}
    },{
      text:'保存',
      handler:function(){ dataDialogHandle()}
    }]">
    <label class="row"><span style="width: 60px; text-align: right; letter-spacing: 2px;"
        class="required-label">编码</span><input type="text" class="textbox input-code hisui-validatebox"
        data-options="required:true,validType:'length[0,75]', missingMessage:'请输入不超过 75 个字符的内容'"
        style="width: 218px; margin-left: 6px;"></label>
    <label class="row" style="margin-top: 10px;"><span style="width: 60px; text-align: right; letter-spacing: 2px;"
        class="required-label">名称</span><input type="text" class="textbox input-name hisui-validatebox"
        style="width: 218px; margin-left: 6px;"
        data-options="required:true,validType:'length[0,37]', missingMessage:'请输入不超过 37 个字符的内容'"></label><br>
    <label class="row" id="dataTypeLabel"><span style="width: 60px; text-align: right; letter-spacing: 2px;">类型</span>
      <div style="margin-left: 6px;">
        <select class="hisui-combobox select-type" style="width: 226px;">
          <option value="main">表单数据</option>
          <option value="sub">列表数据</option>
        </select>
      </div>
    </label>
  </div>

  <!-- 弹窗--图片设置 -->
  <div id="picSettingDialog" class="hisui-dialog" style="width:630px;height:350px;padding:10px;" title="图片设置"
    data-options="modal:true,closed:true,closable:false,buttons:[{
        text:'取消',
        handler:function(){$HUI.dialog('#picSettingDialog').close();}
      },{
        text:'保存',
        handler:function(){ savePicSetting()}
      }]">
    <div class="row">
      <div class="align-right w100 marginRight8">图片类型</div>
      <select class="hisui-combobox pictureType" style="width: 166px;">
        <option value="6">png</option>
        <option value="5">jpeg</option>
      </select>
    </div>
    <div class="row marginTop10">
      <div class="row">
        <div class="w100 align-right marginRight8">左上角坐标</div>
        <div class="row">
          <input type="text" class="leftUpPost textbox restrictedInput" value="" style="width: 94px">
          <button class="button-ml pic-set-get-value-button" style="width: 58px" data-type="left">取值</button>
        </div>
      </div>
      <div class="row" style="margin-left: 40px;">
        <div class="w100 align-right marginRight8">右下角坐标</div>
        <div class="row">
          <input type="text" class="rightDownPost textbox restrictedInput" value="" style="width: 94px">
          <button class="button-ml pic-set-get-value-button" style="width: 58px" data-type="right">取值</button>
        </div>
      </div>
    </div>
    <div class=" row marginTop10">
      <div class="row">
        <div class="w100 align-right marginRight8">上边距</div><input type="text" class="textbox upMargin hisui-numberbox"
          value="" style="width: 160px">
      </div>
      <div class="row" style="margin-left: 40px;">
        <div class="w100 align-right marginRight8">下边距</div><input type="text"
          class="downMargin textbox hisui-numberbox" value="" style="width: 160px">
      </div>
    </div>
    <div class="row marginTop10">
      <div class="row">
        <div class="w100 align-right marginRight8">左边距</div><input type="text"
          class="textbox leftMargin hisui-numberbox" value="" style="width: 160px">
      </div>
      <div class="row" style="margin-left: 40px;">
        <div class="w100 align-right marginRight8">右边距</div><input type="text"
          class="rightMargin textbox hisui-numberbox" value="" style="width: 160px">
      </div>
    </div>
  </div>

  <!-- 弹窗--函数导入 -->
  <div id="importDialog" class="hisui-dialog " style="width:760px;height:400px;padding:10px;" title="函数导入" data-options="modal:true,closed:true,closable:false,buttons:[{
      text:'取消',
      handler:function(){$HUI.dialog('#importDialog').close();}
    },{
      text:'确定',
      handler:function(){ saveImportFun()}
    }]">
    <div class="row">
      <div class="row">
        <div class="marginRight8" style="letter-spacing: 6px;">类名</div><input id="importClassName" type="text"
          class="textbox" value="" style="width: 230px">
      </div>
      <div class="row" style="margin-left: 120px;">
        <div class="align-right w60 marginRight8">方法名</div><input id="importMethodName" type="text" class="textbox"
          value="" style="width: 230px">
      </div>
    </div>
    <div style="margin-top: 20px; display: flex; flex-direction: row; overflow-y: scroll;">
      <!-- <div class="w90 align-right">参数配置</div> -->
      <table class="importTable">
        <thead>
          <tr>
            <th>参数名</th>
            <th>参数值</th>
            <th style="width: 80px;"><button onclick="createImportField()">新增</button></th>
          </tr>
        </thead>
        <tbody id="importTbody"></tbody>
      </table>
    </div>
  </div>

  <!-- 弹窗--新增属性 -->
  <div id="createAttrDialog" class="hisui-dialog" style="width:630px;height:350px;padding:10px;" data-options="modal:true,closed:true,closed:false,closable: false,buttons: [{
          text: '取消',
          handler: function () { $HUI.dialog('#createAttrDialog').close(); }
        }, {
          text: '保存',
          handler: function () { attrDialogHandle() }
        }]">

    <div class="row">
      <label class="row">
        <div class="align-right w100"><span class="red">* </span>编码</div>
        <input type="text" class="textbox input-code" name="input-code" style="width: 160px; margin-left: 6px;">
      </label>

      <label class="row marginLeft40">
        <div class="align-right w100"><span class="red">* </span>名称</div>
        <input type="text" class="textbox input-name " name="input-name" style="width: 160px; margin-left: 6px;">
      </label>
    </div>

    <div class="row marginTop10">
      <div class="row">
        <div class="align-right w100"><span class="red">* </span>数据类型</div>
        <div class="row marginLeft6">
          <div style="margin-right: 20px;">
            <input class="hisui-radio" type="radio" label="数据" value="1" name="dataType" data-options="required:true">
          </div>
          <input class="hisui-radio" type="radio" label="图片" value="2" name="dataType" data-options="required:true">
        </div>
      </div>

      <div class="row select-group" style="margin-left: 90px;">
        <div class="align-right w100"><span class="red">* </span>是否分组</div>
        <div class="row marginLeft6">
          <div style="margin-right: 34px;">
            <input class="hisui-radio" type="radio" label="是" name="isGroup" value="true" data-options="required:true">
          </div>
          <input class="hisui-radio" type="radio" label="否" name="isGroup" value="false" checked
            data-options="required:true">
        </div>
      </div>
    </div>

    <div class="picInAttr">
      <div class="row marginTop10">
        <div class="align-right w100">图片类型</div>
        <div class="marginLeft6">
          <select class="hisui-combobox pictureType" style="width: 166px;">
            <option value="6">png</option>
            <option value="5">jpeg</option>
          </select>
        </div>
      </div>

      <div class="row marginTop10">
        <div class="row">
          <div class="align-right w100">左上角坐标</div>
          <div class="row marginLeft6">
            <input type="text" style="width: 94px" class="leftUpPost textbox restrictedInput" value="">
            <button class="button-ml pic-set-get-value-button" style="width: 58px" data-type="left">取值</button>
          </div>
        </div>
        <div class="row marginLeft40">
          <div class="w100 align-right">右下角坐标</div>
          <div class="row marginLeft6">
            <input type="text" style="width: 94px" class="rightDownPost textbox restrictedInput" value="">
            <button class="button-ml pic-set-get-value-button" style="width: 58px" data-type="right">取值</button>
          </div>
        </div>
      </div>

      <div class="row marginTop10">
        <div class="row">
          <div class="w100 align-right">上边距</div><input type="text" class="textbox upMargin hisui-numberbox" value=""
            style="width: 160px; margin-left: 6px;">
        </div>
        <div class="row marginLeft40">
          <div class="w100 align-right">下边距</div><input type="text" class="textbox downMargin hisui-numberbox" value=""
            style="width: 160px; margin-left: 6px;">
        </div>
      </div>

      <div class="row marginTop10">
        <div class="row">
          <div class="w100 align-right">左边距</div><input type="text" class="textbox leftMargin hisui-numberbox" value=""
            style="width: 160px; margin-left: 6px;">
        </div>
        <div class="row marginLeft40">
          <div class="w100 align-right">右边距</div><input type="text" class="rightMargin textbox hisui-numberbox" value=""
            style="width: 160px; margin-left: 6px;">
        </div>
      </div>
    </div>
  </div>

  <script src="./generateElement.js"></script>
  <script src="./inputChange.js"></script>

  <script>
    var windowParent = window.parent // 父组件全局数据
    var currentCreateTableType = 'main'; // 当前点击【新建】的表类型
    var currentCreateTableIndex = 0; // 当前点击【新建】的表index
    var currentTrIndex = 0  // 当前点击图片【设置】的tr index
    var attrDialogHandle = saveCreateAttr // 属性操作-保存方法
    var dataDialogHandle = saveCreateDataSource  // 数据对象操作-保存方法

    $(document).ready(function () {
      if (windowParent.dev) renderDom()
      $("#createAttrDialog").dialog("close");
      // 函数导入【删除】操作
      $('#importTbody').on('click', '.deleteImportAttrBtn', function () {
        $(this).closest('tr').remove();
      });

      // 【编辑】数据对象
      $(document).on("click", ".edit-table", function () {
        let dataCode = ''
        let dataName = ''
        dataDialogHandle = saveEditDataSource

        currentCreateTableType = $(this).data("type");
        if (currentCreateTableType === 'sub') {
          currentCreateTableIndex = $(this).closest(".subTable").index('.subTable');
          dataCode = window.parent.globalConfig.param.listData[currentCreateTableIndex].dataCode
          dataName = window.parent.globalConfig.param.listData[currentCreateTableIndex].dataName
        } else {
          currentCreateTableIndex = $(this).closest(".mainTable").index('.mainTable');
          dataCode = window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataCode
          dataName = window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataName
        }

        $("#createDataDialog").dialog({
          title: '编辑数据对象',
          onOpen: function () {
            // 在 Dialog 打开时设置蒙层样式
            const dialogContainer = $('#tt2');
            const dialogOverlay = $('.window-mask');

            // 重置蒙层样式
            dialogOverlay.css({
              position: 'fixed',
              top: 0,
              left: 0,
              width: 100000,
              height: 100000,
              zIndex: 1000 // 确保在 Dialog 之上
            });

            // 重置button置底
            const dialogContent = $('.dialog-button')
            dialogContent.css({
              position: 'absolute',
              bottom: '0',
              width: '100%'
            });
          }
        });

        // 回显
        $('#createDataDialog .input-code').val(dataCode).validatebox("validate")
        $('#createDataDialog .input-name').val(dataName).validatebox("validate")
        $('#createDataDialog h2').text('编辑数据对象')
        $('#dataTypeLabel').hide()
        $('#createDataDialog .btn-save').attr("data-type", "editData") // 保存btn标记为编辑模式
        $('#createDataDialog .btn-save').data("type", "editData")
        $HUI.dialog('#createDataDialog').open()
      });

      // 弹窗【保存】
      $('.btn-save').click(function () {
        const type = $(this).data("type")
        const handleMap = {
          createData: saveCreateDataSource,
          editData: saveEditDataSource,
          picSetting: savePicSetting,
          createAttr: saveCreateAttr,
          editAttr: saveEditAttr,
          importFun: saveImportFun
        }

        handleMap[type]()
      });

      // 【分组】切换--更新 isGroup 的值
      $('#tableContent').on('click', '.radioYes, .radioNo', function () {
        const isGroup = $(this).val() === 'true'; // 更新 isGroup 的值
        const trIndex = $(this).closest("tr").index()

        if ($(this).closest('.subTable').length > 0) {
          currentCreateTableType = 'sub'
          currentCreateTableIndex = $(this).closest(".subTable").index('.subTable');
          window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[trIndex].isGroup = isGroup
        } else {
          currentCreateTableType = 'main'
          currentCreateTableIndex = $(this).closest(".mainTable").index('.mainTable');
          window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[trIndex].isGroup = isGroup
        }
      });

      // 【取值】--打印配置
      $('#printConfig').on('click', '.get-value-btn', function () {
        const formatCellAddress = utilFormatCellAddress() || ''

        if ($(this).closest('#totalPagesTr').length > 0) {
          $('#totalPages').val(formatCellAddress)
          window.parent.globalConfig.printConfig.totalPage = formatCellAddress
        } else {
          $('#currentPages').val(formatCellAddress)
          window.parent.globalConfig.printConfig.currPage = formatCellAddress
        }
      });

      // 【取值】按钮的点击事件 -- 图片设置坐标
      $('.pic-set-get-value-button').click(function () {
        try {
          const formatCellAddress = utilFormatCellAddress() || 'G,10'
          const type = $(this).data("type")

          if ($(this).closest('#picSettingDialog').length > 0) {
            if (type === 'left') {
              $('#picSettingDialog .leftUpPost').val(formatCellAddress);
            } else {
              $('#picSettingDialog .rightDownPost').val(formatCellAddress);
            }
          } else {
            if (type === 'left') {
              $('#createAttrDialog .leftUpPost').val(formatCellAddress);
            } else {
              $('#createAttrDialog .rightDownPost').val(formatCellAddress);
            }
          }
        } catch (error) {
          alert(JSON.stringify(error))
        }
      })

      // 图片类型隐藏分组
      $HUI.radio("[name='dataType']", {
        onChecked: function (e, value) {
          // 图片类型隐藏分组
          if (currentCreateTableType === 'sub') {
            $('#createAttrDialog .select-group').toggle($(e.target).attr("value") !== '2');
          }
          $('#createAttrDialog .picInAttr').toggle($(e.target).attr("value") === '2');
        }
      });

      // 禁止input输入，支持删除剪切(未能屏蔽中文字符)
      $(document).on('keydown', '.restrictedInput', function (e) {
        if (e.key === 'Backspace' || e.key === 'Delete' || e.key === 'Cut') {
          // 执行删除或剪切操作的自定义方法
          const inputValue = $(this).val();
          windowParent.Celladdress = inputValue
          const siblingButton = $(this).siblings('button');
          if (siblingButton.length > 0) {
            siblingButton.trigger('click');
          }
        } else {
          e.preventDefault();
        }
      });

      // 禁止input输入，补充屏蔽了中文字符
      $(document).on('input', '.restrictedInput', function () {
        const inputValue = $(this).val();
        if (/[\u4e00-\u9fa5]/.test(inputValue)) {
          $(this).val(inputValue.replace(/[\u4e00-\u9fa5]/g, '')); // 移除中文字符
        }
      });

      $('#debuggerBtn').click(function () {
        const val = $('#debuggerText').val()
        const data = eval(val)
        $('.logText').text(JSON.stringify(data))
      })
    })

    function renderDom () {
      try {
        // const globalConfig = window.parent.globalConfig
        const globalConfig = !windowParent.dev ? window.parent.globalConfig : { "id": "3102889cd08f84f183935f79293848f6", "code": "batch_voucher_demo", "fileId": "7a1d7d4f90784928d068582da26ac071", "dataSource": { "sourceType": "", "className": "hosPrintDemoServiceImpl", "methodName": "createBatchVoucherData" }, "param": { "mainData": [{ "dataCode": "mainObj", "dataName": "附加信息", "dataType": "1", "detailStartRow": null, "detailEndRow": null, "dataAttribute": [{ "fieldCode": "printDate", "fieldName": "打印日期", "fieldType": "data", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "G,10" }, { "fieldCode": "bookKeeper", "fieldName": "记账人", "fieldType": "2", "fieldImgExt": { "pictureType": "5", "leftUpPost": "C,17", "rightDownPost": "D,19", "upMargin": null, "downMargin": null, "leftMargin": null, "rightMargin": null }, "isGroup": null, "position": "" }] }], "listData": [{ "dataCode": "detailObj", "dataName": "附加数据", "dataType": "2", "detailStartRow": 12, "detailEndRow": 14, "dataAttribute": [{ "fieldCode": "code", "fieldName": "预算编码", "fieldType": "1", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "B,12" }, { "fieldCode": "name", "fieldName": "预算名称", "fieldType": "1", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "C,12" }, { "fieldCode": "type", "fieldName": "预算类别", "fieldType": "1", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "D,12" }, { "fieldCode": "person", "fieldName": "预算经办人", "fieldType": "1", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "E,12" }, { "fieldCode": "usedAmount", "fieldName": "预算已使用额度", "fieldType": "1", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "F,12" }, { "fieldCode": "totalAmount", "fieldName": "预算总额度", "fieldType": "1", "fieldImgExt": { "pictureType": "", "leftUpPost": "", "rightDownPost": "", "upMargin": 0, "downMargin": 0, "leftMargin": 0, "rightMargin": 0 }, "isGroup": false, "position": "G,12" }] }] }, "printConfig": { "isOverPrint": false, "isClearNull": true, "isDefPaper": false, "totalPage": "G,19", "currPage": "G,18" } }
        const param = globalConfig.param
        const dataSource = globalConfig.dataSource
        const printConfig = globalConfig.printConfig
        if (!globalConfig) return

        if (windowParent.dev) window.parent.globalConfig = globalConfig

        if (param) {
          convertToNodes(globalConfig)
        }

        // 数据集管理配置
        if (dataSource) {
          $('#dataSourceClassName').val(dataSource.className).validatebox("validate")
          $('#dataSourceMethodName').val(dataSource.methodName).validatebox("validate")
        }

        // 打印
        if (printConfig) {
          if (printConfig.isClearNull) {
            $HUI.radio("[name='clearBlank'][value='true']").setValue(true);
          } else {
            $HUI.radio("[name='clearBlank'][value='false']").setValue(true);
          }

          if (printConfig.isOverPrint) {
            $HUI.radio("[name='Overlay'][value='true']").setValue(true);
          } else {
            $HUI.radio("[name='Overlay'][value='false']").setValue(true);
          }

          if (printConfig.isDefPaper) {
            $HUI.radio("[name='useDefault'][value='true']").setValue(true);
          } else {
            $HUI.radio("[name='useDefault'][value='false']").setValue(true);
          }



          $('#totalPages').val(printConfig.totalPage)
          $('#currentPages').val(printConfig.currPage)
        }
      } catch (error) {
        alert('renderDom' + JSON.stringify(error))
      }
    }

    // 【删除】主表数据对象操作
    function deleteMainTable (button) {
      currentCreateTableIndex = $(button).closest(".mainTable").index();
      window.parent.globalConfig.param.mainData.splice(currentCreateTableIndex, 1)
      $(button).closest('.mainTable').remove();
    }

    // 【删除】从表数据对象操作
    $('#tableContent').on('click', '.subTableDelete', function () {
      const index = $(this).closest('.subTable').index() - 1

      window.parent.globalConfig.param.listData.splice(index, 1)
      $(this).closest('.subTable').remove();
    });

    // 【删除】表格属性操作
    $('#tableContent').on('click', '.deleteAttrBtn', function () {
      const trIndex = $(this).closest("tr").index()
      const tableIndex = $(this).closest("table").index() - 1

      if (tableIndex > 0) { // 从表
        window.parent.globalConfig.param.listData[tableIndex - 1].dataAttribute.splice(trIndex, 1)
      } else {
        window.parent.globalConfig.param.mainData[tableIndex].dataAttribute.splice(trIndex, 1)
      }
      $(this).closest('tr').remove();
    });

    // 打开弹窗--新增数据对象
    function openCreateDataSource () {
      dataDialogHandle = saveCreateDataSource

      $("#createDataDialog").dialog("setTitle", "新增数据对象");
      $('#dataTypeLabel').show()

      // init
      $('#createDataDialog .input-code').val('')
      $('#createDataDialog .input-name').val('')
      $('#createDataDialog .select-type').val('main')

      $('#createDataDialog .btn-save').attr("data-type", "createData") // 重置为新建，和编辑共用一个弹窗
      $('#createDataDialog .btn-save').data("type", "createData")
      $HUI.dialog('#createDataDialog').open()
    }

    // 打开弹窗--新增数据属性
    function openCreateTableField (button) {
      attrDialogHandle = saveCreateAttr

      $("#createAttrDialog").dialog({
        title: '新建属性',
        onOpen: function () {
          // 在 Dialog 打开时设置蒙层样式
          const dialogContainer = $('#tt2');
          const dialogOverlay = $('.window-mask');

          // 设置蒙层样式
          dialogOverlay.css({
            position: 'fixed',
            top: 0,
            left: 0,
            width: 100000,
            height: 100000,
            zIndex: 1000 // 确保在 Dialog 之上
          });

          // 重置button置底
          const dialogContent = $('.dialog-button')
          dialogContent.css({
            position: 'absolute',
            bottom: '0',
            width: '100%'
          });
        }
      });

      updateIndexAndType(button)
      clearPicAttrValue()
      if (currentCreateTableType === 'sub') {
        $('#createAttrDialog .select-group').show()
      } else {
        $('#createAttrDialog .select-group').hide()
      }

      // init
      $('#createAttrDialog .input-code').val('')
      $('#createAttrDialog .input-name').val('')
      $HUI.radio('#createAttrDialog [name="dataType"][value="1"]').setValue(true);
      $HUI.radio('#createAttrDialog [name="isGroup"][value="no"]').setValue(true);
      $('#createAttrDialog .btn-save').attr("data-type", "createAttr") // 保存btn标记为新增模式
      $('#createAttrDialog .btn-save').data("type", "createAttr");// 需要清除缓存

      $HUI.dialog('#createAttrDialog').open()
    }

    // 打开弹窗--编辑数据属性
    function editTableField (button) {
      attrDialogHandle = saveEditAttr

      currentCreateTableType = $(button).closest("table").siblings(".subTable-header").length > 0 ? 'sub' : 'main' // 当前【编辑】的表类型
      if (currentCreateTableType === 'sub') {
        currentCreateTableIndex = $(button).closest(".subTable").index(".subTable");
      } else {
        currentCreateTableIndex = $(button).closest(".mainTable").index(".mainTable");
      }
      currentTrIndex = $(button).closest("tr").index() // 当前【编辑】的tr index

      reverseDisplayWithEditAttr() // 反显数据属性

      // 更新弹窗界面
      $('#createAttrDialog .select-group').toggle(currentCreateTableType === 'sub');
      $("#createAttrDialog").dialog({
        title: '编辑属性',
        onOpen: function () {
          // 在 Dialog 打开时设置蒙层样式
          const dialogContainer = $('#tt2');
          const dialogOverlay = $('.window-mask');

          // 重置蒙层样式
          dialogOverlay.css({
            position: 'fixed',
            top: 0,
            left: 0,
            width: 100000,
            height: 100000,
            zIndex: 1000 // 确保在 Dialog 之上
          });

          // 重置button置底
          const dialogContent = $('.dialog-button')
          dialogContent.css({
            position: 'absolute',
            bottom: '0',
            width: '100%'
          });
        }
      });
      $('#createAttrDialog .btn-save').attr("data-type", "editAttr") // 保存btn标记为编辑模式
      $('#createAttrDialog .btn-save').data("type", "editAttr"); // 需要清除缓存

      $HUI.dialog('#createAttrDialog').open()
    }

    // 打开弹窗--函数导入
    function importDataSource () {
      $HUI.dialog('#importDialog').open();
    }

    // 打开弹窗--图片设置
    function picSettingDialog (button) {
      let fieldImgExt = {}

      currentTrIndex = $(button).closest("tr").index()
      currentCreateTableType = $(button).parents('.subTable').length > 0 ? 'sub' : 'main'
      if (currentCreateTableType === 'sub') {
        currentCreateTableIndex = $(button).closest(".subTable").index() - 1;
      } else {
        currentCreateTableIndex = $(button).closest(".mainTable").index();
      }

      if (currentCreateTableType === 'sub') {
        fieldImgExt = window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt || {}
      } else {
        fieldImgExt = window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt || {}
      }

      // 反显
      if (fieldImgExt) {
        const pictureType = fieldImgExt.pictureType || '5'
        const leftUpPost = fieldImgExt.leftUpPost || ''
        const rightDownPost = fieldImgExt.rightDownPost || ''
        const upMargin = fieldImgExt.upMargin || ''
        const downMargin = fieldImgExt.downMargin || ''
        const leftMargin = fieldImgExt.leftMargin || ''
        const rightMargin = fieldImgExt.rightMargin || ''

        $('#picSettingDialog .pictureType').combobox('setValue', pictureType)
        $('#picSettingDialog .leftUpPost').val(leftUpPost)
        $('#picSettingDialog .rightDownPost').val(rightDownPost)
        $('#picSettingDialog .upMargin').val(upMargin)
        $('#picSettingDialog .downMargin').val(downMargin)
        $('#picSettingDialog .leftMargin').val(leftMargin)
        $('#picSettingDialog .rightMargin').val(rightMargin)
      }

      $HUI.dialog('#picSettingDialog').open()
    }

    // 函数导入-新增属性
    function createImportField () {
      const trStr = '<tr><td><input class="textbox" type="text" value="" /></td><td><input class="textbox" type="text" value="" /></td><td><button class="deleteImportAttrBtn">删除</button></td></tr>'

      $('#importTbody').append(trStr);
    }

    // 【保存】新建数据来源
    function saveCreateDataSource () {
      const code = $('#createDataDialog .input-code').val()
      const name = $('#createDataDialog .input-name').val()
      const tableType = $('#createDataDialog .select-type').combobox('getValue')
      const pass = validateDataSourceForm(code, name)

      if (!pass) return

      if (tableType === 'main') {
        // 新建主表
        const tableTemplate = {
          dataCode: code,
          dataName: name,
          dataType: 1, // 1--主   2--从
          dataAttribute: [
            // { fieldCode: '', fieldName: null, fieldType: '1', fieldImgExt: null, isGroup: false, position: '' }
          ]
        }

        window.parent.globalConfig.param.mainData.push(tableTemplate)
        // 新建主表DOM
        const str = generateMainTable([tableTemplate])
        const mainTables = $('.mainTable');

        if (mainTables.length > 0) {
          $(str).insertAfter($('.mainTable').last());
        } else {
          $('#tableContent').prepend(str)
        }

      } else {
        // 新建从表
        const subTableTemplate = {
          dataCode: code,
          dataName: name,
          dataType: 2, // 1--主   2--从
          detailStartRow: '',
          detailEndRow: '',
          dataAttribute: [
            // { fieldCode: '', fieldName: null, fieldType: '2', fieldImgExt: null, isGroup: false, position: '' }
          ]
        }

        window.parent.globalConfig.param.listData.push(subTableTemplate)
        // 新建从表DOM
        const str = generateSubTable([subTableTemplate])
        $('#tableContent').append(str)
      }

      $HUI.dialog('#createDataDialog').close()

    }

    // 【保存】编辑数据来源
    function saveEditDataSource () {
      const code = $('#createDataDialog .input-code').val()
      const name = $('#createDataDialog .input-name').val()
      const pass = validateDataSourceForm(code, name, 'edit')

      if (!pass) return

      if (currentCreateTableType === 'main') {
        // 编辑主表
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataCode = code
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataName = name

        $(".mainTable").eq(currentCreateTableIndex).find('.table-header__left').text('表单数据: ' + name + '-' + code)
      } else {
        // 编辑从表
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataCode = code
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataName = name

        $(".subTable").eq(currentCreateTableIndex).find('.table-header__left').text('列表数据: ' + name + '-' + code)
      }

      $HUI.dialog('#createDataDialog').close()

    }

    // 【保存】图片设置
    function savePicSetting () {
      const fieldImgExt = {
        pictureType: $('#picSettingDialog .pictureType').combobox('getValue'),
        leftUpPost: $('#picSettingDialog .leftUpPost').val(),
        rightDownPost: $('#picSettingDialog .rightDownPost').val(),
        upMargin: $('#picSettingDialog .upMargin').val(),
        downMargin: $('#picSettingDialog .downMargin').val(),
        leftMargin: $('#picSettingDialog .leftMargin').val(),
        rightMargin: $('#picSettingDialog .rightMargin').val()
      }

      // 区分主从表
      if (currentCreateTableType === 'sub') {
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt = fieldImgExt
      } else {
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt = fieldImgExt
      }

      // 初始化，避免下次记忆脏数据
      $('#picSettingDialog .upMargin').val('')
      $('#picSettingDialog .downMargin').val('')
      $('#picSettingDialog .leftMargin').val('')
      $('#picSettingDialog .rightMargin').val('')

      $HUI.dialog('#picSettingDialog').close()
    }

    // 【保存】新建属性
    function saveCreateAttr () {
      const code = $('#createAttrDialog .input-code').val()
      const name = $('#createAttrDialog .input-name').val()
      const dataType = $('#createAttrDialog input[name="dataType"]:checked').attr('value');
      const isGroup = $('#createAttrDialog input[name="isGroup"]:checked').attr('value') === 'true'
      const pass = validateAttrForm(code, name)

      if (!pass) return

      const attrTemplate = {
        fieldCode: code, fieldName: name, fieldType: dataType, fieldImgExt: dataType === 1 ? null : {
          pictureType: $('#createAttrDialog .pictureType').combobox('getValue'),
          leftUpPost: $('#createAttrDialog .leftUpPost').val(),
          rightDownPost: $('#createAttrDialog .rightDownPost').val(),
          upMargin: $('#createAttrDialog .upMargin').val(),
          downMargin: $('#createAttrDialog .downMargin').val(),
          leftMargin: $('#createAttrDialog .leftMargin').val(),
          rightMargin: $('#createAttrDialog .rightMargin').val()
        }, isGroup: dataType == '1' ? isGroup : undefined, position: ''
      }

      if (currentCreateTableType === 'main') {
        // 新建主表属性
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute.push(attrTemplate)

        const len = window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute.length
        const newTrStr = generateAttributeTrDom(attrTemplate, currentCreateTableType, currentCreateTableIndex, len - 1)

        $(".mainTable").eq(currentCreateTableIndex).find('tbody').append(newTrStr);
      } else {
        // 新建从表属性
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute.push(attrTemplate)

        const len = window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute.length
        const newTrStr = generateAttributeTrDom(attrTemplate, currentCreateTableType, currentCreateTableIndex, len - 1)

        $(".subTable").eq(currentCreateTableIndex).find('tbody').append(newTrStr);
      }

      $HUI.dialog('#createAttrDialog').close();

    }

    // 【保存】编辑属性
    function saveEditAttr () {
      const code = $('#createAttrDialog .input-code').val()
      const name = $('#createAttrDialog .input-name').val()
      const dataType = $('#createAttrDialog input[name="dataType"]:checked').attr('value');
      const isGroup = $('#createAttrDialog input[name="isGroup"]:checked').attr('value') === 'true'
      const pass = validateAttrForm(code, name, 'edit')

      if (!pass) return

      const fieldImgExt = {
        pictureType: $('#createAttrDialog .pictureType').combobox('getValue'),
        leftUpPost: $('#createAttrDialog .leftUpPost').val(),
        rightDownPost: $('#createAttrDialog .rightDownPost').val(),
        upMargin: $('#createAttrDialog .upMargin').val(),
        downMargin: $('#createAttrDialog .downMargin').val(),
        leftMargin: $('#createAttrDialog .leftMargin').val(),
        rightMargin: $('#createAttrDialog .rightMargin').val()
      }

      if (currentCreateTableType === 'main') {
        // 主表属性
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldCode = code
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldName = name
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldType = dataType
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].isGroup = isGroup
        window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt = fieldImgExt

        const newTrStr = generateAttributeTrDom(window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex], currentCreateTableType, currentCreateTableIndex, currentTrIndex)
        $('.log').text(newTrStr)

        $(".mainTable").eq(currentCreateTableIndex).find('tr').eq(currentTrIndex + 1).replaceWith(newTrStr);
      } else {
        // 从表属性
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldCode = code
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldName = name
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldType = dataType
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].isGroup = isGroup
        window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt = fieldImgExt

        const newTrStr = generateAttributeTrDom(window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex], currentCreateTableType, currentCreateTableIndex, currentTrIndex)

        $(".subTable").eq(currentCreateTableIndex).find('tr').eq(currentTrIndex + 1).replaceWith(newTrStr);
      }
      $HUI.dialog('#createAttrDialog').close();

    }

    // 【保存】函数导入
    function saveImportFun () {
      const className = $('#importClassName').val()
      const methodName = $('#importMethodName').val()
      const rowCount = $("#importDialog .importTable").find("tr").length
      let params = []

      for (var i = 0; i < rowCount; i++) {
        params.push({
          "paramCode": $("#importDialog").find("tr").eq(i).find("input").eq(0).val(),
          "paramValue": $("#importDialog").find("tr").eq(i).find("input").eq(1).val()
        });
      }

      // 收集数据
      const importData = {
        "className": className,
        "methodName": methodName,
        "params": params
      }

      if (!className || !methodName) {
        $.messager.popover({
          msg: '请完善对象信息字段~',
          timeout: 3000,
          type: 'alert'
        });
        return
      }

      // 请求接口
      $.ajax({
        url: "/api/print/hosPrintTempManage/importPrintAttrFunc",
        type: "post",
        headers: {
          "Access-Token": window.parent.token
        },
        contentType: "application/json;charset=UTF-8",
        dataType: "json",
        data: JSON.stringify(importData),
        success: function (result) {
          // $('.log').text(JSON.stringify(result))
          if (result.code == '200') {
            window.parent.globalConfig.param = result.data
            const htmlStr = generateTables(result.data)
            $('#tableContent').html(htmlStr)
            $HUI.dialog('#importDialog').close();

          } else {
            $.messager.popover({
              msg: result.msg || '请求失败，请稍候再试~',
              timeout: 3000,
              type: 'alert'
            });
          }

        }, error: function (xhr, textStatus, errorThrown) {
          // 失败回调函数，在请求失败时执行
          $.messager.popover({
            msg: textStatus || '请求失败，请稍候再试~',
            timeout: 3000,
            type: 'alert'
          });
          return
        }
      });
    }

    // 生成DOM节点
    function convertToNodes (data) {
      if (!data || !data.param) {
        $.messager.popover({
          msg: '数据配置为空',
          timeout: 3000,
          type: 'alert'
        });
        $('.log').text(JSON.stringify(data))
        return
      }
      const htmlStr = generateTables(data.param)

      $('#tableContent').html(htmlStr)
    }

    // 【取值】按钮的点击事件 -- 坐标
    function getCoordinateValue (button, type) {
      const row = $(button).closest('tr');
      const input = row.find('input');
      const trIndex = $(button).closest("tr").index()
      let tableIndex = -1
      const formatCellAddress = utilFormatCellAddress() || ''
      input.val(formatCellAddress);

      // 区分主从表
      if (type === 'sub') {
        tableIndex = $(button).closest(".subTable").index('.subTable');
        window.parent.globalConfig.param.listData[tableIndex].dataAttribute[trIndex].position = formatCellAddress
      } else {
        tableIndex = $(button).closest(".mainTable").index('.mainTable');
        window.parent.globalConfig.param.mainData[tableIndex].dataAttribute[trIndex].position = formatCellAddress
      }
    }

    // 更新当前操作的table下标和类型
    function updateIndexAndType (button) {
      currentCreateTableType = $(button).data("type"); // 当前点击【新建】的表类型
      if (currentCreateTableType === 'sub') {
        currentCreateTableIndex = $(button).closest(".subTable").index('.subTable');
      } else {
        currentCreateTableIndex = $(button).closest(".mainTable").index('.mainTable');
      }
    }

    // 【取值】按钮的点击事件 -- 明细起始行
    function getDetailRowValue (button) {
      const row = $(button).closest('div');
      const input = row.find('input');
      const index = $(button).closest("div").index()
      const tableIndex = $(button).closest(".subTable").index('.subTable');
      const rowType = index > 0 ? 'detailEndRow' : 'detailStartRow'
      const formatCellAddress = utilFormatCellAddress() || ''
      const rowLine = formatCellAddress.split(',')[1] || ''

      input.val(rowLine);
      window.parent.globalConfig.param.listData[tableIndex][rowType] = rowLine
    }

    // 转换坐标格式
    function utilFormatCellAddress () {
      const cellAddressPattern = /^\$[A-Z]+\$\d+$/;

      if (!windowParent.Celladdress || !cellAddressPattern.test(windowParent.Celladdress)) {
        return windowParent.Celladdress || '';
      }

      const modifiedAddress = windowParent.Celladdress.replace(/\$/g, '');
      const match = modifiedAddress.match(/(\D)(\d+)/);

      if (!match) {
        return windowParent.Celladdress;
      }

      return match[1] + ',' + match[2];
    }

    // 收集所有表单配置数据
    function collectTableConfig () {
      window.parent.globalConfig.dataSource.className = $('#dataSourceClassName').val()
      window.parent.globalConfig.dataSource.methodName = $('#dataSourceMethodName').val()
      window.parent.globalConfig.printConfig.isOverPrint = $HUI.radio("[name='Overlay'][value='true']").getValue();
      window.parent.globalConfig.printConfig.isClearNull = $HUI.radio("[name='clearBlank'][value='true']").getValue();
      window.parent.globalConfig.printConfig.isDefPaper = $HUI.radio("[name='useDefault'][value='true']").getValue();
      window.parent.globalConfig.printConfig.totalPage = $('#totalPages').val()
      window.parent.globalConfig.printConfig.currPage = $('#currentPages').val()

      return saveFormValidation()
    }

    // 保存模版校验逻辑
    function saveFormValidation () {
      let pass = true
      const dataSource = window.parent.globalConfig.dataSource
      const className = window.parent.globalConfig.dataSource.className
      const methodName = window.parent.globalConfig.dataSource.methodName
      const listData = window.parent.globalConfig.param.listData
      const detailStartRowHasNull = listData.some(function (item) {
        return typeof item.detailStartRow === 'undefined' || item.detailStartRow === null || item.detailStartRow === '';
      });

      if (!className || !methodName) {
        pass = false
        $.messager.popover({
          msg: '数据集管理的类名和方法名不能为空',
          timeout: 3000,
          type: 'alert'
        });
      } else if (detailStartRowHasNull) {
        pass = false
        $.messager.popover({
          msg: '明细开始行不能为空',
          timeout: 3000,
          type: 'alert'
        });
      } else {
        listData.forEach(function (item) {
          if (item.detailEndRow && !/^[1-9]\d*$/.test(item.detailEndRow)) {
            pass = false
            $.messager.popover({
              msg: '明细结束行只能输入正整数',
              timeout: 3000,
              type: 'alert'
            });
          }
        })
      }
      return pass
    }

    // 保存结果提示
    function saveTipHandle (result) {
      if (!result) return

      const resp = JSON.parse(result)
      let msg = "保存成功"
      let status = 'success'

      if (resp.code != '200') {
        msg = resp.msg || '保存失败'
        status = 'error'
      }

      $.messager.alert("", msg, status);
    }

    // 【编辑】属性--反显
    function reverseDisplayWithEditAttr () {
      const tableType = currentCreateTableType === 'sub' ? 'listData' : 'mainData'
      const row = window.parent.globalConfig.param[tableType][currentCreateTableIndex].dataAttribute[currentTrIndex]
      const fieldType = row.fieldType == '1' || row.fieldType === 'data' ? '1' : '2'
      const isGroup = row.isGroup;
      const selector = '#createAttrDialog [name="isGroup"][value="' + isGroup + '"]';
      let fieldImgExt = {}

      $('#createAttrDialog .input-code').val(row.fieldCode)
      $('#createAttrDialog .input-name').val(row.fieldName)

      if (fieldType == '1') { // 数据类型
        $HUI.radio('#createAttrDialog [name="dataType"][value="1"]').setValue(true);

        // 数据类型有分组
        if ($('#createAttrDialog .select-group').length > 0 && currentCreateTableType === 'sub') {
          $('#createAttrDialog .select-group').show();
          $HUI.radio(selector).setValue(true)
        }
      } else { // 图片类型
        $HUI.radio('#createAttrDialog [name="dataType"][value="2"]').setValue(true);

        if ($('#createAttrDialog .select-group').length > 0) {
          $('#createAttrDialog .select-group').hide();
        }

        // 反显图片设置
        if (currentCreateTableType === 'sub') {
          fieldImgExt = window.parent.globalConfig.param.listData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt || {}
        } else {
          fieldImgExt = window.parent.globalConfig.param.mainData[currentCreateTableIndex].dataAttribute[currentTrIndex].fieldImgExt || {}
        }

        if (fieldImgExt) {
          const pictureType = fieldImgExt.pictureType || '5'
          const leftUpPost = fieldImgExt.leftUpPost || ''
          const rightDownPost = fieldImgExt.rightDownPost || ''
          const upMargin = fieldImgExt.upMargin || ''
          const downMargin = fieldImgExt.downMargin || ''
          const leftMargin = fieldImgExt.leftMargin || ''
          const rightMargin = fieldImgExt.rightMargin || ''

          $('#createAttrDialog .pictureType').combobox('setValue', pictureType)
          $('#createAttrDialog .leftUpPost').val(leftUpPost)
          $('#createAttrDialog .rightDownPost').val(rightDownPost)
          $('#createAttrDialog .upMargin').val(upMargin)
          $('#createAttrDialog .downMargin').val(downMargin)
          $('#createAttrDialog .leftMargin').val(leftMargin)
          $('#createAttrDialog .rightMargin').val(rightMargin)
        }
      }
    }

    // 清空图片设置的值
    function clearPicAttrValue () {
      $('.pictureType').combobox('setValue', '5')
      $('.leftUpPost').val('')
      $('.rightDownPost').val('')
      $('.upMargin').val('')
      $('.downMargin').val('')
      $('.leftMargin').val('')
      $('.rightMargin').val('')
    }

    // 校验数据源编码名称
    function validateDataSourceForm (code, name, type) {
      let pass = true

      if (!code || !name) {
        pass = false

        const text = !code ? '编码' : '名称'
        $.messager.popover({
          msg: '请完善数据对象的' + text + '字段',
          timeout: 3000,
          type: 'alert'
        });
      } else if (!/^[0-9a-zA-Z_]{0,75}$/.test(code)) {
        pass = false

        $.messager.popover({
          msg: '编码校验：请输入最多 75 个字符的数字、字母或下划线的编码',
          timeout: 3000,
          type: 'alert'
        });
      } else if (name.length > 37) {
        pass = false

        $.messager.popover({
          msg: '名称校验：请输入最多 37 个字符',
          timeout: 3000,
          type: 'alert'
        });
      } else {
        const listData = windowParent.globalConfig.param.listData
        const mainData = windowParent.globalConfig.param.mainData
        let hasCodeInMainData = false
        let hasCodeInListData = false

        if (type === 'edit') {
          const newMainData = mainData.slice(0, currentCreateTableIndex).concat(mainData.slice(currentCreateTableIndex + 1));
          const newListData = listData.slice(0, currentCreateTableIndex).concat(listData.slice(currentCreateTableIndex + 1));


          hasCodeInMainData = newMainData.some(function (item) {
            return item.dataCode === code
          })
          hasCodeInListData = newListData.some(function (item) {
            return item.dataCode === code
          })
        } else {
          hasCodeInMainData = mainData.some(function (item) {
            return item.dataCode === code
          })
          hasCodeInListData = listData.some(function (item) {
            return item.dataCode === code
          })
        }

        //  编码唯一性校验
        if (hasCodeInMainData || hasCodeInListData) {
          const text = hasCodeInMainData ? '表单数据' : '列表数据'

          pass = false

          $.messager.popover({
            msg: '编码校验：' + text + '中有重复编码',
            timeout: 3000,
            type: 'alert'
          });
        }
      }

      return pass
    }

    // 校验属性操作编码名称
    function validateAttrForm (code, name, type) {
      let pass = true

      if (!code || !name) {
        pass = false

        const text = !code ? '编码' : '名称'
        $.messager.popover({
          msg: '请完善属性' + text + '字段',
          timeout: 3000,
          type: 'alert'
        });
      } else if (!/^[0-9a-zA-Z_]{0,75}$/.test(code)) {
        pass = false

        $.messager.popover({
          msg: '编码校验：请输入最多 75 个字符的数字、字母或下划线的编码',
          timeout: 3000,
          type: 'alert'
        });
      } else if (name.length > 37) {
        pass = false

        $.messager.popover({
          msg: '名称校验：请输入最多 37 个字符',
          timeout: 3000,
          type: 'alert'
        });
      } else {
        const listData = windowParent.globalConfig.param.listData
        const mainData = windowParent.globalConfig.param.mainData
        const parentsList = currentCreateTableType === 'main' ? mainData : listData
        const currentObj = parentsList[currentCreateTableIndex].dataAttribute
        let hasRepeatCode = false

        if (type === 'edit') {
          const newDataAttributeArr = currentObj.slice(0, currentTrIndex).concat(currentObj.slice(currentTrIndex + 1));


          hasRepeatCode = newDataAttributeArr.some(function (item) {
            return item.fieldCode === code
          })
        } else {
          hasRepeatCode = currentObj.some(function (item) {
            return item.fieldCode === code
          })
        }

        //  编码唯一性校验
        if (hasRepeatCode) {
          pass = false

          $.messager.popover({
            msg: '编码重复',
            timeout: 3000,
            type: 'alert'
          });
        }
      }

      return pass
    }
  </script>
</body>

</html>