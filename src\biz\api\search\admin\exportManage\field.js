
export const queryDetailApi = (id) => {
    return {
        url: 'search/observe-field/detail/' + id,
        method: 'GET'
    }
}

// 分页数据
export const queryListApi = (params) => {
    return {
        url: 'search/observe-field/page',
        method: 'GET',
        params
    }
}



// 新增观测字段配置
export const addApi = (data) => {
    return {
        url: 'search/observe-field/insert',
        method: 'POST',
        data
    }
}

// 编辑观测字段配置
export const updateApi = (data) => {
    return {
        url: 'search/observe-field/update',
        method: 'post',
        data
    }
}

// 删除
export const deleteBatchApi = (data) => {
    return {
        url: 'search/observe-field/deletion',
        method: 'POST',
        data
    }
}

