
export function getApproveSearchItemLike(params) {
  return {
    url: "search/search-item/getSearchItemTreeByRoleCode",
    method: "get",
    params
  }
}

export function getStatisticFieldTree(params) {
  return {
    url: "search/search-item/tree/get-statistic-field-tree",
    method: "get",
    params
  }
}

export function customChartSearch(data) {
  return {
    url: "search/advanced/aggregation/top",
    method: "post",
    data
  }
}


export function getSearchItemTreeByRoleCodeAndTag(params) {
  return {
    url: "search/search-item/getSearchItemTreeByRoleCodeAndTag",
    method: "get",
    params
  }
}
export function getExportItemTreeByRoleCodeAndTag(params) {
  return {
    url: "search/search-item/getExportItemTreeByRoleCodeAndTag",
    method: "get",
    params
  }
}

export function getExportItem(params) {
  return {
    url: "search/search-item/getExportItemTreeByRoleCode",
    method: "GET",
    params
  }
}
export function getAllTag(params) {
  return {
    url: "search/tag/config/list/by-tag-type",
    method: "GET",
    params
  }
}
