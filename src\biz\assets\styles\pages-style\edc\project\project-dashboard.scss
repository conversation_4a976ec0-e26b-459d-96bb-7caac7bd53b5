.project-overview-container {
	width: 100%;
	min-width: 1440px;
	height: 100%;
	box-sizing: border-box;
	font-size: 14px !important;
	color: #5e6d82;

	.hos-card {
		.title {
			display: flex;
			align-items: center;
		}

		.title [class*=' hos-icom-'],
		.title [class^='hos-icom-'],
		.title [class*=' hos-icon-'],
		.title [class^='hos-icon-'] {
			margin-right: 5px;
		}
	}

	.top-info {
		margin-bottom: 10px;
		display: flex;
		justify-content: space-between;

		.info-card {
			border-radius: 0px;
			width: 70%;

			.hos-card__body {
				padding: 10px;
			}

			.project-info {
				display: flex;
				flex-wrap: wrap;

				div {
					// width: 300px;
					height: 40px;
					line-height: 40px;
					padding-left: 30px;
					padding-right: 30px;
					border-right: 1px solid #ccc8c8;
				}

				div:last-child {
					border-right: none;
				}

				div.first {
					padding-left: 14px !important;
					display: flex;
					align-items: center;

					span {
						height: 40px;
						line-height: 40px;
					}

					span.circle {
						width: 30px;
						height: 30px;
						line-height: 30px;
						text-align: center;
						border-radius: 50%;
						color: white;
						background: #e98f87;
						overflow: hidden;
						// white-space: nowrap;
						// text-overflow: ellipsis;
						// overflow: hidden;
						// word-break: break-all;
					}

					span.vertical-span {
						margin-left: 10px;
						display: flex;
						flex-direction: column;

						span {
							height: 40px;
						}
					}
				}

				div.time-box {
					display: flex;
					align-items: center;

					.icon-wrapper {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 40px;
						height: 40px;
						margin-right: 5px;
						text-align: center;
						border-radius: 50%;
						border: 1px dashed #ccc8c8;

						i {
							font-size: 18px;
						}
					}
				}
			}

			.project-desc {
				.center-detail {
					margin-bottom: 10px;
					line-height: 20px;
					display: flex;

					.mult-center {
						display: flex;
						flex-wrap: wrap;

						.main-center,
						.sub-center {
							display: inline-block;
							padding-right: 15px;
							border-right: 1px solid #ccc8c8;
						}
						.main-center,
						.sub-center,
						.center-belong {
							margin-right: 15px;
						}
					}
				}
				.dept-detail{
					margin-bottom: 10px;
					line-height: 20px;
					display: flex;
					flex-wrap: wrap;
					.dept-item{
						margin-right: 15px;
						padding-right: 15px;
						border-right: 1px solid #ccc8c8;
						&:last-child{
							border-right: 0;
						}
					}
				}

				padding: 15px 0;
				margin-bottom: 15px;
				border-bottom: 1px solid #ececec;
			}

			.current-status {
				display: flex;
				align-items: center;
				margin-bottom: 15px;

				span {
					display: inline-block;
					line-height: 20px;
				}

				.status-ok {
					display: flex;

					.circle-ok {
						width: 16px;
						height: 16px;
						margin-right: 5px;
						line-height: 16px;
						border-radius: 50%;
						background: #63d5bc;
					}
				}
			}
		}

		.right-card {
			width: 30%;
			margin-left: 10px;
			margin-top: 0;

			.hos-card__body {
				padding: 10px;
				height: 100%;
			}

			.title {
				padding: 0;
				// border-bottom: 1px solid #ececec;
			}

			.data-detail {
				display: flex;
				padding: 20px 40px;

				.total-count {
					width: 25%;
					box-sizing: border-box;
					border-right: 1px solid #ccc8c8;
					text-align: center;
					margin: 24px 0;
					padding: 20px;

					.num {
						cursor: pointer;

						span {
							display: inline-block;
							font-size: 28px;
							margin-right: 10px;
							color: #50a1fd;
						}
					}

					.type {
						margin-top: 10px;
						color: #999999;
					}
				}

				.item-count {
					width: 70%;
					padding-top: 25px;

					.top,
					.bottom {
						display: flex;

						>div {
							width: 45%;
							text-align: center;

							.num {
								span {
									display: inline-block;
									font-size: 28px;
									margin-right: 10px;
									color: #50a1fd;
								}
							}

							.type {
								margin-top: 10px;
								color: #999999;
							}
						}

						.right-border {
							border-right: 1px solid #ccc8c8;
						}
					}

					.top {
						margin-bottom: 15px;
					}

					.bottom {
						margin-top: 15px;
					}
				}
			}
		}
	}

	.top-content {
		display: flex;
		justify-content: space-between;

		.left-card {
			width: 36%;
			min-width: 200px;
			margin-right: 10px;

			.hos-card__body {
				height: 100%;
			}

			// .title {
			//   padding-bottom:10px;
			//   border-bottom: 1px solid #ececec;
			// }

			.current-role {
				margin-bottom: 20px;
			}

			.members {
				width: 100%;

				span {
					display: inline-block;
					margin-bottom: 20px;
				}

				.members-detail {
					width: 100%;
					display: flex;
					flex-wrap: wrap;

					// overflow-x: auto;
					// overflow-y: hidden;
					.member-item {
						width: 50%;
						text-align: center;

						i {
							font-size: 40px;
							color: darkgray;
						}

						.user-name {
							margin-top: 10px;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							min-height: 16px;
						}

						.role {
							margin-top: 5px;
							color: #5ea1f8;
							font-size: 12px;
							min-height: 14px;
						}
					}

					.check-all-members {
						margin-left: 5px;
						word-break: keep-all;
					}
				}
			}
		}

		.todo {
			.title {
				padding: 0 0 10px 0;
				border-bottom: 1px solid #ececec;
			}
		}

		.todo-card {
			width: 99%;
			min-width: 200px;
			margin-right: 10px;

			.hos-card__body {
				height: 100%;
			}

			.title {
				padding-bottom: 10px;
				border-bottom: 1px solid #ececec;
			}

			.message-line {
				cursor: pointer;
				font-size: 14px;
				margin: 10px;
				padding-bottom: 10px;
				border-bottom: dashed 1px #ddd;
			}

			.msg-line {
				display: inline-block;
				width: calc(100% - 150px);
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.calendar-card {
			width: 100%;
			min-width: 655px;
			margin-top: 0;
			box-shadow: none;

			.title {
				height: 35px;
				box-sizing: border-box;
				border-bottom: 1px solid #e2e2e2;
				padding: 10px;
				margin-bottom: 20px;
				font-weight: bold;
			}

			.hos-card__body {
				padding: 0px;
			}
		}

		.r-card {
			// width: 64%;
			width: 100%;
		}

		.a-card {
			width: 100%;
		}
	}

	.bottom-charts {
		width: 100%;
		// display: flex;
		// justify-content: space-between;
		margin-top: 10px;

		.card {
			// width: calc(50% - 0px); // 为了保证两个图表间的间距固定为20px,左边设置mr-20,每个贡献10px出来
			height: 400px;
			// box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
			border-radius: 0px;
			background-color: white;

			&:hover {
				box-shadow: 0 2px 12px 0 rgb(0, 0, 0, 0.1);
			}

			.title {
				width: 100%;
				height: 35px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				box-sizing: border-box;
				padding: 10px;
				border-bottom: 1px solid #ececec;
				color: #303133;
				font-weight: bold;
			}

			#pie-chart,
			#bar-chart,
			#line-chart {
				width: 100%;
				height: calc(100% - 50px);
			}
		}

		.pie-chart-card {
			margin-right: 10px;
		}
	}

	.p-20 {
		padding: 20px 0;
	}
}