.time-line-page {
  position: fixed !important;
  left: 10px;
  right: 10px;
  z-index: 1002;
  width: calc(100vw - 20px);
  height: calc(100% - 40px) !important;
  overflow-x: auto;

  .time-line-wrapper {
    position: relative;
    height: 100%;

    .time-line-header {
      background-color: #fff;
      border-radius: 5px;
    }

    .time-line-main {
      border-top: 1px dashed #e2e2e2;
      box-sizing: border-box;
      width: 100%;
      min-width: 1300px;
      // min-height: 250px;
      // max-height: calc(100vh - 450px);
      // height: auto;
      background-color: #fff;
      border-radius: 5px;
      // margin-top: 10px;
      // padding-top: 60px;
      padding-bottom: 20px;
      padding-left: calc(50% - 700px);
      // overflow-x: auto;
      // overflow-y: hidden;
      overflow-y: auto;
      // position: absolute;
      // bottom: 0;
    }

    .with-head {
      // top: 180px;
    }

    .without-head {
      // top: 0;
    }
  }
}

@import "./components/adm-time-line.scss";
@import "./components/time-point-item.scss";
@import "../patient-info.scss";