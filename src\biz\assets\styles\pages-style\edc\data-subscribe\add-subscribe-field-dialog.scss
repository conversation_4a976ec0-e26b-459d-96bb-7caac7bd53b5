.add-new-subscribe-dialog {
  .hos-dialog__header {}

  .hos-dialog__body {
    padding-top: 0;
    padding-bottom: 0;
    height: 65vh;
  }

  .hos-dialog__footer {}
}

.add-subscribe-out-container {
  width: 100%;
  height: 100%;
  padding: 10px;
  position: relative;
  display: flex;
  flex-direction: column;

  .top {
    height: 95%;
    position: relative;
    padding-left: 180px;

    .left {
      width: 300px;
      position: absolute;
      left: 0;
      border-top: 1px solid #e1e3e9;
      margin-right: 30px;
      padding: 10px;
      box-sizing: border-box;
      height: 100%;
      overflow-y: auto;

      .cate-item {
        width: calc(100% - 25px);
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        border: 1px solid gainsboro;
        margin-bottom: 10px;
        padding: 0 10px;
        cursor: pointer;
      }

      .cate-item.active {
        color: white;
        background-color: #409eff;
      }
    }

    .right {
      margin-left: 130px;
      width: calc(100% - 150px);
      height: 100%;
    }
  }

  .bottom {
    height: 40px;
    display: flex;
    align-items: center;
    margin: 10px 0;
  }
}