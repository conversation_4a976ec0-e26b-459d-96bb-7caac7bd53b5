
export const queryListApi = (params) => {
  return {
    url: 'edc/subject-user/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/subject-user/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/subject-user/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/subject-user/insert',
    method: 'post',
    data,
  }
}

export const addBatchApi = (data) => {
  return {
    url: 'edc/subject-user/insert/batch',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/subject-user/update',
    method: 'post',
    data
  }
}

// 获取指定机构的的系统用户列表 项目列表指定管理员时用到
export const queryOrgUser = (params) => {
  return {
    url: 'edc/subject-user/sys-user-list/select-by-org',
    method: 'get',
    params,
  }
}

// 获取项目管理员数据，项目列表根据管理员过滤时用到
export const queryProjectAdmin = () => {
  return {
    url: 'edc/project/project-admin',
    method: 'get',
  }
}

// 获取指定机构的的系统用户列表树
export const queryOrgUserTree = (params) => {
  return {
    url: 'edc/project-org/syn-tree',
    method: 'get',
    params,
  }
}

// 添加项目成员时，查询未绑定到项目的成员
export const querySubjectUnbindUser = (params) => {
  return {
    url: 'edc/subject-user/unbind/list',
    method: 'get',
    params,
  }
}

// 添加项目成员时，查询未绑定到项目的成员tree
export const querySubjectUnbindUserTree = (params) => {
  return {
    url: 'edc/subject-user/unbind/tree',
    method: 'get',
    params,
  }
}

// 业务参数获取是否开启订阅
export const getParamsEnableDeform = () => {
  return {
    url: '/edc/project/sys-business/enable-deform',
    method: 'get',
  }
}

// 业务参数获取是否开启自动绑定就诊
export const getParamsEnableBindAdm = () => {
  return {
    url: '/edc/project/sys-business/enable-bind-adm',
    method: 'get',
  }
}


