// 数据组件管理）

export const pageListApi = (params) => {
    return {
        url: '/edc/patient-group/page',
        method: 'GET',
        params
    }
}

export const detailApi = (id) => {
    return {
        url: '/edc/patient-group/detail/' + id,
        method: 'GET'
    }
}

export const addApi = (data) => {
    return {
        url: '/edc/patient-group/insert',
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: '/edc/patient-group/update',
        method: 'POST',
        data
    }
}

export const deleteApi = (data) => {
    return {
        url: '/edc/patient-group/deletion',
        method: 'POST',
        data
    }
}