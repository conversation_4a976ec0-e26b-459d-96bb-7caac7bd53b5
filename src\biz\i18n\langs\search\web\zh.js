const t = {}

t.common = {}
t.common.username = '用户名'
t.common.name = '姓名'
t.common.gender = '性别'
t.common.age = '年龄'
t.common.patientTotal = '患者总量'
t.common.admTotal = '就诊总量'
t.common.peopleCount = '人'
t.common.times = '次'
t.common.caseDistribution = '患者分布'
t.common.visitTrends = '就诊趋势'
t.common.outpatientService = '门诊'
t.common.hospitalization = '住院'
t.common.emergencyTreatment = '急诊'
t.common.other = '其他'
t.common.physicalExamination = '体检'
t.common.genderSpread = '性别分布'
t.common.visitDepartment = '就诊科室'
t.common.male = '男'
t.common.female = '女'
t.common.province = '籍贯'
t.common.occupation = '职业'
t.common.nation = '民族'
t.common.diagnosis = '诊断'
t.common.westernMedicine = '西药'
t.common.noData = '暂无数据'
t.common.remind = '提示'
t.common.download = '下载'
t.common.exporting = '导出中'
t.common.line = '行'
t.common.exportItem = '导出项'
t.common.attribute = '属    性'
t.common.specialValue = '特殊值'
t.common.nothing = '无'
t.common.admDate = '就诊日期'
t.common.yes = '是'
t.common.no = '否'
t.common.surgical = '手术'
t.common.regno = '登记号'
t.common.detail = '详情'
t.common.idNo = '身份证号'
t.common.checkOut = '检验'
t.common.medical = '用药'
t.common.checkOn = '检查'
t.common.disease = '疾病'

t.common.all = '全部'
t.common.last1year = '近一年'
t.common.containes = '包含'
t.common.containLike = '包含(like)'
t.common.regexp = '正则匹配'
t.common.notContain = '不包含'
t.common.eq = '等于'
t.common.notEq = '不等于'
t.common.before = '之前'
t.common.after = '之后'
t.common.year = '年'
t.common.month = '月'
t.common.day = '天'
t.common.hour = '小时'
t.common.during = '之内'
t.common.outside = '之外'
t.common.greaterThan = '大于'
t.common.lessThan = '小于'
t.common.greaterOrEq = '大于等于'
t.common.lessOrEq = '小于等于'
t.common.semanticInclude = '语义包含'
t.common.multiple = '多值精确匹配(满足任意)'
t.common.multiple2 = '多值精确匹配(同时包含)'
t.common.multipleLike = '多值模糊匹配(满足任意)'
t.common.multipleParentAllLike = '多值模糊匹配(同时包含)'
t.common.null = '为空'
t.common.notNull = '非空'
t.common.positiveInclude = '阳性包括'
t.common.negativeInclude = '阴性包括'
t.common.openInterval = '开区间'
t.common.max = '最大值'
t.common.min = '最小值'

t.common.view = '查看'
t.common.viewDetail = '查看详情'
t.common.clearRecord = '清空记录'
t.common.confirm = '确定'
t.common.cancel = '取消'
t.common.delete = '删除'
t.common.close = '关闭'
t.common.submit = '提交'
t.common.search = '查询'
t.common.reset = '重置'
t.common.save = '保存'
t.common.edit = '编辑'
t.common.goBack = '返回'
t.common.remove = '移除'
t.common.export = '导出'
t.common.preview = '预览'
t.common.lastPage = '上一页'
t.common.nextPage = '下一页'

// 提示信息
t.msg = {}
t.msg.uploadSuccessful = '上传成功!'
t.msg.uploadFail = '上传失败,请稍后重试'
t.msg.uploadTypeLimit = '上传图标图片只能是 JPG 或者 PNG格式!'
t.msg.uploadSizeLimit = '上传图标图片大小不能超过 10MB!'
t.msg.confirmDelete = '确认删除这条信息?删除后您无法再进行查看和操作!'
t.msg.submitSuccessful = '提交成功!'
t.msg.selectPlease = '请选择'
t.msg.conditionFilename = '文件名/无'
t.msg.conditionBringInto = '纳入条件/无'
t.msg.conditionBringOut = '排除条件/无'
t.msg.inputPlease1 = '请输入内容'
t.msg.confirmDeleteRecords = '确认删除此记录？删除后将无法找回数据'
t.msg.deleteSuccess = '删除成功!'
t.msg.handleSuccess = '操作成功！'
t.msg.statisticsCaution1 = '对当前检索结果中所有的就诊相关的疾病、用药、检验、检查、手术、中药、中草药、西药的比例进行统计'
t.msg.statisticsCaution2 = '疾病统计主要诊断的数据（门诊病历统计门诊诊断；住院病历统计出院诊断）'
t.msg.statisticsCaution3 = '统计所有患者的性别比例（按患者计数）'
t.msg.statisticsCaution4 = '统计所有病历就诊时的年龄（按就诊计数）'
t.msg.statisticsCaution5 = '统计当前检索结果中所有病历的入院日期'
t.msg.getStructInfoErr = '获取结构化信息报错'
t.msg.errorInfo = '报错信息'

// 首页
t.home = {}
t.home.userInfo = '账户信息'
t.home.belongOrg = '所属机构'
t.home.diagnosisStatus = '诊疗情况'
t.home.patientCount = '患者数'
t.home.outpatientVisits = '门诊人次'
t.home.hospitalizationVisits = '住院人次'
t.home.emergencyVisits = '急诊人次'
t.home.noCharts = '抱歉,未获取到该图表的统计结果'
t.home.totalError = '获取统计总数错误'
t.home.visitType = '就诊类型'
t.home.diagnosticDistribution = '诊断分布'
t.home.surgicalDistribution = '手术分布'
t.home.testDistribution = '检验分布'
t.home.inspectionDistribution = '检查分布'
t.home.visitTime = '就诊时间'
t.home.msgStatisticsAll = '统计每年各个就诊类型的就诊人次'
t.home.msgStatistics1year = '统计近一年内每月的各个就诊类型的就诊人次'
t.home.msgCountByHospitalization = '按住院统计'
t.home.msgCountByHospitalization1 = '统计住院病历出院诊断的主要诊断'
t.home.msgCountByHospitalization2 = '统计住院病历手术记录的数据'
t.home.msgCountByHospitalization3 = '统计住院病历检验医嘱的数据'
t.home.msgCountByHospitalization4 = '统计住院病历检查医嘱的数据'
t.home.msgCountByOutpatient = '按门诊统计'
t.home.msgCountByOutpatient1 = '统计门诊病历门诊诊断的主要诊断'
t.home.msgCountByOutpatient2 = '统计门诊病历的手术数据'
t.home.msgCountByOutpatient3 = '统计门诊病历检验医嘱的数据'
t.home.msgCountByOutpatient4 = '统计门诊病历检查医嘱的数据'
t.home.msgVisitDistribution = '以上数据都是基于用户权限范围内、且有就诊记录的患者病历数据的统计'
t.home.medicTendency = '诊疗趋势'
t.home.msgHospitalizationTop5 = '统计一段时间内住院病历的诊断次数、手术例数、检验次数、检查次数top5趋势'
t.home.msgVisitDistributionTop5 = '统计一段时间内门诊病历的诊断次数、手术例数、检验次数、检查次数top5趋势'
t.home.caseDistribution3 = '统计所有病历的就诊时间'

// 专病
t.specialDisease = {}
t.specialDisease.visitInfo = '就诊信息'
t.specialDisease.heartFailure = '心衰'
t.specialDisease.heartFailureResearch = '心衰病种研究'
t.specialDisease.cardiovascular1 = '心血管内科1病房'
t.specialDisease.cardiovascular2 = '心血管内科门诊'
t.specialDisease.userCountTop5 = '用户项目数量TOP5'
t.specialDisease.departmentPros = '科室项目数量统计'
t.specialDisease.baseSituation = '基本情况'
t.specialDisease.caseHomeDetail = '病案首页特征'
t.specialDisease.treatmentData = '诊疗数据'
t.specialDisease.homeStatisticsReport = '首页统计报告'
t.specialDisease.predictAlive = '生存死亡预测'
t.specialDisease.dataDetail = '数据详情'
t.specialDisease.proportion = '占比'
t.specialDisease.numOfHospitalizations = '第几次住院'
t.specialDisease.peopleNum = '人数'
t.specialDisease.visitAge = '就诊时年龄'
t.specialDisease.residentPhysician = '住院医师'
t.specialDisease.hospitalizationDays = '住院天数'
t.specialDisease.admissionPathway = '入院途径'
t.specialDisease.chinesePatentDrug = '中成药'
t.specialDisease.chineseHerbalMedicine = '中草药'
t.specialDisease.dischargeStatus = '出院状态'
t.specialDisease.surgicalTrends = '手术趋势'
t.specialDisease.inspectionTrend = '检验趋势'
t.specialDisease.checkTrends = '检查趋势'
t.specialDisease.hypertension = '高血压'
t.specialDisease.coronaryHeartDisease = '冠心病'
t.specialDisease.patientID = '患者ID'
t.specialDisease.admissionDiagnosis = '入院诊断'
t.specialDisease.symptomsAndSigns = '症状体征'

t.exportRecord = {}
t.exportRecord.createTimeOrder = '创建时间（顺序）'
t.exportRecord.createTimeReverse = '创建时间（倒序）'
t.exportRecord.authing = '审核中'
t.exportRecord.approved = '审核通过'
t.exportRecord.reviewFailed = '审核未通过'
t.exportRecord.audit = '待审核'
t.exportRecord.allReviewStatus = '全部审核状态'
t.exportRecord.askClearRecords = '确认清空所有记录?清空后您无法再进行查看和操作!'
t.exportRecord.getFilePwd = '获取文件密码'
t.exportRecord.submit = '提交申请'
t.exportRecord.fail = '导出失败'
t.exportRecord.success = '导出成功'
t.exportRecord.isExporting = '导出进行中'
t.exportRecord.condition = '筛选条件'
t.exportRecord.bringInto = '纳入标准'
t.exportRecord.bringOut = '排除标准'
t.exportRecord.exportMetrics = '导出指标'
t.exportRecord.exportStatus = '导出状态'
t.exportRecord.viewExportLog = '查看导出日志'
t.exportRecord.exportLines = '导出行数'
t.exportRecord.inputApplyReason = '请输入申请理由'
t.exportRecord.applyReasonCaution = '申请理由为必填项，请填写后提交！'
t.exportRecord.applyAgainCaution = '再次申请理由为必填项，请填写后提交！'
t.exportRecord.uploadCaution = '必须先上传附件才能提交！'
t.exportRecord.askConfirmApply = '您确认要提交申请到OA系统进行审核吗？'
t.exportRecord.extraFile = '附加材料请在下方上传：(必填)'
t.exportRecord.dragRemind = '将文件拖到此处，或'
t.exportRecord.clickUpload = '点击上传'
t.exportRecord.uploadLimit = '只能上传jpg/png文件，且不超过10Mb'
t.exportRecord.applyAgain = '重新申请'
t.exportRecord.verifyIdentity = '验证本人身份'
t.exportRecord.filePwd = '文件密码'
t.exportRecord.systemPwd = '系统密码'
t.exportRecord.exportLog = '导出日志'
t.exportRecord.reasonCaution = '请输入提交申请理由'
t.exportRecord.reasonAgainCaution = '请输入再次提交申请理由'
t.exportRecord.accountCaution = '请输入账号'
t.exportRecord.pwdCaution = '请输入密码'
t.exportRecord.applyOAExport = '提交OA导出申请'
t.exportRecord.clickDownload = '点击了下载'
t.exportRecord.checkRecordDetail = '审核记录详情'
t.exportRecord.exportInfoDetail = '导出信息详情'
t.exportRecord.exportObservePeriod = '导出观测阶段'
t.exportRecord.reInputReason = '填写重新申请理由'
t.exportRecord.noPreview = '当前格式数据暂无预览'

t.timeline = {}
t.timeline.allAdm = '全部就诊'
t.timeline.getPatientInfoErr = '获取患者信息出错'
t.timeline.conditionErr = '获取患者信息出错'
t.timeline.getAdmListErr = '获取就诊列表数据出错'
t.timeline.physicalExaminationDepartment = '体检科室'
t.timeline.physicalExaminationDate = '体检日期'
t.timeline.mr_adm_dept = '住院科室'
t.timeline.mr_adm_datetime = '入院日期'
t.timeline.mr_dish_datetime = '出院日期'
t.timeline.item = '项'
t.timeline.msgBox1 = '住院展示出院诊断'
t.timeline.msgBox2 = '门（急）诊展示门（急）诊诊断'
t.timeline.msgBox3 = '注：诊断名称前面有*的是主要诊断'
t.timeline.msgBox4 = '展示手术记录的手术名称'

// 审核记录
t.auditRecord = {}
t.auditRecord.pass = '通过'
t.auditRecord.reject = '拒绝'
t.auditRecord.applyer = '申请用户'
t.auditRecord.applyReason = '申请原因'
t.auditRecord.viewReasonFile = '查看申请原因附件'
t.auditRecord.currStatus = '当前状态'
t.auditRecord.auditor = '审核人'
t.auditRecord.auditTime = '审核时间'
t.auditRecord.rejectReason = '审核不通过原因'
t.auditRecord.viewFileFirst = '请先查看附件!'
t.auditRecord.inputRejectReasonFirst = '请先填写拒绝理由'
t.auditRecord.auditRecordDetail = '审核记录详情'
t.auditRecord.exportMethod = '导出方式'

t.dataStore = {}
t.dataStore.fullTextSearch = '全文检索'
t.dataStore.advancedSearch = '高级检索'
t.dataStore.patientList = '患者列表'
t.dataStore.dataList = '数据列表'
t.dataStore.theme = '主题'
t.dataStore.inputRegno = '请输入登记号'
t.dataStore.admno = '就诊号'
t.dataStore.inputAdmno = '请输入就诊号'
t.dataStore.clear = '重置'
t.dataStore.claim = '说明：当前功能的目的是为了了解数据情况，只展示部分患者数据,如果需要查看全部数据，当前功能暂不支持'
t.dataStore.selectModel1st = '请先选择模型'
t.dataStore.admTime1st = '首诊时间'
t.dataStore.admAge1st = '首诊年龄'
t.dataStore.admTimeLast = '最近一次就诊时间'

t.dsl = {}
t.dsl.dslSearch = 'DSL查询语句'
t.dsl.search = '查询语句'
t.dsl.please = '请'
t.dsl.select = '选择'
t.dsl.searchDSL = '查询DSL语句'
t.dsl.startSearch = '开始检索'
t.dsl.inputConditionDesc = '请输入查询条件描述'
t.dsl.inputKey = '请输入查询关键字'
t.dsl.application = '应用'
t.dsl.dslStatement = 'DSL语句'

// 全文检索
t.fullText = {}
t.fullText.placeholder = '疾病、用药、手术、检验、检查等'
t.fullText.searchHistory = '检索历史'
t.fullText.unlimit = '不限'
t.fullText.positive = '阳性'
t.fullText.negative = '阴性'
t.fullText.filterWords = '筛选检索词'
t.fullText.synonym = '同义词'
t.fullText.setRange = '指定范围'
t.fullText.startDate = '开始日期'
t.fullText.endDate = '结束日期'
t.fullText.to = '至'
t.fullText.dischargeTime = '出院时间'
t.fullText.dataType = '数据类型'
t.fullText.searchErr = '查询出错'
t.fullText.getIndexIdErr = '获取indexId失败,无法进行跳转!'
t.fullText.checkGroupName = '检验套名称'
t.fullText.setCondition1st = '暂无患者数据，请设置条件'
t.fullText.noMatchPatient = '未找到匹配的患者信息'
t.fullText.searching = '努力查询中'
t.fullText.searchConn = '检索条件'
t.fullText.searchTime = '检索时间'
t.fullText.filterString = '过滤条件'
t.fullText.keywordType = '阴阳性类型'
t.fullText.indexId = '所属模型ID'
t.fullText.searchGuid = '查询Guid'

// 高级检索
t.advanced = {}
t.advanced.accordingTo = '根据'
t.advanced.searchTemplate = '查询模板'
t.advanced.joinTemplate = '加入查询模板'
t.advanced.joinProject = '加入项目'
t.advanced.setSearchDesc = '设置查询描述'
t.advanced.inputSearchDesc = '输入查询描述'
t.advanced.searchDescNotNull = '查询描述不可为空'
t.advanced.brintIntoNotNull = '纳入标准不可为空'
t.advanced.checkSearchField = '请检查查询字段'
t.advanced.exportSuccess = '导出成功，请前往导出记录中查看或下载'
t.advanced.inputCondition1st = '请先填写纳排标准'
t.advanced.selectProject = '选择项目'
t.advanced.proCaution = '请选择项目'
t.advanced.noProject = '暂无项目，请联系EDC系统的系统管理员创建项目后再入组。'
t.advanced.noProject1 = '暂无项目数据'
t.advanced.joinEDCMsg = '正在向EDC中入组患者,该过程需要一段时间,请耐心等待后去EDC查看确认'
t.advanced.matchCase1 = '为您找到相关患者'
t.advanced.unit1 = '个'
t.advanced.msgTips1 = '检索结果加入项目，进行前瞻性研究'
t.advanced.msgTips2 = '检索结果导出到excel'
t.advanced.msgTips3 = '检索结果可视化图表展示'
t.advanced.msgTips4 = '检索结果在线分析'
t.advanced.dataExport = '数据导出'
t.advanced.statistics = '统计信息'
t.advanced.analysis = '数据分析'
t.advanced.birthday = '出生日期'
t.advanced.allCase = '全部病历'
t.advanced.unit2 = '份'
t.advanced.matchCase = '符合条件病历'
t.advanced.diagnostic = '诊断信息'
t.advanced.viewMore = '查看更多'
t.advanced.toDetailFail = '未查询到就诊号或登记号，无法查看患者详情'
t.advanced.batchFilterPatient = '批量筛选患者'
t.advanced.selectFilterField = '选择筛选字段'
t.advanced.uploadExcelFile = '上传excel文件'
t.advanced.clickUpload = '点击上传'
t.advanced.downloadTemplate = '下载模板'
t.advanced.patientID = '病案号'
t.advanced.uploadTypeLimit = '请上传文件类型为.xls/.xlsx的文件'
t.advanced.uploadInputMultiTips = '请上传文件类型为.xls/.xlsx/.csv/.txt的文件，其中表格文件数据取第一列，文本文件使用英文逗号拼接'
t.advanced.fileInputStr = '上传的文件数据'
t.advanced.please = '请'
t.advanced.add = '添加'
t.advanced.orUse = '或使用'
t.advanced.helpSearch = '帮助查询'
t.advanced.viewDSL = '查看DSL'
t.advanced.viewJson = '查看JSON'
t.advanced.viewDesc = '查看文字描述'
t.advanced.copyJson = '复制JSON'
t.advanced.copySuccess = '复制到剪贴板成功!'
t.advanced.checkBringInto = '请检查纳入标准条件'
t.advanced.checkBringOut = '请检查排除标准条件'
t.advanced.collection = '我的收藏'
t.advanced.inputExportTempDesc = '请输入导出模板描述'
t.advanced.accurateExport = '精确导出'
t.advanced.sqlExport = '原表导出'

// 数据分析
t.analysis = {}
t.analysis.addSet = '新建数据集'
t.analysis.setObPeriod = '设置观测阶段'
t.analysis.setDataFormat = '设置数据格式'
t.analysis.generateSet = '生成数据集'
t.analysis.addObPeriod = '添加观测阶段'
t.analysis.lastStep = '上一步'
t.analysis.next = '下一步'
t.analysis.generate = '生成'
t.analysis.addObPeriod1st = '请先添加观测阶段'
t.analysis.inputSetName = '数据集名称'
t.analysis.selectTable = '选择表'
t.analysis.batchSetSpecialValue = '批量设置特殊值'
t.analysis.selectSpecialValue = '选择特殊值'
t.analysis.first = '第一次'
t.analysis.last = '最后一次'
t.analysis.obPeriod = '观测阶段'
t.analysis.setExportItem = '设置导出项'
t.analysis.allPeriod = '全阶段'
t.analysis.comparisonCondition = '比较条件'
t.analysis.timeRange = '时间范围'
t.analysis.exporting = '正在导出需要分析的数据，请稍后前往'
t.analysis.analysisSystem = '在线分析系统'
t.analysis.pleaseSelectObPeriod = '请选择观测阶段'
t.analysis.getObPeriodErr = '获取观测阶段失败,'
t.analysis.setObPeriod1st = '请先设置观测阶段'
t.analysis.setObPeriod1st2 = '存在观测阶段未设置导出项，请先设置观测阶段导出项'
t.analysis.exportingWaiting = '导出正在进行中，请耐心等候'
t.analysis.filterNeedAdd = '有过滤条件未添加'
t.analysis.researchObject = '研究对象'
t.analysis.patient = '患者'
t.analysis.dataFormat = '数据格式'
t.analysis.custom = '自定义'
t.analysis.selectAdm = '选择就诊'
t.analysis.firstAdm = '首次就诊'
t.analysis.lastAdm = '最后一次就诊'
t.analysis.setSpecialValue = '设置特殊值'
t.analysis.table = '表'

// 历史与收藏
t.history = {}
t.history.collection = '收藏'
t.history.removeCollection = '取消收藏'
t.history.medicalRecordNum = '病历号'

// 查询模板
t.template = {}
t.template.inputSearchDesc = '请输入查询描述'

// 统计信息
t.statistics = {}
t.statistics.statisticsInfo = '统计信息'
t.statistics.basicSituation = '病历基本情况'
t.statistics.relatedMRTop10 = '相关病历统计TOP10'
t.statistics.patientMRDistribution = '患者病历分布'
t.statistics.number = '数量'

// 组件
t.component = {}
t.component.formatError = '格式化代码出错：'
t.component.initCodeMirrorErr = '初始化codemirror出错：'
t.component.getInputValErr = '获取编辑框内容失败：'
t.component.updateInputValErr = '修改编辑框内容失败：'
t.component.inputStrLimit = '编辑框内容只能为字符串'
t.component.selectIndex = '选择指标'
t.component.addSameLevelCond = '添加同级条件'
t.component.addLevel2Cond = '添加二级条件'
t.component.turn2Level2 = '转为二级条件'
t.component.turn2Level1 = '转为一级条件'
t.component.addChildCond = '添加子条件'
t.component.allField = '全字段'
t.component.specialDiseaseField = '专病字段'
t.component.getSearchItemErr = '获取查询项错误'
t.component.startDate = '开始日期'
t.component.endDate = '结束日期'
t.component.to = '至'
t.component.click2Edit = '点击修改'
t.component.enter2Save = '请输入文本后按回车确定'
t.component.loading = '加载中'
t.component.selectModel = '选择模型'
t.component.onlyNotSpecial = '只加载非专病模型的索引'
t.component.configModel1st = '暂无相关模型数据,需要配置相关模型'
t.component.toEnableModel = '请联系管理员启用模型'
t.component.defineFilterCond = '自定义过滤条件'
t.component.here = '这里'
t.component.noSuitFilter = '没有合适的过滤条件？点击'
t.component.itemValue = '属性项'
t.component.filterCond = '过滤条件'
t.component.editCond = '编辑条件'
t.component.least1NotNull = '至少一个过滤项属性不能为空'
t.component.any = '任意'
t.component.setExportProp = '设置导出属性'
t.component.noExportableProp = '暂无可导出的属性'
t.component.exportSetConfig = '导出设置配置'
t.component.separateByAdvice = '是否按医嘱项拆分列：'
t.component.separateByCheckItem = '是否按检查项拆分列：'
t.component.separateByItem = '是否按检验项拆分列：'
t.component.montageRecords = '用于拼接多次记录的符号：'
t.component.dataSameTable = '用于同一个表多条数据拼接符号：'
t.component.compressExportFile = '是否要压缩导出后的文件：'
t.component.writeIntoSheet = '导出的列超出excel时是否写入新的sheet：'
t.component.exportMsg1 = '暂无可导出的特殊值，请先选择含有特殊值的导出项'
t.component.exportMsg2 = '说明：特殊导出值配置需要在 后台管理-特殊值配置 中进行配置'
t.component.setExportSpecial = '设置导出特殊值'
t.component.inputExportFileName = '请输入导出文件名'
t.component.fileName = '文件名'
t.component.exportFormatClaim = '导出格式：一个观测阶段一个页签，每个观测阶段内，一次就诊一行数据'
t.component.name = '名称'
t.component.total = '总数'
t.component.percent = '百分比'
t.component.exportModelType = '导出模型类型：'
t.component.observePeriod = '观测阶段'
t.component.first = '首次'
t.component.exportSuccess = '导出任务提交完成，是否跳转到导出记录页面，查看导出记录?'
t.component.filterRange = '过滤范围：'
t.component.filterRangeTime = '时间范围跨就诊的数据'
t.component.filterRangeHit = '检索命中就诊的数据'
t.component.customExport = '自定义导出'
t.component.setExportContent = '自由设置导出内容'
t.component.setExportFilterCond = '设置导出过滤条件'
t.component.startExport = '开始导出'
t.component.useExportHistory = '复用历史导出'
t.component.useExportHistory1 = '直接复用历史导出'
t.component.selectExportRecord = '选择导出记录'
t.component.updateExportContent = '修改导出内容'
t.component.exportTemplate = '导出模板'
t.component.frequentExport = "管理员推荐，常用导出"
t.component.selectExportTemplate = "选择导出模板"
t.component.setExportDesc = '设置导出描述'
t.component.checkCond = '请检查条件'

export default t
