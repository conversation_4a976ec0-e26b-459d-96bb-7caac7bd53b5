
// 获取指定项目的全部子项目
export const queryAllSubApi = (projectId) => {
  return {
    url: `edc/subject/select-by-project/${projectId}`,
    method: 'get'
  }
}

export const deleteSubject = (subjectId) => {
  return {
    url: `edc/subject/delete/${subjectId}`,
    method: 'post'
  }
}

// 获取登录用户的项目缓冲信息
export const getSubjectCacheInfo = (subjectId) => {
  return {
    url: `edc/subject/subject-cache-info/${subjectId}`,
    method: 'get'
  }
}

// 获取子项目的全部入组字段列表
export const queryAllIntoGroupAttr = (params) => {
  return {
    url: `edc/patient-attribute/list`,
    method: 'get',
    params
  }
}

// 患者列表获取用户所属中心配置的字段列表
export const queryOrgPatientListAttr = (params) => {
  return {
    url: `edc/subject-org-config/patient-attr-config`,
    method: 'get',
    params
  }
}

// 获取子项目的菜单
export const getSubjectMenu = (params) => {
  return {
    url: `edc/role-auth/resource`,
    method: 'get',
    params
  }
}

// 获取子项目的页面元素权限
export const getSubjectPermission = (params) => {
  return {
    url: `edc/role-auth/page-preset`,
    method: 'get',
    params
  }
}

export const subjectPageList = (params) => {
  return {
    url: '/edc/subject/page',
    method: 'get',
    params
  }
}

// 根据id批量删除子项目
export const deleteBatchApi = (data) => {
  return {
    url: 'edc/subject/deletion',
    method:'post',
    data
  }
}

export const editSubjectApi = (data) => {
  return {
    url: 'edc/subject/update',
    method: 'post',
    data
  }
}


