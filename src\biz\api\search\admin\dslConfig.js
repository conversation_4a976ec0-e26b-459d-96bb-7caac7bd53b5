
export const queryListApi = (params) => {
  return {
    url: 'search/dsl/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'search/dsl/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'search/dsl/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'search/dsl/insert',
    method: 'post',
    data,
  }
}

export const editModel = (data) => {
  return {
    url: 'search/dsl/update',
    method: 'post',
    data
  }
}



