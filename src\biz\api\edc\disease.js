
// 获取全部机构列表
export const queryAllApi = (params) => {
  return {
    url: 'edc/subject-disease/list',
    method: 'get',
    params,
  }
}


export const queryListApi = (params) => {
  return {
    url: 'edc/subject-disease/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/subject-disease/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/subject-disease/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/subject-disease/insert',
    method: 'post',
    data,
  }
}

export const editDisease = (data) => {
  return {
    url: 'edc/subject-disease/update',
    method: 'post',
    data
  }
}

// 批量设置患者当前病种
export const setPatCurDisease = (data) => {
  return {
    url: 'edc/patient/update-disease',
    method: 'post',
    data
  }
}

// 获取患者病种流转历史
export const queryPatDiseaseHistory = (params) => {
  return {
    url: 'edc/patient/get-disease-history',
    method: 'get',
    params
  }
}

// 获取全程随访第一阶段的表单字段
export const getFirstPeriodFormFields = (params) => {
  return {
    url: 'edc/subject-form/disease/period/tree',
    method: 'get',
    params
  }
}

// 获取全程项目病种入组优先级配置
export const getDiseasePriorityConfig = (params) => {
  return {
    url: 'edc/subject-disease/get-cur-config',
    method: 'get',
    params
  }
}

// 设置全程项目入组优先级
export const setDiseasePriorityConfig = (data) => {
  return {
    url: 'edc/subject-disease/save-priority-config',
    method: 'post',
    data
  }
}


