<template>
  <codemirror ref="myCm" v-loading="loading" :value="editorValue" :options="cmOptions" @changes="onCmCodeChanges"
    @blur="onCmBlur" @paste.native="OnPaste" />
</template>

<script>
  import {
    codemirror
  } from "vue-codemirror"
  import "codemirror/mode/javascript/javascript"
  import "codemirror/mode/sql/sql"
  import 'codemirror/lib/codemirror.css'
  import "codemirror/theme/idea.css"
  import "codemirror/addon/hint/show-hint.js"
  import "codemirror/addon/hint/show-hint.css"
  import "codemirror/addon/hint/sql-hint.js"
  import "codemirror/addon/edit/closebrackets"
  // 折叠
  import 'codemirror/addon/fold/foldgutter.css'
  import 'codemirror/addon/fold/foldcode'
  import 'codemirror/addon/fold/foldgutter'
  import 'codemirror/addon/fold/brace-fold'

  import sqlFormatter from "sql-formatter"

  export default {
    components: {
      codemirror
    },
    props: {
      cmMode: {
        type: String,
        default: ""
      },
      readOnly: {
        type: Boolean
      },
      loading: {
        type: Boolean
      }
    },
    data() {
      return {
        tabSize: 4,
        editorValue: "",
        cmOptions: {
          readOnly: this.readOnly,
          theme: 'idea',
          mode:
            !this.cmMode || this.cmMode == "default"
            ? "application/json" : this.cmMode,
          lineWrapping: true,
          lineNumbers: true,
          smartIndent: true,
          autocorrect: true,
          spellcheck: true,
          extraKeys: {
            "Ctrl-Alt-L": () => {
              try {
                if (
                  this.cmOptions.mode == "application/json" &&
                  this.editorValue
                ) {
                  this.editorValue = this.formatStrInJson(this.editorValue)
                }

                if (this.cmOptions.mode == 'text/x-sql' && this.editorValue) {
                  this.editorValue = sqlFormatter.format(this.editorValue)
                }
              } catch (e) {
                this.$message.error("格式化代码出错：" + e.toString())
              }
            },
            "Tab": "autocomplete"
          },
          lint: true,
          autoCloseBrackets: true,
          styleActiveLine: true,
          autoRefresh: true,
          highlightSelectionMatches: {
            minChars: 2,
            style: "matchhighlight",
            showToken: true
          },
          styleSelectedText: true,
          enableAutoFormatJson: this.autoFormatJson == null ? true : this.autoFormatJson,
          defaultJsonIndentation: this.jsonIndentation ? this.jsonIndentation : 2,
          // 折叠
          foldGutter: true,
          gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
        },
        enableAutoFormatJson: this.autoFormatJson == null ? true : this.autoFormatJson,
        defaultJsonIndentation: this.jsonIndentation ? this.jsonIndentation : 2,
      }
    },
    created() {
      try {
        if (!this.editorValue) {
          this.cmOptions.lint = false
          return
        }

        if (this.cmOptions.mode == "application/json") {
          if (!this.enableAutoFormatJson) {
            return
          }

          this.editorValue = this.formatStrInJson(this.editorValue)
        }
      } catch (e) {
        this.$message.error("初始化codemirror出错：" + e)
      }
    },
    methods: {
      resetLint() {
        if (!this.$refs.myCm.codemirror.getValue()) {
          this.$nextTick(() => {
            this.$refs.myCm.codemirror.setOption("lint", false)
          })

          return
        }

        this.$refs.myCm.codemirror.setOption("lint", false)

        this.$nextTick(() => {
          this.$refs.myCm.codemirror.setOption("lint", true)
        })
      },

      // 获取值
      getValue() {
        try {
          return this.$refs.myCm.codemirror.getValue()
        } catch (e) {
          const errorInfo = e.toString()
          this.$message.error("获取编辑框内容失败：" + errorInfo)
          return errorInfo
        }
      },

      // 修改值
      setValue(value) {
        try {
          if (typeof value !== typeof "") {
            this.$message.error("修改编辑框内容失败：编辑宽内容只能为字符串")
            return
          }
          if (this.cmOptions.mode == "application/json") {
            this.editorValue = this.formatStrInJson(value)
          } else {
            this.editorValue = value
          }
        } catch (e) {
          this.$message.error("修改编辑框内容失败：" + e.toString())
        }
      },

      // 黏贴事件处理函数
      OnPaste(event) {
        if (this.cmOptions.mode == "application/json") {
          try {
            this.editorValue = this.formatStrInJson(this.editorValue)
          } catch (e) {
            // 啥都不做
          }
        }
      },

      // 失去焦点时处理函数
      onCmBlur(cm, event) {
        try {
          const editorValue = cm.getValue()
          if (this.cmOptions.mode == "application/json" && editorValue) {
            if (!this.enableAutoFormatJson) {
              return
            }
            this.editorValue = this.formatStrInJson(editorValue)
          }

          if (this.cmOptions.mode == 'text/x-sql' && editorValue) {
            this.editorValue = sqlFormatter.format(editorValue)
          }
        } catch (e) {
          // 啥也不做
        }
      },

      onCmCodeChanges(cm, changes) {
        this.editorValue = cm.getValue()
        this.resetLint()
      },

      // 格式化字符串为json格式字符串
      formatStrInJson(strValue) {
        return JSON.stringify(
          JSON.parse(strValue),
          null,
          this.defaultJsonIndentation
        )
      }
    }
  }

</script>
