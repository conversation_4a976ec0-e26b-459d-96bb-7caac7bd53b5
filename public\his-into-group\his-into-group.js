function getUrl() {
  return location.origin;
  // return hisIntoGroupBaseUrl + location.search
}
var baseUrl = null;
if (window.location.hostname === "localhost") {
  baseUrl = "http://localhost:9086";
} else {
  baseUrl = location.origin;
}
$(function () {
  var doctorName = "";
  var userId = "";
  var orgId = "";
  var projectList = [];
  var subjectId = "";
  var subjectInfo = null;
  var projectId = "";
  var roleId = "";
  var subProjectEnableAudit = false;
  var formReadonly = false;
  var patientInfo = { regno: regNo };
  var isCenterCase = 0; // 是否允许从外部接口获取数据 0表示不允许
  var baseData = [];
  var metaData = []; // 过滤基本字段后的数组，避免基本字段在拓展表单中重复展示
  var basicInfoFieldObj = { regno: "input", name: "input", initials: "input", gender: "input", idCard: "input", recordId: "input", birthday: "date", intoDate: "date", tel: "input", filterNo: "input", isHisCase: "input", sysUniqueId: "input", projUniqueId: "input" };
  var genderList = [
    { label: "男", value: 1 },
    { label: "女", value: 2 },
    { label: "不确定", value: 3 }
  ];
  var externalResult = [];
  var groupList = [];
  var diseaseList = [];
  var tagList = [];
  // if(isIE()){
  //   $("#chrome").show();
  // }else{
  //   $("#chrome").hide();
  // }
  $("#chrome").on("click", function () {
    // 跳转chrome
    goChrome();
  });
  initApi(function () {
    ajaxGet({
      url: "/openApi/project/" + foreignId + "/" + orgCode,
      success: renderProjectList,
      error: function (err) {
        console.log(err);
      }
    });
  });

  $("#submit").off("click").on("click", submitClick);
  $("#toEdc").off("click").on("click", toEdc);
  function initApi(callback) {
    ajaxGet({
      url: "/openApi/openapibase/apisystem/getAcessToken?appId=Wm7j11Em2fa4&appSecret=65q1P96207G4k9H8",
      success: function (res) {
        localStorage.setItem("his-api-access-token", res);
        callback();
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
  function submitClick() {
    if ($("#submit").prop("disabled")) {
      return;
    }
    var formValue = getFormValue();
    if (!formValue) {
      return;
    }
    var data = $.extend({}, formValue, { projectId: projectId, subjectId: subjectId, orgId: orgId, intoUserId: userId });
    console.log("data", data);
    ajaxPost({
      url: "/openApi/patient/his-into-patient",
      data: data,
      success: function () {
        alert("入组成功");
      }
    });
  }
  function getTimestamp(callback){
    ajaxGet({
      url: "/openApi/custom-valid/time-stamp",
      success: function (res) {
        callback(res);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
  function toEdc() {
    if (!subjectId) {
      return;
    }
    getTimestamp(function(timestamp){
      var aesString = aesEncrypt("signature_csm^" + timestamp);
      var redirect = encodeURIComponent("/edc/subject-transfer?url=/edc/p/patient/patient-list&subjectId=" + subjectId);
      var href = baseUrl + "/csm/login-middle-v1/index?forHis=1&accountId=" + userId + "&aesString=" + aesString + "&redirect=" + redirect;
      window.open(href, "csm_window");
      // openChromeByIMedicalBrowser(href)
    });
  }
  // ================医为客户端调用谷歌浏览器的方法 start =====================
  var EnableLocalWeb = '1';
  var WEBSYSHTTPSERVERURL = "https://localhost:21996/websys/";
  var defaultDllDir = location.href.slice(0,location.href.indexOf("web/"))+"web/addins/plugin";
  var myXmlHttp = null,debuggerflag=false,isUseGetMethod = false,isMozilla = false;
  function websysAjax(bizUrl,data,notReturn,callback){function invkProcessReq(){if(req.readyState === 4 && (req.status===0||req.status === 200||req.status === 500)){ try{var result = "var rtn="+req.responseText;if (result=="var rtn="){result = 'var rtn={"msg":"error","status":404,"rtn":null}';}; eval(result);}catch(exx){} if ("string"==typeof callback && window[callback]){window[callback].call(req,rtn);} if ("function"==typeof callback){callback.call(req,rtn);}}}
    notReturn = notReturn||0; async=false; if (notReturn==1) async=true;	var url = WEBSYSHTTPSERVERURL + bizUrl;	var cspXMLHttp = null;	if (window.XMLHttpRequest) { 	isMozilla = true;		cspXMLHttp = new XMLHttpRequest();	} else if (window.ActiveXObject) { 	isMozilla = false;		try {			cspXMLHttp=new ActiveXObject("Microsoft.XMLHTTP");		} catch (e) {			try { 		 		cspXMLHttp=new ActiveXObject("Msxml2.XMLHTTP");				} catch (E) {				cspXMLHttp=null;			}		}	}	var req = cspXMLHttp;	req.onreadystatechange = invkProcessReq;	var dataArr = [],dataStr = data,timeout=60000;	if ("object"==typeof data){	if(data.slice){		for(var i=0;i<data.length;i++){			for(var j in data[i]){				if (j==="_timeout"){timeout=data[i][j]; continue;}				dataArr.push(j+"="+encodeURIComponent(data[i][j]));			}		}	}else{		for(var k in data){			dataArr.push(k+"="+encodeURIComponent(data[k]));		}	}	dataStr = dataArr.join("&");}	if (isUseGetMethod) {		req.open("GET", url+"?"+dataStr, async);		if (isMozilla) {			req.send(null);		} else {			req.send();		}	} else {		req.open("POST", url, async);		if (async){req.timeout=timeout;}		req.setRequestHeader("NotReturn-Type", notReturn);		req.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");		try{req.send(dataStr);}catch(e){return invkProcessResponse(e,callback);}	}return invkProcessResponse(req);}
  function invkProcessResponse(req,cb) {if(debuggerflag){ debugger;}	if("undefined"==typeof req.status) {/*exception*/		var err=req.name+'('+req.message+')'; /*alert(err);*/ 		if ("function"==typeof cb){cb.call(req,{"msg":err,"status":404,"rtn":null});}		return {"msg":err,"status":404,"rtn":null};	}	if(req.status != 200 && req.status != 500) {		var err=req.statusText+ ' (' + req.status + ')'; 		if ("function"==typeof cb){cb.call(req,{"msg":err,"status":req.status,"rtn":null});}		return {"msg":err,"status":req.status,"rtn":null};	}	var result="var a = "+req.responseText;	eval(result);return a;}
  function invokeDll(mode,ass,cls,q,notReturn,callback){	return websysAjax(ass+'/'+cls,q,notReturn,callback);};

  function ICls() {this.data=[];this.mode=0;this.notReturn=0;this.ass="";this.cls="";this.focusClassName="";this.focusWindowName="";this.focusLazyTime=1000;this.timeout=60000;}
  ICls.prototype.constructor=ICls;
  ICls.prototype.invk = function(c){if (this.focusClassName!="")this.data.push({"_focusClassName":this.focusClassName});if (this.focusWindowName!="")this.data.push({"_focusWindowName":this.focusWindowName}); if((this.focusClassName!="") || (this.focusWindowName!="")) this.data.push({"_focusLazyTime":this.focusLazyTime});this.data.push({"_timeout":this.timeout});var rtn = invokeDll(this.mode,this.ass,this.cls,this.data,this.notReturn,c);return rtn ;};
  ICls.prototype.clear = function(){this.data.length=3;return this;};
  ICls.prototype.prop = function(k,v){var o = {};o[k]=v;this.data.push(o);return this;};
  ICls.prototype.getMthParam = function(arg){	if (!arg.length) return "";	var len = arg.length,hasCallback = 0;	if (len>0&&"function"==typeof arg[len-1]){hasCallback=1;};	var param = "";	if (arg.length>0){		param = "P_COUNT="+(len-hasCallback);		for(var i=0; i<arg.length-hasCallback; i++){			param += "&P_"+i+"="+encodeURIComponent(arg[i]);		}	}return param;};
  ICls.prototype.cmd = function(c){
    this.data.push({"_cmd":c});
    if(arguments.length>0&&"function"==typeof arguments[arguments.length-1]){return this.invk(arguments[arguments.length-1])};
    return this.invk();
  }
    ICls.AssDirList=[];
  /*获取客户端信息*/
  ICls.CmdShell = function(){
    this.ass = "cmd";	this.cls = "cmd";	this.data.push({"_dllDir":defaultDllDir+"/"});	this.data.push({"_version":"*******"});
    this.data.push({"_clientIPExp":""});	this.GetInfo=function(){	this.data.push({"M_GetInfo":this.getMthParam(arguments)});	return this;}
    this.GetIP=function(){	this.data.push({"M_GetIP":this.getMthParam(arguments)});	return this;}
    this.Run=function(){		this.clear();	this.data.push({"M_Run":this.getMthParam(arguments)});   if(arguments.length>0&&"function"==typeof arguments[arguments.length-1]){return this.invk(arguments[arguments.length-1]);}	return this.invk();}
    this.EvalJs=function(){		this.clear();	this.data.push({"M_EvalJs":this.getMthParam(arguments)});   if(arguments.length>0&&"function"==typeof arguments[arguments.length-1]){return this.invk(arguments[arguments.length-1]);}	return this.invk();}
    this.GetConfig=function(){		this.clear();	this.data.push({"M_GetConfig":this.getMthParam(arguments)});   if(arguments.length>0&&"function"==typeof arguments[arguments.length-1]){return this.invk(arguments[arguments.length-1]);}	return this.invk();}
    this.CurrentUserEvalJs=function(){		this.clear();	this.data.push({"M_CurrentUserEvalJs":this.getMthParam(arguments)});   if(arguments.length>0&&"function"==typeof arguments[arguments.length-1]){return this.invk(arguments[arguments.length-1]);}	return this.invk();}
  }
  ICls.CmdShell.prototype = new ICls();
  ICls.CmdShell.prototype.constructor = ICls.CmdShell;
  var CmdShell = new ICls.CmdShell();
  function openChromeByIMedicalBrowser(command){
      var str = "var wsh=new ActiveXObject('WScript.Shell');if (wsh){wsh.Run('" + command + "');wsh=null;}"
      if (CmdShell) {
          CmdShell.clear();
          CmdShell.notReturn = 1;
          CmdShell.EvalJs(str);
      }
  }
  // ================医为客户端调用谷歌浏览器的方法 end =====================
  function renderProjectList(res) {
    // 获取到科研用户姓名
    if (res && res.accountName) {
      doctorName = res.accountName;
      userId = res.accountId;
      orgId = res.orgId;
      $("#doctorName").html(doctorName);
    }
    if (res && res.hisPreProjModelList && res.hisPreProjModelList.length > 0) {
      // 拼数据
      var temArr = [];
      for (var i = 0; i < res.hisPreProjModelList.length; i++) {
        var item = res.hisPreProjModelList[i];
        // 单病种项目
        if (item.type === 1) {
          temArr.push(
            $.extend({}, item.subjectList[0], {
              subjectId: item.subjectList[0].id,
              totalProjectName: item.subjectList[0].name
            })
          );
        } else {
          var projectName = item.projectName;
          for (var j = 0; j < item.subjectList.length; j++) {
            var item2 = item.subjectList[j];
            temArr.push(
              $.extend({}, item2, {
                subjectId: item2.id,
                totalProjectName: projectName + "-" + item2.name
              })
            );
          }
        }
      }
      projectList = temArr;
    } else {
      this.projectList = [];
    }
    renderSelect("#subjectSelect", projectList, {
      label: "totalProjectName",
      value: "subjectId"
    });
    $("#subjectSelect").val("").off("change").on("change", subjectChange);
  }
  function subjectChange() {
    subjectId = $("#subjectSelect").val();
    var tmp = $.grep(projectList, function (item) {
      return item.subjectId == subjectId;
    });
    subjectInfo = tmp.length > 0 ? tmp[0] : null;
    if (subjectInfo) {
      projectId = subjectInfo.projectId;
      roleId = subjectInfo.roleId;
      subProjectEnableAudit = subjectInfo.isEnableAudit;
      ajaxGet({
        url: "/openApi/patient/is/input?subjectId=" + subjectId + "&regno=" + regNo,
        success: function (res) {
          // 如果返回1 表示已经his入组过, 禁用表单
          // 如果返回0 表示没有his入组过, 可填写表单
          if (res) {
            formReadonly = true;
            $("#submit").prop("disabled", true);
            ajaxGet({
              url: "/openApi/patient/info?subjectId=" + subjectId + "&regno=" + regNo,
              success: function (res) {
                patientInfo = res;
                renderCaseIntoGroup();
              }
            });
          } else {
            formReadonly = false;
            $("#submit").prop("disabled", false);
            patientInfo = { regno: regNo };
            renderCaseIntoGroup();
          }
        }
      });
      getSubjectInfo();
    }
  }
  function getSubjectInfo() {
    ajaxGet({
      url: "/openApi/project/subject-cache-info/" + subjectId + "/" + userId,
      success: function (res) {
        if (res) {
          $("#emptySubjectInfo").hide();
          $("#subjectInfo").show();
          $("#projectAdminName .form-control").text(res.projectAdminName);
          if (res.otherOrgList && res.otherOrgList.length > 0) {
            $("#mainOrg .control-label").text("主中心");
            $("#otherOrg").show();
            var otherOrgName = "";
            $.each(res.otherOrgList, function (index, item) {
              otherOrgName += (index > 0 ? "、" : "") + item.orgName;
            });
            $("#otherOrg .form-control").text(otherOrgName);
          } else {
            $("#mainOrg .control-label").text("项目所属中心");
            $("#otherOrg").hide();
          }
          if (res.mainOrg && res.mainOrg.orgName) {
            $("#mainOrg .form-control").text(res.mainOrg.orgName);
          } else {
            $("#mainOrg .form-control").text("暂无");
          }
          if (res.mainDept && res.mainDept.deptName) {
            $("#mainDept").show();
            $("#mainDept .form-control").text(res.mainDept.deptName);
            if (res.otherDeptList && res.otherDeptList.length > 0) {
              $("#otherDept").show();
              var otherDeptName = "";
              $.each(res.otherDeptList, function (index, item) {
                otherDeptName += (index > 0 ? "、" : "") + item.deptName;
              });
              $("#otherDept .form-control").text(otherDeptName);
            } else {
              $("#otherDept").hide();
            }
          } else {
            $("#mainDept").hide();
            $("#otherDept").hide();
          }
          var projectTime = "";
          if (res.projectStartTime && res.projectEndTime) {
            projectTime = res.projectStartTime + " 至 " + res.projectEndTime;
          } else {
            projectTime = "暂无";
          }
          $("#projectTime .form-control").text(projectTime);
          $("#description .form-control").text(res.description ? res.description : '暂无');
        } else {
          $("#emptySubjectInfo").show();
          $("#subjectInfo").hide();
        }
      }
    });
  }
  function renderCaseIntoGroup() {
    getCaseExpandField(function () {
      $("#empty").hide();
      $("#into-group-form").show();
      // 基础字段
      var baseDataHtml = "";
      $.each(baseData, function (index, item) {
        baseDataHtml += '<div class="form-group">' + '<label class="col-sm-2 control-label">' + (item.required ? '<span style="color:red">*</span>' : "") + item.label + "</label>" + '<div class="col-sm-10">' + getInputHtml(item) + "</div>" + "</div>";
      });
      $("#base-data").html(baseDataHtml);
      // 扩展字段
      var metaDataHtml = "";
      $.each(metaData, function (index, item) {
        metaDataHtml += '<div class="form-group">' + '<label class="col-sm-2 control-label">' + (item.required ? '<span style="color:red">*</span>' : "") + item.label + "</label>" + '<div class="col-sm-10">' + getInputHtml(item) + "</div>" + "</div>";
      });
      $("#meta-data").html(metaDataHtml);
      if (formReadonly) {
        setPatientInfo();
        getExternalInfo();
      } else {
        getInfo(function () {
          console.log("ppp", patientInfo);
          setPatientInfo();
          getExternalInfo();
        });
      }
    });
  }
  function setPatientInfo() {
    $.each(baseData, function (index, item) {
      setFieldValue(item, patientInfo[item.code]);
    });
    var extJson = patientInfo.extJson ? JSON.parse(patientInfo.extJson) : {};
    $.each(metaData, function (index, item) {
      if (item.widgetOption) {
        try {
          item.widgetOption = JSON.parse(item.widgetOption);
        } catch (e) {
          item.widgetOption = [];
        }
      }
      if (item.type == "select" || item.type == "selectMulti") {
        renderSelect("#" + item.code, item.widgetOption);
        $("#" + item.code).val("");
      } else if (item.type == "radio") {
        renderRadio("#" + item.code, item.widgetOption);
      } else if (item.type == "checkbox") {
        renderCheckbox("#" + item.code, item.widgetOption);
      }
      setFieldValue(item, extJson[item.code]);
    });
    $("#name").off("input").on("input", getInitials).off("change").on("change", getInitials).off("blur").on("blur", getInitials);
    getInitials();
    $(".date-control").datepicker({
      language: "zh-CN",
      dateFormat: "yy-mm-dd"
    });
    $("#initials").prop("disabled", true);
    getGenderOption(function () {
      renderSelect("#gender", genderList);
      if (!patientInfo.gender) {
        $("#gender").val("");
      } else {
        $("#gender").val(patientInfo.gender);
      }
    });
    if ($("#groupIds").length) {
      getGroupList(function () {
        renderSelect("#groupIds", groupList, { label: "name", value: "id" });
        if (!patientInfo.groupIds) {
          $("#groupIds").val("");
        } else {
          $("#groupIds").val(patientInfo.groupIds);
        }
      });
    }
    if ($("#diseaseId").length) {
      getDiseaseList(function () {
        renderSelect("#diseaseId", diseaseList, { label: "name", value: "id" });
        if (!patientInfo.diseaseId) {
          $("#diseaseId").val("");
        } else {
          $("#diseaseId").val(patientInfo.diseaseId);
        }
      });
    }
    if ($("#labelIds").length) {
      getTagList(function () {
        renderSelect("#labelIds", tagList, { label: "name", value: "id" });
        $("#labelIds").attr("multiple", "multiple");
        if (!patientInfo.labelIds) {
          $("#labelIds").val(null);
        } else {
          var value = patientInfo.labelIds;
          if (value && typeof value == "string") {
            value = value.split(",");
          }
          $("#labelIds").val(value);
        }
      });
    }
  }
  // 当患者是his患者时，调用his接口，获取患者信息，回显
  function getInfo(callback) {
    ajaxGet({
      url: "/openApi/patient/get-patient-info?regno=" + patientInfo.regno,
      success: function (res) {
        if (res) {
          if (res.labelIds) {
            res.labelIds = res.labelIds.split(",");
          }
          $.each(res, function (key, value) {
            if (!patientInfo[key]) {
              patientInfo[key] = value;
            }
          });
          if (!patientInfo.intoDate) {
            patientInfo.intoDate = formatDate();
          }
        }
        callback();
      }
    });
  }
  function formatDate() {
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1; // getMonth() 返回值是 0-11
    var day = date.getDate();

    // 确保月份和日期是两位数
    month = month < 10 ? "0" + month : month;
    day = day < 10 ? "0" + day : day;

    return year + "-" + month + "-" + day;
  }
  function getExternalInfo() {
    var temArr = [];
    $.each(metaData, function (index, item) {
      if (item.externaldataInfo && item.externaldataInfo.CatID) {
        item.ID = item.code;
        item.externaldataInfo.ID = item.code;
        temArr.push(item.externaldataInfo);
      }
    });
    if (temArr.length > 0 && isCenterCase && patientInfo.regno) {
      ajaxPost({
        url: "/openApi/external/data/get-crf-exdata-intogroup",
        data: {
          itemStr: JSON.stringify(temArr),
          regno: patientInfo.regno,
          admIds: "="
        },
        success: function (res) {
          console.log(res, "externaldataInfo");
          if (res && typeof res == "string") {
            externalResult = JSON.parse(res);
          } else {
            externalResult = res;
          }
          setExternalResult();
        }
      });
    }
  }
  function setExternalResult() {
    $.each(externalResult, function (index, item) {
      // 结果取值的字段名 从配置的itemProp中取值
      var externaldataInfo = null;
      for (var i = 0; i < metaData.length; i++) {
        if (metaData[i].ID == item.Message || metaData[i].code == item.Message) {
          externaldataInfo = metaData[i].externaldataInfo;
          break;
        }
      }
      if (!externaldataInfo) return;
      var key = "";
      if (externaldataInfo.ItemProp) {
        key = externaldataInfo.ItemProp;
      } else {
        key = externaldataInfo.ItemCode;
      }
      // 表单拓展字段配置外部接口的值,从第一个里面取,且取配置的键
      var value = "";
      if (item.DataObject && item.DataObject[0] && item.DataObject[0][key]) {
        value = item.DataObject[0][key];
      }
      var extJson = patientInfo.extJson ? JSON.parse(patientInfo.extJson) : {};
      if (!extJson[item.Message] && extJson[item.Message] !== 0) {
        var field = null;
        for (var i = 0; i < metaData.length; i++) {
          if (metaData[i].code == item.Message) {
            field = metaData[i];
            break;
          }
        }
        setFieldValue(field, value);
      }
    });
  }
  function getInitials() {
    var initials = pinyin.getCamelChars($("#name").val());
    $("#initials").val(initials);
  }
  function getGenderOption(callback) {
    if (!genderList.length) {
      ajaxGet({
        url: "/sys/dict/select-usable-pcode?code=gender",
        success: function (res) {
          genderList = res && res.length ? res : [];
          callback();
        }
      });
    } else {
      callback();
    }
  }
  function getTagList(callback) {
    ajaxGet({
      url: "/openApi/project/edc/patient-label/list-by-subject?subjectId=" + subjectId,
      success: function (res) {
        tagList = res && res.length ? res : [];
        callback();
      }
    });
  }
  function getGroupList(callback) {
    ajaxGet({
      url: "/openApi/project/edc/patient-group/list-by-subject?subjectId=" + subjectId,
      success: function (res) {
        groupList = res && res.length ? res : [];
        callback();
      }
    });
  }
  function getDiseaseList(callback) {
    ajaxGet({
      url: "/openApi/project/edc/subject-disease/list?subjectId=" + subjectId,
      success: function (res) {
        diseaseList = res && res.length ? res : [];
        callback();
      }
    });
  }
  function getFormValue() {
    var res = {};
    for (var i = 0; i < baseData.length; i++) {
      var item = baseData[i];
      var data = getFieldValue(item);
      if (!data.isValidPass) {
        alert("必填项不能为空：" + item.label);
        return null;
      }
      res[item.code] = data.value;
    }
    var extJson = {};
    for (var i = 0; i < baseData.length; i++) {
      var item = baseData[i];
      var data = getFieldValue(item);
      if (!data.isValidPass) {
        alert("必填项不能为空：" + item.label);
        return null;
      }
      extJson[item.code] = data.value;
    }
    res.extJson = JSON.stringify(extJson);
    return res;
  }
  function getFieldValue(item) {
    var res = { isValidPass: false, value: null };
    if (item.type == "radio") {
      res.value = $("#" + item.code + " input:checked").val();
    } else if (item.type == "checkbox") {
      res.value = $("#" + item.code + " input:checked")
        .map(function () {
          return this.value;
        })
        .get();
    } else if (item.code == "labelIds") {
      var val = $("#" + item.code).val();
      if (!val) {
        val = "";
      } else {
        val = val.join(",");
      }
      res.value = val;
    } else {
      res.value = $("#" + item.code).val();
    }
    if (item.required && !res.value) {
      res.isValidPass = false;
    } else {
      res.isValidPass = true;
    }
    return res;
  }
  function setFieldValue(item, value) {
    if (value === undefined || value === null) {
      return;
    }
    if (item.code == "labelIds" && !$.isArray(value)) {
      if (value && typeof value == "string") {
        value = value.split(",");
      }
    }
    if (item.type == "radio") {
      $("#" + item.code + " input[value=" + value + "]").prop("checked", true);
    } else if (item.type == "checkbox") {
      if ($.isArray(value)) {
        $.each(value, function (i, val) {
          $("#" + item.code + " input[value=" + val + "]").prop("checked", true);
        });
      }
    } else {
      $("#" + item.code).val(value);
    }
  }

  function getInputHtml(item) {
    if (item.type == "date") {
      return '<input id="' + item.code + '" class="date-control form-control"></input>';
    } else if (item.type == "select") {
      return '<select id="' + item.code + '" class="select-control form-control"></select>';
    } else if (item.type == "selectMulti") {
      return '<select id="' + item.code + '" multiple class="select-control form-control"></select>';
    } else if (item.type == "textarea") {
      return '<textarea id="' + item.code + '" class="form-control" rows="3"></textarea>';
    } else if (item.type == "radio") {
      return '<div id="' + item.code + '" class="radio"></div>';
    } else if (item.type == "checkbox") {
      return '<div id="' + item.code + '" class="checkbox"></div>';
    } else if (item.type == "number") {
      return '<input id="' + item.code + '" type="number" class="form-control"></input>';
    } else {
      return '<input id="' + item.code + '" class="form-control"></input>';
    }
  }
  // 获取项目所有的拓展患者拓展字段
  function getCaseExpandField(callback) {
    if (!subjectId || !orgId) {
      return;
    }
    var curUrl = "/openApi/project/field/" + subjectId + "/" + orgId;

    ajaxGet({
      url: curUrl,
      success: function (res) {
        isCenterCase = res.isCenterCase;
        res = res.fields || [];
        // 拓展字段中有必填项才需要给出提示，并填写
        if (res && res.length > 0) {
          var curBaseData = [];
          var curMetaData = [];
          $.each(res, function (index, item) {
            if (item.isShow == null || item.isShow) {
              if (item.isBaseField || isExtField(item.code)) {
                curBaseData.push({
                  id: item.id,
                  label: item.name,
                  code: item.code,
                  required: item.isRequired === 1,
                  type: item.widgetType ? item.widgetType : basicInfoFieldObj[item.code],
                  isShow: 1,
                  sort: item.listSort,
                  widgetOption: item.widgetOption ? item.widgetOption : "",
                  externaldataInfo: item.externalDataInfo ? JSON.parse(item.externalDataInfo) : ""
                });
              } else {
                curMetaData.push({
                  id: item.id,
                  label: item.name,
                  code: item.code,
                  required: item.isRequired === 1,
                  type: item.widgetType ? item.widgetType : basicInfoFieldObj[item.code],
                  isShow: 1,
                  sort: item.listSort,
                  widgetOption: item.widgetOption ? item.widgetOption : "",
                  externaldataInfo: item.externalDataInfo ? JSON.parse(item.externalDataInfo) : ""
                });
              }
            }
          });
          baseData = curBaseData.sort(function (a, b) {
            if (a.sort < b.sort) {
              return -1;
            } else if (a.sort === b.sort) {
              return 0;
            } else {
              return 1;
            }
          });
          // 扩展字段
          metaData = curMetaData.sort(function (a, b) {
            if (a.sort < b.sort) {
              return -1;
            } else if (a.sort === b.sort) {
              return 0;
            } else {
              return 1;
            }
          });
          console.log(baseData, "baseData");
          console.log(metaData, "metaData");
          callback();
        }
      }
    });
  }
  function renderSelect(selector, options, keyMap) {
    if (!keyMap) {
      keyMap = {
        label: "label",
        value: "value"
      };
    }
    var optionsHtml = "";
    for (var i = 0; i < options.length; i++) {
      var item = options[i];
      optionsHtml += "<option value='" + item[keyMap.value] + "'>" + item[keyMap.label] + "</option>";
    }
    $(selector).html(optionsHtml);
  }
  function renderRadio(selector, options, keyMap) {
    if (!keyMap) {
      keyMap = {
        label: "label",
        value: "value"
      };
    }
    var radioHtml = "";
    var groupName = "group_" + new Date().getTime(); // 生成一个唯一的组名

    for (var i = 0; i < options.length; i++) {
      var item = options[i];
      radioHtml += "<label>" + "<input type='radio' name='" + groupName + "' value='" + item[keyMap.value] + "'>" + item[keyMap.label] + "</label>";
    }

    $(selector).html(radioHtml);
  }
  function renderCheckbox(selector, options, keyMap) {
    if (!keyMap) {
      keyMap = {
        label: "label",
        value: "value"
      };
    }
    var checkboxHtml = "";
    var groupName = "group_" + new Date().getTime(); // 生成一个唯一的组名

    for (var i = 0; i < options.length; i++) {
      var item = options[i];
      checkboxHtml += "<label>" + "<input type='checkbox' name='" + groupName + "' value='" + item[keyMap.value] + "'>" + item[keyMap.label] + "</label>";
    }

    $(selector).html(checkboxHtml);
  }
  function isExtField(code) {
    var extFields = ["ext1", "ext2", "ext3", "momRegno"];
    return $.inArray(code, extFields) > -1;
  }
});
