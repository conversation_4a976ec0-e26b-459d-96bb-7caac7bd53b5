<template>
  <hos-container class="main" style="height: 100%">
    <hos-aside v-show="!isFullScreenTab" :width="isCollapse ? '40px' : '220px'" style="margin-right:10px;">
      <hos-menu
        class="menuLeftMain leftMenu projectMenu"
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="$route.path"
        :unique-opened="true"
        @select="menuSelect"
      >
        <side-menu v-if="menuList && menuList.length>0" :menuList="menuList" :showIcon="true" :no-more="true"></side-menu>
      </hos-menu>
    </hos-aside>
    <hos-main style="height: 100%; padding: 0;background-color: #f5f5f5">
      <template v-if="info && subjectInfo">
        <keep-alive v-if="keepAlive">
          <router-view />
        </keep-alive>
        <router-view v-else />
      </template>
    </hos-main>
  </hos-container>
</template>

<script>
import {deepClone} from "@/utils/index.js"
import { Message } from 'hosui'
import { mapState,mapActions } from 'vuex'
import SideMenu from '@base/components/menu/SideMenu'
import store from '@base/store'
const getAllEdcMenu = (permissionMenuList) => {
  // 过滤出项目视图下的所有菜单
  if (permissionMenuList.length > 0) {
    const tmp = permissionMenuList.filter((item) => item.name == 'edc')
    if (tmp.length > 0) {
      const edcMenu = tmp[0]
      if (edcMenu.children && edcMenu.children.length > 0) {
        const tmp = edcMenu.children.filter((item) => item.name == 'p')
        if (tmp.length > 0) {
          return tmp[0].children || []
        }
      }
    }
  }
  return []
}
const filterRoleMenu = (allMenu,subjectMenu) => {
  // 过滤出当前项目角色下的菜单
  if(!subjectMenu || subjectMenu.length == 0){
    // return allMenu
    return []
  }
  // 是否有匹配的项目角色菜单
  const isSubjectMenuItem = (item) => {
    return subjectMenu.some(subjectMenuItem=>{
      return subjectMenuItem.code == item.name || (item.meta && item.meta.resourceId == subjectMenuItem.resourceId)
    })
  }
  const res = []
  // 递归过滤所有子菜单的权限
  function filterMenuItemChildren(menuItem){
    menuItem.children = menuItem.children.filter(child=>{
      if(child.children && child.children.length>0){
        filterMenuItemChildren(child)
      }
      return isSubjectMenuItem(child)
    })
  }
  allMenu.forEach(item=>{
    if(item.children && item.children.length > 0){
      const new_item = deepClone(item)
      filterMenuItemChildren(new_item)
      if(new_item.children.length>0){
        res.push(new_item)
      }
    }else if(isSubjectMenuItem(item)){
      res.push(item)
    }
  })
  return res
}
export default {
  name: 'ProjectView',
  components: { SideMenu },
  beforeRouteEnter: (to, from, next) => {
    const permissionMenuList = store.state.user.menuList
    const subjectMenu = store.state.subject.subjectMenu
    const allEdcMenu = getAllEdcMenu(permissionMenuList)
    const menuList = filterRoleMenu(allEdcMenu,subjectMenu)
    // console.log('menuList',menuList)
    // console.log('beforeEnter to',to)
    // 递归查找菜单是否存在
    function hasMenu (menuList,m){
      for (let i = 0; i < menuList.length; i++) {
        const menu = menuList[i]
        if(menu.name == m.name){
          return true
        }
        if(menu.children && menu.children.length>0){
          if(hasMenu(menu.children,m)){
            return true
          }
        }
      }
      return false
    }
    // 检查跳转的菜单是否在权限列表里
    if(hasMenu(menuList,to)){
      next()
    }else{
      Message.error('暂无权限')
      next({path: '/403'})
    }
  },
  data() {
    return {
      isCollapse: false,
      activeMenu: {},
    }
  },
  computed: {
    ...mapState({
      isFullScreenTab: state => state.fullScreen.isFullScreenTab,
      info: state => state.user.userInfo,
      subjectInfo: state => state.subject.subjectInfo,
      subjectId: state => state.subject.subjectId,
      subjectMenu: state => state.subject.subjectMenu, // 当前项目角色下的菜单
    }),
    keepAlive() {
      return this.$route.meta.keepAlive
    },
    ...mapState({
      permissionMenuList: (state) => state.user.menuList,
      sysAppName: (state) => state.sys.sysAppName, // 系统应用名称
      browserTabName: (state) => state.sys.browserTabName // Title名称
    }),
    menuList() {
      const allEdcMenu = this.getAllEdcMenu(this.permissionMenuList)
      return this.filterRoleMenu(allEdcMenu,this.subjectMenu)
    }
  },
  created() {},
  methods: {
    ...mapActions(['setSubjectInfo']),
    getAllEdcMenu,
    filterRoleMenu,
    menuSelect(index, indexPath) {
      //此处触发动态路由被点击事件
      this.findMenuByKey(this.menuList, index)
      // 将通过url地址配置的以及通过表格新增行配置的参数生成query对象，传参到route
      const meta = this.activeMenu.meta
      const {queryStr,params} = meta
      const query = {}
      if(queryStr && queryStr.length>0){
        const tmp = queryStr.split('&')
        tmp.forEach(item=>{
          const q = item.split('=')
          query[q[0]] = q[1]
        })
      }
      if(params && params.length>0 && Array.isArray(params)){
        params.forEach(item=>{
          if(item.type == 'fixed'){ // 暂时仅支持固定值的参数
            query[item.name] = item.value
          }
        })
      }
      this.$router.push({ path: this.activeMenu.path, query})
    },
    findMenuByKey(menus, key) {
      for (let i of menus) {
        if (i.name == key) {
          this.activeMenu = { ...i }
        } else if (i.children && i.children.length > 0) {
          this.findMenuByKey(i.children, key)
        }
      }
    }
  },
  watch:{
    info:{
      immediate:true,
      handler(newVal,oldVal){
        if(!oldVal && newVal && !this.subjectInfo){
          if(this.subjectId){
            this.setSubjectInfo(this.subjectId)
          }else{
            this.$message.error('未找到项目信息')
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .projectMenu{
    ::v-deep{
      .hos-submenu__title:hover,.hos-menu-item:focus {
          background-color: var(--leftMenu-bg-color, #e5f3ff);
          color: var(--leftMenu-text-color, #339EFF);
      }
    }
  }
</style>