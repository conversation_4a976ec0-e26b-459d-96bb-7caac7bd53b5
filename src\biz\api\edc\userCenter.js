
export const queryListApi = (params) => {
  return {
    url: 'edc/project/page',
    method: 'get',
    params,
  }
}


export const queryDetailApi = (id) => {
  return {
    url: 'edc/project/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/project/insert',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/project/update',
    method: 'post',
    data
  }
}
// 启动项目
export const pulseOnProject = (params) => {
  return {
    url: 'edc/project/pulse-on-project',
    method: 'post',
    params
  }
}
// 修改项目状态
export const updateStatus = (params) => {
  return {
    url: 'edc/project/update-status',
    method: 'post',
    params
  }
}

export const addSubjectApi = (data) => {
  return {
    url: 'edc/subject/insert',
    method: 'post',
    data
  }
}

export const editSubjectApi = (data) => {
  return {
    url: 'edc/subject/update',
    method: 'post',
    data
  }
}

export const detailSubjectApi = (id) => {
  return {
    url: `edc/subject/detail/${id}`,
    method: 'get',
  }
}



