.mod-pat__attachment {
    $border: 1px solid #e4e7ed;
    $fullHeight: calc(100vh - 220px);

    .att-row {
        height: $fullHeight;
        width: calc(100% - 20px);
        margin: 0 10px !important;
    }

    .left-container {
        height: $fullHeight;
        ;
        border: $border;
        border-radius: 5px;

        .left-inner-container {
            height: 100%;
            position: relative;
            overflow-y: auto;

            .page {
                position: absolute;
                bottom: 10px;
            }
        }
    }

    .right-container {
        height: 100%;

        .right-inner-container {
            height: 100%;
            border-radius: 5px;
        }
    }
}