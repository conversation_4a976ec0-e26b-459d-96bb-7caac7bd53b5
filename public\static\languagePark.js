localLanguagePark = {
  "hos": {
    "excelManage": {
      "sheetManage": "sheet管理",
      "templateFile": "模板文件",
      "beforeImplement": "执行前[导入前是否要执行校验]",
      "HandlingMethod": "处理方式",
      "noHandling": "不处理",
      "executionClass": "执行类",
      "executionMethods": "执行方法",
      "dataProcessing": "数据处理[默认系统处理，如果选定方法则方法会得到所有封装的数据集 allValues]",
      "system": "系统",
      "dataPreprocessing": "数据预处理",
      "afterImplement": "执行后[数据插入后的逻辑处理]",
      "sheetConfig": "sheet配置",
      "startLine": "开始行",
      "processingStrategy": "处理策略",
      "processAll": "全部处理",
      "processingItemByItem": "逐条处理",
      "uniqueField": "唯一字段",
      "enterMultipleFieldsSeparatedByCommas": "可以输入多个字段,以逗号分隔",
      "columnsOccupied": "所占列",
      "columnsOccupiedNotEmpt": "所占列不能为空",
      "columnType": "列类型",
      "valueSource": "值来源",
      "configInfo": "配置信息",
      "tableName": "表名",
      "queryCriteria": "查询条件",
      "comparisonField": "对比字段",
      "dataPolicy": "数据策略",
      "singleChoice": "单选",
      "multipleChoice": "多选",
      "importUpdate": "导入更新",
      "whetherToHide": "是否隐藏",
      "dataVerification": "数据验证",
      "length": "长度",
      "max": "最大值",
      "min": "最小值",
      "isItEmpty": "是否为空",
      "des": "描述",
      "example": "示例",
      "clickUpload": "点击上传",
      "inputSql": "请输入sql",
      "listSet": "列设置",
      "copy": "复制",
      "beforeImportDo": "导入前执行",
      "xlsOrxlsx": "只允许导入xls或者xlsx格式文件",
      "importRules": "导入规则",
      "sheetConfig": "sheet配置",
      "currentUserId": "登录者ID",
      "currentUserDeptId": "登录者部门ID",
      "currentTime": "当前时间",
      "joinTable": "联表",
      "systemKey": "系统主键",
      "directImport": "直接导入",
      "dict": "字典项",
      "string": "字符型",
      "number": "数字型",
      "date": "日期型",
      "pleaseSlect": "请选择",
      "selectingItemByItem": "逐条查询",
      "allRight": "全查比对",
      "canSave": "是否入库",
      "primaryKey": "是否主键",
      "exportName": "导出文件名",
      "exportManagement": "导出管理",
      "columnManagement": "列管理",
      "exportIndex": "sheet导出顺序",
      "sheetImportOrder": "sheet导入顺序",
      "dataStartLine": "数据开始行",
      "sheetGenerationOrder": "sheet生成顺序",
      "sheetExportOrder": "sheet导出顺序",
      "executeScript": "执行脚本",
      "columnType": "列类型",
      "multiHeader": "所属多表头",
      "source": "值来源",
      "isHide": "是否隐藏",
      "color": "颜色",
      "width": "宽度",
      "alignment": "对齐方式",
      "fatherAndSon": "父子",
      "queryParam": "请求参数",
      "defaultValue": "默认值",
      "configurationInformation": "配置信息",
      "queryCondition": "查询条件",
      "compareField": "对比字段",
      "SingleOrMultipleChoice": "单选/多选",
      "over": "完成",
      "successCount": "成功条数",
      "failCount": "失败条数",
      "counts": "条",
      "none": "无",
      "orgPerson": "组织人员",
      "sysManage": "系统管理",
      "placeholder": {
        "placeExportName": "请输入导出文件名",
        "placesheetGenerationOrder": "请输入sheet生成顺序",
        "pleaseSheetExportOrder": "请输入heet导出顺序",
        "pleaseDataStartLine": "请输入数据开始行",
        "pleaseExecuteScript": "请输入执行脚本",
        "pleaseColor": "请输入颜色",
        "pleaseWidth": "请输入宽度",
        "pleaseColumnType": "请选择列类型",
        "pleaseValueSource": "请选择值来源",
        "pleaseAlignment": "请选择对齐方式",
        "pleaseMultiHeader": "请选择所属多表头",
        "pleaseConfigurationInformation": "请输入配置信息",
        "pleaseTableName": "请输入表名",
        "pleaseQueryCondition": "请输入查询条件",
        "pleaseCompareField": "请输入对比字段",
        "pleaseSingleOrMultipleChoice": "请选择单选/多选",
        "category": "请选择分组",
        "dataFormat": "请输入格式化数据"
      },
      "copySheetConfirm": "确定要复制【{0}】吗？",
      "exportOrder": "导入排序",
      "category": "分组",
      "selectData": "请勾选需要导出的数据",
      "dataFormat": "格式化",
      "requiredSql": "sql不能为空",
      "requiredClass": "执行类不能为空",
      "requiredMethods": "执行方法不能为空"
    }
  }
}