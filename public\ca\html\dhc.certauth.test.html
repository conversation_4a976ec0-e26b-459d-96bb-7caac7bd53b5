﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页</title>
    <!--引用HISUI-->
    <link rel="stylesheet" type="text/css" href="../../scripts_lib//hisui-0.1.0/dist/css/hisui.lite.min.css">
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.min.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.hisui.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/locale/hisui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../scripts/websys.jquery.bsp.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/websys.css">

    <!--公共js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/common.data.js"></script>
    <script type="text/javascript" src="../scripts/dhccertauth/lib/json2.js"></script>
</head>
<body class="hisui-layout">
    <div data-options="region:'center',title:'',border:false,collapsible:false,split:true,headerCls:'panel-header-gray'" >
        <table cellspacing="10">
            <tr>
                <td>请求路由：</td>
                <td><input id="path" type="text" class="textbox tdtext" style="width: 400px;height: 40px;"></input></td>
                <td><a class="hisui-linkbutton" data-options="iconCls:'icon-paper-plane',plain:true" id="btn" style="width:120px">发送请求</a></td>
            </tr>
            <tr>
                <td>请求消息体：</td>
                <td><textarea id="input" class="hisui-textbox" data-options="multiline:true,required:true" style="width:400px;height:300px;word-wrap:break-word"></textarea></td>
                <td>返回消息体：</td>
                <td><textarea id="output" class="hisui-textbox" data-options="multiline:true,required:true" style="width:400px;height:300px;word-wrap:break-word"></textarea></td>
            </tr>
        </table>
    </div>
    <script>
        function test() {
            var baseURL = $("#path").val();
            var data = $("#input").val();
            if (data != "") {
                data = JSON.parse(data);
            }
            var result = $ipost(baseURL , data, false);
            $("#output").val(JSON.stringify(result));
        } 
        $("#path").val("http://127.0.0.1:20004/hosca/careSign/getLoginQrInfo");
        //$("#path").val("http://127.0.0.1:8022/hosCa/careSign/getAuthTypeList");
        $("#input").val("{  \"params\": {    \"organizationID\": \"2\",    \"userCode\": \"\",    \"product\": \"HIS\",    \"authTypeCode\": \"PHONE\",    \"venderCode\": \"SHCA\"  }}");
        //$("#input").val("{\"params\": \n{\"organizationID\": \"2\",\"langID\": \"20\"},\n\"action\": \"CARESIGN_GET_AUTHTYPELIST\"}");
        $("#btn").click(function(){test();});
    </script>
</body>
</html>
