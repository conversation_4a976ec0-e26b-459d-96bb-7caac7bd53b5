.his-into-group {
  padding: 20px;
}
.top-content {
  border: 1px solid #3a5dae;
  margin-bottom: 10px;
}
.left-doctor,.right-patient{
  min-height: 300px;
}
.left-doctor .left-header {
  border-bottom: 1px solid #dcdfe6;
  padding: 15px 20px;
  margin-bottom: 20px;
}
.right-patient {
  border-left: 1px solid #dcdfe6;
}
.right-patient .right-header {
  border-bottom: 1px solid #dcdfe6;
  padding: 15px 20px;
}
.his-into-group .empty {
  color: #dcdfe6;
  padding: 10px 0;
}
.right-patient .right-content .info {
  width: 100%;
  height: 100%;
  padding-top: 20px;
  padding-right: 40px;
}
.footer {
  height: 80px;
  border: 1px solid #dcdfe6;
  text-align: center;
  padding: 10px;
}
.checkbox label, .radio label{
  margin-right: 10px;
}
.form-control.only-show{
  border:none;
  box-shadow: none;
}