// 查询从数据库导入列表
export const getImportTable = (params) => {
  return {
    url: '/hos/interface/selectImportTable',
    method: 'get',
    params
  }
}
// 从数据库导入
export const importTable = ({source,data}) => {
  return {
    url: `/hos/interface/import/${source}`,
    method: 'post',
    data
  }
}
//导出sql结构脚本
export const exportSqlScript = ({source,data}) => {
  return {
    url: `/hos/interface/structure/${source}`,
    method: 'post',
    data
  }
}
//导出sql数据脚本
export const exportSqlDataScript = ({source,data}) => {
  return {
    url: `/hos/interface/data/${source}`,
    method: 'post',
    data
  }
}
// 从同步数据库
export const syncFromDb = ({id,source}) => {
  return {
    url: `/hos/interface/syncFromDb/${id}/${source}`,
    method: 'get'
  }
}