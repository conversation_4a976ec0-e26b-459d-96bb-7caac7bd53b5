var context_path = "/csm";
var orgCode = getQueryString('orgCode');
var foreignId = getQueryString('foreignId');
var regNo = getQueryString('regNo');
var deptId = getQueryString('deptId');
// for mock
// var orgCode = "HOS-HX";
// var foreignId = "17851";
// var regNo = "P9867152970";
function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) {
    return unescape(r[2]);
  }

  return null;
}
