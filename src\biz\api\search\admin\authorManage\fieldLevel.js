export const queryFieldListByRole = (params) => {
  return {
    url: 'search/es-property-metadata/list-by-index-id',
    method: 'GET',
    params
  }
}

export const queryFieldPageByRole = (params) => {
  return {
    url: 'search/permission/property/page',
    method: 'GET',
    params
  }
}

export const insertOrUpdate = (data) => {
  return {
    url: 'search/permission/property/update-single',
    method: 'POST',
    data
  }
}
export const oneKeyAuth = (data) => {
  return {
    url: 'search/permission/property/update-save-batch',
    method: 'POST',
    data
  }
}
export const oneKeyCancel = (data) => {
  return {
    url: 'search/permission/property/update-del-batch',
    method: 'POST',
    data
  }
}

export const authAllField = (params) => {
  return {
    url: `search/permission/property/update-save-all/${params.roleCode}/${params.indexId}`,
    method: "GET"
  }
}

export const cancelAllField = (params) => {
  return {
    url: `search/permission/property/update-del-all/${params.roleCode}/${params.indexId}`,
    method: "GET"
  }
}

