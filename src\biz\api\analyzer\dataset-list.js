export const pageList = (params) => {
  return {
    url: `/analyzer/dataset/page`,
    method: 'get',
    params
  }
}

export const list = (params) => {
  return {
    url: `/analyzer/dataset/list`,
    method: 'get',
    params
  }
}

export const tree = (params) => {
  return {
    url: `/analyzer/dataset/tree`,
    method: 'get',
    params,
    ignoreError:params && params.ignoreError ? true : false
  }
}

export const deleteBatch = (data) => {
  return {
    url: `/analyzer/dataset/delete/batch/ids`,
    method: 'POST',
    data
  }
}

export const addDataset = (data) => {
  return {
    url: `/analyzer/dataset/save`,
    method: 'post',
    data
  }
}

export const updateDataset = (data) => {
  return {
    url: `/analyzer/dataset/update/` + data.datasetId,
    method: 'post',
    data
  }
}

export const updateSubset = (data) => {
  return {
    url: `/analyzer/subset/update/` + data.subsetId,
    method: 'post',
    data
  }
}

export const deleteBatchSubset = (data) => {
  return {
    url: `/analyzer/subset/delete/batch/ids`,
    method: 'POST',
    data
  }
}

export const getDatasetDetail = (data) => {
  return {
    url: `/analyzer/dataset/detail/id/` + data.datasetId,
    method: 'get',
  }
}

export const getDatasetColList = (data) => {
  return {
    url: `/analyzer/dataset/cols/list/` + data.datasetId,
    method: 'get',
  }
}

export const getAllSubDataset = (datasetId) => {
  return {
    url: `/analyzer/subset/list/${datasetId}`,
    method: 'get',
  }
}

export const getSubDatasetById = (data) => {
  return {
    url: `/analyzer/subset/${data.subsetId}`,
    method: 'get',
  }
}

export const saveSubDataset = (data) => {
  return {
    url: `/analyzer/subset/save`,
    method: 'post',
    data
  }
}

export const saveByFile = (data) => {
  return {
    url: `/analyzer/subset/save/by_file`,
    method: 'post',
    data
  }
}
