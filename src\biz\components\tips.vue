<template>
  <hos-tooltip popper-class="biz-tips" effect="dark" :placement="placement">
    <template #content>
      <!-- 传参content可用富文本标签添加样式 
          @example: content="提示文字<br/>添加换行<br/>提示文字" -->

      <!-- 或者直接使用插槽自定义 @example: 如下: -->
      <!-- <tips>
            <div>提示文字</div>
            <hos-tag type="warning">标签</hos-tag>
            <div>提示文字</div>
          </tips> -->
      <slot>
        <div v-html="content" />
      </slot>
    </template>
    <i class="hos-icon-question" style="font-size: 14px;" :style="{color:iconColor}" />
  </hos-tooltip>
</template>
<script>
  export default {
    name: 'tips',
    props: {
      iconColor: {
        type: String,
        default() {
          return '#868180'
        }
      },
      content: {
        type: String,
        default() {
          return ''
        }
      },
      placement: {
        type: String,
        default() {
          return 'right'
        }
      }
    }
  }
</script>
