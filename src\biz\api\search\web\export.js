
// 获取指定模型的全部特殊值
export const getAllSpecByIndexId = (indexId) => {
  return {
    url: `search/export-spec/select-list-by-indexid//${indexId}`,
    method: 'get'
  }
}

// 获取指定模型的全部导出属性
export const getAllPropertyByIndexId = (indexId) => {
  return {
    url: `search/export-prop/select-by-indexid/${indexId}`,
    method: 'get'
  }
}

// 查询所有导出项过滤配置
export const getAllFilterItem = () => {
  return {
    url: `search/export-filter-item/list`,
    method: 'get'
  }
}

// 分页查询导出模板列表
export const pageListExportTemp = (params) => {
  return {
    url: `search/export-temp/page`,
    method: 'get',
    params
  }
}
// 新增导出模板
export const addExportTemplate = (data) => {
  return {
    url: `search/export-temp/insert`,
    method: 'post',
    data
  }
}
// 删除导出模板
export const deleteExportTemplate = (data) => {
  return {
    url: `search/export-temp/deletion`,
    method: 'post',
    data
  }
}

