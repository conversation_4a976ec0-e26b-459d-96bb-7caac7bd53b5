<template>
  <div>
    <div class="analysis-data-item">
      <div class="analysis-data-title">{{ $t('已选变量') }} <tips v-if="methodConfig.stepTips.step1!==''" :content="methodConfig.stepTips.step1" /></div>
      <hos-row :gutter="10" style="margin: 0 0 10px;">
        <hos-col :span="8" style="padding-left: 0;">
          <div style="margin-bottom: 10px;">{{ $t('结局') }}<span style="font-size: 12px;color:#aaa;margin-left: 5px;;">({{ $t('数值型定性变量，变量数=1') }})</span></div>
          <drop-var v-model="eventVarArr" :can-drop-func="eventVarArrCanDropFunc" :is-only-one="true" />
        </hos-col>
        <hos-col :span="8">
          <div style="margin-bottom: 10px;">{{ $t('时间') }}<span style="font-size: 12px;color:#aaa;margin-left: 5px;;">({{ $t('代表时间的变量，变量数=1') }})</span></div>
          <drop-var v-model="durationVarArr" :is-only-one="true" />
        </hos-col>
        <hos-col :span="8" style="padding-right: 0;">
          <div style="margin-bottom: 10px;">{{ $t('自变量') }}<span style="font-size: 12px;color:#aaa;margin-left: 5px;;">({{ $t('数值型变量，变量数≥1') }})</span></div>
          <drop-var v-model="featureVarArr" :can-drop-func="featureVarArrCanDropFunc" />
        </hos-col>
      </hos-row>
      <div>
        <hos-button type="primary" @click="startAnalysis">{{ $t('开始分析') }}</hos-button>
      </div>
    </div>
    <div class="analysis-data-item">
      <div class="analysis-data-title">{{ $t('分析结果') }}</div>
      <div class="analysis-data-content">
        <hos-table :data="analysisResult.summary_data" style="width: 100%">
          <hos-table-column prop="feature" :label="$t('变量')" />
          <hos-table-column v-for="(col, index) in analysisResult.summary_columns" :key="index" :prop="col" :label="col">
            <template slot-scope="scope">
              {{ scope.row[col] }}
            </template>
          </hos-table-column>
        </hos-table>
        <div v-if="analysisResult.concordance" style="display: flex; justify-content: space-between; align-items: center;">
          <span>Concordance = {{ analysisResult.concordance }}</span>
          <hos-button type="primary" @click="openKmDialog">{{ $t('KM曲线') }}</hos-button>
        </div>
      </div>
    </div>
    <div class="analysis-data-item">
      <div class="analysis-data-title">{{ $t('结果解释') }}</div>
      <div class="analysis-data-content">
        <div v-if="analysisResult.resultDescHtml" style="margin-bottom:10px;" v-html="analysisResult.resultDescHtml" />
        <empty-box v-else :show-img="false" />
      </div>
      <div style="margin-bottom:10px;font-weight: bold;font-size: 14px;">{{ $t('方法说明') }}</div>
      <div class="method-desc" v-html="methodConfig.methodDesc" />
    </div>
    <km-dialog ref="kmDialog" />
  </div>
</template>

<script>

import analysisMethodMixin from '../analysis-method-mixin'
import kmDialog from '../km-dialog.vue'
const analysisResult = {
  resultDesc: "",
  resultDescHtml: "",
  concordance: "",
  summary_columns: [],
  summary_data: []
}
export default {
  components: { kmDialog },
  methodName: 'survival_analysis',
  mixins: [analysisMethodMixin],
  data() {
    return {
      loading: false,
      eventVarArr: [],
      durationVarArr: [],
      featureVarArr: [],
      analysisResult: this.deepClone(analysisResult),
    }
  },
  computed: {
    cols() {
      return [...this.eventVarArr, ...this.durationVarArr, ...this.featureVarArr].map(item => item.name)
    }
  },
  methods: {
    async startAnalysis() {
      if (this.eventVarArr.length === 0) {
        this.$message.warning(this.$t('请拖入一个结局变量'))
        return
      }
      if (this.durationVarArr.length === 0) {
        this.$message.warning(this.$t('请拖入一个时间变量'))
        return
      }
      if (this.featureVarArr.length === 0) {
        this.$message.warning(this.$t('请至少拖入一个自变量'))
        return
      }
      const p = { eventCol: this.eventVarArr[0].name, durationCol: this.durationVarArr[0].name, featureCols: [] }
      this.featureVarArr.forEach(item => {
        p.featureCols.push(item.name)
      })
      try {
        this.methodConfig.loading = true
        const { data, code, message } = await this.methodConfig.api(this,p)
        if (code == 200) {
          this.analysisResult.resultDesc = data.resultDesc || ""
          this.analysisResult.resultDescHtml = data.resultDescHtml || ""
          this.analysisResult.concordance = data.concordance || ""
          this.analysisResult.summary_columns = data.summary_columns || []
          this.analysisResult.summary_data = data.summary_data || []
          this.$message.success(message)
        }
      } catch (e) {
        this.analysisResult = this.deepClone(analysisResult)
        //
      } finally {
        this.methodConfig.loading = false
      }
    },
    openKmDialog() {
      this.$refs.kmDialog.open({
        eventVarArr: this.eventVarArr,
        durationVarArr: this.durationVarArr,
        featureVarArr: this.featureVarArr,
      })
    },
    eventVarArrCanDropFunc(col) {
      // 数值型定性变量
      if (!col.is_numeric || col.type !== this.$t('定性')) {
        this.$message.warning(this.$t('请拖入数值型定性变量'))
        return false
      }
      return true
    },
    featureVarArrCanDropFunc(col) {
      // 数值型变量
      if (!col.is_numeric) {
        this.$message.warning(this.$t('请拖入数值型变量'))
        return false
      }
      return true
    },
  }
}
</script>
