!function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="c92c")}({2409:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},"394c":function(e,t,n){"use strict";(function(e){var r;n.d(t,"a",(function(){return i})),r=(()=>{var t={470:t=>{function n(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var n,r="",i=0,o=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(o===a-1||1===s);else if(o!==a-1&&2===s){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var u=r.lastIndexOf("/");if(u!==r.length-1){-1===u?(r="",i=0):i=(r=r.slice(0,u)).length-1-r.lastIndexOf("/"),o=a,s=0;continue}}else if(2===r.length||1===r.length){r="",i=0,o=a,s=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(o+1,a):r=e.slice(o+1,a),i=a-o-1;o=a,s=0}else 46===n&&-1!==s?++s:s=-1}return r}var i={resolve:function(){for(var t,i="",o=!1,s=arguments.length-1;s>=-1&&!o;s--){var a;s>=0?a=arguments[s]:(void 0===t&&(t=e.cwd()),a=t),n(a),0!==a.length&&(i=a+"/"+i,o=47===a.charCodeAt(0))}return i=r(i,!o),o?i.length>0?"/"+i:"/":i.length>0?i:"."},normalize:function(e){if(n(e),0===e.length)return".";var t=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=r(e,!t)).length||t||(e="."),e.length>0&&i&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return n(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var r=arguments[t];n(r),r.length>0&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":i.normalize(e)},relative:function(e,t){if(n(e),n(t),e===t)return"";if((e=i.resolve(e))===(t=i.resolve(t)))return"";for(var r=1;r<e.length&&47===e.charCodeAt(r);++r);for(var o=e.length,s=o-r,a=1;a<t.length&&47===t.charCodeAt(a);++a);for(var u=t.length-a,l=s<u?s:u,c=-1,h=0;h<=l;++h){if(h===l){if(u>l){if(47===t.charCodeAt(a+h))return t.slice(a+h+1);if(0===h)return t.slice(a+h)}else s>l&&(47===e.charCodeAt(r+h)?c=h:0===h&&(c=0));break}var f=e.charCodeAt(r+h);if(f!==t.charCodeAt(a+h))break;47===f&&(c=h)}var d="";for(h=r+c+1;h<=o;++h)h!==o&&47!==e.charCodeAt(h)||(0===d.length?d+="..":d+="/..");return d.length>0?d+t.slice(a+c):(a+=c,47===t.charCodeAt(a)&&++a,t.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(n(e),0===e.length)return".";for(var t=e.charCodeAt(0),r=47===t,i=-1,o=!0,s=e.length-1;s>=1;--s)if(47===(t=e.charCodeAt(s))){if(!o){i=s;break}}else o=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');n(e);var r,i=0,o=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var a=t.length-1,u=-1;for(r=e.length-1;r>=0;--r){var l=e.charCodeAt(r);if(47===l){if(!s){i=r+1;break}}else-1===u&&(s=!1,u=r+1),a>=0&&(l===t.charCodeAt(a)?-1==--a&&(o=r):(a=-1,o=u))}return i===o?o=u:-1===o&&(o=e.length),e.slice(i,o)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!s){i=r+1;break}}else-1===o&&(s=!1,o=r+1);return-1===o?"":e.slice(i,o)},extname:function(e){n(e);for(var t=-1,r=0,i=-1,o=!0,s=0,a=e.length-1;a>=0;--a){var u=e.charCodeAt(a);if(47!==u)-1===i&&(o=!1,i=a+1),46===u?-1===t?t=a:1!==s&&(s=1):-1!==t&&(s=-1);else if(!o){r=a+1;break}}return-1===t||-1===i||0===s||1===s&&t===i-1&&t===r+1?"":e.slice(t,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){n(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var r,i=e.charCodeAt(0),o=47===i;o?(t.root="/",r=1):r=0;for(var s=-1,a=0,u=-1,l=!0,c=e.length-1,h=0;c>=r;--c)if(47!==(i=e.charCodeAt(c)))-1===u&&(l=!1,u=c+1),46===i?-1===s?s=c:1!==h&&(h=1):-1!==s&&(h=-1);else if(!l){a=c+1;break}return-1===s||-1===u||0===h||1===h&&s===u-1&&s===a+1?-1!==u&&(t.base=t.name=0===a&&o?e.slice(1,u):e.slice(a,u)):(0===a&&o?(t.name=e.slice(1,s),t.base=e.slice(1,u)):(t.name=e.slice(a,s),t.base=e.slice(a,u)),t.ext=e.slice(s,u)),a>0?t.dir=e.slice(0,a-1):o&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};i.posix=i,t.exports=i},447:(t,n,r)=>{var i;if(r.r(n),r.d(n,{URI:()=>g,Utils:()=>N}),"object"==typeof e)i="win32"===e.platform;else if("object"==typeof navigator){var o=navigator.userAgent;i=o.indexOf("Windows")>=0}var s,a,u=(s=function(e,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),l=/^\w[\w\d+.-]*$/,c=/^\//,h=/^\/\//,f="",d="/",m=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,g=function(){function e(e,t,n,r,i,o){void 0===o&&(o=!1),"object"==typeof e?(this.scheme=e.scheme||f,this.authority=e.authority||f,this.path=e.path||f,this.query=e.query||f,this.fragment=e.fragment||f):(this.scheme=function(e,t){return e||t?e:"file"}(e,o),this.authority=t||f,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==d&&(t=d+t):t=d}return t}(this.scheme,n||f),this.query=r||f,this.fragment=i||f,function(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!l.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!c.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(h.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,o))}return e.isUri=function(t){return t instanceof e||!!t&&"string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"function"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString},Object.defineProperty(e.prototype,"fsPath",{get:function(){return C(this,!1)},enumerable:!1,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=f),void 0===n?n=this.authority:null===n&&(n=f),void 0===r?r=this.path:null===r&&(r=f),void 0===i?i=this.query:null===i&&(i=f),void 0===o?o=this.fragment:null===o&&(o=f),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new v(t,n,r,i,o)},e.parse=function(e,t){void 0===t&&(t=!1);var n=m.exec(e);return n?new v(n[2]||f,E(n[4]||f),E(n[5]||f),E(n[7]||f),E(n[9]||f),t):new v(f,f,f,f,f)},e.file=function(e){var t=f;if(i&&(e=e.replace(/\\/g,d)),e[0]===d&&e[1]===d){var n=e.indexOf(d,2);-1===n?(t=e.substring(2),e=d):(t=e.substring(2,n),e=e.substring(n)||d)}return new v("file",t,e,f,f)},e.from=function(e){return new v(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),S(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new v(t);return n._formatted=t.external,n._fsPath=t._sep===p?t.fsPath:null,n}return t},e}(),p=i?1:void 0,v=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return u(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=C(this,!1)),this._fsPath},enumerable:!1,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?S(this,!0):(this._formatted||(this._formatted=S(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=p),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(g),y=((a={})[58]="%3A",a[47]="%2F",a[63]="%3F",a[35]="%23",a[91]="%5B",a[93]="%5D",a[64]="%40",a[33]="%21",a[36]="%24",a[38]="%26",a[39]="%27",a[40]="%28",a[41]="%29",a[42]="%2A",a[43]="%2B",a[44]="%2C",a[59]="%3B",a[61]="%3D",a[32]="%20",a);function b(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var s=y[o];void 0!==s?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=s):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function _(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=y[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function C(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,i&&(n=n.replace(/\//g,"\\")),n}function S(e,t){var n=t?_:b,r="",i=e.scheme,o=e.authority,s=e.path,a=e.query,u=e.fragment;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=d,r+=d),o){var l=o.indexOf("@");if(-1!==l){var c=o.substr(0,l);o=o.substr(l+1),-1===(l=c.indexOf(":"))?r+=n(c,!1):(r+=n(c.substr(0,l),!1),r+=":",r+=n(c.substr(l+1),!1)),r+="@"}-1===(l=(o=o.toLowerCase()).indexOf(":"))?r+=n(o,!1):(r+=n(o.substr(0,l),!1),r+=o.substr(l))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2))(h=s.charCodeAt(1))>=65&&h<=90&&(s="/"+String.fromCharCode(h+32)+":"+s.substr(3));else if(s.length>=2&&58===s.charCodeAt(1)){var h;(h=s.charCodeAt(0))>=65&&h<=90&&(s=String.fromCharCode(h+32)+":"+s.substr(2))}r+=n(s,!0)}return a&&(r+="?",r+=n(a,!1)),u&&(r+="#",r+=t?u:b(u,!1)),r}var A=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function E(e){return e.match(A)?e.replace(A,(function(e){return function e(t){try{return decodeURIComponent(t)}catch(n){return t.length>3?t.substr(0,3)+e(t.substr(3)):t}}(e)})):e}var N,w=r(470),x=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)r[i]=o[s];return r},L=w.posix||w;!function(e){e.joinPath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return e.with({path:L.join.apply(L,x([e.path],t))})},e.resolvePath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=e.path||"/";return e.with({path:L.resolve.apply(L,x([r],t))})},e.dirname=function(e){var t=L.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)?e:e.with({path:t})},e.basename=function(e){return L.basename(e.path)},e.extname=function(e){return L.extname(e.path)}}(N||(N={}))}},n={};function r(e){if(n[e])return n[e].exports;var i=n[e]={exports:{}};return t[e](i,i.exports,r),i.exports}return r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r(447)})();const{URI:i,Utils:o}=r}).call(this,n("4c39"))},"4c39":function(e,t){var n,r,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var u,l=[],c=!1,h=-1;function f(){c&&u&&(c=!1,u.length?l=u.concat(l):h=-1,l.length&&d())}function d(){if(!c){var e=a(f);c=!0;for(var t=l.length;t;){for(u=l,l=[];++h<t;)u&&u[h].run();h=-1,t=l.length}u=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function m(e,t){this.fun=e,this.array=t}function g(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new m(e,t)),1!==l.length||c||a(d)},m.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},aac4:function(e,t,n){"use strict";(function(e,r){var i;n.d(t,"a",(function(){return p})),n.d(t,"d",(function(){return _})),n.d(t,"b",(function(){return C})),n.d(t,"c",(function(){return S})),n.d(t,"e",(function(){return A}));let o=!1,s=!1,a=!1,u=!1,l=!1,c=!1,h=!1,f=void 0,d="en",m=void 0,g=void 0;const p="object"==typeof self?self:"object"==typeof e?e:{};let v=void 0;void 0!==p.vscode&&void 0!==p.vscode.process?v=p.vscode.process:void 0!==r&&(v=r);const y="string"==typeof(null===(i=null==v?void 0:v.versions)||void 0===i?void 0:i.electron)&&"renderer"===v.type;if("object"!=typeof navigator||y){if("object"==typeof v){o="win32"===v.platform,s="darwin"===v.platform,a="linux"===v.platform,u=a&&!!v.env.SNAP&&!!v.env.SNAP_REVISION,f="en",d="en";const e=v.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];f=t.locale,d=n||"en",m=t._translationsConfigFile}catch(e){}l=!0}}else g=navigator.userAgent,o=g.indexOf("Windows")>=0,s=g.indexOf("Macintosh")>=0,h=(g.indexOf("Macintosh")>=0||g.indexOf("iPad")>=0||g.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,a=g.indexOf("Linux")>=0,c=!0,f=navigator.language,d=f;let b=0;s?b=1:o?b=3:a&&(b=2);const _=o,C=s,S=c,A=function(){if(p.setImmediate)return p.setImmediate.bind(p);if("function"==typeof p.postMessage&&!p.importScripts){let e=[];p.addEventListener("message",t=>{if(t.data&&t.data.vscodeSetImmediateId)for(let n=0,r=e.length;n<r;n++){const r=e[n];if(r.id===t.data.vscodeSetImmediateId)return e.splice(n,1),void r.callback()}});let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),p.postMessage({vscodeSetImmediateId:r},"*")}}if("function"==typeof(null==v?void 0:v.nextTick))return v.nextTick.bind(v);const e=Promise.resolve();return t=>e.then(t)}()}).call(this,n("2409"),n("4c39"))},b5a0:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return a}));var r=n("aac4");let i;if(void 0!==r.a.vscode&&void 0!==r.a.vscode.process){const e=r.a.vscode.process;i={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd:()=>e.cwd(),nextTick:e=>Object(r.e)(e)}}else i=void 0!==e?{get platform(){return e.platform},get arch(){return e.arch},get env(){return Object({NODE_ENV:"production",BASE_URL:""})},cwd:()=>Object({NODE_ENV:"production",BASE_URL:""}).VSCODE_CWD||e.cwd(),nextTick:t=>e.nextTick(t)}:{get platform(){return r.d?"win32":r.b?"darwin":"linux"},get arch(){},nextTick:e=>Object(r.e)(e),get env(){return{}},cwd:()=>"/"};const o=i.cwd,s=i.env,a=i.platform}).call(this,n("4c39"))},c92c:function(e,t,n){"use strict";n.r(t);const r=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{if(e.stack)throw new Error(e.message+"\n\n"+e.stack);throw e},0)}}emit(e){this.listeners.forEach(t=>{t(e)})}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function i(e){s(e)||r.onUnexpectedError(e)}function o(e){if(e instanceof Error){let{name:t,message:n}=e;return{$isError:!0,name:t,message:n,stack:e.stacktrace||e.stack}}return e}function s(e){return e instanceof Error&&"Canceled"===e.name&&"Canceled"===e.message}Error;function a(e){const t=this;let n,r=!1;return function(){return r||(r=!0,n=e.apply(t,arguments)),n}}var u;!function(e){e.is=function(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]};const t=Object.freeze([]);e.empty=function(){return t},e.single=function*(e){yield e},e.from=function(e){return e||t},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){for(const n of e)if(t(n))return!0;return!1},e.find=function(e,t){for(const n of e)if(t(n))return n},e.filter=function*(e,t){for(const n of e)t(n)&&(yield n)},e.map=function*(e,t){let n=0;for(const r of e)yield t(r,n++)},e.concat=function*(...e){for(const t of e)for(const e of t)yield e},e.concatNested=function*(e){for(const t of e)for(const e of t)yield e},e.reduce=function(e,t,n){let r=n;for(const n of e)r=t(r,n);return r},e.slice=function*(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]},e.consume=function(t,n=Number.POSITIVE_INFINITY){const r=[];if(0===n)return[r,t];const i=t[Symbol.iterator]();for(let t=0;t<n;t++){const t=i.next();if(t.done)return[r,e.empty()];r.push(t.value)}return[r,{[Symbol.iterator]:()=>i}]},e.equals=function(e,t,n=((e,t)=>e===t)){const r=e[Symbol.iterator](),i=t[Symbol.iterator]();for(;;){const e=r.next(),t=i.next();if(e.done!==t.done)return!1;if(e.done)return!0;if(!n(e.value,t.value))return!1}}}(u||(u={}));let l=null;function c(e){return null==l||l.trackDisposable(e),e}function h(e){null==l||l.markAsDisposed(e)}function f(e,t){null==l||l.setParent(e,t)}class d extends Error{constructor(e){super(`Encountered errors while disposing of store. Errors: [${e.join(", ")}]`),this.errors=e}}function m(e){if(u.is(e)){let t=[];for(const n of e)if(n)try{n.dispose()}catch(e){t.push(e)}if(1===t.length)throw t[0];if(t.length>1)throw new d(t);return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function g(...e){const t=p(()=>m(e));return function(e,t){if(l)for(const n of e)l.setParent(n,t)}(e,t),t}function p(e){const t=c({dispose:a(()=>{h(t),e()})});return t}class v{constructor(){this._toDispose=new Set,this._isDisposed=!1,c(this)}dispose(){this._isDisposed||(h(this),this._isDisposed=!0,this.clear())}clear(){try{m(this._toDispose.values())}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return f(e,this),this._isDisposed?v.DISABLE_DISPOSED_WARNING:this._toDispose.add(e),e}}v.DISABLE_DISPOSED_WARNING=!1;class y{constructor(){this._store=new v,c(this),f(this._store,this)}dispose(){h(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}y.None=Object.freeze({dispose(){}});class b{constructor(e){this.element=e,this.next=b.Undefined,this.prev=b.Undefined}}b.Undefined=new b(void 0);class _{constructor(){this._first=b.Undefined,this._last=b.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===b.Undefined}clear(){let e=this._first;for(;e!==b.Undefined;){const t=e.next;e.prev=b.Undefined,e.next=b.Undefined,e=t}this._first=b.Undefined,this._last=b.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){const n=new b(e);if(this._first===b.Undefined)this._first=n,this._last=n;else if(t){const e=this._last;this._last=n,n.prev=e,e.next=n}else{const e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let r=!1;return()=>{r||(r=!0,this._remove(n))}}shift(){if(this._first!==b.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==b.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==b.Undefined&&e.next!==b.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===b.Undefined&&e.next===b.Undefined?(this._first=b.Undefined,this._last=b.Undefined):e.next===b.Undefined?(this._last=this._last.prev,this._last.next=b.Undefined):e.prev===b.Undefined&&(this._first=this._first.next,this._first.prev=b.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==b.Undefined;)yield e.element,e=e.next}}var C=n("aac4");const S=C.a.performance&&"function"==typeof C.a.performance.now;class A{constructor(e){this._highResolution=S&&e,this._startTime=this._now(),this._stopTime=-1}static create(e=!0){return new A(e)}stop(){this._stopTime=this._now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?C.a.performance.now():Date.now()}}var E;!function(e){function t(e){return(t,n=null,r)=>{let i,o=!1;return i=e(e=>{if(!o)return i?i.dispose():o=!0,t.call(n,e)},null,r),o&&i.dispose(),i}}function n(e,t){return s((n,r=null,i)=>e(e=>n.call(r,t(e)),null,i))}function r(e,t){return s((n,r=null,i)=>e(e=>{t(e),n.call(r,e)},null,i))}function i(e,t){return s((n,r=null,i)=>e(e=>t(e)&&n.call(r,e),null,i))}function o(e,t,r){let i=r;return n(e,e=>(i=t(i,e),i))}function s(e){let t;const n=new w({onFirstListenerAdd(){t=e(n.fire,n)},onLastListenerRemove(){t.dispose()}});return n.event}function a(e,t,n=100,r=!1,i){let o,s=void 0,a=void 0,u=0;const l=new w({leakWarningThreshold:i,onFirstListenerAdd(){o=e(e=>{u++,s=t(s,e),r&&!a&&(l.fire(s),s=void 0),clearTimeout(a),a=setTimeout(()=>{const e=s;s=void 0,a=void 0,(!r||u>1)&&l.fire(e),u=0},n)})},onLastListenerRemove(){o.dispose()}});return l.event}function u(e,t=((e,t)=>e===t)){let n,r=!0;return i(e,e=>{const i=r||!t(e,n);return r=!1,n=e,i})}e.None=()=>y.None,e.once=t,e.map=n,e.forEach=r,e.filter=i,e.signal=function(e){return e},e.any=function(...e){return(t,n=null,r)=>g(...e.map(e=>e(e=>t.call(n,e),null,r)))},e.reduce=o,e.debounce=a,e.latch=u,e.split=function(t,n){return[e.filter(t,n),e.filter(t,e=>!n(e))]},e.buffer=function(e,t=!1,n=[]){let r=n.slice(),i=e(e=>{r?r.push(e):s.fire(e)});const o=()=>{r&&r.forEach(e=>s.fire(e)),r=null},s=new w({onFirstListenerAdd(){i||(i=e(e=>s.fire(e)))},onFirstListenerDidAdd(){r&&(t?setTimeout(o):o())},onLastListenerRemove(){i&&i.dispose(),i=null}});return s.event};class l{constructor(e){this.event=e}map(e){return new l(n(this.event,e))}forEach(e){return new l(r(this.event,e))}filter(e){return new l(i(this.event,e))}reduce(e,t){return new l(o(this.event,e,t))}latch(){return new l(u(this.event))}debounce(e,t=100,n=!1,r){return new l(a(this.event,e,t,n,r))}on(e,t,n){return this.event(e,t,n)}once(e,n,r){return t(this.event)(e,n,r)}}e.chain=function(e){return new l(e)},e.fromNodeEventEmitter=function(e,t,n=(e=>e)){const r=(...e)=>i.fire(n(...e)),i=new w({onFirstListenerAdd:()=>e.on(t,r),onLastListenerRemove:()=>e.removeListener(t,r)});return i.event},e.fromDOMEventEmitter=function(e,t,n=(e=>e)){const r=(...e)=>i.fire(n(...e)),i=new w({onFirstListenerAdd:()=>e.addEventListener(t,r),onLastListenerRemove:()=>e.removeEventListener(t,r)});return i.event},e.toPromise=function(e){return new Promise(n=>t(e)(n))}}(E||(E={}));class N{constructor(e){this._listenerCount=0,this._invocationCount=0,this._elapsedOverall=0,this._name=`${e}_${N._idPool++}`}start(e){this._stopWatch=new A(!0),this._listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this._elapsedOverall+=e,this._invocationCount+=1,this._stopWatch=void 0}}}N._idPool=0;class w{constructor(e){var t;this._disposed=!1,this._options=e,this._leakageMon=void 0,this._perfMon=(null===(t=this._options)||void 0===t?void 0:t._profName)?new N(this._options._profName):void 0}get event(){return this._event||(this._event=(e,t,n)=>{var r;this._listeners||(this._listeners=new _);const i=this._listeners.isEmpty();i&&this._options&&this._options.onFirstListenerAdd&&this._options.onFirstListenerAdd(this);const o=this._listeners.push(t?[e,t]:e);i&&this._options&&this._options.onFirstListenerDidAdd&&this._options.onFirstListenerDidAdd(this),this._options&&this._options.onListenerDidAdd&&this._options.onListenerDidAdd(this,e,t);const s=null===(r=this._leakageMon)||void 0===r?void 0:r.check(this._listeners.size),a=p(()=>{if(s&&s(),!this._disposed&&(o(),this._options&&this._options.onLastListenerRemove)){this._listeners&&!this._listeners.isEmpty()||this._options.onLastListenerRemove(this)}});return n instanceof v?n.add(a):Array.isArray(n)&&n.push(a),a}),this._event}fire(e){var t,n;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new _);for(let t of this._listeners)this._deliveryQueue.push([t,e]);for(null===(t=this._perfMon)||void 0===t||t.start(this._deliveryQueue.size);this._deliveryQueue.size>0;){const[e,t]=this._deliveryQueue.shift();try{"function"==typeof e?e.call(void 0,t):e[0].call(e[1],t)}catch(e){i(e)}}null===(n=this._perfMon)||void 0===n||n.stop()}}dispose(){var e,t,n,r,i;this._disposed||(this._disposed=!0,null===(e=this._listeners)||void 0===e||e.clear(),null===(t=this._deliveryQueue)||void 0===t||t.clear(),null===(r=null===(n=this._options)||void 0===n?void 0:n.onLastListenerRemove)||void 0===r||r.call(n),null===(i=this._leakageMon)||void 0===i||i.dispose())}}function x(e){const t=[];for(const n of function(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}(e))"function"==typeof e[n]&&t.push(n);return t}function L(e){return e>=65&&e<=90}function T(e){return 55296<=e&&e<=56319}function k(e){return 56320<=e&&e<=57343}function O(e,t){return t-56320+(e-55296<<10)+65536}String.fromCharCode(65279);class I{constructor(){this._data=JSON.parse("[0,0,0,51592,51592,11,44424,44424,11,72251,72254,5,7150,7150,7,48008,48008,11,55176,55176,11,128420,128420,14,3276,3277,5,9979,9980,14,46216,46216,11,49800,49800,11,53384,53384,11,70726,70726,5,122915,122916,5,129320,129327,14,2558,2558,5,5906,5908,5,9762,9763,14,43360,43388,8,45320,45320,11,47112,47112,11,48904,48904,11,50696,50696,11,52488,52488,11,54280,54280,11,70082,70083,1,71350,71350,7,73111,73111,5,127892,127893,14,128726,128727,14,129473,129474,14,2027,2035,5,2901,2902,5,3784,3789,5,6754,6754,5,8418,8420,5,9877,9877,14,11088,11088,14,44008,44008,5,44872,44872,11,45768,45768,11,46664,46664,11,47560,47560,11,48456,48456,11,49352,49352,11,50248,50248,11,51144,51144,11,52040,52040,11,52936,52936,11,53832,53832,11,54728,54728,11,69811,69814,5,70459,70460,5,71096,71099,7,71998,71998,5,72874,72880,5,119149,119149,7,127374,127374,14,128335,128335,14,128482,128482,14,128765,128767,14,129399,129400,14,129680,129685,14,1476,1477,5,2377,2380,7,2759,2760,5,3137,3140,7,3458,3459,7,4153,4154,5,6432,6434,5,6978,6978,5,7675,7679,5,9723,9726,14,9823,9823,14,9919,9923,14,10035,10036,14,42736,42737,5,43596,43596,5,44200,44200,11,44648,44648,11,45096,45096,11,45544,45544,11,45992,45992,11,46440,46440,11,46888,46888,11,47336,47336,11,47784,47784,11,48232,48232,11,48680,48680,11,49128,49128,11,49576,49576,11,50024,50024,11,50472,50472,11,50920,50920,11,51368,51368,11,51816,51816,11,52264,52264,11,52712,52712,11,53160,53160,11,53608,53608,11,54056,54056,11,54504,54504,11,54952,54952,11,68108,68111,5,69933,69940,5,70197,70197,7,70498,70499,7,70845,70845,5,71229,71229,5,71727,71735,5,72154,72155,5,72344,72345,5,73023,73029,5,94095,94098,5,121403,121452,5,126981,127182,14,127538,127546,14,127990,127990,14,128391,128391,14,128445,128449,14,128500,128505,14,128752,128752,14,129160,129167,14,129356,129356,14,129432,129442,14,129648,129651,14,129751,131069,14,173,173,4,1757,1757,1,2274,2274,1,2494,2494,5,2641,2641,5,2876,2876,5,3014,3016,7,3262,3262,7,3393,3396,5,3570,3571,7,3968,3972,5,4228,4228,7,6086,6086,5,6679,6680,5,6912,6915,5,7080,7081,5,7380,7392,5,8252,8252,14,9096,9096,14,9748,9749,14,9784,9786,14,9833,9850,14,9890,9894,14,9938,9938,14,9999,9999,14,10085,10087,14,12349,12349,14,43136,43137,7,43454,43456,7,43755,43755,7,44088,44088,11,44312,44312,11,44536,44536,11,44760,44760,11,44984,44984,11,45208,45208,11,45432,45432,11,45656,45656,11,45880,45880,11,46104,46104,11,46328,46328,11,46552,46552,11,46776,46776,11,47000,47000,11,47224,47224,11,47448,47448,11,47672,47672,11,47896,47896,11,48120,48120,11,48344,48344,11,48568,48568,11,48792,48792,11,49016,49016,11,49240,49240,11,49464,49464,11,49688,49688,11,49912,49912,11,50136,50136,11,50360,50360,11,50584,50584,11,50808,50808,11,51032,51032,11,51256,51256,11,51480,51480,11,51704,51704,11,51928,51928,11,52152,52152,11,52376,52376,11,52600,52600,11,52824,52824,11,53048,53048,11,53272,53272,11,53496,53496,11,53720,53720,11,53944,53944,11,54168,54168,11,54392,54392,11,54616,54616,11,54840,54840,11,55064,55064,11,65438,65439,5,69633,69633,5,69837,69837,1,70018,70018,7,70188,70190,7,70368,70370,7,70465,70468,7,70712,70719,5,70835,70840,5,70850,70851,5,71132,71133,5,71340,71340,7,71458,71461,5,71985,71989,7,72002,72002,7,72193,72202,5,72281,72283,5,72766,72766,7,72885,72886,5,73104,73105,5,92912,92916,5,113824,113827,4,119173,119179,5,121505,121519,5,125136,125142,5,127279,127279,14,127489,127490,14,127570,127743,14,127900,127901,14,128254,128254,14,128369,128370,14,128400,128400,14,128425,128432,14,128468,128475,14,128489,128494,14,128715,128720,14,128745,128745,14,128759,128760,14,129004,129023,14,129296,129304,14,129340,129342,14,129388,129392,14,129404,129407,14,129454,129455,14,129485,129487,14,129659,129663,14,129719,129727,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2363,2363,7,2402,2403,5,2507,2508,7,2622,2624,7,2691,2691,7,2786,2787,5,2881,2884,5,3006,3006,5,3072,3072,5,3170,3171,5,3267,3268,7,3330,3331,7,3406,3406,1,3538,3540,5,3655,3662,5,3897,3897,5,4038,4038,5,4184,4185,5,4352,4447,8,6068,6069,5,6155,6157,5,6448,6449,7,6742,6742,5,6783,6783,5,6966,6970,5,7042,7042,7,7143,7143,7,7212,7219,5,7412,7412,5,8206,8207,4,8294,8303,4,8596,8601,14,9410,9410,14,9742,9742,14,9757,9757,14,9770,9770,14,9794,9794,14,9828,9828,14,9855,9855,14,9882,9882,14,9900,9903,14,9929,9933,14,9963,9967,14,9987,9988,14,10006,10006,14,10062,10062,14,10175,10175,14,11744,11775,5,42607,42607,5,43043,43044,7,43263,43263,5,43444,43445,7,43569,43570,5,43698,43700,5,43766,43766,5,44032,44032,11,44144,44144,11,44256,44256,11,44368,44368,11,44480,44480,11,44592,44592,11,44704,44704,11,44816,44816,11,44928,44928,11,45040,45040,11,45152,45152,11,45264,45264,11,45376,45376,11,45488,45488,11,45600,45600,11,45712,45712,11,45824,45824,11,45936,45936,11,46048,46048,11,46160,46160,11,46272,46272,11,46384,46384,11,46496,46496,11,46608,46608,11,46720,46720,11,46832,46832,11,46944,46944,11,47056,47056,11,47168,47168,11,47280,47280,11,47392,47392,11,47504,47504,11,47616,47616,11,47728,47728,11,47840,47840,11,47952,47952,11,48064,48064,11,48176,48176,11,48288,48288,11,48400,48400,11,48512,48512,11,48624,48624,11,48736,48736,11,48848,48848,11,48960,48960,11,49072,49072,11,49184,49184,11,49296,49296,11,49408,49408,11,49520,49520,11,49632,49632,11,49744,49744,11,49856,49856,11,49968,49968,11,50080,50080,11,50192,50192,11,50304,50304,11,50416,50416,11,50528,50528,11,50640,50640,11,50752,50752,11,50864,50864,11,50976,50976,11,51088,51088,11,51200,51200,11,51312,51312,11,51424,51424,11,51536,51536,11,51648,51648,11,51760,51760,11,51872,51872,11,51984,51984,11,52096,52096,11,52208,52208,11,52320,52320,11,52432,52432,11,52544,52544,11,52656,52656,11,52768,52768,11,52880,52880,11,52992,52992,11,53104,53104,11,53216,53216,11,53328,53328,11,53440,53440,11,53552,53552,11,53664,53664,11,53776,53776,11,53888,53888,11,54000,54000,11,54112,54112,11,54224,54224,11,54336,54336,11,54448,54448,11,54560,54560,11,54672,54672,11,54784,54784,11,54896,54896,11,55008,55008,11,55120,55120,11,64286,64286,5,66272,66272,5,68900,68903,5,69762,69762,7,69817,69818,5,69927,69931,5,70003,70003,5,70070,70078,5,70094,70094,7,70194,70195,7,70206,70206,5,70400,70401,5,70463,70463,7,70475,70477,7,70512,70516,5,70722,70724,5,70832,70832,5,70842,70842,5,70847,70848,5,71088,71089,7,71102,71102,7,71219,71226,5,71231,71232,5,71342,71343,7,71453,71455,5,71463,71467,5,71737,71738,5,71995,71996,5,72000,72000,7,72145,72147,7,72160,72160,5,72249,72249,7,72273,72278,5,72330,72342,5,72752,72758,5,72850,72871,5,72882,72883,5,73018,73018,5,73031,73031,5,73109,73109,5,73461,73462,7,94031,94031,5,94192,94193,7,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,126976,126979,14,127184,127231,14,127344,127345,14,127405,127461,14,127514,127514,14,127561,127567,14,127778,127779,14,127896,127896,14,127985,127986,14,127995,127999,5,128326,128328,14,128360,128366,14,128378,128378,14,128394,128397,14,128405,128406,14,128422,128423,14,128435,128443,14,128453,128464,14,128479,128480,14,128484,128487,14,128496,128498,14,128640,128709,14,128723,128724,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129096,129103,14,129292,129292,14,129311,129311,14,129329,129330,14,129344,129349,14,129360,129374,14,129394,129394,14,129402,129402,14,129413,129425,14,129445,129450,14,129466,129471,14,129483,129483,14,129511,129535,14,129653,129655,14,129667,129670,14,129705,129711,14,129731,129743,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2307,2307,7,2366,2368,7,2382,2383,7,2434,2435,7,2497,2500,5,2519,2519,5,2563,2563,7,2631,2632,5,2677,2677,5,2750,2752,7,2763,2764,7,2817,2817,5,2879,2879,5,2891,2892,7,2914,2915,5,3008,3008,5,3021,3021,5,3076,3076,5,3146,3149,5,3202,3203,7,3264,3265,7,3271,3272,7,3298,3299,5,3390,3390,5,3402,3404,7,3426,3427,5,3535,3535,5,3544,3550,7,3635,3635,7,3763,3763,7,3893,3893,5,3953,3966,5,3981,3991,5,4145,4145,7,4157,4158,5,4209,4212,5,4237,4237,5,4520,4607,10,5970,5971,5,6071,6077,5,6089,6099,5,6277,6278,5,6439,6440,5,6451,6456,7,6683,6683,5,6744,6750,5,6765,6770,7,6846,6846,5,6964,6964,5,6972,6972,5,7019,7027,5,7074,7077,5,7083,7085,5,7146,7148,7,7154,7155,7,7222,7223,5,7394,7400,5,7416,7417,5,8204,8204,5,8233,8233,4,8288,8292,4,8413,8416,5,8482,8482,14,8986,8987,14,9193,9203,14,9654,9654,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9775,14,9792,9792,14,9800,9811,14,9825,9826,14,9831,9831,14,9852,9853,14,9872,9873,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9936,9936,14,9941,9960,14,9974,9974,14,9982,9985,14,9992,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10145,10145,14,11013,11015,14,11503,11505,5,12334,12335,5,12951,12951,14,42612,42621,5,43014,43014,5,43047,43047,7,43204,43205,5,43335,43345,5,43395,43395,7,43450,43451,7,43561,43566,5,43573,43574,5,43644,43644,5,43710,43711,5,43758,43759,7,44005,44005,5,44012,44012,7,44060,44060,11,44116,44116,11,44172,44172,11,44228,44228,11,44284,44284,11,44340,44340,11,44396,44396,11,44452,44452,11,44508,44508,11,44564,44564,11,44620,44620,11,44676,44676,11,44732,44732,11,44788,44788,11,44844,44844,11,44900,44900,11,44956,44956,11,45012,45012,11,45068,45068,11,45124,45124,11,45180,45180,11,45236,45236,11,45292,45292,11,45348,45348,11,45404,45404,11,45460,45460,11,45516,45516,11,45572,45572,11,45628,45628,11,45684,45684,11,45740,45740,11,45796,45796,11,45852,45852,11,45908,45908,11,45964,45964,11,46020,46020,11,46076,46076,11,46132,46132,11,46188,46188,11,46244,46244,11,46300,46300,11,46356,46356,11,46412,46412,11,46468,46468,11,46524,46524,11,46580,46580,11,46636,46636,11,46692,46692,11,46748,46748,11,46804,46804,11,46860,46860,11,46916,46916,11,46972,46972,11,47028,47028,11,47084,47084,11,47140,47140,11,47196,47196,11,47252,47252,11,47308,47308,11,47364,47364,11,47420,47420,11,47476,47476,11,47532,47532,11,47588,47588,11,47644,47644,11,47700,47700,11,47756,47756,11,47812,47812,11,47868,47868,11,47924,47924,11,47980,47980,11,48036,48036,11,48092,48092,11,48148,48148,11,48204,48204,11,48260,48260,11,48316,48316,11,48372,48372,11,48428,48428,11,48484,48484,11,48540,48540,11,48596,48596,11,48652,48652,11,48708,48708,11,48764,48764,11,48820,48820,11,48876,48876,11,48932,48932,11,48988,48988,11,49044,49044,11,49100,49100,11,49156,49156,11,49212,49212,11,49268,49268,11,49324,49324,11,49380,49380,11,49436,49436,11,49492,49492,11,49548,49548,11,49604,49604,11,49660,49660,11,49716,49716,11,49772,49772,11,49828,49828,11,49884,49884,11,49940,49940,11,49996,49996,11,50052,50052,11,50108,50108,11,50164,50164,11,50220,50220,11,50276,50276,11,50332,50332,11,50388,50388,11,50444,50444,11,50500,50500,11,50556,50556,11,50612,50612,11,50668,50668,11,50724,50724,11,50780,50780,11,50836,50836,11,50892,50892,11,50948,50948,11,51004,51004,11,51060,51060,11,51116,51116,11,51172,51172,11,51228,51228,11,51284,51284,11,51340,51340,11,51396,51396,11,51452,51452,11,51508,51508,11,51564,51564,11,51620,51620,11,51676,51676,11,51732,51732,11,51788,51788,11,51844,51844,11,51900,51900,11,51956,51956,11,52012,52012,11,52068,52068,11,52124,52124,11,52180,52180,11,52236,52236,11,52292,52292,11,52348,52348,11,52404,52404,11,52460,52460,11,52516,52516,11,52572,52572,11,52628,52628,11,52684,52684,11,52740,52740,11,52796,52796,11,52852,52852,11,52908,52908,11,52964,52964,11,53020,53020,11,53076,53076,11,53132,53132,11,53188,53188,11,53244,53244,11,53300,53300,11,53356,53356,11,53412,53412,11,53468,53468,11,53524,53524,11,53580,53580,11,53636,53636,11,53692,53692,11,53748,53748,11,53804,53804,11,53860,53860,11,53916,53916,11,53972,53972,11,54028,54028,11,54084,54084,11,54140,54140,11,54196,54196,11,54252,54252,11,54308,54308,11,54364,54364,11,54420,54420,11,54476,54476,11,54532,54532,11,54588,54588,11,54644,54644,11,54700,54700,11,54756,54756,11,54812,54812,11,54868,54868,11,54924,54924,11,54980,54980,11,55036,55036,11,55092,55092,11,55148,55148,11,55216,55238,9,65056,65071,5,65529,65531,4,68097,68099,5,68159,68159,5,69446,69456,5,69688,69702,5,69808,69810,7,69815,69816,7,69821,69821,1,69888,69890,5,69932,69932,7,69957,69958,7,70016,70017,5,70067,70069,7,70079,70080,7,70089,70092,5,70095,70095,5,70191,70193,5,70196,70196,5,70198,70199,5,70367,70367,5,70371,70378,5,70402,70403,7,70462,70462,5,70464,70464,5,70471,70472,7,70487,70487,5,70502,70508,5,70709,70711,7,70720,70721,7,70725,70725,7,70750,70750,5,70833,70834,7,70841,70841,7,70843,70844,7,70846,70846,7,70849,70849,7,71087,71087,5,71090,71093,5,71100,71101,5,71103,71104,5,71216,71218,7,71227,71228,7,71230,71230,7,71339,71339,5,71341,71341,5,71344,71349,5,71351,71351,5,71456,71457,7,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123628,123631,5,125252,125258,5,126980,126980,14,127183,127183,14,127245,127247,14,127340,127343,14,127358,127359,14,127377,127386,14,127462,127487,6,127491,127503,14,127535,127535,14,127548,127551,14,127568,127569,14,127744,127777,14,127780,127891,14,127894,127895,14,127897,127899,14,127902,127984,14,127987,127989,14,127991,127994,14,128000,128253,14,128255,128317,14,128329,128334,14,128336,128359,14,128367,128368,14,128371,128377,14,128379,128390,14,128392,128393,14,128398,128399,14,128401,128404,14,128407,128419,14,128421,128421,14,128424,128424,14,128433,128434,14,128444,128444,14,128450,128452,14,128465,128467,14,128476,128478,14,128481,128481,14,128483,128483,14,128488,128488,14,128495,128495,14,128499,128499,14,128506,128591,14,128710,128714,14,128721,128722,14,128725,128725,14,128728,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129664,129666,14,129671,129679,14,129686,129704,14,129712,129718,14,129728,129730,14,129744,129750,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2259,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3134,3136,5,3142,3144,5,3157,3158,5,3201,3201,5,3260,3260,5,3263,3263,5,3266,3266,5,3270,3270,5,3274,3275,7,3285,3286,5,3328,3329,5,3387,3388,5,3391,3392,7,3398,3400,7,3405,3405,5,3415,3415,5,3457,3457,5,3530,3530,5,3536,3537,7,3542,3542,5,3551,3551,5,3633,3633,5,3636,3642,5,3761,3761,5,3764,3772,5,3864,3865,5,3895,3895,5,3902,3903,7,3967,3967,7,3974,3975,5,3993,4028,5,4141,4144,5,4146,4151,5,4155,4156,7,4182,4183,7,4190,4192,5,4226,4226,5,4229,4230,5,4253,4253,5,4448,4519,9,4957,4959,5,5938,5940,5,6002,6003,5,6070,6070,7,6078,6085,7,6087,6088,7,6109,6109,5,6158,6158,4,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6848,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7673,5,8203,8203,4,8205,8205,13,8232,8232,4,8234,8238,4,8265,8265,14,8293,8293,4,8400,8412,5,8417,8417,5,8421,8432,5,8505,8505,14,8617,8618,14,9000,9000,14,9167,9167,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9776,9783,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9935,14,9937,9937,14,9939,9940,14,9961,9962,14,9968,9973,14,9975,9978,14,9981,9981,14,9986,9986,14,9989,9989,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10084,14,10133,10135,14,10160,10160,14,10548,10549,14,11035,11036,14,11093,11093,14,11647,11647,5,12330,12333,5,12336,12336,14,12441,12442,5,12953,12953,14,42608,42610,5,42654,42655,5,43010,43010,5,43019,43019,5,43045,43046,5,43052,43052,5,43188,43203,7,43232,43249,5,43302,43309,5,43346,43347,7,43392,43394,5,43443,43443,5,43446,43449,5,43452,43453,5,43493,43493,5,43567,43568,7,43571,43572,7,43587,43587,5,43597,43597,7,43696,43696,5,43703,43704,5,43713,43713,5,43756,43757,5,43765,43765,7,44003,44004,7,44006,44007,7,44009,44010,7,44013,44013,5,44033,44059,12,44061,44087,12,44089,44115,12,44117,44143,12,44145,44171,12,44173,44199,12,44201,44227,12,44229,44255,12,44257,44283,12,44285,44311,12,44313,44339,12,44341,44367,12,44369,44395,12,44397,44423,12,44425,44451,12,44453,44479,12,44481,44507,12,44509,44535,12,44537,44563,12,44565,44591,12,44593,44619,12,44621,44647,12,44649,44675,12,44677,44703,12,44705,44731,12,44733,44759,12,44761,44787,12,44789,44815,12,44817,44843,12,44845,44871,12,44873,44899,12,44901,44927,12,44929,44955,12,44957,44983,12,44985,45011,12,45013,45039,12,45041,45067,12,45069,45095,12,45097,45123,12,45125,45151,12,45153,45179,12,45181,45207,12,45209,45235,12,45237,45263,12,45265,45291,12,45293,45319,12,45321,45347,12,45349,45375,12,45377,45403,12,45405,45431,12,45433,45459,12,45461,45487,12,45489,45515,12,45517,45543,12,45545,45571,12,45573,45599,12,45601,45627,12,45629,45655,12,45657,45683,12,45685,45711,12,45713,45739,12,45741,45767,12,45769,45795,12,45797,45823,12,45825,45851,12,45853,45879,12,45881,45907,12,45909,45935,12,45937,45963,12,45965,45991,12,45993,46019,12,46021,46047,12,46049,46075,12,46077,46103,12,46105,46131,12,46133,46159,12,46161,46187,12,46189,46215,12,46217,46243,12,46245,46271,12,46273,46299,12,46301,46327,12,46329,46355,12,46357,46383,12,46385,46411,12,46413,46439,12,46441,46467,12,46469,46495,12,46497,46523,12,46525,46551,12,46553,46579,12,46581,46607,12,46609,46635,12,46637,46663,12,46665,46691,12,46693,46719,12,46721,46747,12,46749,46775,12,46777,46803,12,46805,46831,12,46833,46859,12,46861,46887,12,46889,46915,12,46917,46943,12,46945,46971,12,46973,46999,12,47001,47027,12,47029,47055,12,47057,47083,12,47085,47111,12,47113,47139,12,47141,47167,12,47169,47195,12,47197,47223,12,47225,47251,12,47253,47279,12,47281,47307,12,47309,47335,12,47337,47363,12,47365,47391,12,47393,47419,12,47421,47447,12,47449,47475,12,47477,47503,12,47505,47531,12,47533,47559,12,47561,47587,12,47589,47615,12,47617,47643,12,47645,47671,12,47673,47699,12,47701,47727,12,47729,47755,12,47757,47783,12,47785,47811,12,47813,47839,12,47841,47867,12,47869,47895,12,47897,47923,12,47925,47951,12,47953,47979,12,47981,48007,12,48009,48035,12,48037,48063,12,48065,48091,12,48093,48119,12,48121,48147,12,48149,48175,12,48177,48203,12,48205,48231,12,48233,48259,12,48261,48287,12,48289,48315,12,48317,48343,12,48345,48371,12,48373,48399,12,48401,48427,12,48429,48455,12,48457,48483,12,48485,48511,12,48513,48539,12,48541,48567,12,48569,48595,12,48597,48623,12,48625,48651,12,48653,48679,12,48681,48707,12,48709,48735,12,48737,48763,12,48765,48791,12,48793,48819,12,48821,48847,12,48849,48875,12,48877,48903,12,48905,48931,12,48933,48959,12,48961,48987,12,48989,49015,12,49017,49043,12,49045,49071,12,49073,49099,12,49101,49127,12,49129,49155,12,49157,49183,12,49185,49211,12,49213,49239,12,49241,49267,12,49269,49295,12,49297,49323,12,49325,49351,12,49353,49379,12,49381,49407,12,49409,49435,12,49437,49463,12,49465,49491,12,49493,49519,12,49521,49547,12,49549,49575,12,49577,49603,12,49605,49631,12,49633,49659,12,49661,49687,12,49689,49715,12,49717,49743,12,49745,49771,12,49773,49799,12,49801,49827,12,49829,49855,12,49857,49883,12,49885,49911,12,49913,49939,12,49941,49967,12,49969,49995,12,49997,50023,12,50025,50051,12,50053,50079,12,50081,50107,12,50109,50135,12,50137,50163,12,50165,50191,12,50193,50219,12,50221,50247,12,50249,50275,12,50277,50303,12,50305,50331,12,50333,50359,12,50361,50387,12,50389,50415,12,50417,50443,12,50445,50471,12,50473,50499,12,50501,50527,12,50529,50555,12,50557,50583,12,50585,50611,12,50613,50639,12,50641,50667,12,50669,50695,12,50697,50723,12,50725,50751,12,50753,50779,12,50781,50807,12,50809,50835,12,50837,50863,12,50865,50891,12,50893,50919,12,50921,50947,12,50949,50975,12,50977,51003,12,51005,51031,12,51033,51059,12,51061,51087,12,51089,51115,12,51117,51143,12,51145,51171,12,51173,51199,12,51201,51227,12,51229,51255,12,51257,51283,12,51285,51311,12,51313,51339,12,51341,51367,12,51369,51395,12,51397,51423,12,51425,51451,12,51453,51479,12,51481,51507,12,51509,51535,12,51537,51563,12,51565,51591,12,51593,51619,12,51621,51647,12,51649,51675,12,51677,51703,12,51705,51731,12,51733,51759,12,51761,51787,12,51789,51815,12,51817,51843,12,51845,51871,12,51873,51899,12,51901,51927,12,51929,51955,12,51957,51983,12,51985,52011,12,52013,52039,12,52041,52067,12,52069,52095,12,52097,52123,12,52125,52151,12,52153,52179,12,52181,52207,12,52209,52235,12,52237,52263,12,52265,52291,12,52293,52319,12,52321,52347,12,52349,52375,12,52377,52403,12,52405,52431,12,52433,52459,12,52461,52487,12,52489,52515,12,52517,52543,12,52545,52571,12,52573,52599,12,52601,52627,12,52629,52655,12,52657,52683,12,52685,52711,12,52713,52739,12,52741,52767,12,52769,52795,12,52797,52823,12,52825,52851,12,52853,52879,12,52881,52907,12,52909,52935,12,52937,52963,12,52965,52991,12,52993,53019,12,53021,53047,12,53049,53075,12,53077,53103,12,53105,53131,12,53133,53159,12,53161,53187,12,53189,53215,12,53217,53243,12,53245,53271,12,53273,53299,12,53301,53327,12,53329,53355,12,53357,53383,12,53385,53411,12,53413,53439,12,53441,53467,12,53469,53495,12,53497,53523,12,53525,53551,12,53553,53579,12,53581,53607,12,53609,53635,12,53637,53663,12,53665,53691,12,53693,53719,12,53721,53747,12,53749,53775,12,53777,53803,12,53805,53831,12,53833,53859,12,53861,53887,12,53889,53915,12,53917,53943,12,53945,53971,12,53973,53999,12,54001,54027,12,54029,54055,12,54057,54083,12,54085,54111,12,54113,54139,12,54141,54167,12,54169,54195,12,54197,54223,12,54225,54251,12,54253,54279,12,54281,54307,12,54309,54335,12,54337,54363,12,54365,54391,12,54393,54419,12,54421,54447,12,54449,54475,12,54477,54503,12,54505,54531,12,54533,54559,12,54561,54587,12,54589,54615,12,54617,54643,12,54645,54671,12,54673,54699,12,54701,54727,12,54729,54755,12,54757,54783,12,54785,54811,12,54813,54839,12,54841,54867,12,54869,54895,12,54897,54923,12,54925,54951,12,54953,54979,12,54981,55007,12,55009,55035,12,55037,55063,12,55065,55091,12,55093,55119,12,55121,55147,12,55149,55175,12,55177,55203,12,55243,55291,10,65024,65039,5,65279,65279,4,65520,65528,4,66045,66045,5,66422,66426,5,68101,68102,5,68152,68154,5,68325,68326,5,69291,69292,5,69632,69632,7,69634,69634,7,69759,69761,5]")}static getInstance(){return I._INSTANCE||(I._INSTANCE=new I),I._INSTANCE}getGraphemeBreakType(e){if(e<32)return 10===e?3:13===e?2:4;if(e<127)return 0;const t=this._data,n=t.length/3;let r=1;for(;r<=n;)if(e<t[3*r])r*=2;else{if(!(e>t[3*r+1]))return t[3*r+2];r=2*r+1}return 0}}I._INSTANCE=null;class M{constructor(e,t,n,r){this.vsWorker=e,this.req=t,this.method=n,this.args=r,this.type=0}}class P{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}}class V{constructor(e,t,n,r){this.vsWorker=e,this.req=t,this.eventName=n,this.arg=r,this.type=2}}class R{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}}class F{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}}class D{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t){const n=String(++this._lastSentReq);return new Promise((r,i)=>{this._pendingReplies[n]={resolve:r,reject:i},this._send(new M(this._workerId,n,e,t))})}listen(e,t){let n=null;const r=new w({onFirstListenerAdd:()=>{n=String(++this._lastSentReq),this._pendingEmitters.set(n,r),this._send(new V(this._workerId,n,e,t))},onLastListenerRemove:()=>{this._pendingEmitters.delete(n),this._send(new F(this._workerId,n)),n=null}});return r.event}handleMessage(e){e&&e.vsWorker&&(-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e))}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq])return;let t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;return e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),void t.reject(n)}t.resolve(e.res)}_handleRequestMessage(e){let t=e.req;this._handler.handleMessage(e.method,e.args).then(e=>{this._send(new P(this._workerId,t,e,void 0))},e=>{e.detail instanceof Error&&(e.detail=o(e.detail)),this._send(new P(this._workerId,t,void 0,o(e)))})}_handleSubscribeEventMessage(e){const t=e.req,n=this._handler.handleEvent(e.eventName,e.arg)(e=>{this._send(new R(this._workerId,t,e))});this._pendingEvents.set(t,n)}_handleEventMessage(e){this._pendingEmitters.has(e.req)&&this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){this._pendingEvents.has(e.req)&&(this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req))}_send(e){let t=[];if(0===e.type)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else 1===e.type&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}}function K(e){return"o"===e[0]&&"n"===e[1]&&L(e.charCodeAt(2))}function j(e){return/^onDynamic/.test(e)&&L(e.charCodeAt(9))}function U(e,t,n){const r=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},i=e=>function(t){return n(e,t)};let o={};for(const t of e)j(t)?o[t]=i(t):K(t)?o[t]=n(t,void 0):o[t]=r(t);return o}class B{constructor(e,t){this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new D({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t)=>this._handleMessage(e,t),handleEvent:(e,t)=>this._handleEvent(e,t)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t){if("$initialize"===e)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!=typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch(e){return Promise.reject(e)}}_handleEvent(e,t){if(!this._requestHandler)throw new Error("Missing requestHandler");if(j(e)){const n=this._requestHandler[e].call(this._requestHandler,t);if("function"!=typeof n)throw new Error(`Missing dynamic event ${e} on request handler.`);return n}if(K(e)){const t=this._requestHandler[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on request handler.`);return t}throw new Error("Malformed event name "+e)}initialize(e,t,n,r){this._protocol.setWorkerId(e);const i=U(r,(e,t)=>this._protocol.sendMessage(e,t),(e,t)=>this._protocol.listen(e,t));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(i),Promise.resolve(x(this._requestHandler))):(t&&(void 0!==t.baseUrl&&delete t.baseUrl,void 0!==t.paths&&void 0!==t.paths.vs&&delete t.paths.vs,void 0!==typeof t.trustedTypesPolicy&&delete t.trustedTypesPolicy,t.catchError=!0,C.a.require.config(t)),new Promise((e,t)=>{(0,C.a.require)([n],n=>{this._requestHandler=n.create(i),this._requestHandler?e(x(this._requestHandler)):t(new Error("No RequestHandler!"))},t)}))}}class q{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function $(e,t){return(t<<5)-t+e|0}function W(e,t){t=$(149417,t);for(let n=0,r=e.length;n<r;n++)t=$(e.charCodeAt(n),t);return t}function H(e,t,n=32){const r=n-t;return(e<<t|(~((1<<r)-1)&e)>>>r)>>>0}function z(e,t=0,n=e.byteLength,r=0){for(let i=0;i<n;i++)e[t+i]=r}function G(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(e=>e.toString(16).padStart(2,"0")).join(""):function(e,t,n="0"){for(;e.length<t;)e=n+e;return e}((e>>>0).toString(16),t/4)}class J{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){const t=e.length;if(0===t)return;const n=this._buff;let r,i,o=this._buffLen,s=this._leftoverHighSurrogate;for(0!==s?(r=s,i=-1,s=0):(r=e.charCodeAt(0),i=0);;){let a=r;if(T(r)){if(!(i+1<t)){s=r;break}{const t=e.charCodeAt(i+1);k(t)?(i++,a=O(r,t)):a=65533}}else k(r)&&(a=65533);if(o=this._push(n,o,a),i++,!(i<t))break;r=e.charCodeAt(i)}this._buffLen=o,this._leftoverHighSurrogate=s}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(1984&n)>>>6,e[t++]=128|(63&n)>>>0):n<65536?(e[t++]=224|(61440&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0):(e[t++]=240|(1835008&n)>>>18,e[t++]=128|(258048&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),G(this._h0)+G(this._h1)+G(this._h2)+G(this._h3)+G(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,z(this._buff,this._buffLen),this._buffLen>56&&(this._step(),z(this._buff));const e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){const e=J._bigBlock32,t=this._buffDV;for(let n=0;n<64;n+=4)e.setUint32(n,t.getUint32(n,!1),!1);for(let t=64;t<320;t+=4)e.setUint32(t,H(e.getUint32(t-12,!1)^e.getUint32(t-32,!1)^e.getUint32(t-56,!1)^e.getUint32(t-64,!1),1),!1);let n,r,i,o=this._h0,s=this._h1,a=this._h2,u=this._h3,l=this._h4;for(let t=0;t<80;t++)t<20?(n=s&a|~s&u,r=1518500249):t<40?(n=s^a^u,r=1859775393):t<60?(n=s&a|s&u|a&u,r=2400959708):(n=s^a^u,r=3395469782),i=H(o,5)+n+l+r+e.getUint32(4*t,!1)&4294967295,l=u,u=a,a=H(s,30),s=o,o=i;this._h0=this._h0+o&4294967295,this._h1=this._h1+s&4294967295,this._h2=this._h2+a&4294967295,this._h3=this._h3+u&4294967295,this._h4=this._h4+l&4294967295}}J._bigBlock32=new DataView(new ArrayBuffer(320));class X{constructor(e){this.source=e}getElements(){const e=this.source,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}}function Z(e,t,n){return new te(new X(e),new X(t)).ComputeDiff(n).changes}class Y{static Assert(e,t){if(!e)throw new Error(t)}}class Q{static Copy(e,t,n,r,i){for(let o=0;o<i;o++)n[r+o]=e[t+o]}static Copy2(e,t,n,r,i){for(let o=0;o<i;o++)n[r+o]=e[t+o]}}class ee{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new q(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class te{constructor(e,t,n=null){this.ContinueProcessingPredicate=n,this._originalSequence=e,this._modifiedSequence=t;const[r,i,o]=te._getElements(e),[s,a,u]=te._getElements(t);this._hasStrings=o&&u,this._originalStringElements=r,this._originalElementsOrHash=i,this._modifiedStringElements=s,this._modifiedElementsOrHash=a,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&"string"==typeof e[0]}static _getElements(e){const t=e.getElements();if(te._isStringArray(t)){const e=new Int32Array(t.length);for(let n=0,r=t.length;n<r;n++)e[n]=W(t[n],0);return[t,e,!0]}return t instanceof Int32Array?[[],t,!1]:[[],new Int32Array(t),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}ElementsAreStrictEqual(e,t){if(!this.ElementsAreEqual(e,t))return!1;return te._getStrictElement(this._originalSequence,e)===te._getStrictElement(this._modifiedSequence,t)}static _getStrictElement(e,t){return"function"==typeof e.getStrictElement?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,r,i){const o=[!1];let s=this.ComputeDiffRecursive(e,t,n,r,o);return i&&(s=this.PrettifyChanges(s)),{quitEarly:o[0],changes:s}}ComputeDiffRecursive(e,t,n,r,i){for(i[0]=!1;e<=t&&n<=r&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&r>=n&&this.ElementsAreEqual(t,r);)t--,r--;if(e>t||n>r){let i;return n<=r?(Y.Assert(e===t+1,"originalStart should only be one more than originalEnd"),i=[new q(e,0,n,r-n+1)]):e<=t?(Y.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),i=[new q(e,t-e+1,n,0)]):(Y.Assert(e===t+1,"originalStart should only be one more than originalEnd"),Y.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),i=[]),i}const o=[0],s=[0],a=this.ComputeRecursionPoint(e,t,n,r,o,s,i),u=o[0],l=s[0];if(null!==a)return a;if(!i[0]){const o=this.ComputeDiffRecursive(e,u,n,l,i);let s=[];return s=i[0]?[new q(u+1,t-(u+1)+1,l+1,r-(l+1)+1)]:this.ComputeDiffRecursive(u+1,t,l+1,r,i),this.ConcatenateChanges(o,s)}return[new q(e,t-e+1,n,r-n+1)]}WALKTRACE(e,t,n,r,i,o,s,a,u,l,c,h,f,d,m,g,p,v){let y=null,b=null,_=new ee,C=t,S=n,A=f[0]-g[0]-r,E=-1073741824,N=this.m_forwardHistory.length-1;do{const t=A+e;t===C||t<S&&u[t-1]<u[t+1]?(d=(c=u[t+1])-A-r,c<E&&_.MarkNextChange(),E=c,_.AddModifiedElement(c+1,d),A=t+1-e):(d=(c=u[t-1]+1)-A-r,c<E&&_.MarkNextChange(),E=c-1,_.AddOriginalElement(c,d+1),A=t-1-e),N>=0&&(e=(u=this.m_forwardHistory[N])[0],C=1,S=u.length-1)}while(--N>=-1);if(y=_.getReverseChanges(),v[0]){let e=f[0]+1,t=g[0]+1;if(null!==y&&y.length>0){const n=y[y.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}b=[new q(e,h-e+1,t,m-t+1)]}else{_=new ee,C=o,S=s,A=f[0]-g[0]-a,E=1073741824,N=p?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const e=A+i;e===C||e<S&&l[e-1]>=l[e+1]?(d=(c=l[e+1]-1)-A-a,c>E&&_.MarkNextChange(),E=c+1,_.AddOriginalElement(c+1,d+1),A=e+1-i):(d=(c=l[e-1])-A-a,c>E&&_.MarkNextChange(),E=c,_.AddModifiedElement(c+1,d+1),A=e-1-i),N>=0&&(i=(l=this.m_reverseHistory[N])[0],C=1,S=l.length-1)}while(--N>=-1);b=_.getChanges()}return this.ConcatenateChanges(y,b)}ComputeRecursionPoint(e,t,n,r,i,o,s){let a=0,u=0,l=0,c=0,h=0,f=0;e--,n--,i[0]=0,o[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const d=t-e+(r-n),m=d+1,g=new Int32Array(m),p=new Int32Array(m),v=r-n,y=t-e,b=e-n,_=t-r,C=(y-v)%2==0;g[v]=e,p[y]=t,s[0]=!1;for(let S=1;S<=d/2+1;S++){let d=0,A=0;l=this.ClipDiagonalBound(v-S,S,v,m),c=this.ClipDiagonalBound(v+S,S,v,m);for(let e=l;e<=c;e+=2){a=e===l||e<c&&g[e-1]<g[e+1]?g[e+1]:g[e-1]+1,u=a-(e-v)-b;const n=a;for(;a<t&&u<r&&this.ElementsAreEqual(a+1,u+1);)a++,u++;if(g[e]=a,a+u>d+A&&(d=a,A=u),!C&&Math.abs(e-y)<=S-1&&a>=p[e])return i[0]=a,o[0]=u,n<=p[e]&&S<=1448?this.WALKTRACE(v,l,c,b,y,h,f,_,g,p,a,t,i,u,r,o,C,s):null}const E=(d-e+(A-n)-S)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(d,E))return s[0]=!0,i[0]=d,o[0]=A,E>0&&S<=1448?this.WALKTRACE(v,l,c,b,y,h,f,_,g,p,a,t,i,u,r,o,C,s):(e++,n++,[new q(e,t-e+1,n,r-n+1)]);h=this.ClipDiagonalBound(y-S,S,y,m),f=this.ClipDiagonalBound(y+S,S,y,m);for(let d=h;d<=f;d+=2){a=d===h||d<f&&p[d-1]>=p[d+1]?p[d+1]-1:p[d-1],u=a-(d-y)-_;const m=a;for(;a>e&&u>n&&this.ElementsAreEqual(a,u);)a--,u--;if(p[d]=a,C&&Math.abs(d-v)<=S&&a<=g[d])return i[0]=a,o[0]=u,m>=g[d]&&S<=1448?this.WALKTRACE(v,l,c,b,y,h,f,_,g,p,a,t,i,u,r,o,C,s):null}if(S<=1447){let e=new Int32Array(c-l+2);e[0]=v-l+1,Q.Copy2(g,l,e,1,c-l+1),this.m_forwardHistory.push(e),e=new Int32Array(f-h+2),e[0]=y-h+1,Q.Copy2(p,h,e,1,f-h+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(v,l,c,b,y,h,f,_,g,p,a,t,i,u,r,o,C,s)}PrettifyChanges(e){for(let t=0;t<e.length;t++){const n=e[t],r=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,i=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,o=n.originalLength>0,s=n.modifiedLength>0;for(;n.originalStart+n.originalLength<r&&n.modifiedStart+n.modifiedLength<i&&(!o||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!s||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){const e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}let a=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],a)&&(e[t]=a[0],e.splice(t+1,1),t--)}for(let t=e.length-1;t>=0;t--){const n=e[t];let r=0,i=0;if(t>0){const n=e[t-1];r=n.originalStart+n.originalLength,i=n.modifiedStart+n.modifiedLength}const o=n.originalLength>0,s=n.modifiedLength>0;let a=0,u=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){const t=n.originalStart-e,l=n.modifiedStart-e;if(t<r||l<i)break;if(o&&!this.OriginalElementsAreEqual(t,t+n.originalLength))break;if(s&&!this.ModifiedElementsAreEqual(l,l+n.modifiedLength))break;const c=(t===r&&l===i?5:0)+this._boundaryScore(t,n.originalLength,l,n.modifiedLength);c>u&&(u=c,a=e)}n.originalStart-=a,n.modifiedStart-=a;const l=[null];t>0&&this.ChangesOverlap(e[t-1],e[t],l)&&(e[t-1]=l[0],e.splice(t,1),t++)}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){const n=e[t-1],r=e[t],i=r.originalStart-n.originalStart-n.originalLength,o=n.originalStart,s=r.originalStart+r.originalLength,a=s-o,u=n.modifiedStart,l=r.modifiedStart+r.modifiedLength,c=l-u;if(i<5&&a<20&&c<20){const e=this._findBetterContiguousSequence(o,a,u,c,i);if(e){const[t,o]=e;t===n.originalStart+n.originalLength&&o===n.modifiedStart+n.modifiedLength||(n.originalLength=t-n.originalStart,n.modifiedLength=o-n.modifiedStart,r.originalStart=t+i,r.modifiedStart=o+i,r.originalLength=s-r.originalStart,r.modifiedLength=l-r.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,r,i){if(t<i||r<i)return null;const o=e+t-i+1,s=n+r-i+1;let a=0,u=0,l=0;for(let t=e;t<o;t++)for(let e=n;e<s;e++){const n=this._contiguousSequenceScore(t,e,i);n>0&&n>a&&(a=n,u=t,l=e)}return a>0?[u,l]:null}_contiguousSequenceScore(e,t,n){let r=0;for(let i=0;i<n;i++){if(!this.ElementsAreEqual(e+i,t+i))return 0;r+=this._originalStringElements[e+i].length}return r}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,r){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,r)?1:0)}ConcatenateChanges(e,t){let n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){const r=new Array(e.length+t.length-1);return Q.Copy(e,0,r,0,e.length-1),r[e.length-1]=n[0],Q.Copy(t,1,r,e.length,t.length-1),r}{const n=new Array(e.length+t.length);return Q.Copy(e,0,n,0,e.length),Q.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(Y.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),Y.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){const r=e.originalStart;let i=e.originalLength;const o=e.modifiedStart;let s=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(i=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(s=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new q(r,i,o,s),!0}return n[0]=null,!1}ClipDiagonalBound(e,t,n,r){if(e>=0&&e<r)return e;const i=t%2==0;if(e<0){return i===(n%2==0)?0:1}return i===((r-n-1)%2==0)?r-1:r-2}}var ne=n("b5a0");class re extends Error{constructor(e,t,n){let r;"string"==typeof t&&0===t.indexOf("not ")?(r="must not be",t=t.replace(/^not /,"")):r="must be";const i=-1!==e.indexOf(".")?"property":"argument";let o=`The "${e}" ${i} ${r} of type ${t}`;o+=". Received type "+typeof n,super(o),this.code="ERR_INVALID_ARG_TYPE"}}function ie(e,t){if("string"!=typeof e)throw new re(t,"string",e)}function oe(e){return 47===e||92===e}function se(e){return 47===e}function ae(e){return e>=65&&e<=90||e>=97&&e<=122}function ue(e,t,n,r){let i="",o=0,s=-1,a=0,u=0;for(let l=0;l<=e.length;++l){if(l<e.length)u=e.charCodeAt(l);else{if(r(u))break;u=47}if(r(u)){if(s===l-1||1===a);else if(2===a){if(i.length<2||2!==o||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2)){if(i.length>2){const e=i.lastIndexOf(n);-1===e?(i="",o=0):(i=i.slice(0,e),o=i.length-1-i.lastIndexOf(n)),s=l,a=0;continue}if(0!==i.length){i="",o=0,s=l,a=0;continue}}t&&(i+=i.length>0?n+"..":"..",o=2)}else i.length>0?i+=`${n}${e.slice(s+1,l)}`:i=e.slice(s+1,l),o=l-s-1;s=l,a=0}else 46===u&&-1!==a?++a:a=-1}return i}function le(e,t){if(null===t||"object"!=typeof t)throw new re("pathObject","Object",t);const n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const ce={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let o;if(i>=0){if(o=e[i],ie(o,"path"),0===o.length)continue}else 0===t.length?o=ne.a():(o=ne.b["="+t]||ne.a(),(void 0===o||o.slice(0,2).toLowerCase()!==t.toLowerCase()&&92===o.charCodeAt(2))&&(o=t+"\\"));const s=o.length;let a=0,u="",l=!1;const c=o.charCodeAt(0);if(1===s)oe(c)&&(a=1,l=!0);else if(oe(c))if(l=!0,oe(o.charCodeAt(1))){let e=2,t=e;for(;e<s&&!oe(o.charCodeAt(e));)e++;if(e<s&&e!==t){const n=o.slice(t,e);for(t=e;e<s&&oe(o.charCodeAt(e));)e++;if(e<s&&e!==t){for(t=e;e<s&&!oe(o.charCodeAt(e));)e++;e!==s&&e===t||(u=`\\\\${n}\\${o.slice(t,e)}`,a=e)}}}else a=1;else ae(c)&&58===o.charCodeAt(1)&&(u=o.slice(0,2),a=2,s>2&&oe(o.charCodeAt(2))&&(l=!0,a=3));if(u.length>0)if(t.length>0){if(u.toLowerCase()!==t.toLowerCase())continue}else t=u;if(r){if(t.length>0)break}else if(n=`${o.slice(a)}\\${n}`,r=l,l&&t.length>0)break}return n=ue(n,!r,"\\",oe),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){ie(e,"path");const t=e.length;if(0===t)return".";let n,r=0,i=!1;const o=e.charCodeAt(0);if(1===t)return se(o)?"\\":e;if(oe(o))if(i=!0,oe(e.charCodeAt(1))){let i=2,o=i;for(;i<t&&!oe(e.charCodeAt(i));)i++;if(i<t&&i!==o){const s=e.slice(o,i);for(o=i;i<t&&oe(e.charCodeAt(i));)i++;if(i<t&&i!==o){for(o=i;i<t&&!oe(e.charCodeAt(i));)i++;if(i===t)return`\\\\${s}\\${e.slice(o)}\\`;i!==o&&(n=`\\\\${s}\\${e.slice(o,i)}`,r=i)}}}else r=1;else ae(o)&&58===e.charCodeAt(1)&&(n=e.slice(0,2),r=2,t>2&&oe(e.charCodeAt(2))&&(i=!0,r=3));let s=r<t?ue(e.slice(r),!i,"\\",oe):"";return 0!==s.length||i||(s="."),s.length>0&&oe(e.charCodeAt(t-1))&&(s+="\\"),void 0===n?i?"\\"+s:s:i?`${n}\\${s}`:`${n}${s}`},isAbsolute(e){ie(e,"path");const t=e.length;if(0===t)return!1;const n=e.charCodeAt(0);return oe(n)||t>2&&ae(n)&&58===e.charCodeAt(1)&&oe(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let t,n;for(let r=0;r<e.length;++r){const i=e[r];ie(i,"path"),i.length>0&&(void 0===t?t=n=i:t+="\\"+i)}if(void 0===t)return".";let r=!0,i=0;if("string"==typeof n&&oe(n.charCodeAt(0))){++i;const e=n.length;e>1&&oe(n.charCodeAt(1))&&(++i,e>2&&(oe(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&oe(t.charCodeAt(i));)i++;i>=2&&(t="\\"+t.slice(i))}return ce.normalize(t)},relative(e,t){if(ie(e,"from"),ie(t,"to"),e===t)return"";const n=ce.resolve(e),r=ce.resolve(t);if(n===r)return"";if((e=n.toLowerCase())===(t=r.toLowerCase()))return"";let i=0;for(;i<e.length&&92===e.charCodeAt(i);)i++;let o=e.length;for(;o-1>i&&92===e.charCodeAt(o-1);)o--;const s=o-i;let a=0;for(;a<t.length&&92===t.charCodeAt(a);)a++;let u=t.length;for(;u-1>a&&92===t.charCodeAt(u-1);)u--;const l=u-a,c=s<l?s:l;let h=-1,f=0;for(;f<c;f++){const n=e.charCodeAt(i+f);if(n!==t.charCodeAt(a+f))break;92===n&&(h=f)}if(f!==c){if(-1===h)return r}else{if(l>c){if(92===t.charCodeAt(a+f))return r.slice(a+f+1);if(2===f)return r.slice(a+f)}s>c&&(92===e.charCodeAt(i+f)?h=f:2===f&&(h=3)),-1===h&&(h=0)}let d="";for(f=i+h+1;f<=o;++f)f!==o&&92!==e.charCodeAt(f)||(d+=0===d.length?"..":"\\..");return a+=h,d.length>0?`${d}${r.slice(a,u)}`:(92===r.charCodeAt(a)&&++a,r.slice(a,u))},toNamespacedPath(e){if("string"!=typeof e)return e;if(0===e.length)return"";const t=ce.resolve(e);if(t.length<=2)return e;if(92===t.charCodeAt(0)){if(92===t.charCodeAt(1)){const e=t.charCodeAt(2);if(63!==e&&46!==e)return"\\\\?\\UNC\\"+t.slice(2)}}else if(ae(t.charCodeAt(0))&&58===t.charCodeAt(1)&&92===t.charCodeAt(2))return"\\\\?\\"+t;return e},dirname(e){ie(e,"path");const t=e.length;if(0===t)return".";let n=-1,r=0;const i=e.charCodeAt(0);if(1===t)return oe(i)?e:".";if(oe(i)){if(n=r=1,oe(e.charCodeAt(1))){let i=2,o=i;for(;i<t&&!oe(e.charCodeAt(i));)i++;if(i<t&&i!==o){for(o=i;i<t&&oe(e.charCodeAt(i));)i++;if(i<t&&i!==o){for(o=i;i<t&&!oe(e.charCodeAt(i));)i++;if(i===t)return e;i!==o&&(n=r=i+1)}}}}else ae(i)&&58===e.charCodeAt(1)&&(n=t>2&&oe(e.charCodeAt(2))?3:2,r=n);let o=-1,s=!0;for(let n=t-1;n>=r;--n)if(oe(e.charCodeAt(n))){if(!s){o=n;break}}else s=!1;if(-1===o){if(-1===n)return".";o=n}return e.slice(0,o)},basename(e,t){void 0!==t&&ie(t,"ext"),ie(e,"path");let n,r=0,i=-1,o=!0;if(e.length>=2&&ae(e.charCodeAt(0))&&58===e.charCodeAt(1)&&(r=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let s=t.length-1,a=-1;for(n=e.length-1;n>=r;--n){const u=e.charCodeAt(n);if(oe(u)){if(!o){r=n+1;break}}else-1===a&&(o=!1,a=n+1),s>=0&&(u===t.charCodeAt(s)?-1==--s&&(i=n):(s=-1,i=a))}return r===i?i=a:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=r;--n)if(oe(e.charCodeAt(n))){if(!o){r=n+1;break}}else-1===i&&(o=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname(e){ie(e,"path");let t=0,n=-1,r=0,i=-1,o=!0,s=0;e.length>=2&&58===e.charCodeAt(1)&&ae(e.charCodeAt(0))&&(t=r=2);for(let a=e.length-1;a>=t;--a){const t=e.charCodeAt(a);if(oe(t)){if(!o){r=a+1;break}}else-1===i&&(o=!1,i=a+1),46===t?-1===n?n=a:1!==s&&(s=1):-1!==n&&(s=-1)}return-1===n||-1===i||0===s||1===s&&n===i-1&&n===r+1?"":e.slice(n,i)},format:le.bind(null,"\\"),parse(e){ie(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.length;let r=0,i=e.charCodeAt(0);if(1===n)return oe(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(oe(i)){if(r=1,oe(e.charCodeAt(1))){let t=2,i=t;for(;t<n&&!oe(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&oe(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&!oe(e.charCodeAt(t));)t++;t===n?r=t:t!==i&&(r=t+1)}}}}else if(ae(i)&&58===e.charCodeAt(1)){if(n<=2)return t.root=t.dir=e,t;if(r=2,oe(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let o=-1,s=r,a=-1,u=!0,l=e.length-1,c=0;for(;l>=r;--l)if(i=e.charCodeAt(l),oe(i)){if(!u){s=l+1;break}}else-1===a&&(u=!1,a=l+1),46===i?-1===o?o=l:1!==c&&(c=1):-1!==o&&(c=-1);return-1!==a&&(-1===o||0===c||1===c&&o===a-1&&o===s+1?t.base=t.name=e.slice(s,a):(t.name=e.slice(s,o),t.base=e.slice(s,a),t.ext=e.slice(o,a))),t.dir=s>0&&s!==r?e.slice(0,s-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},he={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){const i=r>=0?e[r]:ne.a();ie(i,"path"),0!==i.length&&(t=`${i}/${t}`,n=47===i.charCodeAt(0))}return t=ue(t,!n,"/",se),n?"/"+t:t.length>0?t:"."},normalize(e){if(ie(e,"path"),0===e.length)return".";const t=47===e.charCodeAt(0),n=47===e.charCodeAt(e.length-1);return 0===(e=ue(e,!t,"/",se)).length?t?"/":n?"./":".":(n&&(e+="/"),t?"/"+e:e)},isAbsolute:e=>(ie(e,"path"),e.length>0&&47===e.charCodeAt(0)),join(...e){if(0===e.length)return".";let t;for(let n=0;n<e.length;++n){const r=e[n];ie(r,"path"),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":he.normalize(t)},relative(e,t){if(ie(e,"from"),ie(t,"to"),e===t)return"";if((e=he.resolve(e))===(t=he.resolve(t)))return"";const n=e.length,r=n-1,i=t.length-1,o=r<i?r:i;let s=-1,a=0;for(;a<o;a++){const n=e.charCodeAt(1+a);if(n!==t.charCodeAt(1+a))break;47===n&&(s=a)}if(a===o)if(i>o){if(47===t.charCodeAt(1+a))return t.slice(1+a+1);if(0===a)return t.slice(1+a)}else r>o&&(47===e.charCodeAt(1+a)?s=a:0===a&&(s=0));let u="";for(a=1+s+1;a<=n;++a)a!==n&&47!==e.charCodeAt(a)||(u+=0===u.length?"..":"/..");return`${u}${t.slice(1+s)}`},toNamespacedPath:e=>e,dirname(e){if(ie(e,"path"),0===e.length)return".";const t=47===e.charCodeAt(0);let n=-1,r=!0;for(let t=e.length-1;t>=1;--t)if(47===e.charCodeAt(t)){if(!r){n=t;break}}else r=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename(e,t){void 0!==t&&ie(t,"ext"),ie(e,"path");let n,r=0,i=-1,o=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let s=t.length-1,a=-1;for(n=e.length-1;n>=0;--n){const u=e.charCodeAt(n);if(47===u){if(!o){r=n+1;break}}else-1===a&&(o=!1,a=n+1),s>=0&&(u===t.charCodeAt(s)?-1==--s&&(i=n):(s=-1,i=a))}return r===i?i=a:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!o){r=n+1;break}}else-1===i&&(o=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname(e){ie(e,"path");let t=-1,n=0,r=-1,i=!0,o=0;for(let s=e.length-1;s>=0;--s){const a=e.charCodeAt(s);if(47!==a)-1===r&&(i=!1,r=s+1),46===a?-1===t?t=s:1!==o&&(o=1):-1!==t&&(o=-1);else if(!i){n=s+1;break}}return-1===t||-1===r||0===o||1===o&&t===r-1&&t===n+1?"":e.slice(t,r)},format:le.bind(null,"/"),parse(e){ie(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=47===e.charCodeAt(0);let r;n?(t.root="/",r=1):r=0;let i=-1,o=0,s=-1,a=!0,u=e.length-1,l=0;for(;u>=r;--u){const t=e.charCodeAt(u);if(47!==t)-1===s&&(a=!1,s=u+1),46===t?-1===i?i=u:1!==l&&(l=1):-1!==i&&(l=-1);else if(!a){o=u+1;break}}if(-1!==s){const r=0===o&&n?1:o;-1===i||0===l||1===l&&i===s-1&&i===o+1?t.base=t.name=e.slice(r,s):(t.name=e.slice(r,i),t.base=e.slice(r,s),t.ext=e.slice(i,s))}return o>0?t.dir=e.slice(0,o-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};he.win32=ce.win32=ce,he.posix=ce.posix=he;"win32"===ne.c?ce.normalize:he.normalize,"win32"===ne.c?ce.resolve:he.resolve,"win32"===ne.c?ce.relative:he.relative,"win32"===ne.c?ce.dirname:he.dirname,"win32"===ne.c?ce.basename:he.basename,"win32"===ne.c?ce.extname:he.extname,"win32"===ne.c?ce.sep:he.sep;const fe=/^\w[\w\d+.-]*$/,de=/^\//,me=/^\/\//;function ge(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!fe.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!de.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(me.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const pe="/",ve=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class ye{constructor(e,t,n,r,i,o=!1){"object"==typeof e?(this.scheme=e.scheme||"",this.authority=e.authority||"",this.path=e.path||"",this.query=e.query||"",this.fragment=e.fragment||""):(this.scheme=function(e,t){return e||t?e:"file"}(e,o),this.authority=t||"",this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==pe&&(t=pe+t):t=pe}return t}(this.scheme,n||""),this.query=r||"",this.fragment=i||"",ge(this,o))}static isUri(e){return e instanceof ye||!!e&&("string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString)}get fsPath(){return Ee(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:o}=e;return void 0===t?t=this.scheme:null===t&&(t=""),void 0===n?n=this.authority:null===n&&(n=""),void 0===r?r=this.path:null===r&&(r=""),void 0===i?i=this.query:null===i&&(i=""),void 0===o?o=this.fragment:null===o&&(o=""),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new _e(t,n,r,i,o)}static parse(e,t=!1){const n=ve.exec(e);return n?new _e(n[2]||"",xe(n[4]||""),xe(n[5]||""),xe(n[7]||""),xe(n[9]||""),t):new _e("","","","","")}static file(e){let t="";if(C.d&&(e=e.replace(/\\/g,pe)),e[0]===pe&&e[1]===pe){const n=e.indexOf(pe,2);-1===n?(t=e.substring(2),e=pe):(t=e.substring(2,n),e=e.substring(n)||pe)}return new _e("file",t,e,"","")}static from(e){const t=new _e(e.scheme,e.authority,e.path,e.query,e.fragment);return ge(t,!0),t}static joinPath(e,...t){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return n=C.d&&"file"===e.scheme?ye.file(ce.join(Ee(e,!0),...t)).path:he.join(e.path,...t),e.with({path:n})}toString(e=!1){return Ne(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof ye)return e;{const t=new _e(e);return t._formatted=e.external,t._fsPath=e._sep===be?e.fsPath:null,t}}return e}}const be=C.d?1:void 0;class _e extends ye{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Ee(this,!1)),this._fsPath}toString(e=!1){return e?Ne(this,!0):(this._formatted||(this._formatted=Ne(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=be),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const Ce={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Se(e,t){let n=void 0,r=-1;for(let i=0;i<e.length;i++){const o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));const t=Ce[o];void 0!==t?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=t):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function Ae(e){let t=void 0;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=Ce[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function Ee(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,C.d&&(n=n.replace(/\//g,"\\")),n}function Ne(e,t){const n=t?Ae:Se;let r="",{scheme:i,authority:o,path:s,query:a,fragment:u}=e;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=pe,r+=pe),o){let e=o.indexOf("@");if(-1!==e){const t=o.substr(0,e);o=o.substr(e+1),e=t.indexOf(":"),-1===e?r+=n(t,!1):(r+=n(t.substr(0,e),!1),r+=":",r+=n(t.substr(e+1),!1)),r+="@"}o=o.toLowerCase(),e=o.indexOf(":"),-1===e?r+=n(o,!1):(r+=n(o.substr(0,e),!1),r+=o.substr(e))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){const e=s.charCodeAt(1);e>=65&&e<=90&&(s=`/${String.fromCharCode(e+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){const e=s.charCodeAt(0);e>=65&&e<=90&&(s=`${String.fromCharCode(e+32)}:${s.substr(2)}`)}r+=n(s,!0)}return a&&(r+="?",r+=n(a,!1)),u&&(r+="#",r+=t?u:Se(u,!1)),r}const we=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function xe(e){return e.match(we)?e.replace(we,e=>function e(t){try{return decodeURIComponent(t)}catch(n){return t.length>3?t.substr(0,3)+e(t.substr(3)):t}}(e)):e}class Le{constructor(e,t){this.lineNumber=e,this.column=t}with(e=this.lineNumber,t=this.column){return e===this.lineNumber&&t===this.column?this:new Le(e,t)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(e){return Le.equals(this,e)}static equals(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(e){return Le.isBefore(this,e)}static isBefore(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}isBeforeOrEqual(e){return Le.isBeforeOrEqual(this,e)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}static compare(e,t){let n=0|e.lineNumber,r=0|t.lineNumber;if(n===r){return(0|e.column)-(0|t.column)}return n-r}clone(){return new Le(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(e){return new Le(e.lineNumber,e.column)}static isIPosition(e){return e&&"number"==typeof e.lineNumber&&"number"==typeof e.column}}class Te{constructor(e,t,n,r){e>n||e===n&&t>r?(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=r)}isEmpty(){return Te.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(e){return Te.containsPosition(this,e)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn))}containsRange(e){return Te.containsRange(this,e)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)))}strictContainsRange(e){return Te.strictContainsRange(this,e)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)))}plusRange(e){return Te.plusRange(this,e)}static plusRange(e,t){let n,r,i,o;return t.startLineNumber<e.startLineNumber?(n=t.startLineNumber,r=t.startColumn):t.startLineNumber===e.startLineNumber?(n=t.startLineNumber,r=Math.min(t.startColumn,e.startColumn)):(n=e.startLineNumber,r=e.startColumn),t.endLineNumber>e.endLineNumber?(i=t.endLineNumber,o=t.endColumn):t.endLineNumber===e.endLineNumber?(i=t.endLineNumber,o=Math.max(t.endColumn,e.endColumn)):(i=e.endLineNumber,o=e.endColumn),new Te(n,r,i,o)}intersectRanges(e){return Te.intersectRanges(this,e)}static intersectRanges(e,t){let n=e.startLineNumber,r=e.startColumn,i=e.endLineNumber,o=e.endColumn,s=t.startLineNumber,a=t.startColumn,u=t.endLineNumber,l=t.endColumn;return n<s?(n=s,r=a):n===s&&(r=Math.max(r,a)),i>u?(i=u,o=l):i===u&&(o=Math.min(o,l)),n>i||n===i&&r>o?null:new Te(n,r,i,o)}equalsRange(e){return Te.equalsRange(this,e)}static equalsRange(e,t){return!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return Te.getEndPosition(this)}static getEndPosition(e){return new Le(e.endLineNumber,e.endColumn)}getStartPosition(){return Te.getStartPosition(this)}static getStartPosition(e){return new Le(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(e,t){return new Te(this.startLineNumber,this.startColumn,e,t)}setStartPosition(e,t){return new Te(e,t,this.endLineNumber,this.endColumn)}collapseToStart(){return Te.collapseToStart(this)}static collapseToStart(e){return new Te(e.startLineNumber,e.startColumn,e.startLineNumber,e.startColumn)}static fromPositions(e,t=e){return new Te(e.lineNumber,e.column,t.lineNumber,t.column)}static lift(e){return e?new Te(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):null}static isIRange(e){return e&&"number"==typeof e.startLineNumber&&"number"==typeof e.startColumn&&"number"==typeof e.endLineNumber&&"number"==typeof e.endColumn}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){const n=0|e.startLineNumber,r=0|t.startLineNumber;if(n===r){const n=0|e.startColumn,r=0|t.startColumn;if(n===r){const n=0|e.endLineNumber,r=0|t.endLineNumber;if(n===r){return(0|e.endColumn)-(0|t.endColumn)}return n-r}return n-r}return n-r}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}}function ke(e,t,n,r){return new te(e,t,n).ComputeDiff(r)}class Oe{constructor(e){const t=[],n=[];for(let r=0,i=e.length;r<i;r++)t[r]=Re(e[r],1),n[r]=Fe(e[r],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const r=[],i=[],o=[];let s=0;for(let a=t;a<=n;a++){const t=this.lines[a],n=e?this._startColumns[a]:1,u=e?this._endColumns[a]:t.length+1;for(let e=n;e<u;e++)r[s]=t.charCodeAt(e-1),i[s]=a+1,o[s]=e,s++}return new Ie(r,i,o)}}class Ie{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}getElements(){return this._charCodes}getStartLineNumber(e){return this._lineNumbers[e]}getStartColumn(e){return this._columns[e]}getEndLineNumber(e){return this._lineNumbers[e]}getEndColumn(e){return this._columns[e]+1}}class Me{constructor(e,t,n,r,i,o,s,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=r,this.modifiedStartLineNumber=i,this.modifiedStartColumn=o,this.modifiedEndLineNumber=s,this.modifiedEndColumn=a}static createFromDiffChange(e,t,n){let r,i,o,s,a,u,l,c;return 0===e.originalLength?(r=0,i=0,o=0,s=0):(r=t.getStartLineNumber(e.originalStart),i=t.getStartColumn(e.originalStart),o=t.getEndLineNumber(e.originalStart+e.originalLength-1),s=t.getEndColumn(e.originalStart+e.originalLength-1)),0===e.modifiedLength?(a=0,u=0,l=0,c=0):(a=n.getStartLineNumber(e.modifiedStart),u=n.getStartColumn(e.modifiedStart),l=n.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),c=n.getEndColumn(e.modifiedStart+e.modifiedLength-1)),new Me(r,i,o,s,a,u,l,c)}}class Pe{constructor(e,t,n,r,i){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=r,this.charChanges=i}static createFromDiffResult(e,t,n,r,i,o,s){let a,u,l,c,h=void 0;if(0===t.originalLength?(a=n.getStartLineNumber(t.originalStart)-1,u=0):(a=n.getStartLineNumber(t.originalStart),u=n.getEndLineNumber(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(l=r.getStartLineNumber(t.modifiedStart)-1,c=0):(l=r.getStartLineNumber(t.modifiedStart),c=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1)),o&&t.originalLength>0&&t.originalLength<20&&t.modifiedLength>0&&t.modifiedLength<20&&i()){const o=n.createCharSequence(e,t.originalStart,t.originalStart+t.originalLength-1),a=r.createCharSequence(e,t.modifiedStart,t.modifiedStart+t.modifiedLength-1);let u=ke(o,a,i,!0).changes;s&&(u=function(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,i=e.length;r<i;r++){const i=e[r],o=i.originalStart-(n.originalStart+n.originalLength),s=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(o,s)<3?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}(u)),h=[];for(let e=0,t=u.length;e<t;e++)h.push(Me.createFromDiffChange(u[e],o,a))}return new Pe(a,u,l,c,h)}}class Ve{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Oe(e),this.modified=new Oe(t),this.continueLineDiff=De(n.maxComputationTime),this.continueCharDiff=De(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};const e=ke(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const e=[];for(let n=0,r=t.length;n<r;n++)e.push(Pe.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}const r=[];let i=0,o=0;for(let e=-1,n=t.length;e<n;e++){const s=e+1<n?t[e+1]:null,a=s?s.originalStart:this.originalLines.length,u=s?s.modifiedStart:this.modifiedLines.length;for(;i<a&&o<u;){const e=this.originalLines[i],t=this.modifiedLines[o];if(e!==t){{let n=Re(e,1),s=Re(t,1);for(;n>1&&s>1;){if(e.charCodeAt(n-2)!==t.charCodeAt(s-2))break;n--,s--}(n>1||s>1)&&this._pushTrimWhitespaceCharChange(r,i+1,1,n,o+1,1,s)}{let n=Fe(e,1),s=Fe(t,1);const a=e.length+1,u=t.length+1;for(;n<a&&s<u;){if(e.charCodeAt(n-1)!==e.charCodeAt(s-1))break;n++,s++}(n<a||s<u)&&this._pushTrimWhitespaceCharChange(r,i+1,n,a,o+1,s,u)}}i++,o++}s&&(r.push(Pe.createFromDiffResult(this.shouldIgnoreTrimWhitespace,s,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),i+=s.originalLength,o+=s.modifiedLength)}return{quitEarly:n,changes:r}}_pushTrimWhitespaceCharChange(e,t,n,r,i,o,s){if(this._mergeTrimWhitespaceCharChange(e,t,n,r,i,o,s))return;let a=void 0;this.shouldComputeCharChanges&&(a=[new Me(t,n,t,r,i,o,i,s)]),e.push(new Pe(t,t,i,i,a))}_mergeTrimWhitespaceCharChange(e,t,n,r,i,o,s){const a=e.length;if(0===a)return!1;const u=e[a-1];return 0!==u.originalEndLineNumber&&0!==u.modifiedEndLineNumber&&(u.originalEndLineNumber+1===t&&u.modifiedEndLineNumber+1===i&&(u.originalEndLineNumber=t,u.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new Me(t,n,t,r,i,o,i,s)),!0))}}function Re(e,t){const n=function(e){for(let t=0,n=e.length;t<n;t++){const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1}(e);return-1===n?t:n+1}function Fe(e,t){const n=function(e,t=e.length-1){for(let n=t;n>=0;n--){const t=e.charCodeAt(n);if(32!==t&&9!==t)return n}return-1}(e);return-1===n?t:n+2}function De(e){if(0===e)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}function Ke(e){return e<0?0:e>255?255:0|e}function je(e){return e<0?0:e>4294967295?4294967295:0|e}class Ue{constructor(e,t){this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}}class Be{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=je(e);const n=this.values,r=this.prefixSum,i=t.length;return 0!==i&&(this.values=new Uint32Array(n.length+i),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+i),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}changeValue(e,t){return e=je(e),t=je(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=je(e),t=je(t);const n=this.values,r=this.prefixSum;if(e>=n.length)return!1;let i=n.length-e;return t>=i&&(t=i),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=je(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,r=0,i=0,o=0;for(;t<=n;)if(r=t+(n-t)/2|0,i=this.prefixSum[r],o=i-this.values[r],e<o)n=r-1;else{if(!(e>=i))break;t=r+1}return new Ue(r,e-o)}}const qe=function(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of"`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?")e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}();const $e={maxLen:1e3,windowSize:15,timeBudget:150};function We(e,t,n,r){let i;for(;i=e.exec(t);){const t=i.index||0;if(t<=n&&e.lastIndex>=n)return i;if(r>0&&t>r)return null}return null}class He{constructor(e){let t=Ke(e);this._defaultValue=t,this._asciiMap=He._createAsciiMap(t),this._map=new Map}static _createAsciiMap(e){let t=new Uint8Array(256);for(let n=0;n<256;n++)t[n]=e;return t}set(e,t){let n=Ke(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}}class ze{constructor(e,t,n){const r=new Uint8Array(e*t);for(let i=0,o=e*t;i<o;i++)r[i]=n;this._data=r,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}}class Ge{constructor(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++){let[i,o,s]=e[r];o>t&&(t=o),i>n&&(n=i),s>n&&(n=s)}t++,n++;let r=new ze(n,t,0);for(let t=0,n=e.length;t<n;t++){let[n,i,o]=e[t];r.set(n,i,o)}this._states=r,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}let Je=null;let Xe=null;class Ze{static _createLink(e,t,n,r,i){let o=i-1;do{const n=t.charCodeAt(o);if(2!==e.get(n))break;o--}while(o>r);if(r>0){const e=t.charCodeAt(r-1),n=t.charCodeAt(o);(40===e&&41===n||91===e&&93===n||123===e&&125===n)&&o--}return{range:{startLineNumber:n,startColumn:r+1,endLineNumber:n,endColumn:o+2},url:t.substring(r,o+1)}}static computeLinks(e,t=function(){return null===Je&&(Je=new Ge([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Je}()){const n=function(){if(null===Xe){Xe=new He(0);const e=" \t<>'\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…";for(let t=0;t<e.length;t++)Xe.set(e.charCodeAt(t),1);const t=".,;";for(let e=0;e<t.length;e++)Xe.set(t.charCodeAt(e),2)}return Xe}();let r=[];for(let i=1,o=e.getLineCount();i<=o;i++){const o=e.getLineContent(i),s=o.length;let a=0,u=0,l=0,c=1,h=!1,f=!1,d=!1,m=!1;for(;a<s;){let e=!1;const s=o.charCodeAt(a);if(13===c){let t;switch(s){case 40:h=!0,t=0;break;case 41:t=h?0:1;break;case 91:d=!0,f=!0,t=0;break;case 93:d=!1,t=f?0:1;break;case 123:m=!0,t=0;break;case 125:t=m?0:1;break;case 39:t=34===l||96===l?0:1;break;case 34:t=39===l||96===l?0:1;break;case 96:t=39===l||34===l?0:1;break;case 42:t=42===l?1:0;break;case 124:t=124===l?1:0;break;case 32:t=d?0:1;break;default:t=n.get(s)}1===t&&(r.push(Ze._createLink(n,o,i,u,a)),e=!0)}else if(12===c){let t;91===s?(f=!0,t=0):t=n.get(s),1===t?e=!0:c=13}else c=t.nextState(c,s),0===c&&(e=!0);e&&(c=1,h=!1,f=!1,m=!1,u=a+1,l=s),a++}13===c&&r.push(Ze._createLink(n,o,i,u,s))}return r}}class Ye{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,t,n,r,i){if(e&&t){let n=this.doNavigateValueSet(t,i);if(n)return{range:e,value:n}}if(n&&r){let e=this.doNavigateValueSet(r,i);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){let n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}numberReplace(e,t){let n=Math.pow(10,e.length-(e.lastIndexOf(".")+1)),r=Number(e),i=parseFloat(e);return isNaN(r)||isNaN(i)||r!==i?null:0!==r||t?(r=Math.floor(r*n),r+=t?n:-n,String(r/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let r=null;for(let i=0,o=e.length;null===r&&i<o;i++)r=this.valueSetReplace(e[i],t,n);return r}valueSetReplace(e,t,n){let r=e.indexOf(t);return r>=0?(r+=n?1:-1,r<0?r=e.length-1:r%=e.length,e[r]):null}}Ye.INSTANCE=new Ye;const Qe=Object.freeze((function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));var et,tt;(tt=et||(et={})).isCancellationToken=function(e){return e===tt.None||e===tt.Cancelled||e instanceof nt||!(!e||"object"!=typeof e)&&"boolean"==typeof e.isCancellationRequested&&"function"==typeof e.onCancellationRequested},tt.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:E.None}),tt.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Qe});class nt{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Qe:(this._emitter||(this._emitter=new w),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class rt{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new nt),this._token}cancel(){this._token?this._token instanceof nt&&this._token.cancel():this._token=et.Cancelled}dispose(e=!1){e&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof nt&&this._token.dispose():this._token=et.None}}class it{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const ot=new it,st=new it,at=new it,ut=new Array(230),lt={},ct=[],ht=Object.create(null),ft=Object.create(null),dt=[],mt=[];for(let e=0;e<=193;e++)dt[e]=-1;for(let e=0;e<=126;e++)mt[e]=-1;var gt,pt,vt,yt,bt,_t,Ct,St,At,Et,Nt,wt,xt,Lt,Tt,kt,Ot,It,Mt,Pt,Vt,Rt,Ft,Dt,Kt,jt,Ut,Bt,qt,$t,Wt,Ht,zt,Gt,Jt,Xt;!function(){const e=[[0,1,0,"None",0,"unknown",0,"VK_UNKNOWN","",""],[0,1,1,"Hyper",0,"",0,"","",""],[0,1,2,"Super",0,"",0,"","",""],[0,1,3,"Fn",0,"",0,"","",""],[0,1,4,"FnLock",0,"",0,"","",""],[0,1,5,"Suspend",0,"",0,"","",""],[0,1,6,"Resume",0,"",0,"","",""],[0,1,7,"Turbo",0,"",0,"","",""],[0,1,8,"Sleep",0,"",0,"VK_SLEEP","",""],[0,1,9,"WakeUp",0,"",0,"","",""],[31,0,10,"KeyA",31,"A",65,"VK_A","",""],[32,0,11,"KeyB",32,"B",66,"VK_B","",""],[33,0,12,"KeyC",33,"C",67,"VK_C","",""],[34,0,13,"KeyD",34,"D",68,"VK_D","",""],[35,0,14,"KeyE",35,"E",69,"VK_E","",""],[36,0,15,"KeyF",36,"F",70,"VK_F","",""],[37,0,16,"KeyG",37,"G",71,"VK_G","",""],[38,0,17,"KeyH",38,"H",72,"VK_H","",""],[39,0,18,"KeyI",39,"I",73,"VK_I","",""],[40,0,19,"KeyJ",40,"J",74,"VK_J","",""],[41,0,20,"KeyK",41,"K",75,"VK_K","",""],[42,0,21,"KeyL",42,"L",76,"VK_L","",""],[43,0,22,"KeyM",43,"M",77,"VK_M","",""],[44,0,23,"KeyN",44,"N",78,"VK_N","",""],[45,0,24,"KeyO",45,"O",79,"VK_O","",""],[46,0,25,"KeyP",46,"P",80,"VK_P","",""],[47,0,26,"KeyQ",47,"Q",81,"VK_Q","",""],[48,0,27,"KeyR",48,"R",82,"VK_R","",""],[49,0,28,"KeyS",49,"S",83,"VK_S","",""],[50,0,29,"KeyT",50,"T",84,"VK_T","",""],[51,0,30,"KeyU",51,"U",85,"VK_U","",""],[52,0,31,"KeyV",52,"V",86,"VK_V","",""],[53,0,32,"KeyW",53,"W",87,"VK_W","",""],[54,0,33,"KeyX",54,"X",88,"VK_X","",""],[55,0,34,"KeyY",55,"Y",89,"VK_Y","",""],[56,0,35,"KeyZ",56,"Z",90,"VK_Z","",""],[22,0,36,"Digit1",22,"1",49,"VK_1","",""],[23,0,37,"Digit2",23,"2",50,"VK_2","",""],[24,0,38,"Digit3",24,"3",51,"VK_3","",""],[25,0,39,"Digit4",25,"4",52,"VK_4","",""],[26,0,40,"Digit5",26,"5",53,"VK_5","",""],[27,0,41,"Digit6",27,"6",54,"VK_6","",""],[28,0,42,"Digit7",28,"7",55,"VK_7","",""],[29,0,43,"Digit8",29,"8",56,"VK_8","",""],[30,0,44,"Digit9",30,"9",57,"VK_9","",""],[21,0,45,"Digit0",21,"0",48,"VK_0","",""],[3,1,46,"Enter",3,"Enter",13,"VK_RETURN","",""],[9,1,47,"Escape",9,"Escape",27,"VK_ESCAPE","",""],[1,1,48,"Backspace",1,"Backspace",8,"VK_BACK","",""],[2,1,49,"Tab",2,"Tab",9,"VK_TAB","",""],[10,1,50,"Space",10,"Space",32,"VK_SPACE","",""],[83,0,51,"Minus",83,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[81,0,52,"Equal",81,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[87,0,53,"BracketLeft",87,"[",219,"VK_OEM_4","[","OEM_4"],[89,0,54,"BracketRight",89,"]",221,"VK_OEM_6","]","OEM_6"],[88,0,55,"Backslash",88,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,0,56,"IntlHash",0,"",0,"","",""],[80,0,57,"Semicolon",80,";",186,"VK_OEM_1",";","OEM_1"],[90,0,58,"Quote",90,"'",222,"VK_OEM_7","'","OEM_7"],[86,0,59,"Backquote",86,"`",192,"VK_OEM_3","`","OEM_3"],[82,0,60,"Comma",82,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[84,0,61,"Period",84,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[85,0,62,"Slash",85,"/",191,"VK_OEM_2","/","OEM_2"],[8,1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL","",""],[59,1,64,"F1",59,"F1",112,"VK_F1","",""],[60,1,65,"F2",60,"F2",113,"VK_F2","",""],[61,1,66,"F3",61,"F3",114,"VK_F3","",""],[62,1,67,"F4",62,"F4",115,"VK_F4","",""],[63,1,68,"F5",63,"F5",116,"VK_F5","",""],[64,1,69,"F6",64,"F6",117,"VK_F6","",""],[65,1,70,"F7",65,"F7",118,"VK_F7","",""],[66,1,71,"F8",66,"F8",119,"VK_F8","",""],[67,1,72,"F9",67,"F9",120,"VK_F9","",""],[68,1,73,"F10",68,"F10",121,"VK_F10","",""],[69,1,74,"F11",69,"F11",122,"VK_F11","",""],[70,1,75,"F12",70,"F12",123,"VK_F12","",""],[0,1,76,"PrintScreen",0,"",0,"","",""],[79,1,77,"ScrollLock",79,"ScrollLock",145,"VK_SCROLL","",""],[7,1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE","",""],[19,1,79,"Insert",19,"Insert",45,"VK_INSERT","",""],[14,1,80,"Home",14,"Home",36,"VK_HOME","",""],[11,1,81,"PageUp",11,"PageUp",33,"VK_PRIOR","",""],[20,1,82,"Delete",20,"Delete",46,"VK_DELETE","",""],[13,1,83,"End",13,"End",35,"VK_END","",""],[12,1,84,"PageDown",12,"PageDown",34,"VK_NEXT","",""],[17,1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",""],[15,1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",""],[18,1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",""],[16,1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",""],[78,1,89,"NumLock",78,"NumLock",144,"VK_NUMLOCK","",""],[108,1,90,"NumpadDivide",108,"NumPad_Divide",111,"VK_DIVIDE","",""],[103,1,91,"NumpadMultiply",103,"NumPad_Multiply",106,"VK_MULTIPLY","",""],[106,1,92,"NumpadSubtract",106,"NumPad_Subtract",109,"VK_SUBTRACT","",""],[104,1,93,"NumpadAdd",104,"NumPad_Add",107,"VK_ADD","",""],[3,1,94,"NumpadEnter",3,"",0,"","",""],[94,1,95,"Numpad1",94,"NumPad1",97,"VK_NUMPAD1","",""],[95,1,96,"Numpad2",95,"NumPad2",98,"VK_NUMPAD2","",""],[96,1,97,"Numpad3",96,"NumPad3",99,"VK_NUMPAD3","",""],[97,1,98,"Numpad4",97,"NumPad4",100,"VK_NUMPAD4","",""],[98,1,99,"Numpad5",98,"NumPad5",101,"VK_NUMPAD5","",""],[99,1,100,"Numpad6",99,"NumPad6",102,"VK_NUMPAD6","",""],[100,1,101,"Numpad7",100,"NumPad7",103,"VK_NUMPAD7","",""],[101,1,102,"Numpad8",101,"NumPad8",104,"VK_NUMPAD8","",""],[102,1,103,"Numpad9",102,"NumPad9",105,"VK_NUMPAD9","",""],[93,1,104,"Numpad0",93,"NumPad0",96,"VK_NUMPAD0","",""],[107,1,105,"NumpadDecimal",107,"NumPad_Decimal",110,"VK_DECIMAL","",""],[92,0,106,"IntlBackslash",92,"OEM_102",226,"VK_OEM_102","",""],[58,1,107,"ContextMenu",58,"ContextMenu",93,"","",""],[0,1,108,"Power",0,"",0,"","",""],[0,1,109,"NumpadEqual",0,"",0,"","",""],[71,1,110,"F13",71,"F13",124,"VK_F13","",""],[72,1,111,"F14",72,"F14",125,"VK_F14","",""],[73,1,112,"F15",73,"F15",126,"VK_F15","",""],[74,1,113,"F16",74,"F16",127,"VK_F16","",""],[75,1,114,"F17",75,"F17",128,"VK_F17","",""],[76,1,115,"F18",76,"F18",129,"VK_F18","",""],[77,1,116,"F19",77,"F19",130,"VK_F19","",""],[0,1,117,"F20",0,"",0,"VK_F20","",""],[0,1,118,"F21",0,"",0,"VK_F21","",""],[0,1,119,"F22",0,"",0,"VK_F22","",""],[0,1,120,"F23",0,"",0,"VK_F23","",""],[0,1,121,"F24",0,"",0,"VK_F24","",""],[0,1,122,"Open",0,"",0,"","",""],[0,1,123,"Help",0,"",0,"","",""],[0,1,124,"Select",0,"",0,"","",""],[0,1,125,"Again",0,"",0,"","",""],[0,1,126,"Undo",0,"",0,"","",""],[0,1,127,"Cut",0,"",0,"","",""],[0,1,128,"Copy",0,"",0,"","",""],[0,1,129,"Paste",0,"",0,"","",""],[0,1,130,"Find",0,"",0,"","",""],[0,1,131,"AudioVolumeMute",112,"AudioVolumeMute",173,"VK_VOLUME_MUTE","",""],[0,1,132,"AudioVolumeUp",113,"AudioVolumeUp",175,"VK_VOLUME_UP","",""],[0,1,133,"AudioVolumeDown",114,"AudioVolumeDown",174,"VK_VOLUME_DOWN","",""],[105,1,134,"NumpadComma",105,"NumPad_Separator",108,"VK_SEPARATOR","",""],[110,0,135,"IntlRo",110,"ABNT_C1",193,"VK_ABNT_C1","",""],[0,1,136,"KanaMode",0,"",0,"","",""],[0,0,137,"IntlYen",0,"",0,"","",""],[0,1,138,"Convert",0,"",0,"","",""],[0,1,139,"NonConvert",0,"",0,"","",""],[0,1,140,"Lang1",0,"",0,"","",""],[0,1,141,"Lang2",0,"",0,"","",""],[0,1,142,"Lang3",0,"",0,"","",""],[0,1,143,"Lang4",0,"",0,"","",""],[0,1,144,"Lang5",0,"",0,"","",""],[0,1,145,"Abort",0,"",0,"","",""],[0,1,146,"Props",0,"",0,"","",""],[0,1,147,"NumpadParenLeft",0,"",0,"","",""],[0,1,148,"NumpadParenRight",0,"",0,"","",""],[0,1,149,"NumpadBackspace",0,"",0,"","",""],[0,1,150,"NumpadMemoryStore",0,"",0,"","",""],[0,1,151,"NumpadMemoryRecall",0,"",0,"","",""],[0,1,152,"NumpadMemoryClear",0,"",0,"","",""],[0,1,153,"NumpadMemoryAdd",0,"",0,"","",""],[0,1,154,"NumpadMemorySubtract",0,"",0,"","",""],[0,1,155,"NumpadClear",0,"",0,"","",""],[0,1,156,"NumpadClearEntry",0,"",0,"","",""],[5,1,0,"",5,"Ctrl",17,"VK_CONTROL","",""],[4,1,0,"",4,"Shift",16,"VK_SHIFT","",""],[6,1,0,"",6,"Alt",18,"VK_MENU","",""],[57,1,0,"",57,"Meta",0,"VK_COMMAND","",""],[5,1,157,"ControlLeft",5,"",0,"VK_LCONTROL","",""],[4,1,158,"ShiftLeft",4,"",0,"VK_LSHIFT","",""],[6,1,159,"AltLeft",6,"",0,"VK_LMENU","",""],[57,1,160,"MetaLeft",57,"",0,"VK_LWIN","",""],[5,1,161,"ControlRight",5,"",0,"VK_RCONTROL","",""],[4,1,162,"ShiftRight",4,"",0,"VK_RSHIFT","",""],[6,1,163,"AltRight",6,"",0,"VK_RMENU","",""],[57,1,164,"MetaRight",57,"",0,"VK_RWIN","",""],[0,1,165,"BrightnessUp",0,"",0,"","",""],[0,1,166,"BrightnessDown",0,"",0,"","",""],[0,1,167,"MediaPlay",0,"",0,"","",""],[0,1,168,"MediaRecord",0,"",0,"","",""],[0,1,169,"MediaFastForward",0,"",0,"","",""],[0,1,170,"MediaRewind",0,"",0,"","",""],[114,1,171,"MediaTrackNext",119,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK","",""],[115,1,172,"MediaTrackPrevious",120,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK","",""],[116,1,173,"MediaStop",121,"MediaStop",178,"VK_MEDIA_STOP","",""],[0,1,174,"Eject",0,"",0,"","",""],[117,1,175,"MediaPlayPause",122,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE","",""],[0,1,176,"MediaSelect",123,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT","",""],[0,1,177,"LaunchMail",124,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL","",""],[0,1,178,"LaunchApp2",125,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2","",""],[0,1,179,"LaunchApp1",0,"",0,"VK_MEDIA_LAUNCH_APP1","",""],[0,1,180,"SelectTask",0,"",0,"","",""],[0,1,181,"LaunchScreenSaver",0,"",0,"","",""],[0,1,182,"BrowserSearch",115,"BrowserSearch",170,"VK_BROWSER_SEARCH","",""],[0,1,183,"BrowserHome",116,"BrowserHome",172,"VK_BROWSER_HOME","",""],[112,1,184,"BrowserBack",117,"BrowserBack",166,"VK_BROWSER_BACK","",""],[113,1,185,"BrowserForward",118,"BrowserForward",167,"VK_BROWSER_FORWARD","",""],[0,1,186,"BrowserStop",0,"",0,"VK_BROWSER_STOP","",""],[0,1,187,"BrowserRefresh",0,"",0,"VK_BROWSER_REFRESH","",""],[0,1,188,"BrowserFavorites",0,"",0,"VK_BROWSER_FAVORITES","",""],[0,1,189,"ZoomToggle",0,"",0,"","",""],[0,1,190,"MailReply",0,"",0,"","",""],[0,1,191,"MailForward",0,"",0,"","",""],[0,1,192,"MailSend",0,"",0,"","",""],[109,1,0,"",109,"KeyInComposition",229,"","",""],[111,1,0,"",111,"ABNT_C2",194,"VK_ABNT_C2","",""],[91,1,0,"",91,"OEM_8",223,"VK_OEM_8","",""],[0,1,0,"",0,"",0,"VK_CLEAR","",""],[0,1,0,"",0,"",0,"VK_KANA","",""],[0,1,0,"",0,"",0,"VK_HANGUL","",""],[0,1,0,"",0,"",0,"VK_JUNJA","",""],[0,1,0,"",0,"",0,"VK_FINAL","",""],[0,1,0,"",0,"",0,"VK_HANJA","",""],[0,1,0,"",0,"",0,"VK_KANJI","",""],[0,1,0,"",0,"",0,"VK_CONVERT","",""],[0,1,0,"",0,"",0,"VK_NONCONVERT","",""],[0,1,0,"",0,"",0,"VK_ACCEPT","",""],[0,1,0,"",0,"",0,"VK_MODECHANGE","",""],[0,1,0,"",0,"",0,"VK_SELECT","",""],[0,1,0,"",0,"",0,"VK_PRINT","",""],[0,1,0,"",0,"",0,"VK_EXECUTE","",""],[0,1,0,"",0,"",0,"VK_SNAPSHOT","",""],[0,1,0,"",0,"",0,"VK_HELP","",""],[0,1,0,"",0,"",0,"VK_APPS","",""],[0,1,0,"",0,"",0,"VK_PROCESSKEY","",""],[0,1,0,"",0,"",0,"VK_PACKET","",""],[0,1,0,"",0,"",0,"VK_DBE_SBCSCHAR","",""],[0,1,0,"",0,"",0,"VK_DBE_DBCSCHAR","",""],[0,1,0,"",0,"",0,"VK_ATTN","",""],[0,1,0,"",0,"",0,"VK_CRSEL","",""],[0,1,0,"",0,"",0,"VK_EXSEL","",""],[0,1,0,"",0,"",0,"VK_EREOF","",""],[0,1,0,"",0,"",0,"VK_PLAY","",""],[0,1,0,"",0,"",0,"VK_ZOOM","",""],[0,1,0,"",0,"",0,"VK_NONAME","",""],[0,1,0,"",0,"",0,"VK_PA1","",""],[0,1,0,"",0,"",0,"VK_OEM_CLEAR","",""]];let t=[],n=[];for(const r of e){const[e,i,o,s,a,u,l,c,h,f]=r;if(n[o]||(n[o]=!0,ct[o]=s,ht[s]=o,ft[s.toLowerCase()]=o,i&&(dt[o]=a,0!==a&&3!==a&&5!==a&&4!==a&&6!==a&&57!==a&&(mt[a]=o))),!t[a]){if(t[a]=!0,!u)throw new Error(`String representation missing for key code ${a} around scan code ${s}`);ot.define(a,u),st.define(a,h||u),at.define(a,f||h||u)}l&&(ut[l]=a),c&&(lt[c]=a)}mt[3]=46}(),function(e){e.toString=function(e){return ot.keyCodeToStr(e)},e.fromString=function(e){return ot.strToKeyCode(e)},e.toUserSettingsUS=function(e){return st.keyCodeToStr(e)},e.toUserSettingsGeneral=function(e){return at.keyCodeToStr(e)},e.fromUserSettings=function(e){return st.strToKeyCode(e)||at.strToKeyCode(e)},e.toElectronAccelerator=function(e){if(e>=93&&e<=108)return null;switch(e){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return ot.keyCodeToStr(e)}}(gt||(gt={}));class Zt extends Te{constructor(e,t,n,r){super(e,t,n,r),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=r}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return Zt.selectionsEqual(this,e)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,t){return 0===this.getDirection()?new Zt(this.startLineNumber,this.startColumn,e,t):new Zt(e,t,this.startLineNumber,this.startColumn)}getPosition(){return new Le(this.positionLineNumber,this.positionColumn)}setStartPosition(e,t){return 0===this.getDirection()?new Zt(e,t,this.endLineNumber,this.endColumn):new Zt(this.endLineNumber,this.endColumn,e,t)}static fromPositions(e,t=e){return new Zt(e.lineNumber,e.column,t.lineNumber,t.column)}static liftSelection(e){return new Zt(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&"number"==typeof e.selectionStartLineNumber&&"number"==typeof e.selectionStartColumn&&"number"==typeof e.positionLineNumber&&"number"==typeof e.positionColumn}static createWithDirection(e,t,n,r,i){return 0===i?new Zt(e,t,n,r):new Zt(n,r,e,t)}}class Yt{constructor(e,t,n){this._tokenBrand=void 0,this.offset=0|e,this.type=t,this.language=n}toString(){return"("+this.offset+", "+this.type+")"}}!function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"}(pt||(pt={})),function(e){e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"}(vt||(vt={})),function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"}(yt||(yt={})),function(e){e[e.Deprecated=1]="Deprecated"}(bt||(bt={})),function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"}(_t||(_t={})),function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"}(Ct||(Ct={})),function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"}(St||(St={})),function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(At||(At={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(Et||(Et={})),function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"}(Nt||(Nt={})),function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.guides=13]="guides",e[e.codeLens=14]="codeLens",e[e.codeLensFontFamily=15]="codeLensFontFamily",e[e.codeLensFontSize=16]="codeLensFontSize",e[e.colorDecorators=17]="colorDecorators",e[e.columnSelection=18]="columnSelection",e[e.comments=19]="comments",e[e.contextmenu=20]="contextmenu",e[e.copyWithSyntaxHighlighting=21]="copyWithSyntaxHighlighting",e[e.cursorBlinking=22]="cursorBlinking",e[e.cursorSmoothCaretAnimation=23]="cursorSmoothCaretAnimation",e[e.cursorStyle=24]="cursorStyle",e[e.cursorSurroundingLines=25]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=26]="cursorSurroundingLinesStyle",e[e.cursorWidth=27]="cursorWidth",e[e.disableLayerHinting=28]="disableLayerHinting",e[e.disableMonospaceOptimizations=29]="disableMonospaceOptimizations",e[e.domReadOnly=30]="domReadOnly",e[e.dragAndDrop=31]="dragAndDrop",e[e.emptySelectionClipboard=32]="emptySelectionClipboard",e[e.extraEditorClassName=33]="extraEditorClassName",e[e.fastScrollSensitivity=34]="fastScrollSensitivity",e[e.find=35]="find",e[e.fixedOverflowWidgets=36]="fixedOverflowWidgets",e[e.folding=37]="folding",e[e.foldingStrategy=38]="foldingStrategy",e[e.foldingHighlight=39]="foldingHighlight",e[e.foldingImportsByDefault=40]="foldingImportsByDefault",e[e.unfoldOnClickAfterEndOfLine=41]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=42]="fontFamily",e[e.fontInfo=43]="fontInfo",e[e.fontLigatures=44]="fontLigatures",e[e.fontSize=45]="fontSize",e[e.fontWeight=46]="fontWeight",e[e.formatOnPaste=47]="formatOnPaste",e[e.formatOnType=48]="formatOnType",e[e.glyphMargin=49]="glyphMargin",e[e.gotoLocation=50]="gotoLocation",e[e.hideCursorInOverviewRuler=51]="hideCursorInOverviewRuler",e[e.hover=52]="hover",e[e.inDiffEditor=53]="inDiffEditor",e[e.inlineSuggest=54]="inlineSuggest",e[e.letterSpacing=55]="letterSpacing",e[e.lightbulb=56]="lightbulb",e[e.lineDecorationsWidth=57]="lineDecorationsWidth",e[e.lineHeight=58]="lineHeight",e[e.lineNumbers=59]="lineNumbers",e[e.lineNumbersMinChars=60]="lineNumbersMinChars",e[e.linkedEditing=61]="linkedEditing",e[e.links=62]="links",e[e.matchBrackets=63]="matchBrackets",e[e.minimap=64]="minimap",e[e.mouseStyle=65]="mouseStyle",e[e.mouseWheelScrollSensitivity=66]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=67]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=68]="multiCursorMergeOverlapping",e[e.multiCursorModifier=69]="multiCursorModifier",e[e.multiCursorPaste=70]="multiCursorPaste",e[e.occurrencesHighlight=71]="occurrencesHighlight",e[e.overviewRulerBorder=72]="overviewRulerBorder",e[e.overviewRulerLanes=73]="overviewRulerLanes",e[e.padding=74]="padding",e[e.parameterHints=75]="parameterHints",e[e.peekWidgetDefaultFocus=76]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=77]="definitionLinkOpensInPeek",e[e.quickSuggestions=78]="quickSuggestions",e[e.quickSuggestionsDelay=79]="quickSuggestionsDelay",e[e.readOnly=80]="readOnly",e[e.renameOnType=81]="renameOnType",e[e.renderControlCharacters=82]="renderControlCharacters",e[e.renderFinalNewline=83]="renderFinalNewline",e[e.renderLineHighlight=84]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=85]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=86]="renderValidationDecorations",e[e.renderWhitespace=87]="renderWhitespace",e[e.revealHorizontalRightPadding=88]="revealHorizontalRightPadding",e[e.roundedSelection=89]="roundedSelection",e[e.rulers=90]="rulers",e[e.scrollbar=91]="scrollbar",e[e.scrollBeyondLastColumn=92]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=93]="scrollBeyondLastLine",e[e.scrollPredominantAxis=94]="scrollPredominantAxis",e[e.selectionClipboard=95]="selectionClipboard",e[e.selectionHighlight=96]="selectionHighlight",e[e.selectOnLineNumbers=97]="selectOnLineNumbers",e[e.showFoldingControls=98]="showFoldingControls",e[e.showUnused=99]="showUnused",e[e.snippetSuggestions=100]="snippetSuggestions",e[e.smartSelect=101]="smartSelect",e[e.smoothScrolling=102]="smoothScrolling",e[e.stickyTabStops=103]="stickyTabStops",e[e.stopRenderingLineAfter=104]="stopRenderingLineAfter",e[e.suggest=105]="suggest",e[e.suggestFontSize=106]="suggestFontSize",e[e.suggestLineHeight=107]="suggestLineHeight",e[e.suggestOnTriggerCharacters=108]="suggestOnTriggerCharacters",e[e.suggestSelection=109]="suggestSelection",e[e.tabCompletion=110]="tabCompletion",e[e.tabIndex=111]="tabIndex",e[e.unusualLineTerminators=112]="unusualLineTerminators",e[e.useShadowDOM=113]="useShadowDOM",e[e.useTabStops=114]="useTabStops",e[e.wordSeparators=115]="wordSeparators",e[e.wordWrap=116]="wordWrap",e[e.wordWrapBreakAfterCharacters=117]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=118]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=119]="wordWrapColumn",e[e.wordWrapOverride1=120]="wordWrapOverride1",e[e.wordWrapOverride2=121]="wordWrapOverride2",e[e.wrappingIndent=122]="wrappingIndent",e[e.wrappingStrategy=123]="wrappingStrategy",e[e.showDeprecated=124]="showDeprecated",e[e.inlayHints=125]="inlayHints",e[e.editorClassName=126]="editorClassName",e[e.pixelRatio=127]="pixelRatio",e[e.tabFocusMode=128]="tabFocusMode",e[e.layoutInfo=129]="layoutInfo",e[e.wrappingInfo=130]="wrappingInfo"}(wt||(wt={})),function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(xt||(xt={})),function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"}(Lt||(Lt={})),function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"}(Tt||(Tt={})),function(e){e[e.Other=0]="Other",e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(kt||(kt={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(Ot||(Ot={})),function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.Semicolon=80]="Semicolon",e[e.Equal=81]="Equal",e[e.Comma=82]="Comma",e[e.Minus=83]="Minus",e[e.Period=84]="Period",e[e.Slash=85]="Slash",e[e.Backquote=86]="Backquote",e[e.BracketLeft=87]="BracketLeft",e[e.Backslash=88]="Backslash",e[e.BracketRight=89]="BracketRight",e[e.Quote=90]="Quote",e[e.OEM_8=91]="OEM_8",e[e.IntlBackslash=92]="IntlBackslash",e[e.Numpad0=93]="Numpad0",e[e.Numpad1=94]="Numpad1",e[e.Numpad2=95]="Numpad2",e[e.Numpad3=96]="Numpad3",e[e.Numpad4=97]="Numpad4",e[e.Numpad5=98]="Numpad5",e[e.Numpad6=99]="Numpad6",e[e.Numpad7=100]="Numpad7",e[e.Numpad8=101]="Numpad8",e[e.Numpad9=102]="Numpad9",e[e.NumpadMultiply=103]="NumpadMultiply",e[e.NumpadAdd=104]="NumpadAdd",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=106]="NumpadSubtract",e[e.NumpadDecimal=107]="NumpadDecimal",e[e.NumpadDivide=108]="NumpadDivide",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.AudioVolumeMute=112]="AudioVolumeMute",e[e.AudioVolumeUp=113]="AudioVolumeUp",e[e.AudioVolumeDown=114]="AudioVolumeDown",e[e.BrowserSearch=115]="BrowserSearch",e[e.BrowserHome=116]="BrowserHome",e[e.BrowserBack=117]="BrowserBack",e[e.BrowserForward=118]="BrowserForward",e[e.MediaTrackNext=119]="MediaTrackNext",e[e.MediaTrackPrevious=120]="MediaTrackPrevious",e[e.MediaStop=121]="MediaStop",e[e.MediaPlayPause=122]="MediaPlayPause",e[e.LaunchMediaPlayer=123]="LaunchMediaPlayer",e[e.LaunchMail=124]="LaunchMail",e[e.LaunchApp2=125]="LaunchApp2",e[e.MAX_VALUE=126]="MAX_VALUE"}(It||(It={})),function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"}(Mt||(Mt={})),function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"}(Pt||(Pt={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(Vt||(Vt={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"}(Rt||(Rt={})),function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"}(Ft||(Ft={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(Dt||(Dt={})),function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"}(Kt||(Kt={})),function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"}(jt||(jt={})),function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"}(Ut||(Ut={})),function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"}(Bt||(Bt={})),function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"}(qt||(qt={})),function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}($t||($t={})),function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"}(Wt||(Wt={})),function(e){e[e.Deprecated=1]="Deprecated"}(Ht||(Ht={})),function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"}(zt||(zt={})),function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"}(Gt||(Gt={})),function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"}(Jt||(Jt={})),function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"}(Xt||(Xt={}));class Qt{static chord(e,t){return function(e,t){return(e|(65535&t)<<16>>>0)>>>0}(e,t)}}Qt.CtrlCmd=2048,Qt.Shift=1024,Qt.Alt=512,Qt.WinCtrl=256;var en=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))};class tn extends class{constructor(e,t,n,r){this._uri=e,this._lines=t,this._eol=n,this._versionId=r,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const t=e.changes;for(const e of t)this._acceptDeleteRange(e.range),this._acceptInsertText(new Le(e.range.startLineNumber,e.range.startColumn),e.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this._lines[r].length+e;this._lineStarts=new Be(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.changeValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}_acceptInsertText(e,t){if(0===t.length)return;let n=t.split(/\r\n|\r|\n/);if(1===n.length)return void this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);let r=new Uint32Array(n.length-1);for(let t=1;t<n.length;t++)this._lines.splice(e.lineNumber+t-1,0,n[t]),r[t-1]=n[t].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}}{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){let n=function e(t,n,r,i,o=$e){if(r.length>o.maxLen){let s=t-o.maxLen/2;return s<0?s=0:i+=s,e(t,n,r=r.substring(s,t+o.maxLen/2),i,o)}const s=Date.now(),a=t-1-i;let u=-1,l=null;for(let e=1;!(Date.now()-s>=o.timeBudget);e++){const t=a-o.windowSize*e;n.lastIndex=Math.max(0,t);const i=We(n,r,a,u);if(!i&&l)break;if(l=i,t<=0)break;u=t}if(l){let e={word:l[0],startColumn:i+1+l.index,endColumn:i+1+l.index+l[0].length};return n.lastIndex=0,e}return null}(e.column,function(e){let t=qe;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}(t),this._lines[e.lineNumber-1],0);return n?new Te(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){const t=this._lines,n=this._wordenize.bind(this);let r=0,i="",o=0,s=[];return{*[Symbol.iterator](){for(;;)if(o<s.length){const e=i.substring(s[o].start,s[o].end);o+=1,yield e}else{if(!(r<t.length))break;i=t[r],s=n(i,e),o=0,r+=1}}}}getLineWords(e,t){let n=this._lines[e-1],r=this._wordenize(n,t),i=[];for(const e of r)i.push({word:n.substring(e.start,e.end),startColumn:e.start+1,endColumn:e.end+1});return i}_wordenize(e,t){const n=[];let r;for(t.lastIndex=0;(r=t.exec(e))&&0!==r[0].length;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);let t=this._eol,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this._lines[n].substring(e.startColumn-1));for(let e=n+1;e<r;e++)i.push(this._lines[e]);return i.push(this._lines[r].substring(0,e.endColumn-1)),i.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();let t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){const t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!Le.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,r=!0;else{let e=this._lines[t-1].length+1;n<1?(n=1,r=!0):n>e&&(n=e,r=!0)}return r?{lineNumber:t,column:n}:e}}class nn{constructor(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(e){return this._models[e]}_getModels(){let e=[];return Object.keys(this._models).forEach(t=>e.push(this._models[t])),e}acceptNewModel(e){this._models[e.url]=new tn(ye.parse(e.url),e.lines,e.EOL,e.versionId)}acceptModelChanged(e,t){if(!this._models[e])return;this._models[e].onEvents(t)}acceptRemovedModel(e){this._models[e]&&delete this._models[e]}computeDiff(e,t,n,r){return en(this,void 0,void 0,(function*(){const i=this._getModel(e),o=this._getModel(t);if(!i||!o)return null;const s=i.getLinesContent(),a=o.getLinesContent(),u=new Ve(s,a,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:n,shouldMakePrettyDiff:!0,maxComputationTime:r}).computeDiff(),l=!(u.changes.length>0)&&this._modelsAreIdentical(i,o);return{quitEarly:u.quitEarly,identical:l,changes:u.changes}}))}_modelsAreIdentical(e,t){const n=e.getLineCount();if(n!==t.getLineCount())return!1;for(let r=1;r<=n;r++){if(e.getLineContent(r)!==t.getLineContent(r))return!1}return!0}computeMoreMinimalEdits(e,t){return en(this,void 0,void 0,(function*(){const n=this._getModel(e);if(!n)return t;const r=[];let i=void 0;t=t.slice(0).sort((e,t)=>{if(e.range&&t.range)return Te.compareRangesUsingStarts(e.range,t.range);return(e.range?0:1)-(t.range?0:1)});for(let{range:e,text:o,eol:s}of t){if("number"==typeof s&&(i=s),Te.isEmpty(e)&&!o)continue;const t=n.getValueInRange(e);if(o=o.replace(/\r\n|\n|\r/g,n.eol),t===o)continue;if(Math.max(o.length,t.length)>nn._diffLimit){r.push({range:e,text:o});continue}const a=Z(t,o,!1),u=n.offsetAt(Te.lift(e).getStartPosition());for(const e of a){const t=n.positionAt(u+e.originalStart),i=n.positionAt(u+e.originalStart+e.originalLength),s={text:o.substr(e.modifiedStart,e.modifiedLength),range:{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:i.lineNumber,endColumn:i.column}};n.getValueInRange(s.range)!==s.text&&r.push(s)}}return"number"==typeof i&&r.push({eol:i,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),r}))}computeLinks(e){return en(this,void 0,void 0,(function*(){let t=this._getModel(e);return t?function(e){return e&&"function"==typeof e.getLineCount&&"function"==typeof e.getLineContent?Ze.computeLinks(e):[]}(t):null}))}textualSuggest(e,t,n,r){return en(this,void 0,void 0,(function*(){const i=new A(!0),o=new RegExp(n,r),s=new Set;e:for(let n of e){const e=this._getModel(n);if(e)for(let n of e.words(o))if(n!==t&&isNaN(Number(n))&&(s.add(n),s.size>nn._suggestionsLimit))break e}return{words:Array.from(s),duration:i.elapsed()}}))}computeWordRanges(e,t,n,r){return en(this,void 0,void 0,(function*(){let i=this._getModel(e);if(!i)return Object.create(null);const o=new RegExp(n,r),s=Object.create(null);for(let e=t.startLineNumber;e<t.endLineNumber;e++){let t=i.getLineWords(e,o);for(const n of t){if(!isNaN(Number(n.word)))continue;let t=s[n.word];t||(t=[],s[n.word]=t),t.push({startLineNumber:e,startColumn:n.startColumn,endLineNumber:e,endColumn:n.endColumn})}}return s}))}navigateValueSet(e,t,n,r,i){return en(this,void 0,void 0,(function*(){let o=this._getModel(e);if(!o)return null;let s=new RegExp(r,i);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});let a=o.getValueInRange(t),u=o.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},s);if(!u)return null;let l=o.getValueInRange(u);return Ye.INSTANCE.navigateValueSet(t,a,u,l,n)}))}loadForeignModule(e,t,n){let r={host:function(e,t){const n=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)};let r={};for(const t of e)r[t]=n(t);return r}(n,(e,t)=>this._host.fhr(e,t)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(r,t),Promise.resolve(x(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(e,t){if(!this._foreignModule||"function"!=typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(e){return Promise.reject(e)}}}nn._diffLimit=1e5,nn._suggestionsLimit=1e4,"function"==typeof importScripts&&(C.a.monaco={editor:void 0,languages:void 0,CancellationTokenSource:rt,Emitter:w,KeyCode:It,KeyMod:Qt,Position:Le,Range:Te,Selection:Zt,SelectionDirection:qt,MarkerSeverity:Mt,MarkerTag:Pt,Uri:ye,Token:Yt});let rn=!1;function on(e){if(rn)return;rn=!0;const t=new B(e=>{self.postMessage(e)},t=>new nn(t,e));self.onmessage=e=>{t.onmessage(e.data)}}function sn(e,t){void 0===t&&(t=!1);var n=e.length,r=0,i="",o=0,s=16,a=0,u=0,l=0,c=0,h=0;function f(t,n){for(var i=0,o=0;i<t||!n;){var s=e.charCodeAt(r);if(s>=48&&s<=57)o=16*o+s-48;else if(s>=65&&s<=70)o=16*o+s-65+10;else{if(!(s>=97&&s<=102))break;o=16*o+s-97+10}r++,i++}return i<t&&(o=-1),o}function d(){if(i="",h=0,o=r,u=a,c=l,r>=n)return o=n,s=17;var t=e.charCodeAt(r);if(an(t)){do{r++,i+=String.fromCharCode(t),t=e.charCodeAt(r)}while(an(t));return s=15}if(un(t))return r++,i+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,i+="\n"),a++,l=r,s=14;switch(t){case 123:return r++,s=1;case 125:return r++,s=2;case 91:return r++,s=3;case 93:return r++,s=4;case 58:return r++,s=6;case 44:return r++,s=5;case 34:return r++,i=function(){for(var t="",i=r;;){if(r>=n){t+=e.substring(i,r),h=2;break}var o=e.charCodeAt(r);if(34===o){t+=e.substring(i,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(un(o)){t+=e.substring(i,r),h=2;break}h=6}r++}else{if(t+=e.substring(i,r),++r>=n){h=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var s=f(4,!0);s>=0?t+=String.fromCharCode(s):h=4;break;default:h=5}i=r}}return t}(),s=10;case 47:var d=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!un(e.charCodeAt(r));)r++;return i=e.substring(d,r),s=12}if(42===e.charCodeAt(r+1)){r+=2;for(var g=n-1,p=!1;r<g;){var v=e.charCodeAt(r);if(42===v&&47===e.charCodeAt(r+1)){r+=2,p=!0;break}r++,un(v)&&(13===v&&10===e.charCodeAt(r)&&r++,a++,l=r)}return p||(r++,h=1),i=e.substring(d,r),s=13}return i+=String.fromCharCode(t),r++,s=16;case 45:if(i+=String.fromCharCode(t),++r===n||!ln(e.charCodeAt(r)))return s=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=function(){var t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&ln(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(!(++r<e.length&&ln(e.charCodeAt(r))))return h=3,e.substring(t,r);for(r++;r<e.length&&ln(e.charCodeAt(r));)r++}var n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if((++r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&ln(e.charCodeAt(r))){for(r++;r<e.length&&ln(e.charCodeAt(r));)r++;n=r}else h=3;return e.substring(t,n)}(),s=11;default:for(;r<n&&m(t);)r++,t=e.charCodeAt(r);if(o!==r){switch(i=e.substring(o,r)){case"true":return s=8;case"false":return s=9;case"null":return s=7}return s=16}return i+=String.fromCharCode(t),r++,s=16}}function m(e){if(an(e)||un(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,i="",o=0,s=16,h=0},getPosition:function(){return r},scan:t?function(){var e;do{e=d()}while(e>=12&&e<=15);return e}:d,getToken:function(){return s},getTokenValue:function(){return i},getTokenOffset:function(){return o},getTokenLength:function(){return r-o},getTokenStartLine:function(){return u},getTokenStartCharacter:function(){return o-c},getTokenError:function(){return h}}}function an(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function un(e){return 10===e||13===e||8232===e||8233===e}function ln(e){return e>=48&&e<=57}function cn(e,t,n){var r,i,o,s,a;if(t){for(s=t.offset,a=s+t.length,o=s;o>0&&!fn(e,o-1);)o--;for(var u=a;u<e.length&&!fn(e,u);)u++;i=e.substring(o,u),r=function(e,t){var n=0,r=0,i=t.tabSize||4;for(;n<e.length;){var o=e.charAt(n);if(" "===o)r++;else{if("\t"!==o)break;r+=i}n++}return Math.floor(r/i)}(i,n)}else i=e,r=0,o=0,s=0,a=e.length;var l,c=function(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if("\r"===r)return n+1<t.length&&"\n"===t.charAt(n+1)?"\r\n":"\r";if("\n"===r)return"\n"}return e&&e.eol||"\n"}(n,e),h=!1,f=0;l=n.insertSpaces?hn(" ",n.tabSize||4):"\t";var d=sn(i,!1),m=!1;function g(){return c+hn(l,r+f)}function p(){var e=d.scan();for(h=!1;15===e||14===e;)h=h||14===e,e=d.scan();return m=16===e||0!==d.getTokenError(),e}var v=[];function y(n,r,i){m||t&&!(r<a&&i>s)||e.substring(r,i)===n||v.push({offset:r,length:i-r,content:n})}var b=p();if(17!==b){var _=d.getTokenOffset()+o;y(hn(l,r),o,_)}for(;17!==b;){for(var C=d.getTokenOffset()+d.getTokenLength()+o,S=p(),A="",E=!1;!h&&(12===S||13===S);){y(" ",C,d.getTokenOffset()+o),C=d.getTokenOffset()+d.getTokenLength()+o,A=(E=12===S)?g():"",S=p()}if(2===S)1!==b&&(f--,A=g());else if(4===S)3!==b&&(f--,A=g());else{switch(b){case 3:case 1:f++,A=g();break;case 5:case 12:A=g();break;case 13:h?A=g():E||(A=" ");break;case 6:E||(A=" ");break;case 10:if(6===S){E||(A="");break}case 7:case 8:case 9:case 11:case 2:case 4:12===S||13===S?E||(A=" "):5!==S&&17!==S&&(m=!0);break;case 16:m=!0}!h||12!==S&&13!==S||(A=g())}17===S&&(A=n.insertFinalNewline?c:""),y(A,C,d.getTokenOffset()+o),b=S}return v}function hn(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function fn(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}var dn;function mn(e,t,n){void 0===n&&(n=dn.DEFAULT);var r=sn(e,!1);function i(e){return e?function(){return e(r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}function o(e){return e?function(t){return e(t,r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}var s=i(t.onObjectBegin),a=o(t.onObjectProperty),u=i(t.onObjectEnd),l=i(t.onArrayBegin),c=i(t.onArrayEnd),h=o(t.onLiteralValue),f=o(t.onSeparator),d=i(t.onComment),m=o(t.onError),g=n&&n.disallowComments,p=n&&n.allowTrailingComma;function v(){for(;;){var e=r.scan();switch(r.getTokenError()){case 4:y(14);break;case 5:y(15);break;case 3:y(13);break;case 1:g||y(11);break;case 2:y(12);break;case 6:y(16)}switch(e){case 12:case 13:g?y(10):d();break;case 16:y(1);break;case 15:case 14:break;default:return e}}}function y(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),m(e),t.length+n.length>0)for(var i=r.getToken();17!==i;){if(-1!==t.indexOf(i)){v();break}if(-1!==n.indexOf(i))break;i=v()}}function b(e){var t=r.getTokenValue();return e?h(t):a(t),v(),!0}function _(){switch(r.getToken()){case 3:return function(){l(),v();for(var e=!1;4!==r.getToken()&&17!==r.getToken();){if(5===r.getToken()){if(e||y(4,[],[]),f(","),v(),4===r.getToken()&&p)break}else e&&y(6,[],[]);_()||y(4,[],[4,5]),e=!0}return c(),4!==r.getToken()?y(8,[4],[]):v(),!0}();case 1:return function(){s(),v();for(var e=!1;2!==r.getToken()&&17!==r.getToken();){if(5===r.getToken()){if(e||y(4,[],[]),f(","),v(),2===r.getToken()&&p)break}else e&&y(6,[],[]);(10!==r.getToken()?(y(3,[],[2,5]),0):(b(!1),6===r.getToken()?(f(":"),v(),_()||y(4,[],[2,5])):y(5,[],[2,5]),1))||y(4,[],[2,5]),e=!0}return u(),2!==r.getToken()?y(7,[2],[]):v(),!0}();case 10:return b(!0);default:return function(){switch(r.getToken()){case 11:var e=r.getTokenValue(),t=Number(e);isNaN(t)&&(y(2),t=0),h(t);break;case 7:h(null);break;case 8:h(!0);break;case 9:h(!1);break;default:return!1}return v(),!0}()}}return v(),17===r.getToken()?!!n.allowEmptyContent||(y(4,[],[]),!1):_()?(17!==r.getToken()&&y(9,[],[]),!0):(y(4,[],[]),!1)}self.onmessage=e=>{rn||on(null)},function(e){e.DEFAULT={allowTrailingComma:!1}}(dn||(dn={}));var gn,pn,vn,yn,bn,_n,Cn,Sn,An,En,Nn,wn,xn,Ln,Tn,kn,On,In,Mn,Pn,Vn,Rn,Fn,Dn,Kn,jn,Un,Bn=sn,qn=function(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=dn.DEFAULT);var r=null,i=[],o=[];function s(e){Array.isArray(i)?i.push(e):null!==r&&(i[r]=e)}return mn(e,{onObjectBegin:function(){var e={};s(e),o.push(i),i=e,r=null},onObjectProperty:function(e){r=e},onObjectEnd:function(){i=o.pop()},onArrayBegin:function(){var e=[];s(e),o.push(i),i=e,r=null},onArrayEnd:function(){i=o.pop()},onLiteralValue:s,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},n),i[0]},$n=function e(t,n,r){if(void 0===r&&(r=!1),function(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){var i=t.children;if(Array.isArray(i))for(var o=0;o<i.length&&i[o].offset<=n;o++){var s=e(i[o],n,r);if(s)return s}return t}},Wn=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var i=t.parent.children.indexOf(t);-1!==i&&n.push(i)}return n},Hn=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,i=t.children;r<i.length;r++){var o=i[r],s=o.children[1];s&&(n[o.children[0].value]=e(s))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}};function zn(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(typeof e!=typeof t)return!1;if("object"!=typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;var n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!zn(e[n],t[n]))return!1}else{var i=[];for(r in e)i.push(r);i.sort();var o=[];for(r in t)o.push(r);if(o.sort(),!zn(i,o))return!1;for(n=0;n<i.length;n++)if(!zn(e[i[n]],t[i[n]]))return!1}return!0}function Gn(e){return"number"==typeof e}function Jn(e){return void 0!==e}function Xn(e){return"boolean"==typeof e}function Zn(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}function Yn(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function Qn(e){return Zn(e,"(?i)")?new RegExp(e.substring(4),"iu"):new RegExp(e,"u")}!function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647}(gn||(gn={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647}(pn||(pn={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=pn.MAX_VALUE),t===Number.MAX_VALUE&&(t=pn.MAX_VALUE),{line:e,character:t}},e.is=function(e){var t=e;return Mr.objectLiteral(t)&&Mr.uinteger(t.line)&&Mr.uinteger(t.character)}}(vn||(vn={})),(bn=yn||(yn={})).create=function(e,t,n,r){if(Mr.uinteger(e)&&Mr.uinteger(t)&&Mr.uinteger(n)&&Mr.uinteger(r))return{start:vn.create(e,t),end:vn.create(n,r)};if(vn.is(e)&&vn.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+n+", "+r+"]")},bn.is=function(e){var t=e;return Mr.objectLiteral(t)&&vn.is(t.start)&&vn.is(t.end)},function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return Mr.defined(t)&&yn.is(t.range)&&(Mr.string(t.uri)||Mr.undefined(t.uri))}}(_n||(_n={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return Mr.defined(t)&&yn.is(t.targetRange)&&Mr.string(t.targetUri)&&(yn.is(t.targetSelectionRange)||Mr.undefined(t.targetSelectionRange))&&(yn.is(t.originSelectionRange)||Mr.undefined(t.originSelectionRange))}}(Cn||(Cn={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return Mr.numberRange(t.red,0,1)&&Mr.numberRange(t.green,0,1)&&Mr.numberRange(t.blue,0,1)&&Mr.numberRange(t.alpha,0,1)}}(Sn||(Sn={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return yn.is(t.range)&&Sn.is(t.color)}}(An||(An={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return Mr.string(t.label)&&(Mr.undefined(t.textEdit)||Mn.is(t))&&(Mr.undefined(t.additionalTextEdits)||Mr.typedArray(t.additionalTextEdits,Mn.is))}}(En||(En={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(Nn||(Nn={})),function(e){e.create=function(e,t,n,r,i){var o={startLine:e,endLine:t};return Mr.defined(n)&&(o.startCharacter=n),Mr.defined(r)&&(o.endCharacter=r),Mr.defined(i)&&(o.kind=i),o},e.is=function(e){var t=e;return Mr.uinteger(t.startLine)&&Mr.uinteger(t.startLine)&&(Mr.undefined(t.startCharacter)||Mr.uinteger(t.startCharacter))&&(Mr.undefined(t.endCharacter)||Mr.uinteger(t.endCharacter))&&(Mr.undefined(t.kind)||Mr.string(t.kind))}}(wn||(wn={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return Mr.defined(t)&&_n.is(t.location)&&Mr.string(t.message)}}(xn||(xn={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(Ln||(Ln={})),function(e){e.Unnecessary=1,e.Deprecated=2}(Tn||(Tn={})),function(e){e.is=function(e){var t=e;return null!=t&&Mr.string(t.href)}}(kn||(kn={})),function(e){e.create=function(e,t,n,r,i,o){var s={range:e,message:t};return Mr.defined(n)&&(s.severity=n),Mr.defined(r)&&(s.code=r),Mr.defined(i)&&(s.source=i),Mr.defined(o)&&(s.relatedInformation=o),s},e.is=function(e){var t,n=e;return Mr.defined(n)&&yn.is(n.range)&&Mr.string(n.message)&&(Mr.number(n.severity)||Mr.undefined(n.severity))&&(Mr.integer(n.code)||Mr.string(n.code)||Mr.undefined(n.code))&&(Mr.undefined(n.codeDescription)||Mr.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Mr.string(n.source)||Mr.undefined(n.source))&&(Mr.undefined(n.relatedInformation)||Mr.typedArray(n.relatedInformation,xn.is))}}(On||(On={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return Mr.defined(n)&&n.length>0&&(i.arguments=n),i},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.string(t.title)&&Mr.string(t.command)}}(In||(In={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return Mr.objectLiteral(t)&&Mr.string(t.newText)&&yn.is(t.range)}}(Mn||(Mn={})),function(e){e.create=function(e,t,n){var r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){var t=e;return void 0!==t&&Mr.objectLiteral(t)&&Mr.string(t.label)&&(Mr.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Mr.string(t.description)||void 0===t.description)}}(Pn||(Pn={})),function(e){e.is=function(e){return"string"==typeof e}}(Vn||(Vn={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){var t=e;return Mn.is(t)&&(Pn.is(t.annotationId)||Vn.is(t.annotationId))}}(Rn||(Rn={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return Mr.defined(t)&&nr.is(t.textDocument)&&Array.isArray(t.edits)}}(Fn||(Fn={})),function(e){e.create=function(e,t,n){var r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"create"===t.kind&&Mr.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Mr.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Mr.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||Vn.is(t.annotationId))}}(Dn||(Dn={})),function(e){e.create=function(e,t,n,r){var i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},e.is=function(e){var t=e;return t&&"rename"===t.kind&&Mr.string(t.oldUri)&&Mr.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Mr.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Mr.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||Vn.is(t.annotationId))}}(Kn||(Kn={})),function(e){e.create=function(e,t,n){var r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"delete"===t.kind&&Mr.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Mr.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Mr.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||Vn.is(t.annotationId))}}(jn||(jn={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return Mr.string(e.kind)?Dn.is(e)||Kn.is(e)||jn.is(e):Fn.is(e)})))}}(Un||(Un={}));var er,tr,nr,rr,ir,or,sr,ar,ur,lr,cr,hr,fr,dr,mr,gr,pr,vr,yr,br,_r,Cr,Sr,Ar,Er,Nr,wr,xr,Lr,Tr,kr=function(){function e(e,t){this.edits=e,this.changeAnnotations=t}return e.prototype.insert=function(e,t,n){var r,i;if(void 0===n?r=Mn.insert(e,t):Vn.is(n)?(i=n,r=Rn.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=Rn.insert(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.replace=function(e,t,n){var r,i;if(void 0===n?r=Mn.replace(e,t):Vn.is(n)?(i=n,r=Rn.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=Rn.replace(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.delete=function(e,t){var n,r;if(void 0===t?n=Mn.del(e):Vn.is(t)?(r=t,n=Rn.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(t),n=Rn.del(e,r)),this.edits.push(n),void 0!==r)return r},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e.prototype.assertChangeAnnotations=function(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")},e}(),Or=function(){function e(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}return e.prototype.all=function(){return this._annotations},Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.manage=function(e,t){var n;if(Vn.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error("Id "+n+" is already in use.");if(void 0===t)throw new Error("No annotation provided for id "+n);return this._annotations[n]=t,this._size++,n},e.prototype.nextId=function(){return this._counter++,this._counter.toString()},e}();!function(){function e(e){var t=this;this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new Or(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach((function(e){if(Fn.is(e)){var n=new kr(e.edits,t._changeAnnotations);t._textEditChanges[e.textDocument.uri]=n}}))):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new kr(e.changes[n]);t._textEditChanges[n]=r}))):this._workspaceEdit={}}Object.defineProperty(e.prototype,"edit",{get:function(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),e.prototype.getTextEditChange=function(e){if(nr.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t={uri:e.uri,version:e.version};if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:i=[]};this._workspaceEdit.documentChanges.push(n),r=new kr(i,this._changeAnnotations),this._textEditChanges[t.uri]=r}return r}if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var i=[];this._workspaceEdit.changes[e]=i,r=new kr(i),this._textEditChanges[e]=r}return r},e.prototype.initDocumentChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new Or,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},e.prototype.initChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))},e.prototype.createFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,o;if(Pn.is(t)||Vn.is(t)?r=t:n=t,void 0===r?i=Dn.create(e,n):(o=Vn.is(r)?r:this._changeAnnotations.manage(r),i=Dn.create(e,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o},e.prototype.renameFile=function(e,t,n,r){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var i,o,s;if(Pn.is(n)||Vn.is(n)?i=n:r=n,void 0===i?o=Kn.create(e,t,r):(s=Vn.is(i)?i:this._changeAnnotations.manage(i),o=Kn.create(e,t,r,s)),this._workspaceEdit.documentChanges.push(o),void 0!==s)return s},e.prototype.deleteFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,o;if(Pn.is(t)||Vn.is(t)?r=t:n=t,void 0===r?i=jn.create(e,n):(o=Vn.is(r)?r:this._changeAnnotations.manage(r),i=jn.create(e,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o}}();!function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.string(t.uri)}}(er||(er={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.string(t.uri)&&Mr.integer(t.version)}}(tr||(tr={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.string(t.uri)&&(null===t.version||Mr.integer(t.version))}}(nr||(nr={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.string(t.uri)&&Mr.string(t.languageId)&&Mr.integer(t.version)&&Mr.string(t.text)}}(rr||(rr={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(ir||(ir={})),function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(ir||(ir={})),function(e){e.is=function(e){var t=e;return Mr.objectLiteral(e)&&ir.is(t.kind)&&Mr.string(t.value)}}(or||(or={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(sr||(sr={})),function(e){e.PlainText=1,e.Snippet=2}(ar||(ar={})),function(e){e.Deprecated=1}(ur||(ur={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){var t=e;return t&&Mr.string(t.newText)&&yn.is(t.insert)&&yn.is(t.replace)}}(lr||(lr={})),function(e){e.asIs=1,e.adjustIndentation=2}(cr||(cr={})),function(e){e.create=function(e){return{label:e}}}(hr||(hr={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(fr||(fr={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return Mr.string(t)||Mr.objectLiteral(t)&&Mr.string(t.language)&&Mr.string(t.value)}}(dr||(dr={})),function(e){e.is=function(e){var t=e;return!!t&&Mr.objectLiteral(t)&&(or.is(t.contents)||dr.is(t.contents)||Mr.typedArray(t.contents,dr.is))&&(void 0===e.range||yn.is(e.range))}}(mr||(mr={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(gr||(gr={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return Mr.defined(t)&&(i.documentation=t),Mr.defined(n)?i.parameters=n:i.parameters=[],i}}(pr||(pr={})),function(e){e.Text=1,e.Read=2,e.Write=3}(vr||(vr={})),function(e){e.create=function(e,t){var n={range:e};return Mr.number(t)&&(n.kind=t),n}}(yr||(yr={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(br||(br={})),function(e){e.Deprecated=1}(_r||(_r={})),function(e){e.create=function(e,t,n,r,i){var o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o}}(Cr||(Cr={})),function(e){e.create=function(e,t,n,r,i,o){var s={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(s.children=o),s},e.is=function(e){var t=e;return t&&Mr.string(t.name)&&Mr.number(t.kind)&&yn.is(t.range)&&yn.is(t.selectionRange)&&(void 0===t.detail||Mr.string(t.detail))&&(void 0===t.deprecated||Mr.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(Sr||(Sr={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(Ar||(Ar={})),function(e){e.create=function(e,t){var n={diagnostics:e};return null!=t&&(n.only=t),n},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.typedArray(t.diagnostics,On.is)&&(void 0===t.only||Mr.typedArray(t.only,Mr.string))}}(Er||(Er={})),function(e){e.create=function(e,t,n){var r={title:e},i=!0;return"string"==typeof t?(i=!1,r.kind=t):In.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&Mr.string(t.title)&&(void 0===t.diagnostics||Mr.typedArray(t.diagnostics,On.is))&&(void 0===t.kind||Mr.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||In.is(t.command))&&(void 0===t.isPreferred||Mr.boolean(t.isPreferred))&&(void 0===t.edit||Un.is(t.edit))}}(Nr||(Nr={})),function(e){e.create=function(e,t){var n={range:e};return Mr.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return Mr.defined(t)&&yn.is(t.range)&&(Mr.undefined(t.command)||In.is(t.command))}}(wr||(wr={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return Mr.defined(t)&&Mr.uinteger(t.tabSize)&&Mr.boolean(t.insertSpaces)}}(xr||(xr={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return Mr.defined(t)&&yn.is(t.range)&&(Mr.undefined(t.target)||Mr.string(t.target))}}(Lr||(Lr={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){var n=t;return void 0!==n&&yn.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(Tr||(Tr={}));var Ir;!function(e){e.create=function(e,t,n,r){return new Pr(e,t,n,r)},e.is=function(e){var t=e;return!!(Mr.defined(t)&&Mr.string(t.uri)&&(Mr.undefined(t.languageId)||Mr.string(t.languageId))&&Mr.uinteger(t.lineCount)&&Mr.func(t.getText)&&Mr.func(t.positionAt)&&Mr.func(t.offsetAt))},e.applyEdits=function(e,t){for(var n=e.getText(),r=function e(t,n){if(t.length<=1)return t;var r=t.length/2|0,i=t.slice(0,r),o=t.slice(r);e(i,n),e(o,n);var s=0,a=0,u=0;for(;s<i.length&&a<o.length;){var l=n(i[s],o[a]);t[u++]=l<=0?i[s++]:o[a++]}for(;s<i.length;)t[u++]=i[s++];for(;a<o.length;)t[u++]=o[a++];return t}(t,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=n.length,o=r.length-1;o>=0;o--){var s=r[o],a=e.offsetAt(s.range.start),u=e.offsetAt(s.range.end);if(!(u<=i))throw new Error("Overlapping edit");n=n.substring(0,a)+s.newText+n.substring(u,n.length),i=a}return n}}(Ir||(Ir={}));var Mr,Pr=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return vn.create(0,e);for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return vn.create(o,e-t[o])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Mr||(Mr={}));var Vr,Rr,Fr,Dr=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},Kr=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(t,n){for(var r=0,i=t;r<i.length;r++){var o=i[r];if(e.isIncremental(o)){var s=Ur(o.range),a=this.offsetAt(s.start),u=this.offsetAt(s.end);this._content=this._content.substring(0,a)+o.text+this._content.substring(u,this._content.length);var l=Math.max(s.start.line,0),c=Math.max(s.end.line,0),h=this._lineOffsets,f=jr(o.text,!1,a);if(c-l===f.length)for(var d=0,m=f.length;d<m;d++)h[d+l+1]=f[d];else f.length<1e4?h.splice.apply(h,Dr([l+1,c-l],f,!1)):this._lineOffsets=h=h.slice(0,l+1).concat(f,h.slice(c+1));var g=o.text.length-(u-a);if(0!==g)for(d=l+1+f.length,m=h.length;d<m;d++)h[d]=h[d]+g}else{if(!e.isFull(o))throw new Error("Unknown change event received");this._content=o.text,this._lineOffsets=void 0}}this._version=n},e.prototype.getLineOffsets=function(){return void 0===this._lineOffsets&&(this._lineOffsets=jr(this._content,!0)),this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return{line:o,character:e-t[o]}},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e.isIncremental=function(e){var t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)},e.isFull=function(e){var t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength},e}();function jr(e,t,n){void 0===n&&(n=0);for(var r=t?[n]:[],i=0;i<e.length;i++){var o=e.charCodeAt(i);13!==o&&10!==o||(13===o&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function Ur(e){var t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function Br(e){var t=Ur(e.range);return t!==e.range?{newText:e.newText,range:t}:e}function qr(e,t){return 0===t.length?e:e.replace(/\{(\d+)\}/g,(function(e,n){var r=n[0];return void 0!==t[r]?t[r]:e}))}function $r(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return qr(t,n)}function Wr(e){return $r}!function(e){e.create=function(e,t,n,r){return new Kr(e,t,n,r)},e.update=function(e,t,n){if(e instanceof Kr)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){for(var n=e.getText(),r=0,i=[],o=0,s=function e(t,n){if(t.length<=1)return t;var r=t.length/2|0,i=t.slice(0,r),o=t.slice(r);e(i,n),e(o,n);var s=0,a=0,u=0;for(;s<i.length&&a<o.length;){var l=n(i[s],o[a]);t[u++]=l<=0?i[s++]:o[a++]}for(;s<i.length;)t[u++]=i[s++];for(;a<o.length;)t[u++]=o[a++];return t}(t.map(Br),(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n}));o<s.length;o++){var a=s[o],u=e.offsetAt(a.range.start);if(u<r)throw new Error("Overlapping edit");u>r&&i.push(n.substring(r,u)),a.newText.length&&i.push(a.newText),r=e.offsetAt(a.range.end)}return i.push(n.substr(r)),i.join("")}}(Vr||(Vr={})),function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.Deprecated=2]="Deprecated",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter",e[e.PropertyExpected=513]="PropertyExpected",e[e.CommaExpected=514]="CommaExpected",e[e.ColonExpected=515]="ColonExpected",e[e.ValueExpected=516]="ValueExpected",e[e.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",e[e.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",e[e.TrailingComma=519]="TrailingComma",e[e.DuplicateKey=520]="DuplicateKey",e[e.CommentNotPermitted=521]="CommentNotPermitted",e[e.SchemaResolveError=768]="SchemaResolveError"}(Rr||(Rr={})),(Fr||(Fr={})).LATEST={textDocument:{completion:{completionItem:{documentationFormat:[ir.Markdown,ir.PlainText],commitCharactersSupport:!0}}}};var Hr,zr,Gr=(Hr=function(e,t){return(Hr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}Hr(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),Jr=Wr(),Xr={"color-hex":{errorMessage:Jr("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:Jr("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:Jr("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:Jr("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:Jr("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/}},Zr=function(){function e(e,t,n){void 0===n&&(n=0),this.offset=t,this.length=n,this.parent=e}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}(),Yr=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="null",r.value=null,r}return Gr(t,e),t}(Zr),Qr=function(e){function t(t,n,r){var i=e.call(this,t,r)||this;return i.type="boolean",i.value=n,i}return Gr(t,e),t}(Zr),ei=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="array",r.items=[],r}return Gr(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!1,configurable:!0}),t}(Zr),ti=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="number",r.isInteger=!0,r.value=Number.NaN,r}return Gr(t,e),t}(Zr),ni=function(e){function t(t,n,r){var i=e.call(this,t,n,r)||this;return i.type="string",i.value="",i}return Gr(t,e),t}(Zr),ri=function(e){function t(t,n,r){var i=e.call(this,t,n)||this;return i.type="property",i.colonOffset=-1,i.keyNode=r,i}return Gr(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!1,configurable:!0}),t}(Zr),ii=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="object",r.properties=[],r}return Gr(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!1,configurable:!0}),t}(Zr);function oi(e){return Xn(e)?e?{}:{not:{}}:e}!function(e){e[e.Key=0]="Key",e[e.Enum=1]="Enum"}(zr||(zr={}));var si=function(){function e(e,t){void 0===e&&(e=-1),this.focusOffset=e,this.exclude=t,this.schemas=[]}return e.prototype.add=function(e){this.schemas.push(e)},e.prototype.merge=function(e){Array.prototype.push.apply(this.schemas,e.schemas)},e.prototype.include=function(e){return(-1===this.focusOffset||hi(e,this.focusOffset))&&e!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),ai=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.add=function(e){},e.prototype.merge=function(e){},e.prototype.include=function(e){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),ui=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.merge(r)}},e.prototype.merge=function(e){this.problems=this.problems.concat(e.problems)},e.prototype.mergeEnumValues=function(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t=0,n=this.problems;t<n.length;t++){var r=n[t];r.code===Rr.EnumValueMismatch&&(r.message=Jr("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map((function(e){return JSON.stringify(e)})).join(", ")))}}},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++},e.prototype.compare=function(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();function li(e){return Hn(e)}function ci(e){return Wn(e)}function hi(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}var fi=function(){function e(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]),this.root=e,this.syntaxErrors=t,this.comments=n}return e.prototype.getNodeFromOffset=function(e,t){if(void 0===t&&(t=!1),this.root)return $n(this.root,e,t)},e.prototype.visit=function(e){if(this.root){var t=function(n){var r=e(n),i=n.children;if(Array.isArray(i))for(var o=0;o<i.length&&r;o++)r=t(i[o]);return r};t(this.root)}},e.prototype.validate=function(e,t,n){if(void 0===n&&(n=Ln.Warning),this.root&&t){var r=new ui;return di(this.root,t,r,ai.instance),r.problems.map((function(t){var r,i=yn.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return On.create(i,t.message,null!==(r=t.severity)&&void 0!==r?r:n,t.code)}))}},e.prototype.getMatchingSchemas=function(e,t,n){void 0===t&&(t=-1);var r=new si(t,n);return this.root&&e&&di(this.root,e,new ui,r),r.schemas},e}();function di(e,t,n,r){if(e&&r.include(e)){var i=e;switch(i.type){case"object":!function(e,t,n,r){for(var i=Object.create(null),o=[],s=0,a=e.properties;s<a.length;s++){var u=(y=a[s]).keyNode.value;i[u]=y.valueNode,o.push(u)}if(Array.isArray(t.required))for(var l=0,c=t.required;l<c.length;l++){var h=c[l];if(!i[h]){var f=e.parent&&"property"===e.parent.type&&e.parent.keyNode,d=f?{offset:f.offset,length:f.length}:{offset:e.offset,length:1};n.problems.push({location:d,message:Jr("MissingRequiredPropWarning",'Missing property "{0}".',h)})}}var m=function(e){for(var t=o.indexOf(e);t>=0;)o.splice(t,1),t=o.indexOf(e)};if(t.properties)for(var g=0,p=Object.keys(t.properties);g<p.length;g++){h=p[g];m(h);var v=t.properties[h];if(k=i[h])if(Xn(v))if(v)n.propertiesMatches++,n.propertiesValueMatches++;else{var y=k.parent;n.problems.push({location:{offset:y.keyNode.offset,length:y.keyNode.length},message:t.errorMessage||Jr("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}else{var b=new ui;di(k,v,b,r),n.mergePropertyMatch(b)}}if(t.patternProperties)for(var _=0,C=Object.keys(t.patternProperties);_<C.length;_++)for(var S=C[_],A=Qn(S),E=0,N=o.slice(0);E<N.length;E++){h=N[E];if(A.test(h))if(m(h),k=i[h])if(Xn(v=t.patternProperties[S]))if(v)n.propertiesMatches++,n.propertiesValueMatches++;else{y=k.parent;n.problems.push({location:{offset:y.keyNode.offset,length:y.keyNode.length},message:t.errorMessage||Jr("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}else{b=new ui;di(k,v,b,r),n.mergePropertyMatch(b)}}if("object"==typeof t.additionalProperties)for(var w=0,x=o;w<x.length;w++){h=x[w];if(k=i[h]){b=new ui;di(k,t.additionalProperties,b,r),n.mergePropertyMatch(b)}}else if(!1===t.additionalProperties&&o.length>0)for(var L=0,T=o;L<T.length;L++){var k;h=T[L];if(k=i[h]){y=k.parent;n.problems.push({location:{offset:y.keyNode.offset,length:y.keyNode.length},message:t.errorMessage||Jr("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}}Gn(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("MaxPropWarning","Object has more properties than limit of {0}.",t.maxProperties)});Gn(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)});if(t.dependencies)for(var O=0,I=Object.keys(t.dependencies);O<I.length;O++){u=I[O];if(i[u]){var M=t.dependencies[u];if(Array.isArray(M))for(var P=0,V=M;P<V.length;P++){var R=V[P];i[R]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",R,u)})}else if(v=oi(M)){b=new ui;di(e,v,b,r),n.mergePropertyMatch(b)}}}var F=oi(t.propertyNames);if(F)for(var D=0,K=e.properties;D<K.length;D++){var j=K[D];(u=j.keyNode)&&di(u,F,n,ai.instance)}}(i,t,n,r);break;case"array":!function(e,t,n,r){if(Array.isArray(t.items)){for(var i=t.items,o=0;o<i.length;o++){var s=oi(i[o]),a=new ui;(f=e.items[o])?(di(f,s,a,r),n.mergePropertyMatch(a)):e.items.length>=i.length&&n.propertiesValueMatches++}if(e.items.length>i.length)if("object"==typeof t.additionalItems)for(var u=i.length;u<e.items.length;u++){a=new ui;di(e.items[u],t.additionalItems,a,r),n.mergePropertyMatch(a)}else!1===t.additionalItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",i.length)})}else{var l=oi(t.items);if(l)for(var c=0,h=e.items;c<h.length;c++){var f=h[c];a=new ui;di(f,l,a,r),n.mergePropertyMatch(a)}}var d=oi(t.contains);if(d){e.items.some((function(e){var t=new ui;return di(e,d,t,ai.instance),!t.hasProblems()}))||n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||Jr("requiredItemMissingWarning","Array does not contain required item.")})}Gn(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("minItemsWarning","Array has too few items. Expected {0} or more.",t.minItems)});Gn(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("maxItemsWarning","Array has too many items. Expected {0} or fewer.",t.maxItems)});if(!0===t.uniqueItems){var m=li(e);m.some((function(e,t){return t!==m.lastIndexOf(e)}))&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("uniqueItemsWarning","Array has duplicate items.")})}}(i,t,n,r);break;case"string":!function(e,t,n,r){Gn(t.minLength)&&e.value.length<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("minLengthWarning","String is shorter than the minimum length of {0}.",t.minLength)});Gn(t.maxLength)&&e.value.length>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("maxLengthWarning","String is longer than the maximum length of {0}.",t.maxLength)});if(i=t.pattern,"string"==typeof i){Qn(t.pattern).test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||Jr("patternWarning",'String does not match the pattern of "{0}".',t.pattern)})}var i;if(t.format)switch(t.format){case"uri":case"uri-reference":var o=void 0;if(e.value){var s=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);s?s[2]||"uri"!==t.format||(o=Jr("uriSchemeMissing","URI with a scheme is expected.")):o=Jr("uriMissing","URI is expected.")}else o=Jr("uriEmpty","URI expected.");o&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||Jr("uriFormatWarning","String is not a URI: {0}",o)});break;case"color-hex":case"date-time":case"date":case"time":case"email":var a=Xr[t.format];e.value&&a.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||a.errorMessage})}}(i,t,n);break;case"number":!function(e,t,n,r){var i=e.value;function o(e){var t,n=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return n&&{value:Number(n[1]+(n[2]||"")),multiplier:((null===(t=n[2])||void 0===t?void 0:t.length)||0)-(parseInt(n[3])||0)}}if(Gn(t.multipleOf)){var s=-1;if(Number.isInteger(t.multipleOf))s=i%t.multipleOf;else{var a=o(t.multipleOf),u=o(i);if(a&&u){var l=Math.pow(10,Math.abs(u.multiplier-a.multiplier));u.multiplier<a.multiplier?u.value*=l:a.value*=l,s=u.value%a.value}}0!==s&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("multipleOfWarning","Value is not divisible by {0}.",t.multipleOf)})}function c(e,t){return Gn(t)?t:Xn(t)&&t?e:void 0}function h(e,t){if(!Xn(t)||!t)return e}var f=c(t.minimum,t.exclusiveMinimum);Gn(f)&&i<=f&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",f)});var d=c(t.maximum,t.exclusiveMaximum);Gn(d)&&i>=d&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",d)});var m=h(t.minimum,t.exclusiveMinimum);Gn(m)&&i<m&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("minimumWarning","Value is below the minimum of {0}.",m)});var g=h(t.maximum,t.exclusiveMaximum);Gn(g)&&i>g&&n.problems.push({location:{offset:e.offset,length:e.length},message:Jr("maximumWarning","Value is above the maximum of {0}.",g)})}(i,t,n);break;case"property":return di(i.valueNode,t,n,r)}!function(){function e(e){return i.type===e||"integer"===e&&"number"===i.type&&i.isInteger}Array.isArray(t.type)?t.type.some(e)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||Jr("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(e(t.type)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||Jr("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)}));if(Array.isArray(t.allOf))for(var o=0,s=t.allOf;o<s.length;o++){var a=s[o];di(i,oi(a),n,r)}var u=oi(t.not);if(u){var l=new ui,c=r.newSub();di(i,u,l,c),l.hasProblems()||n.problems.push({location:{offset:i.offset,length:i.length},message:Jr("notSchemaWarning","Matches a schema that is not allowed.")});for(var h=0,f=c.schemas;h<f.length;h++){var d=f[h];d.inverted=!d.inverted,r.add(d)}}var m=function(e,t){for(var o=[],s=void 0,a=0,u=e;a<u.length;a++){var l=oi(u[a]),c=new ui,h=r.newSub();if(di(i,l,c,h),c.hasProblems()||o.push(l),s)if(t||c.hasProblems()||s.validationResult.hasProblems()){var f=c.compare(s.validationResult);f>0?s={schema:l,validationResult:c,matchingSchemas:h}:0===f&&(s.matchingSchemas.merge(h),s.validationResult.mergeEnumValues(c))}else s.matchingSchemas.merge(h),s.validationResult.propertiesMatches+=c.propertiesMatches,s.validationResult.propertiesValueMatches+=c.propertiesValueMatches;else s={schema:l,validationResult:c,matchingSchemas:h}}return o.length>1&&t&&n.problems.push({location:{offset:i.offset,length:1},message:Jr("oneOfWarning","Matches multiple schemas when only one must validate.")}),s&&(n.merge(s.validationResult),n.propertiesMatches+=s.validationResult.propertiesMatches,n.propertiesValueMatches+=s.validationResult.propertiesValueMatches,r.merge(s.matchingSchemas)),o.length};Array.isArray(t.anyOf)&&m(t.anyOf,!1);Array.isArray(t.oneOf)&&m(t.oneOf,!0);var g=function(e){var t=new ui,o=r.newSub();di(i,oi(e),t,o),n.merge(t),n.propertiesMatches+=t.propertiesMatches,n.propertiesValueMatches+=t.propertiesValueMatches,r.merge(o)},p=oi(t.if);p&&function(e,t,n){var o=oi(e),s=new ui,a=r.newSub();di(i,o,s,a),r.merge(a),s.hasProblems()?n&&g(n):t&&g(t)}(p,oi(t.then),oi(t.else));if(Array.isArray(t.enum)){for(var v=li(i),y=!1,b=0,_=t.enum;b<_.length;b++){var C=_[b];if(zn(v,C)){y=!0;break}}n.enumValues=t.enum,n.enumValueMatch=y,y||n.problems.push({location:{offset:i.offset,length:i.length},code:Rr.EnumValueMismatch,message:t.errorMessage||Jr("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map((function(e){return JSON.stringify(e)})).join(", "))})}if(Jn(t.const)){zn(v=li(i),t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:i.offset,length:i.length},code:Rr.EnumValueMismatch,message:t.errorMessage||Jr("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&i.parent&&n.problems.push({location:{offset:i.parent.offset,length:i.parent.length},severity:Ln.Warning,message:t.deprecationMessage,code:Rr.Deprecated})}(),r.add({node:i,schema:t})}}function mi(e,t){var n=[],r=-1,i=e.getText(),o=Bn(i,!1),s=t&&t.collectComments?[]:void 0;function a(){for(;;){var t=o.scan();switch(c(),t){case 12:case 13:Array.isArray(s)&&s.push(yn.create(e.positionAt(o.getTokenOffset()),e.positionAt(o.getTokenOffset()+o.getTokenLength())));break;case 15:case 14:break;default:return t}}}function u(t,i,o,s,a){if(void 0===a&&(a=Ln.Error),0===n.length||o!==r){var u=yn.create(e.positionAt(o),e.positionAt(s));n.push(On.create(u,t,a,i,e.languageId)),r=o}}function l(e,t,n,r,s){void 0===n&&(n=void 0),void 0===r&&(r=[]),void 0===s&&(s=[]);var l=o.getTokenOffset(),c=o.getTokenOffset()+o.getTokenLength();if(l===c&&l>0){for(l--;l>0&&/\s/.test(i.charAt(l));)l--;c=l+1}if(u(e,t,l,c),n&&h(n,!1),r.length+s.length>0)for(var f=o.getToken();17!==f;){if(-1!==r.indexOf(f)){a();break}if(-1!==s.indexOf(f))break;f=a()}return n}function c(){switch(o.getTokenError()){case 4:return l(Jr("InvalidUnicode","Invalid unicode sequence in string."),Rr.InvalidUnicode),!0;case 5:return l(Jr("InvalidEscapeCharacter","Invalid escape character in string."),Rr.InvalidEscapeCharacter),!0;case 3:return l(Jr("UnexpectedEndOfNumber","Unexpected end of number."),Rr.UnexpectedEndOfNumber),!0;case 1:return l(Jr("UnexpectedEndOfComment","Unexpected end of comment."),Rr.UnexpectedEndOfComment),!0;case 2:return l(Jr("UnexpectedEndOfString","Unexpected end of string."),Rr.UnexpectedEndOfString),!0;case 6:return l(Jr("InvalidCharacter","Invalid characters in string. Control characters must be escaped."),Rr.InvalidCharacter),!0}return!1}function h(e,t){return e.length=o.getTokenOffset()+o.getTokenLength()-e.offset,t&&a(),e}var f=new ni(void 0,0,0);function d(t,n){var r=new ri(t,o.getTokenOffset(),f),i=m(r);if(!i){if(16!==o.getToken())return;l(Jr("DoubleQuotesExpected","Property keys must be doublequoted"),Rr.Undefined);var s=new ni(r,o.getTokenOffset(),o.getTokenLength());s.value=o.getTokenValue(),i=s,a()}r.keyNode=i;var c=n[i.value];if(c?(u(Jr("DuplicateKeyWarning","Duplicate object key"),Rr.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,Ln.Warning),"object"==typeof c&&u(Jr("DuplicateKeyWarning","Duplicate object key"),Rr.DuplicateKey,c.keyNode.offset,c.keyNode.offset+c.keyNode.length,Ln.Warning),n[i.value]=!0):n[i.value]=r,6===o.getToken())r.colonOffset=o.getTokenOffset(),a();else if(l(Jr("ColonExpected","Colon expected"),Rr.ColonExpected),10===o.getToken()&&e.positionAt(i.offset+i.length).line<e.positionAt(o.getTokenOffset()).line)return r.length=i.length,r;var h=g(r);return h?(r.valueNode=h,r.length=h.offset+h.length-r.offset,r):l(Jr("ValueExpected","Value expected"),Rr.ValueExpected,r,[],[2,5])}function m(e){if(10===o.getToken()){var t=new ni(e,o.getTokenOffset());return t.value=o.getTokenValue(),h(t,!0)}}function g(e){return function(e){if(3===o.getToken()){var t=new ei(e,o.getTokenOffset());a();for(var n=!1;4!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){n||l(Jr("ValueExpected","Value expected"),Rr.ValueExpected);var r=o.getTokenOffset();if(a(),4===o.getToken()){n&&u(Jr("TrailingComma","Trailing comma"),Rr.TrailingComma,r,r+1);continue}}else n&&l(Jr("ExpectedComma","Expected comma"),Rr.CommaExpected);var i=g(t);i?t.items.push(i):l(Jr("PropertyExpected","Value expected"),Rr.ValueExpected,void 0,[],[4,5]),n=!0}return 4!==o.getToken()?l(Jr("ExpectedCloseBracket","Expected comma or closing bracket"),Rr.CommaOrCloseBacketExpected,t):h(t,!0)}}(e)||function(e){if(1===o.getToken()){var t=new ii(e,o.getTokenOffset()),n=Object.create(null);a();for(var r=!1;2!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){r||l(Jr("PropertyExpected","Property expected"),Rr.PropertyExpected);var i=o.getTokenOffset();if(a(),2===o.getToken()){r&&u(Jr("TrailingComma","Trailing comma"),Rr.TrailingComma,i,i+1);continue}}else r&&l(Jr("ExpectedComma","Expected comma"),Rr.CommaExpected);var s=d(t,n);s?t.properties.push(s):l(Jr("PropertyExpected","Property expected"),Rr.PropertyExpected,void 0,[],[2,5]),r=!0}return 2!==o.getToken()?l(Jr("ExpectedCloseBrace","Expected comma or closing brace"),Rr.CommaOrCloseBraceExpected,t):h(t,!0)}}(e)||m(e)||function(e){if(11===o.getToken()){var t=new ti(e,o.getTokenOffset());if(0===o.getTokenError()){var n=o.getTokenValue();try{var r=JSON.parse(n);if(!Gn(r))return l(Jr("InvalidNumberFormat","Invalid number format."),Rr.Undefined,t);t.value=r}catch(e){return l(Jr("InvalidNumberFormat","Invalid number format."),Rr.Undefined,t)}t.isInteger=-1===n.indexOf(".")}return h(t,!0)}}(e)||function(e){switch(o.getToken()){case 7:return h(new Yr(e,o.getTokenOffset()),!0);case 8:return h(new Qr(e,!0,o.getTokenOffset()),!0);case 9:return h(new Qr(e,!1,o.getTokenOffset()),!0);default:return}}(e)}var p=void 0;return 17!==a()&&((p=g(p))?17!==o.getToken()&&l(Jr("End of file expected","End of file expected."),Rr.Undefined):l(Jr("Invalid symbol","Expected a JSON object, array or literal."),Rr.Undefined)),new fi(p,n,s)}var gi=Wr(),pi=function(){function e(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=Promise),void 0===r&&(r={}),this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r}return e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--){var n=this.contributions[t].resolveCompletion;if(n){var r=n(e);if(r)return r}}return this.promiseConstructor.resolve(e)},e.prototype.doComplete=function(e,t,n){var r=this,i={items:[],isIncomplete:!1},o=e.getText(),s=e.offsetAt(t),a=n.getNodeFromOffset(s,!0);if(this.isInComment(e,a?a.offset:0,s))return Promise.resolve(i);if(a&&s===a.offset+a.length&&s>0){var u=o[s-1];("object"===a.type&&"}"===u||"array"===a.type&&"]"===u)&&(a=a.parent)}var l,c=this.getCurrentWord(e,s);if(!a||"string"!==a.type&&"number"!==a.type&&"boolean"!==a.type&&"null"!==a.type){var h=s-c.length;h>0&&'"'===o[h-1]&&h--,l=yn.create(e.positionAt(h),t)}else l=yn.create(e.positionAt(a.offset),e.positionAt(a.offset+a.length));var f={},d={add:function(e){var t=e.label,n=f[t];if(n)n.documentation||(n.documentation=e.documentation),n.detail||(n.detail=e.detail);else{if((t=t.replace(/[\n]/g,"↵")).length>60){var r=t.substr(0,57).trim()+"...";f[r]||(t=r)}l&&void 0!==e.insertText&&(e.textEdit=Mn.replace(l,e.insertText)),e.label=t,f[t]=e,i.items.push(e)}},setAsIncomplete:function(){i.isIncomplete=!0},error:function(e){},log:function(e){},getNumberOfProposals:function(){return i.items.length}};return this.schemaService.getSchemaForResource(e.uri,n).then((function(t){var u=[],h=!0,m="",g=void 0;if(a&&"string"===a.type){var p=a.parent;p&&"property"===p.type&&p.keyNode===a&&(h=!p.valueNode,g=p,m=o.substr(a.offset+1,a.length-2),p&&(a=p.parent))}if(a&&"object"===a.type){if(a.offset===s)return i;a.properties.forEach((function(e){g&&g===e||(f[e.keyNode.value]=hr.create("__"))}));var v="";h&&(v=r.evaluateSeparatorAfter(e,e.offsetAt(l.end))),t?r.getPropertyCompletions(t,n,a,h,v,d):r.getSchemaLessPropertyCompletions(n,a,m,d);var y=ci(a);r.contributions.forEach((function(t){var n=t.collectPropertyCompletions(e.uri,y,c,h,""===v,d);n&&u.push(n)})),!t&&c.length>0&&'"'!==o.charAt(s-c.length-1)&&(d.add({kind:sr.Property,label:r.getLabelForValue(c),insertText:r.getInsertTextForProperty(c,void 0,!1,v),insertTextFormat:ar.Snippet,documentation:""}),d.setAsIncomplete())}var b={};return t?r.getValueCompletions(t,n,a,s,e,d,b):r.getSchemaLessValueCompletions(n,a,s,e,d),r.contributions.length>0&&r.getContributedValueCompletions(n,a,s,e,d,u),r.promiseConstructor.all(u).then((function(){if(0===d.getNumberOfProposals()){var t=s;!a||"string"!==a.type&&"number"!==a.type&&"boolean"!==a.type&&"null"!==a.type||(t=a.offset+a.length);var n=r.evaluateSeparatorAfter(e,t);r.addFillerValueCompletions(b,n,d)}return i}))}))},e.prototype.getPropertyCompletions=function(e,t,n,r,i,o){var s=this;t.getMatchingSchemas(e.schema,n.offset).forEach((function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach((function(e){var n=t[e];if("object"==typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var a={kind:sr.Property,label:e,insertText:s.getInsertTextForProperty(e,n,r,i),insertTextFormat:ar.Snippet,filterText:s.getFilterTextForValue(e),documentation:s.fromMarkup(n.markdownDescription)||n.description||""};void 0!==n.suggestSortText&&(a.sortText=n.suggestSortText),a.insertText&&Yn(a.insertText,"$1"+i)&&(a.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(a)}}));var a=e.schema.propertyNames;if("object"==typeof a&&!a.deprecationMessage&&!a.doNotSuggest){var u=function(e,t){void 0===t&&(t=void 0);var n={kind:sr.Property,label:e,insertText:s.getInsertTextForProperty(e,void 0,r,i),insertTextFormat:ar.Snippet,filterText:s.getFilterTextForValue(e),documentation:t||s.fromMarkup(a.markdownDescription)||a.description||""};void 0!==a.suggestSortText&&(n.sortText=a.suggestSortText),n.insertText&&Yn(n.insertText,"$1"+i)&&(n.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(n)};if(a.enum)for(var l=0;l<a.enum.length;l++){var c=void 0;a.markdownEnumDescriptions&&l<a.markdownEnumDescriptions.length?c=s.fromMarkup(a.markdownEnumDescriptions[l]):a.enumDescriptions&&l<a.enumDescriptions.length&&(c=a.enumDescriptions[l]),u(a.enum[l],c)}a.const&&u(a.const)}}}))},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var i=this,o=function(e){e.properties.forEach((function(e){var t=e.keyNode.value;r.add({kind:sr.Property,label:t,insertText:i.getInsertTextForValue(t,""),insertTextFormat:ar.Snippet,filterText:i.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){var s=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e!==t.parent&&e.keyNode.value===s&&e.valueNode&&"object"===e.valueNode.type&&o(e.valueNode),!0}))}else"array"===t.parent.type&&t.parent.items.forEach((function(e){"object"===e.type&&e!==t&&o(e)}));else"object"===t.type&&r.add({kind:sr.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:ar.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,n,r,i){var o=this,s=n;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(s=t.offset+t.length,t=t.parent),!t)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:ar.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:ar.Snippet,documentation:""});var a=this.evaluateSeparatorAfter(r,s),u=function(e){e.parent&&!hi(e.parent,n,!0)&&i.add({kind:o.getSuggestionKind(e.type),label:o.getLabelTextForMatchingNode(e,r),insertText:o.getInsertTextForMatchingNode(e,r,a),insertTextFormat:ar.Snippet,documentation:""}),"boolean"===e.type&&o.addBooleanValueCompletion(!e.value,a,i)};if("property"===t.type&&n>(t.colonOffset||0)){var l=t.valueNode;if(l&&(n>l.offset+l.length||"object"===l.type||"array"===l.type))return;var c=t.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===c&&e.valueNode&&u(e.valueNode),!0})),"$schema"===c&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(a,i)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var h=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===h&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(u),!0}))}else t.items.forEach(u)},e.prototype.getValueCompletions=function(e,t,n,r,i,o,s){var a=r,u=void 0,l=void 0;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(a=n.offset+n.length,l=n,n=n.parent),n){if("property"===n.type&&r>(n.colonOffset||0)){var c=n.valueNode;if(c&&r>c.offset+c.length)return;u=n.keyNode.value,n=n.parent}if(n&&(void 0!==u||"array"===n.type)){for(var h=this.evaluateSeparatorAfter(i,a),f=0,d=t.getMatchingSchemas(e.schema,n.offset,l);f<d.length;f++){var m=d[f];if(m.node===n&&!m.inverted&&m.schema){if("array"===n.type&&m.schema.items)if(Array.isArray(m.schema.items)){var g=this.findItemAtOffset(n,i,r);g<m.schema.items.length&&this.addSchemaValueCompletions(m.schema.items[g],h,o,s)}else this.addSchemaValueCompletions(m.schema.items,h,o,s);if(void 0!==u){var p=!1;if(m.schema.properties)(_=m.schema.properties[u])&&(p=!0,this.addSchemaValueCompletions(_,h,o,s));if(m.schema.patternProperties&&!p)for(var v=0,y=Object.keys(m.schema.patternProperties);v<y.length;v++){var b=y[v];if(Qn(b).test(u)){p=!0;var _=m.schema.patternProperties[b];this.addSchemaValueCompletions(_,h,o,s)}}if(m.schema.additionalProperties&&!p){_=m.schema.additionalProperties;this.addSchemaValueCompletions(_,h,o,s)}}}}"$schema"!==u||n.parent||this.addDollarSchemaCompletions(h,o),s.boolean&&(this.addBooleanValueCompletion(!0,h,o),this.addBooleanValueCompletion(!1,h,o)),s.null&&this.addNullValueCompletion(h,o)}}else this.addSchemaValueCompletions(e.schema,"",o,s)},e.prototype.getContributedValueCompletions=function(e,t,n,r,i,o){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),t&&"property"===t.type&&n>(t.colonOffset||0)){var s=t.keyNode.value,a=t.valueNode;if((!a||n<=a.offset+a.length)&&t.parent){var u=ci(t.parent);this.contributions.forEach((function(e){var t=e.collectValueCompletions(r.uri,u,s,i);t&&o.push(t)}))}}}else this.contributions.forEach((function(e){var t=e.collectDefaultCompletions(r.uri,i);t&&o.push(t)}))},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var i=this;"object"==typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.anyOf)&&e.anyOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.oneOf)&&e.oneOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})))},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var i=this;void 0===r&&(r=0);var o=!1;if(Jn(e.default)){for(var s=e.type,a=e.default,u=r;u>0;u--)a=[a],s="array";n.add({kind:this.getSuggestionKind(s),label:this.getLabelForValue(a),insertText:this.getInsertTextForValue(a,t),insertTextFormat:ar.Snippet,detail:gi("json.suggest.default","Default value")}),o=!0}Array.isArray(e.examples)&&e.examples.forEach((function(s){for(var a=e.type,u=s,l=r;l>0;l--)u=[u],a="array";n.add({kind:i.getSuggestionKind(a),label:i.getLabelForValue(u),insertText:i.getInsertTextForValue(u,t),insertTextFormat:ar.Snippet}),o=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((function(s){var a,u,l=e.type,c=s.body,h=s.label;if(Jn(c)){e.type;for(var f=r;f>0;f--)c=[c],"array";a=i.getInsertTextForSnippetValue(c,t),u=i.getFilterTextForSnippetValue(c),h=h||i.getLabelForSnippetValue(c)}else{if("string"!=typeof s.bodyText)return;var d="",m="",g="";for(f=r;f>0;f--)d=d+g+"[\n",m=m+"\n"+g+"]",g+="\t",l="array";a=d+g+s.bodyText.split("\n").join("\n"+g)+m+t,h=h||a,u=a.replace(/[\n]/g,"")}n.add({kind:i.getSuggestionKind(l),label:h,documentation:i.fromMarkup(s.markdownDescription)||s.description,insertText:a,insertTextFormat:ar.Snippet,filterText:u}),o=!0})),!o&&"object"==typeof e.items&&!Array.isArray(e.items)&&r<5&&this.addDefaultValueCompletions(e.items,t,n,r+1)},e.prototype.addEnumValueCompletions=function(e,t,n){if(Jn(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:ar.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(var r=0,i=e.enum.length;r<i;r++){var o=e.enum[r],s=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?s=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(s=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(o),insertText:this.getInsertTextForValue(o,t),insertTextFormat:ar.Snippet,documentation:s})}},e.prototype.collectTypes=function(e,t){if(!Array.isArray(e.enum)&&!Jn(e.const)){var n=e.type;Array.isArray(n)?n.forEach((function(e){return t[e]=!0})):n&&(t[n]=!0)}},e.prototype.addFillerValueCompletions=function(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:ar.Snippet,detail:gi("defaults.object","New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:ar.Snippet,detail:gi("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:ar.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:ar.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this;this.schemaService.getRegisteredSchemaIds((function(e){return"http"===e||"https"===e})).forEach((function(r){return t.add({kind:sr.Module,label:n.getLabelForValue(r),filterText:n.getFilterTextForValue(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:ar.Snippet,documentation:""})}))},e.prototype.getLabelForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){return function e(t,n,r){if(null!==t&&"object"==typeof t){var i=n+"\t";if(Array.isArray(t)){if(0===t.length)return"[]";for(var o="[\n",s=0;s<t.length;s++)o+=i+e(t[s],i,r),s<t.length-1&&(o+=","),o+="\n";return o+=n+"]"}var a=Object.keys(t);if(0===a.length)return"{}";for(o="{\n",s=0;s<a.length;s++){var u=a[s];o+=i+JSON.stringify(u)+": "+e(t[u],i,r),s<a.length-1&&(o+=","),o+="\n"}return o+=n+"}"}return r(t)}(e,"",(function(e){return"string"==typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)}))+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),'"${1:'+(n=this.getInsertTextForPlainText(n))+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:void 0}if(!e)return sr.Value;switch(e){case"string":return sr.Value;case"object":return sr.Module;case"property":return sr.Property;default:return sr.Value}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var i=this.getInsertTextForValue(e,"");if(!n)return i;var o,s=i+": ",a=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){var u=t.defaultSnippets[0].body;Jn(u)&&(o=this.getInsertTextForSnippetValue(u,""))}a+=t.defaultSnippets.length}if(t.enum&&(o||1!==t.enum.length||(o=this.getInsertTextForGuessedValue(t.enum[0],"")),a+=t.enum.length),Jn(t.default)&&(o||(o=this.getInsertTextForGuessedValue(t.default,"")),a++),Array.isArray(t.examples)&&t.examples.length&&(o||(o=this.getInsertTextForGuessedValue(t.examples[0],"")),a+=t.examples.length),0===a){var l=Array.isArray(t.type)?t.type[0]:t.type;switch(l||(t.properties?l="object":t.items&&(l="array")),l){case"boolean":o="$1";break;case"string":o='"$1"';break;case"object":o="{$1}";break;case"array":o="[$1]";break;case"number":case"integer":o="${1:0}";break;case"null":o="${1:null}";break;default:return i}}}return(!o||a>1)&&(o="$1"),s+o+r},e.prototype.getCurrentWord=function(e,t){for(var n=t-1,r=e.getText();n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var n=Bn(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(e,t,n){for(var r=Bn(t.getText(),!0),i=e.items,o=i.length-1;o>=0;o--){var s=i[o];if(n>s.offset+s.length)return r.setPosition(s.offset+s.length),5===r.scan()&&n>=r.getTokenOffset()+r.getTokenLength()?o+1:o;if(n>=s.offset)return o}return 0},e.prototype.isInComment=function(e,t,n){var r=Bn(e.getText(),!1);r.setPosition(t);for(var i=r.scan();17!==i&&r.getTokenOffset()+r.getTokenLength()<n;)i=r.scan();return(12===i||13===i)&&r.getTokenOffset()<=n},e.prototype.fromMarkup=function(e){if(e&&this.doesSupportMarkdown())return{kind:ir.Markdown,value:e}},e.prototype.doesSupportMarkdown=function(){if(!Jn(this.supportsMarkdown)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(ir.Markdown)}return this.supportsMarkdown},e.prototype.doesSupportsCommitCharacters=function(){if(!Jn(this.supportsCommitCharacters)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsCommitCharacters=e&&e.completionItem&&!!e.completionItem.commitCharactersSupport}return this.supportsCommitCharacters},e}(),vi=function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}return e.prototype.doHover=function(e,t,n){var r=e.offsetAt(t),i=n.getNodeFromOffset(r);if(!i||("object"===i.type||"array"===i.type)&&r>i.offset+1&&r<i.offset+i.length-1)return this.promise.resolve(null);var o=i;if("string"===i.type){var s=i.parent;if(s&&"property"===s.type&&s.keyNode===i&&!(i=s.valueNode))return this.promise.resolve(null)}for(var a=yn.create(e.positionAt(o.offset),e.positionAt(o.offset+o.length)),u=function(e){return{contents:e,range:a}},l=ci(i),c=this.contributions.length-1;c>=0;c--){var h=this.contributions[c].getInfoContribution(e.uri,l);if(h)return h.then((function(e){return u(e)}))}return this.schemaService.getSchemaForResource(e.uri,n).then((function(e){if(e&&i){var t=n.getMatchingSchemas(e.schema,i.offset),r=void 0,o=void 0,s=void 0,a=void 0;t.every((function(e){if(e.node===i&&!e.inverted&&e.schema&&(r=r||e.schema.title,o=o||e.schema.markdownDescription||yi(e.schema.description),e.schema.enum)){var t=e.schema.enum.indexOf(li(i));e.schema.markdownEnumDescriptions?s=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(s=yi(e.schema.enumDescriptions[t])),s&&"string"!=typeof(a=e.schema.enum[t])&&(a=JSON.stringify(a))}return!0}));var l="";return r&&(l=yi(r)),o&&(l.length>0&&(l+="\n\n"),l+=o),s&&(l.length>0&&(l+="\n\n"),l+="`"+function(e){if(-1!==e.indexOf("`"))return"`` "+e+" ``";return e}(a)+"`: "+s),u([l])}return null}))},e}();function yi(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3").replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}var bi=n("394c");function _i(e,t){if("string"!=typeof e)throw new TypeError("Expected a string");for(var n,r=String(e),i="",o=!!t&&!!t.extended,s=!!t&&!!t.globstar,a=!1,u=t&&"string"==typeof t.flags?t.flags:"",l=0,c=r.length;l<c;l++)switch(n=r[l]){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":i+="\\"+n;break;case"?":if(o){i+=".";break}case"[":case"]":if(o){i+=n;break}case"{":if(o){a=!0,i+="(";break}case"}":if(o){a=!1,i+=")";break}case",":if(a){i+="|";break}i+="\\"+n;break;case"*":for(var h=r[l-1],f=1;"*"===r[l+1];)f++,l++;var d=r[l+1];if(s)f>1&&("/"===h||void 0===h||"{"===h||","===h)&&("/"===d||void 0===d||","===d||"}"===d)?("/"===d?l++:"/"===h&&i.endsWith("\\/")&&(i=i.substr(0,i.length-2)),i+="((?:[^/]*(?:/|$))*)"):i+="([^/]*)";else i+=".*";break;default:i+=n}return u&&~u.indexOf("g")||(i="^"+i+"$"),new RegExp(i,u)}var Ci=Wr(),Si=function(){function e(e,t){this.globWrappers=[];try{for(var n=0,r=e;n<r.length;n++){var i=r[n],o="!"!==i[0];o||(i=i.substring(1)),i.length>0&&("/"===i[0]&&(i=i.substring(1)),this.globWrappers.push({regexp:_i("**/"+i,{extended:!0,globstar:!0}),include:o}))}this.uris=t}catch(e){this.globWrappers.length=0,this.uris=[]}}return e.prototype.matchesPattern=function(e){for(var t=!1,n=0,r=this.globWrappers;n<r.length;n++){var i=r[n],o=i.regexp,s=i.include;o.test(e)&&(t=s)}return t},e.prototype.getURIs=function(){return this.uris},e}(),Ai=function(){function e(e,t,n){this.service=e,this.url=t,this.dependencies={},n&&(this.unresolvedSchema=this.service.promise.resolve(new Ei(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.url)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((function(t){return e.service.resolveSchemaContent(t,e.url,e.dependencies)}))),this.resolvedSchema},e.prototype.clearSchema=function(){this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies={}},e}(),Ei=function(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t},Ni=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){var t=this.getSectionRecursive(e,this.schema);if(t)return oi(t)},e.prototype.getSectionRecursive=function(e,t){if(!t||"boolean"==typeof t||0===e.length)return t;var n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(var r=0,i=Object.keys(t.patternProperties);r<i.length;r++){var o=i[r];if(Qn(o).test(n))return this.getSectionRecursive(e,t.patternProperties[o])}else{if("object"==typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){var s=parseInt(n,10);if(!isNaN(s)&&t.items[s])return this.getSectionRecursive(e,t.items[s])}else if(t.items)return this.getSectionRecursive(e,t.items)}},e}(),wi=function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}return e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter((function(t){var n=bi.a.parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){var t=this;this.cachedSchemaForResource=void 0;for(var n=!1,r=[e=Li(e)],i=Object.keys(this.schemasById).map((function(e){return t.schemasById[e]}));r.length;)for(var o=r.pop(),s=0;s<i.length;s++){var a=i[s];a&&(a.url===o||a.dependencies[o])&&(a.url!==o&&r.push(a.url),a.clearSchema(),i[s]=void 0,n=!0)}return n},e.prototype.setSchemaContributions=function(e){if(e.schemas){var t=e.schemas;for(var n in t){var r=Li(n);this.contributionSchemas[r]=this.addSchemaHandle(r,t[n])}}if(Array.isArray(e.schemaAssociations))for(var i=0,o=e.schemaAssociations;i<o.length;i++){var s=o[i],a=s.uris.map(Li),u=this.addFilePatternAssociation(s.pattern,a);this.contributionAssociations.push(u)}},e.prototype.addSchemaHandle=function(e,t){var n=new Ai(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.addFilePatternAssociation=function(e,t){var n=new Si(e,t);return this.filePatternAssociations.push(n),n},e.prototype.registerExternalSchema=function(e,t,n){var r=Li(e);return this.registeredSchemasIds[r]=!0,this.cachedSchemaForResource=void 0,t&&this.addFilePatternAssociation(t,[r]),n?this.addSchemaHandle(r,n):this.getOrAddSchemaHandle(r)},e.prototype.clearExternalSchemas=function(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0,this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t=0,n=this.contributionAssociations;t<n.length;t++){var r=n[t];this.filePatternAssociations.push(r)}},e.prototype.getResolvedSchema=function(e){var t=Li(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(void 0)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=Ci("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",Ti(e));return this.promise.resolve(new Ei({},[t]))}return this.requestService(e).then((function(t){if(!t){var n=Ci("json.schema.nocontent","Unable to load schema from '{0}': No content.",Ti(e));return new Ei({},[n])}var r,i=[];r=qn(t,i);var o=i.length?[Ci("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",Ti(e),i[0].offset)]:[];return new Ei(r,o)}),(function(t){var n=t.toString(),r=t.toString().split("Error: ");return r.length>1&&(n=r[1]),Yn(n,".")&&(n=n.substr(0,n.length-1)),new Ei({},[Ci("json.schema.nocontent","Unable to load schema from '{0}': {1}.",Ti(e),n)])}))},e.prototype.resolveSchemaContent=function(e,t,n){var r=this,i=e.errors.slice(0),o=e.schema;if(o.$schema){var s=Li(o.$schema);if("http://json-schema.org/draft-03/schema"===s)return this.promise.resolve(new Ni({},[Ci("json.schema.draft03.notsupported","Draft-03 schemas are not supported.")]));"https://json-schema.org/draft/2019-09/schema"===s&&i.push(Ci("json.schema.draft201909.notsupported","Draft 2019-09 schemas are not yet fully supported."))}var a=this.contextService,u=function(e,t,n,r){var o=r?decodeURIComponent(r):void 0,s=function(e,t){if(!t)return e;var n=e;return"/"===t[0]&&(t=t.substr(1)),t.split("/").some((function(e){return e=e.replace(/~1/g,"/").replace(/~0/g,"~"),!(n=n[e])})),n}(t,o);if(s)for(var a in s)s.hasOwnProperty(a)&&!e.hasOwnProperty(a)&&(e[a]=s[a]);else i.push(Ci("json.schema.invalidref","$ref '{0}' in '{1}' can not be resolved.",o,n))},l=function(e,t,n,o,s){a&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/\/.*/.test(t)&&(t=a.resolveRelativePath(t,o)),t=Li(t);var l=r.getOrAddSchemaHandle(t);return l.getUnresolvedSchema().then((function(r){if(s[t]=!0,r.errors.length){var o=n?t+"#"+n:t;i.push(Ci("json.schema.problemloadingref","Problems loading reference '{0}': {1}",o,r.errors[0]))}return u(e,r.schema,t,n),c(e,r.schema,t,l.dependencies)}))},c=function(e,t,n,i){if(!e||"object"!=typeof e)return Promise.resolve(null);for(var o=[e],s=[],a=[],c=function(e){for(var r=[];e.$ref;){var s=e.$ref,c=s.split("#",2);if(delete e.$ref,c[0].length>0)return void a.push(l(e,c[0],c[1],n,i));-1===r.indexOf(s)&&(u(e,t,n,c[1]),r.push(s))}!function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];"object"==typeof i&&o.push(i)}}(e.items,e.additionalItems,e.additionalProperties,e.not,e.contains,e.propertyNames,e.if,e.then,e.else),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if("object"==typeof i)for(var s in i){var a=s,u=i[a];"object"==typeof u&&o.push(u)}}}(e.definitions,e.properties,e.patternProperties,e.dependencies),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if(Array.isArray(i))for(var s=0,a=i;s<a.length;s++){var u=a[s];"object"==typeof u&&o.push(u)}}}(e.anyOf,e.allOf,e.oneOf,e.items)};o.length;){var h=o.pop();s.indexOf(h)>=0||(s.push(h),c(h))}return r.promise.all(a)};return c(o,o,t,n).then((function(e){return new Ni(o,i)}))},e.prototype.getSchemaForResource=function(e,t){if(t&&t.root&&"object"===t.root.type){var n=t.root.properties.filter((function(e){return"$schema"===e.keyNode.value&&e.valueNode&&"string"===e.valueNode.type}));if(n.length>0){var r=n[0].valueNode;if(r&&"string"===r.type){var i=li(r);if(i&&Zn(i,".")&&this.contextService&&(i=this.contextService.resolveRelativePath(i,e)),i){var o=Li(i);return this.getOrAddSchemaHandle(o).getResolvedSchema()}}}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;for(var s=Object.create(null),a=[],u=function(e){try{return bi.a.parse(e).with({fragment:null,query:null}).toString()}catch(t){return e}}(e),l=0,c=this.filePatternAssociations;l<c.length;l++){var h=c[l];if(h.matchesPattern(u))for(var f=0,d=h.getURIs();f<d.length;f++){var m=d[f];s[m]||(a.push(m),s[m]=!0)}}var g=a.length>0?this.createCombinedSchema(e,a).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:g},g},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((function(e){return{$ref:e}}))};return this.addSchemaHandle(n,r)},e.prototype.getMatchingSchemas=function(e,t,n){if(n){var r=n.id||"schemaservice://untitled/matchingSchemas/"+xi++;return this.resolveSchemaContent(new Ei(n),r,{}).then((function(e){return t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted}))}))}return this.getSchemaForResource(e.uri,t).then((function(e){return e?t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted})):[]}))},e}(),xi=0;function Li(e){try{return bi.a.parse(e).toString()}catch(t){return e}}function Ti(e){try{var t=bi.a.parse(e);if("file"===t.scheme)return t.fsPath}catch(e){}return e}var ki=Wr(),Oi=function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}return e.prototype.configure=function(e){e&&(this.validationEnabled=!1!==e.validate,this.commentSeverity=e.allowComments?void 0:Ln.Error)},e.prototype.doValidation=function(e,t,n,r){var i=this;if(!this.validationEnabled)return this.promise.resolve([]);var o=[],s={},a=function(e){var t=e.range.start.line+" "+e.range.start.character+" "+e.message;s[t]||(s[t]=!0,o.push(e))},u=function(r){var s=(null==n?void 0:n.trailingCommas)?Mi(n.trailingCommas):Ln.Error,u=(null==n?void 0:n.comments)?Mi(n.comments):i.commentSeverity,l=(null==n?void 0:n.schemaValidation)?Mi(n.schemaValidation):Ln.Warning,c=(null==n?void 0:n.schemaRequest)?Mi(n.schemaRequest):Ln.Warning;if(r){if(r.errors.length&&t.root&&c){var h=t.root,f="object"===h.type?h.properties[0]:void 0;if(f&&"$schema"===f.keyNode.value){var d=f.valueNode||f,m=yn.create(e.positionAt(d.offset),e.positionAt(d.offset+d.length));a(On.create(m,r.errors[0],c,Rr.SchemaResolveError))}else{m=yn.create(e.positionAt(h.offset),e.positionAt(h.offset+1));a(On.create(m,r.errors[0],c,Rr.SchemaResolveError))}}else if(l){var g=t.validate(e,r.schema,l);g&&g.forEach(a)}(function e(t){if(t&&"object"==typeof t){if(Xn(t.allowComments))return t.allowComments;if(t.allOf)for(var n=0,r=t.allOf;n<r.length;n++){var i=r[n],o=e(i);if(Xn(o))return o}}return})(r.schema)&&(u=void 0),function e(t){if(t&&"object"==typeof t){if(Xn(t.allowTrailingCommas))return t.allowTrailingCommas;var n=t;if(Xn(n.allowsTrailingCommas))return n.allowsTrailingCommas;if(t.allOf)for(var r=0,i=t.allOf;r<i.length;r++){var o=i[r],s=e(o);if(Xn(s))return s}}return}(r.schema)&&(s=void 0)}for(var p=0,v=t.syntaxErrors;p<v.length;p++){var y=v[p];if(y.code===Rr.TrailingComma){if("number"!=typeof s)continue;y.severity=s}a(y)}if("number"==typeof u){var b=ki("InvalidCommentToken","Comments are not permitted in JSON.");t.comments.forEach((function(e){a(On.create(e,b,u,Rr.CommentNotPermitted))}))}return o};if(r){var l=r.id||"schemaservice://untitled/"+Ii++;return this.jsonSchemaService.resolveSchemaContent(new Ei(r),l,{}).then((function(e){return u(e)}))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((function(e){return u(e)}))},e}(),Ii=0;function Mi(e){switch(e){case"error":return Ln.Error;case"warning":return Ln.Warning;case"ignore":return}}function Pi(e){return e<48?0:e<=57?e-48:(e<97&&(e+=32),e>=97&&e<=102?e-97+10:0)}function Vi(e){if("#"===e[0])switch(e.length){case 4:return{red:17*Pi(e.charCodeAt(1))/255,green:17*Pi(e.charCodeAt(2))/255,blue:17*Pi(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*Pi(e.charCodeAt(1))/255,green:17*Pi(e.charCodeAt(2))/255,blue:17*Pi(e.charCodeAt(3))/255,alpha:17*Pi(e.charCodeAt(4))/255};case 7:return{red:(16*Pi(e.charCodeAt(1))+Pi(e.charCodeAt(2)))/255,green:(16*Pi(e.charCodeAt(3))+Pi(e.charCodeAt(4)))/255,blue:(16*Pi(e.charCodeAt(5))+Pi(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*Pi(e.charCodeAt(1))+Pi(e.charCodeAt(2)))/255,green:(16*Pi(e.charCodeAt(3))+Pi(e.charCodeAt(4)))/255,blue:(16*Pi(e.charCodeAt(5))+Pi(e.charCodeAt(6)))/255,alpha:(16*Pi(e.charCodeAt(7))+Pi(e.charCodeAt(8)))/255}}}var Ri=function(){function e(e){this.schemaService=e}return e.prototype.findDocumentSymbols=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var o=n.resultLimit||Number.MAX_VALUE,s=e.uri;if(("vscode://defaultsettings/keybindings.json"===s||Yn(s.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var a=[],u=0,l=i.items;u<l.length;u++){var c=l[u];if("object"===c.type)for(var h=0,f=c.properties;h<f.length;h++){var d=f[h];if("key"===d.keyNode.value&&d.valueNode){var m=_n.create(e.uri,Fi(e,c));if(a.push({name:li(d.valueNode),kind:br.Function,location:m}),--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(s),a}}}return a}for(var g=[{node:i,containerName:""}],p=0,v=!1,y=[],b=function(t,n){"array"===t.type?t.items.forEach((function(e){e&&g.push({node:e,containerName:n})})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var s=_n.create(e.uri,Fi(e,t)),a=n?n+"."+t.keyNode.value:t.keyNode.value;y.push({name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),location:s,containerName:n}),g.push({node:i,containerName:a})}else v=!0}))};p<g.length;){var _=g[p++];b(_.node,_.containerName)}return v&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(s),y},e.prototype.findDocumentSymbols2=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var o=n.resultLimit||Number.MAX_VALUE,s=e.uri;if(("vscode://defaultsettings/keybindings.json"===s||Yn(s.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var a=[],u=0,l=i.items;u<l.length;u++){var c=l[u];if("object"===c.type)for(var h=0,f=c.properties;h<f.length;h++){var d=f[h];if("key"===d.keyNode.value&&d.valueNode){var m=Fi(e,c),g=Fi(e,d.keyNode);if(a.push({name:li(d.valueNode),kind:br.Function,range:m,selectionRange:g}),--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(s),a}}}return a}for(var p=[],v=[{node:i,result:p}],y=0,b=!1,_=function(t,n){"array"===t.type?t.items.forEach((function(t,i){if(t)if(o>0){o--;var s=Fi(e,t),a=s,u={name:String(i),kind:r.getSymbolKind(t.type),range:s,selectionRange:a,children:[]};n.push(u),v.push({result:u.children,node:t})}else b=!0})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var s=Fi(e,t),a=Fi(e,t.keyNode),u=[],l={name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),range:s,selectionRange:a,children:u,detail:r.getDetail(i)};n.push(l),v.push({result:u,node:i})}else b=!0}))};y<v.length;){var C=v[y++];_(C.node,C.result)}return b&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(s),p},e.prototype.getSymbolKind=function(e){switch(e){case"object":return br.Module;case"string":return br.String;case"number":return br.Number;case"array":return br.Array;case"boolean":return br.Boolean;default:return br.Variable}},e.prototype.getKeyLabel=function(e){var t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"↵")),t&&t.trim()?t:'"'+t+'"'},e.prototype.getDetail=function(e){if(e)return"boolean"===e.type||"number"===e.type||"null"===e.type||"string"===e.type?String(e.value):"array"===e.type?e.children.length?void 0:"[]":"object"===e.type?e.children.length?void 0:"{}":void 0},e.prototype.findDocumentColors=function(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then((function(r){var i=[];if(r)for(var o=n&&"number"==typeof n.resultLimit?n.resultLimit:Number.MAX_VALUE,s={},a=0,u=t.getMatchingSchemas(r.schema);a<u.length;a++){var l=u[a];if(!l.inverted&&l.schema&&("color"===l.schema.format||"color-hex"===l.schema.format)&&l.node&&"string"===l.node.type){var c=String(l.node.offset);if(!s[c]){var h=Vi(li(l.node));if(h){var f=Fi(e,l.node);i.push({color:h,range:f})}if(s[c]=!0,--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),i}}}return i}))},e.prototype.getColorPresentations=function(e,t,n,r){var i,o=[],s=Math.round(255*n.red),a=Math.round(255*n.green),u=Math.round(255*n.blue);function l(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}return i=1===n.alpha?"#"+l(s)+l(a)+l(u):"#"+l(s)+l(a)+l(u)+l(Math.round(255*n.alpha)),o.push({label:i,textEdit:Mn.replace(r,JSON.stringify(i))}),o},e}();function Fi(e,t){return yn.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}var Di=Wr(),Ki={schemaAssociations:[],schemas:{"http://json-schema.org/schema#":{$ref:"http://json-schema.org/draft-07/schema#"},"http://json-schema.org/draft-04/schema#":{$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},ji={id:Di("schema.json.id","A unique identifier for the schema."),$schema:Di("schema.json.$schema","The schema to verify this document against."),title:Di("schema.json.title","A descriptive title of the element."),description:Di("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:Di("schema.json.default","A default value. Used by suggestions."),multipleOf:Di("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:Di("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:Di("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:Di("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:Di("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:Di("schema.json.maxLength","The maximum length of a string."),minLength:Di("schema.json.minLength","The minimum length of a string."),pattern:Di("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:Di("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:Di("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:Di("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:Di("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:Di("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:Di("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:Di("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:Di("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:Di("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:Di("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:Di("schema.json.properties","A map of property names to schemas for each property."),patternProperties:Di("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:Di("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:Di("schema.json.enum","The set of literal values that are valid."),type:Di("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:Di("schema.json.format","Describes the format expected for the value."),allOf:Di("schema.json.allOf","An array of schemas, all of which must match."),anyOf:Di("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:Di("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:Di("schema.json.not","A schema which must not match."),$id:Di("schema.json.$id","A unique identifier for the schema."),$ref:Di("schema.json.$ref","Reference a definition hosted on any location."),$comment:Di("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:Di("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:Di("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:Di("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:Di("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:Di("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:Di("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:Di("schema.json.contentEncoding","Describes the content encoding of a string property."),if:Di("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:Di("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:Di("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(var Ui in Ki.schemas){var Bi=Ki.schemas[Ui];for(var qi in Bi.properties){var $i=Bi.properties[qi];"boolean"==typeof $i&&($i=Bi.properties[qi]={});var Wi=ji[qi];Wi&&($i.description=Wi)}}function Hi(e,t){var n=[],r=[],i=[],o=-1,s=Bn(e.getText(),!1),a=s.scan();function u(e){n.push(e),r.push(i.length)}for(;17!==a;){switch(a){case 1:case 3:var l={startLine:f=e.positionAt(s.getTokenOffset()).line,endLine:f,kind:1===a?"object":"array"};i.push(l);break;case 2:case 4:var c=2===a?"object":"array";if(i.length>0&&i[i.length-1].kind===c){l=i.pop();var h=e.positionAt(s.getTokenOffset()).line;l&&h>l.startLine+1&&o!==l.startLine&&(l.endLine=h-1,u(l),o=l.startLine)}break;case 13:var f=e.positionAt(s.getTokenOffset()).line,d=e.positionAt(s.getTokenOffset()+s.getTokenLength()).line;1===s.getTokenError()&&f+1<e.lineCount?s.setPosition(e.offsetAt(vn.create(f+1,0))):f<d&&(u({startLine:f,endLine:d,kind:Nn.Comment}),o=f);break;case 12:var m=e.getText().substr(s.getTokenOffset(),s.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(m){h=e.positionAt(s.getTokenOffset()).line;if(m[1]){l={startLine:h,endLine:h,kind:Nn.Region};i.push(l)}else{for(var g=i.length-1;g>=0&&i[g].kind!==Nn.Region;)g--;if(g>=0){l=i[g];i.length=g,h>l.startLine&&o!==l.startLine&&(l.endLine=h,u(l),o=l.startLine)}}}}a=s.scan()}var p=t&&t.rangeLimit;if("number"!=typeof p||n.length<=p)return n;t&&t.onRangeLimitExceeded&&t.onRangeLimitExceeded(e.uri);for(var v=[],y=0,b=r;y<b.length;y++){(E=b[y])<30&&(v[E]=(v[E]||0)+1)}var _=0,C=0;for(g=0;g<v.length;g++){var S=v[g];if(S){if(S+_>p){C=g;break}_+=S}}var A=[];for(g=0;g<n.length;g++){var E;"number"==typeof(E=r[g])&&(E<C||E===C&&_++<p)&&A.push(n[g])}return A}function zi(e,t,n){function r(t,n){return yn.create(e.positionAt(t),e.positionAt(n))}var i=Bn(e.getText(),!0);function o(e,t){return i.setPosition(e),i.scan()===t?i.getTokenOffset()+i.getTokenLength():-1}return t.map((function(t){for(var i=e.offsetAt(t),s=n.getNodeFromOffset(i,!0),a=[];s;){switch(s.type){case"string":case"object":case"array":var u=s.offset+1,l=s.offset+s.length-1;u<l&&i>=u&&i<=l&&a.push(r(u,l)),a.push(r(s.offset,s.offset+s.length));break;case"number":case"boolean":case"null":case"property":a.push(r(s.offset,s.offset+s.length))}if("property"===s.type||s.parent&&"array"===s.parent.type){var c=o(s.offset+s.length,5);-1!==c&&a.push(r(s.offset,c))}s=s.parent}for(var h=void 0,f=a.length-1;f>=0;f--)h=Tr.create(a[f],h);return h||(h=Tr.create(yn.create(t,t))),h}))}function Gi(e,t){var n=[];return t.visit((function(r){var i;if("property"===r.type&&"$ref"===r.keyNode.value&&"string"===(null===(i=r.valueNode)||void 0===i?void 0:i.type)){var o=r.valueNode.value,s=function(e,t){var n=function(e){if("#"===e)return[];if("#"!==e[0]||"/"!==e[1])return null;return e.substring(2).split(/\//).map(Xi)}(t);if(!n)return null;return function e(t,n){if(!n)return null;if(0===t.length)return n;var r=t.shift();if(n&&"object"===n.type){var i=n.properties.find((function(e){return e.keyNode.value===r}));return i?e(t,i.valueNode):null}if(n&&"array"===n.type&&r.match(/^(0|[1-9][0-9]*)$/)){var o=Number.parseInt(r),s=n.items[o];return s?e(t,s):null}return null}(n,e.root)}(t,o);if(s){var a=e.positionAt(s.offset);n.push({target:e.uri+"#"+(a.line+1)+","+(a.character+1),range:Ji(e,r.valueNode)})}}return!0})),Promise.resolve(n)}function Ji(e,t){return yn.create(e.positionAt(t.offset+1),e.positionAt(t.offset+t.length-1))}function Xi(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function Zi(e){var t=e.promiseConstructor||Promise,n=new wi(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(Ki);var r=new pi(n,e.contributions,t,e.clientCapabilities),i=new vi(n,e.contributions,t),o=new Ri(n),s=new Oi(n,t);return{configure:function(e){n.clearExternalSchemas(),e.schemas&&e.schemas.forEach((function(e){n.registerExternalSchema(e.uri,e.fileMatch,e.schema)})),s.configure(e)},resetSchema:function(e){return n.onResourceChange(e)},doValidation:s.doValidation.bind(s),parseJSONDocument:function(e){return mi(e,{collectComments:!0})},newJSONDocument:function(e,t){return function(e,t){return void 0===t&&(t=[]),new fi(e,t,[])}(e,t)},getMatchingSchemas:n.getMatchingSchemas.bind(n),doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:o.findDocumentSymbols.bind(o),findDocumentSymbols2:o.findDocumentSymbols2.bind(o),findDocumentColors:o.findDocumentColors.bind(o),getColorPresentations:o.getColorPresentations.bind(o),doHover:i.doHover.bind(i),getFoldingRanges:Hi,getSelectionRanges:zi,findDefinition:function(){return Promise.resolve([])},findLinks:Gi,format:function(e,t,n){var r=void 0;if(t){var i=e.offsetAt(t.start);r={offset:i,length:e.offsetAt(t.end)-i}}var o={tabSize:n?n.tabSize:4,insertSpaces:!0===(null==n?void 0:n.insertSpaces),insertFinalNewline:!0===(null==n?void 0:n.insertFinalNewline),eol:"\n"};return function(e,t,n){return cn(e,t,n)}(e.getText(),r,o).map((function(t){return Mn.replace(yn.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length)),t.content)}))}}}var Yi,Qi=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))},eo=function(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};"undefined"!=typeof fetch&&(Yi=function(e){return fetch(e).then((function(e){return e.text()}))});var to=function(){function e(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=Zi({workspaceContext:{resolveRelativePath:function(e,t){return function(e,t){if(function(e){return e.charCodeAt(0)===no}(t)){var n=bi.a.parse(e),r=t.split("/");return n.with({path:io(r)}).toString()}return function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=bi.a.parse(e),i=r.path.split("/"),o=0,s=t;o<s.length;o++){var a=s[o];i.push.apply(i,a.split("/"))}return r.with({path:io(i)}).toString()}(e,t)}(t.substr(0,t.lastIndexOf("/")+1),e)}},schemaRequestService:t.enableSchemaRequest&&Yi}),this._languageService.configure(this._languageSettings)}return e.prototype.doValidation=function(e){return Qi(this,void 0,void 0,(function(){var t,n;return eo(this,(function(r){return(t=this._getTextDocument(e))?(n=this._languageService.parseJSONDocument(t),[2,this._languageService.doValidation(t,n,this._languageSettings)]):[2,Promise.resolve([])]}))}))},e.prototype.doComplete=function(e,t){return Qi(this,void 0,void 0,(function(){var n,r;return eo(this,(function(i){return n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n),[2,this._languageService.doComplete(n,t,r)]}))}))},e.prototype.doResolve=function(e){return Qi(this,void 0,void 0,(function(){return eo(this,(function(t){return[2,this._languageService.doResolve(e)]}))}))},e.prototype.doHover=function(e,t){return Qi(this,void 0,void 0,(function(){var n,r;return eo(this,(function(i){return n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n),[2,this._languageService.doHover(n,t,r)]}))}))},e.prototype.format=function(e,t,n){return Qi(this,void 0,void 0,(function(){var r,i;return eo(this,(function(o){return r=this._getTextDocument(e),i=this._languageService.format(r,t,n),[2,Promise.resolve(i)]}))}))},e.prototype.resetSchema=function(e){return Qi(this,void 0,void 0,(function(){return eo(this,(function(t){return[2,Promise.resolve(this._languageService.resetSchema(e))]}))}))},e.prototype.findDocumentSymbols=function(e){return Qi(this,void 0,void 0,(function(){var t,n,r;return eo(this,(function(i){return t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentSymbols(t,n),[2,Promise.resolve(r)]}))}))},e.prototype.findDocumentColors=function(e){return Qi(this,void 0,void 0,(function(){var t,n,r;return eo(this,(function(i){return t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentColors(t,n),[2,Promise.resolve(r)]}))}))},e.prototype.getColorPresentations=function(e,t,n){return Qi(this,void 0,void 0,(function(){var r,i,o;return eo(this,(function(s){return r=this._getTextDocument(e),i=this._languageService.parseJSONDocument(r),o=this._languageService.getColorPresentations(r,i,t,n),[2,Promise.resolve(o)]}))}))},e.prototype.getFoldingRanges=function(e,t){return Qi(this,void 0,void 0,(function(){var n,r;return eo(this,(function(i){return n=this._getTextDocument(e),r=this._languageService.getFoldingRanges(n,t),[2,Promise.resolve(r)]}))}))},e.prototype.getSelectionRanges=function(e,t){return Qi(this,void 0,void 0,(function(){var n,r,i;return eo(this,(function(o){return n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n),i=this._languageService.getSelectionRanges(n,t,r),[2,Promise.resolve(i)]}))}))},e.prototype._getTextDocument=function(e){for(var t=0,n=this._ctx.getMirrorModels();t<n.length;t++){var r=n[t];if(r.uri.toString()===e)return Vr.create(e,this._languageId,r.version,r.getValue())}return null},e}(),no="/".charCodeAt(0),ro=".".charCodeAt(0);function io(e){for(var t=[],n=0,r=e;n<r.length;n++){var i=r[n];0===i.length||1===i.length&&i.charCodeAt(0)===ro||(2===i.length&&i.charCodeAt(0)===ro&&i.charCodeAt(1)===ro?t.pop():t.push(i))}e.length>1&&0===e[e.length-1].length&&t.push("");var o=t.join("/");return 0===e[0].length&&(o="/"+o),o}self.onmessage=function(){on((function(e,t){return new to(e,t)}))}}});