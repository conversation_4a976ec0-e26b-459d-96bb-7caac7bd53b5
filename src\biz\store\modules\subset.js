import { getSubsetId,setSubsetId,getSubsetInfo,setSubsetInfo } from "@/utils/subset-util";
const subset = {
  state: {
    subsetId: getSubsetId() || null,
    subsetInfo:getSubsetInfo() || null,
  },
  actions: {
    /**
     * @description 设置数据子集信息
     * @param {Object} state vuex state
     * @param {Object} subsetInfo subsetId
     */
    async setSubsetInfo({
      state,
      dispatch,
      rootState
    }, subsetInfo) {
      const info = {...subsetInfo}
      setSubsetInfo(info)
      setSubsetId(info.subsetId)
      state.subsetInfo = info
    },
  }
}

export default subset