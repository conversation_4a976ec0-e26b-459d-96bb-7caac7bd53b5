export const queryDetailApi = (params) => {
    return {
        code: 200,
        data: {

        }
    }
}
// 新增，修改，删除导出过滤项属性配置（修改itemConfigJson参数的值）
export const updateConfig = (data) => {
  return {
      url: 'search/export-filter-item/update-config',
      method: 'POST',
      data
  }
}

// 分页数据
export const queryListApi = (params) => {
    return {
        url: 'search/export-filter-item/page',
        method: 'GET',
        params
    }
}

// 新增接口
export const addApi = (data) => {
    return {
        url: 'search/export-filter-item/insert-record',
        method: 'POST',
        data
    }
}

// 修改导出过滤项
export const updateApi = (data) => {
    return {
        url: 'search/export-filter-item/update-record',
        method: 'POST',
        data
    }
}

// 删除
export const deleteBatchApi = (data) => {
    return {
        url: 'search/export-filter-item/deletion',
        method: 'POST',
        data
    }
}
