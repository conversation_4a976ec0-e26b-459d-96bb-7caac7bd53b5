<template>
  <div class="rules-group-container">
    <div class="rules-group-body">
      <rules-group-logical v-show="false" :show-logical="showLogical" :logical-operator="curLogical"
        :logical-style-object="logicalStyleObject" @change-logical="toggleLogical" />
      <!-- <hos-button :disabled="queryBuilderObj.disabled" v-if="!showLogical" @click="addRule">{{ $t('添加') }}</hos-button> -->
    </div>
    <div class="rules-list sub-rules-list">
      <component :is="child.type" v-for="(child, i) in curChildren" :key="i" ref="ruleItem" :type="child.type"
        parent-type="query-builder-sub-group" :query.sync="child.query" :parent-query.sync="curParentQuery" :rule-types="ruleTypes" :rules="rules"
:index="i" :parent-index="index" :max-depth="maxDepth"
        :depth="depth + 1" :field-data="fieldData" :cate-items="cateItems" :current-group-index="currentGroupIndex"
        :current-condition-index="currentConditionIndex" @change-query="changeQuery(i)" @child-add-requested="addChildAfter"
        @child-deletion-requested="removeChild" @calculateOffset="calculateOffset" @update-parent-query="updateParentQueryBySub"
        @group-add-requested="addGroupAfter" @autoComSelect="autoComSelect" @open-search-dialog="openSearchDialog" />
    </div>
  </div>
</template>

<script>
  import QueryBuilderRule from "./QueryBuilderRule.vue"
  import QueryBuilderGroup from "./QueryBuilderGroup.vue"
  import RulesGroupLogical from "./RulesGroupLogical.vue"
  import {deepClone} from "@/utils/index.js"
  import { relationMap2, filterTypeList } from "./mapData.js"

  const defaultQuery = {
    logical: "AND",
    children: [{
      type: "query-builder-rule",
      query: {
        rule: null,
        fieldItemInfo: {},
        relative: null,
        value: null
      }
    }]
  }

  export default {
    name: "QueryBuilderSubGroup",

    components: {
      QueryBuilderRule,
      QueryBuilderGroup,
      RulesGroupLogical
    },
    /* eslint-disable */
    props: [
      "ruleTypes", "type", "parentType", "query", "parentQuery",
       "rules", "index", "parentIndex", "maxDepth",
        "depth", "allowDelFirst", "singleSelectCate", "fieldData", "cateItems",
        "currentGroupIndex", "currentConditionIndex"
        ],
    /* eslint-enable */
    inject: {
      "queryBuilderObj": {
        from: 'queryBuilderObj'
      },
      allTypeRelativesObj: {
          from: "allTypeRelativesObj",
          default: () => {}
      }
    },
    data() {
      return {
        showLogical: true,
        showAddBtn: false,
        logicalStyleObject: {
          top: "5px",
          left: "-10px"
        },
        transformStyleObject: {
          transform: null
        },
        computedParentQuery: {},
      }
    },
    computed: {
      hasMultipleRule() {
        if (this.query) {
          return this.query.children ? this.query.children.length > 1 : false
        } else {
          return false
        }
      },
      curLogical() {
        return this.query ? this.query.logical : "AND"
      },
      curParentQuery() {
        if (this.parentQuery && this.parentQuery.children && this.parentQuery.children[this.index]) {
          return this.parentQuery.children[this.index]
        } else {
          return {}
        }
      },
      curChildren() {
        if (this.parentQuery && this.parentQuery.children && this.parentQuery.children[this.index]) {
          const firstRule = this.parentQuery.children[this.index]
          const res = [...firstRule.subRules]
          // delete firstRule.subRules
          return [firstRule, ...res]
        } else {
          return []
        }
      }
    },
    watch: {
      query: {
        deep: true,
        handler: function(val) {
          this.computedParentQuery = val
        }
      }
    },
    created() {
      // 初始化时计算topOffset
      this.calculateOffset(this.query)
    },
    methods: {
      updateParentQueryBySub(updatedquery) {
        this.computedParentQuery = updatedquery
        this.$emit("update:query", updatedquery)
      },
      toggleLogical() {
        const updated_query = deepClone(this.query)
        updated_query.logical = updated_query.logical === "AND" ? "OR" : "AND"
        this.$emit("update:query", updated_query)
      },
      addRule() {
        this.addChildAfter()
      },
      getTopOffset(query) {
        let topH = 5 - 20
        if (query && query.children && query.children.length > 0) {
          // 单个条件相加top
          const oneH = 20
          // 循环子节点获取总top
          query.children.forEach(c => {
            if (c.query.children && c.query.children.length > 0) {
              topH += this.getTopOffset(c.query)
              topH += 20
            } else {
              topH += oneH
            }
          })
        }
        return topH
      },
      // 计算逻辑词的偏移量
      calculateOffset(query) {
        if (query && query.children && query.children.length > 0) {
          this.showLogical = true
          this.logicalStyleObject.top = this.getTopOffset(query) + "px"
          this.transformStyleObject.transform = query.logical === "AND" ? "rotateY(180deg)" : "rotateY(0deg)"
        } else {
          this.showLogical = false
        }
      },
      addGroup() {
        this.addGroupAfter()
      },
      addChildAfter(index = null, isParentQuery) {
        console.log('sub-group-addChildAfter', this.parentQuery, this.query)
        const child = {
          type: "query-builder-rule",
          query: {
            rule: null,
            fieldItemInfo: {},
            relative: null,
            value: null
          }
        }
        const updated_query = deepClone(this.parentQuery)
        if (isParentQuery) {
          updated_query.children.splice(this.index + 1, 0, child)
        } else {
          const firstRule = updated_query.children[this.index]
          firstRule.subRules.push(child)
        }

        this.$emit("update-parent-query", updated_query)
        // 计算逻辑连接词的位置
        this.$emit("calculateOffset", updated_query)
      },
      addGroupAfter(index) {
        let updated_query = deepClone(this.query)
        if (this.depth < this.maxDepth) {
          const group = {
            type: "query-builder-group",
            query: {
              logical: "AND",
              // 默认添加分组时插入一个值
              children: [{
                type: "query-builder-rule",
                maxDepth: this.maxDepth,
                depth: this.depth,
                query: {
                  rule: null,
                  fieldItemInfo: {},
                  relative: null,
                  value: null
                }
              }]
            }
          }
          if (index) {
            // 在指定index后面追加
            updated_query.children.splice(index + 1, 0, group)
          } else {
            // 如果没有条件，初始化
            if (!updated_query) {
              updated_query = defaultQuery
              updated_query.children = []
            }

            updated_query.children.push(group)
          }

          this.$emit("update:query", updated_query)
          // 计算逻辑连接词的位置
          this.calculateOffset(updated_query)
        }
      },
      // 删除分组
      removeGroup() {
        this.$emit("child-deletion-requested", this.index)
      },

      removeChild(index) {
        const updated_query = deepClone(this.parentQuery)
        const curQuery = updated_query.children[this.index]
        if (index === 0) {
          // 把第一个子条件提到主条件上
          const firstSubRule = curQuery.subRules.shift()
          Object.assign(curQuery, firstSubRule)
        } else {
          // 删掉index-1子条件
          curQuery.subRules.splice(index - 1, 1)
        }
        if (curQuery.subRules.length === 0) {
          delete curQuery.subRules
        }
        this.$emit("update:parent-query", updated_query)
        // 计算逻辑连接词的位置
        this.$emit("calculateOffset", updated_query)
      },
      // 查询项自动补全下拉选中
      autoComSelect(index, gIndex, curItem) {
        const cgIndex = gIndex == null ? this.index : gIndex
        // 是否绑定字典
        let selectOptions = []
        let curDataType = curItem.fieldInfo.datatypeCode ? curItem.fieldInfo.datatypeCode : "string"
        if (curItem.fieldInfo && curItem.fieldInfo.dictId &&
          this.singleSelectCate && this.singleSelectCate[curItem.fieldInfo.dictId]) {
          // curDataType = "select"
          const dictId = curItem.fieldInfo.dictId
          // 绑定下拉的值
          this.$api('biz.search.web.dictionary.getSubDictItem',[dictId]).then(res => {
            const options = res.data[dictId]
            selectOptions = options.map(item => {
              return {
                label: item.dictItemName,
                data: item.dictItemName
              }
            })
            curItem.fieldInfo["selectOptions"] = selectOptions
            this.updateChild(index, cgIndex, curItem, curDataType, selectOptions)
          })
        } else {
          this.updateChild(index, cgIndex, curItem, curDataType, selectOptions)
        }
      },
      // 打开查询项弹出框
      openSearchDialog(index, typeId) {
        this.$emit("open-search-dialog", this.index, undefined, index, typeId)
      },
      /**
       * @param groupIndex 当前条件在所在条件组中的index
       * @param conditionIndex 条件index, 如果有条件组，则条件组的index
       */
      updateChild(groupIndex, conditionIndex, curItem, curDataType, selectOptions) {
        const updated_query = deepClone(this.query)
        const curFieldInfo = curItem.fieldInfo ? curItem.fieldInfo : {}
        curFieldInfo.searchItemId = curItem.searchItemId
        let curOhterFieldInfo = {}
        let otherValue = null
        if (curItem.searchFieldOtherId && curItem.searchFieldOtherId > 0) {
          curOhterFieldInfo = curItem.otherFieldInfo
          otherValue = curItem.searchFieldOtherValue
        }
        if (conditionIndex === 0) {
          const curQuery = updated_query.children[groupIndex].query
          curQuery.fieldItemInfo = curFieldInfo
          curQuery.inputType = curDataType
          curQuery.selectOptions = selectOptions
          if (curFieldInfo.isNegative) {
            const operators = filterTypeList
            curQuery.relative = operators.length > 0 ? operators[0].code : ""
          } else {
            const operators = this.allTypeRelativesObj.idRelativesMap[curFieldInfo.datatype]
            curQuery.relative = operators.length > 0 ? relationMap2[operators[0].relativeType].code : ""
          }
          curQuery.value = ""
          curQuery.otherFieldItemInfo = curOhterFieldInfo
          curQuery.otherValue = otherValue
        } else {
          updated_query.children.forEach((item, index) => {
            if (index === conditionIndex && conditionIndex > 0) {
              if (item.query.children) {
                item.query.children.forEach((citem, ci) => {
                  if (ci === groupIndex) {
                    // let query = citem.query
                    citem.query.fieldItemInfo = curFieldInfo
                    if (curFieldInfo.isNegative) {
                      const operators = filterTypeList
                      citem.query.relative = operators.length > 0 ? operators[0].code : ""
                    } else {
                      const operators = this.allTypeRelativesObj.idRelativesMap[curFieldInfo.datatype]
                      citem.query.relative = operators.length > 0 ? relationMap2[operators[0].relativeType].code : ""
                    }
                    citem.query.inputType = curDataType
                    citem.value = ""
                    citem.otherFieldItemInfo = curOhterFieldInfo
                    citem.otherValue = otherValue
                  }
                })
              } else {
                // let query = item.query

                item.query.fieldItemInfo = curFieldInfo
                if (curFieldInfo.isNegative) {
                  const operators = filterTypeList
                  item.query.relative = operators.length > 0 ? operators[0].code : ""
                } else {
                  const operators = this.allTypeRelativesObj.idRelativesMap[curFieldInfo.datatype]
                  item.query.relative = operators.length > 0 ? relationMap2[operators[0].relativeType].code : ""
                }
                item.query.inputType = curDataType
                item.value = ""
                item.otherFieldItemInfo = curOhterFieldInfo
                item.otherValue = otherValue
              }
            }
          })
        }

        this.$emit("update:query", updated_query)
      },
      valid() {
        let flg = false
        const ruleArr = this.$refs.ruleItem
        if (ruleArr && ruleArr instanceof Array) {
          ruleArr.forEach(item => {
            if (item.valid()) {
              flg = true
            }
          })
        }

        return flg
      },
      clearValid() {
        const ruleArr = this.$refs.ruleItem
        if (ruleArr && ruleArr instanceof Array) {
          ruleArr.forEach(item => {
            if (item.clearValid) {
              item.clearValid()
            } else {
              item.hasError = false
              item.fieldError = false
              item.relativeError = false
              item.valueError = false
            }
          })
        }
      },
      changeQuery(i) {
        const updated_query = deepClone(this.parentQuery)
        const curQuery = updated_query.children[this.index]
        const changeQuery = this.curChildren[i]
        if (i === 0) {
          curQuery.query = changeQuery.query
        } else {
          curQuery.subRules[i - 1].query = changeQuery.query
        }
        this.$emit("update:parent-query", updated_query)
      }
    }
  }
</script>
