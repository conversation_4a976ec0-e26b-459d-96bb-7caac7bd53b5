export const queryListApi = (params) => {
  return {
      url: 'edc/tab/container/page',
      method: 'get',
      params,
  }
}

export const queryAll = (params) => {
  return {
      url: 'edc/tab/container/list',
      method: 'get',
      params,
  }
}

export const queryDetailApi = (id) => {
  return {
      url: 'edc/tab/container/detail/'+id,
      method: 'get',
  }
}

export const addApi = (data) => {
  return {
      url: 'edc/tab/container/insert',
      method: 'post',
      data,
  }
}

export const deleteApi = (data) => {
  return {
    url: 'edc/tab/container/delete/' + data,
    method: 'delete',
  }
}

export const deleteBatchApi = (data) => {
  return {
      url: 'edc/tab/container/deletion',
      method: 'post',
      data,
  }
}

// 编辑
export const editApi = (data) => {
  return {
      url: 'edc/tab/container/update',
      method: 'post',
      data
  }
}

