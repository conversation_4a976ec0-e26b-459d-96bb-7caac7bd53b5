<template>
  <div class="biz-crud h-fit">
    <hos-biz-table v-bind="$attrs" v-on="$listeners" @after-load="refreshTableLayout" @selection-change="changeSelection" @row-click="rowClick" :uid="uid"
      :cols="cols" :form="{
        labelWidth: option.queryFormLabelWidth ? option.queryFormLabelWidth : 'auto',
        model: queryParams
      }" :columnSelected="columnSelected" :outAsyncSlot="asyncSlot" :data="option.buttons.query.api" :page="page" :props="tableProps"
      ref="hosBizTable">
      <template #top>
        <slot name="top"></slot>
      </template>
      <template #toolbar
        v-if="columnSelected || (option.buttons.add && option.buttons.add.isShow !== false) || (option.buttons.delete && option.buttons.delete.isShow !== false) || this.$scopedSlots.buttonLeftOnTable || this.$scopedSlots.buttonRightOnTable">
        <hos-button-group class="hos">
          <!-- 批量操作 -->
          <hos-button v-if="
              option.buttons.add && (option.buttons.add.isShow == true || option.buttons.add.isShow == undefined)
                ? true
                : false
            " v-has-permi="{ key: option.buttons.add.permission || 'add' }" icon="hos-icom-add" @click="addDialog()">{{
            $t("operation.add") }}
          </hos-button>
          <hos-button v-if="
              option.buttons.delete &&
              (option.buttons.delete.isShow == true || option.buttons.delete.isShow == undefined)
                ? true
                : false
            " v-has-permi="{ key: option.buttons.delete.permission || 'delete' }" :disabled="disableBatchDelete"
            icon="hos-icom-cancel" @click="deletion()">{{ $t("operation.delete") }}</hos-button>
          <slot name="buttonLeftOnTable" :form="queryParams" />
          <div style="float: right; padding: 8px 0">
            <slot name="buttonRightOnTable" />
          </div>
          <hosI18n v-if="tbCode" :tableUid="uid" :tbCode="tbCode" :ids="ids" :isBiz="true"></hosI18n>
        </hos-button-group>
      </template>
      <template #form v-if="(queryFormFieldExcludeTree && queryFormFieldExcludeTree.length > 0) || $slots.form">
        <hos-row type="flex" :gutter="20" style="flex-wrap:wrap;" v-if="!$slots.form">
          <hos-col :span="totalFormItemSpan > (24 - queryResetBtnSpan) ? (24 - queryResetBtnSpan) : totalFormItemSpan">
            <hos-row type="flex" :gutter="20" style="flex-wrap:wrap;">
              <hos-col v-for="item in queryFormFieldExcludeTree" :key="item.field"
                :span="calcSpan(item.span)">
                <!-- 查询表单开始 -->
                <hos-form-item :label="item.label" :rules="item.rules" :prop="item.field">
                  <!-- 输入框 -->
                  <hos-input v-if="item.inputType == 'input' || item.inputType == 'input-number'"
                    v-model.trim="queryParams[item.field]" :placeholder="item.placeholder || $t('common.pleaseEnter')"
                    :clearable="item.clearable !== false" :disabled="item.disabled"
                    @change="(value) => queryFormChange(item.field, value)" @keyup.enter="handleQueryForm('query')" />
                  <!-- 开关 -->
                  <hos-switch v-else-if="item.inputType == 'switch'" v-model.trim="queryParams[item.field]"
                    :disabled="item.disabled" :active-value="item.switchOption.enableValue"
                    :inactive-value="item.switchOption.disableValue" active-color="#409eff" inactive-color="#ccc"
                    @change="(value) => queryFormChange(item.field, value)" />
                  <!-- 下拉框 -->
                  <hos-select v-else-if="item.inputType == 'select'" v-model="queryParams[item.field]" :clearable="item.clearable === false ? item.clearable : true"
                    :placeholder="item.placeholder || $t('common.pleaseSelect')" @change="(value) => {$emit('queryParamChange', { field: item.field, value})}">
                    <hos-option v-for="o_item in item.SelectOption.localOptions" :key="o_item[item.SelectOption.option]"
                      :label="o_item[item.SelectOption.label]" :value="o_item[item.SelectOption.option]">
                    </hos-option>
                  </hos-select>
                  <!-- 日期时间框  -->
                  <hos-date-picker v-else-if="item.inputType.indexOf('date') >= 0" v-model="queryParams[item.field]"
                    style="width: 100%" :placeholder="item.placeholder || $t('common.pleaseSelect')" :type="item.inputType"
                    :clearable="item.clearable !== false" :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
                    :start-placeholder="item.startPlaceholder || $t('开始时间')" :end-placeholder="item.endPlaceholder || $t('结束时间')"
                    @change="(value) => queryFormChange(item.field, value)" />
                  <template v-else-if="item.inputType == 'slot' && item.slotName">
                    <slot :name="item.slotName" :form="queryParams"></slot>
                  </template>
                  <!-- 待扩展的表单类型，请自行扩展 -->
                  <hos-input v-else :placeholder="$t('组件不支持此类型表单请至组件内部自行扩展')" disabled />
                </hos-form-item>
              </hos-col>
            </hos-row>
          </hos-col>
          <!-- 查询重置操作按钮 -->
          <hos-col :style="option.formButtonStyle || {}" :span="queryResetBtnSpan" v-if="!option.isHideQueryResetBtn">
            <hos-form-item label-width="20px">
              <hos-biz-button run="form.search" type="primary">{{$t('operation.query')}}</hos-biz-button>
              <hos-biz-button @click="reset()">{{$t('operation.reset')}}</hos-biz-button>
              <slot name="extraButton"></slot>
            </hos-form-item>
          </hos-col>
        </hos-row>
        <!-- 使用完全自定义的查询表单 -->
        <slot v-else name="form"></slot>
        <!-- 查询表单结束 -->
      </template>
      <template #bottom>
        <slot name="bottom"></slot>
      </template>
      <template #operation="{row}">
        <hos-tooltip v-if="
              option.buttons.edit && (option.buttons.edit.isShow == true || option.buttons.edit.isShow == undefined)
                ? true
                : false
            " class="pl5 pr5" :content="$t('operation.edit')">
          <i class="hos-icom-edit" @click="editDialog(row)"
            v-has-permi="{ key: option.buttons.edit.permission || 'update' }"></i>
        </hos-tooltip>
        <hos-tooltip v-if="
              option.buttons.delete && (option.buttons.delete.isShow == true || option.buttons.delete.isShow == undefined)
                ? true
                : false
            " class="pl5 pr5" :content="$t('operation.delete')">
          <i class="hos-icom-cancel" @click="deleteRow(row)"
            v-has-permi="{ key: option.buttons.delete.permission || 'delete' }"></i>
        </hos-tooltip>
        <!-- <hos-tooltip class="pl5 pr5" :content="$t('operation.view')"
            v-has-permi="{ key: 'position:dict:list:view' }">
            <i class="hos-icon-view" @click="view(row)"></i>
        </hos-tooltip> -->
        <slot name="operation" :row="row" :form="queryParams"></slot>
      </template>
    </hos-biz-table>
    <hos-biz-dialog :title="editDialogTitle" width="36%" :uid="uid+'EditDialog'"
      :close-on-click-modal="false"></hos-biz-dialog>
  </div>
</template>

<script>
import { _debounce } from "@/utils/throttle.js"
const CrudEditComp = require("./CrudEdit.vue").default
export default {
  name: 'BizCrud',
  props: {
    uid: {
			default: 0,
		},
    // 序号国际化时，要指定表编码
    tbCode:{

    },
    keyMap: {
      type: Object,
      default() {
        return {
          pageNumber: 'current',
          pageSize: 'size'
        }
      }
    },
    page:{
      type:[Object,Boolean],
      default(){
        return true
      }
    },
    option: {
      require: true,
      type: Object,
      default: () => {
        return {
          title:'',
          enableRowClick: false,  // 是否启用单行选中，启用-行点击选中，不影响多选框交互，不可反选；禁用-行点击无效
          queryFormColNum:3, // 表格顶部查询表单每行显示几列
          editColNum:2, // 新增、修改弹框表单一行几列，默认2列
          queryFormLabelWidth: null, // 顶部表单label宽度
          dialogWidth: '500px', // crud弹框宽度
          dialogFormWidth: '', // crud弹框中的表单宽度
          dialogLabelWidth: '100px', // curd弹框中的表单标题宽度
          useDefaultEdit: false, // 默认不用默认的编辑弹框
          isHideQueryResetBtn: false, // 隐藏查询重置按钮
          // 查询表单条件
          queryFormFields: [],
          // 按钮
          buttons: {
            query: {},
            edit: {},
            delete: {
              // showRemind: false, // 删除提示是否添加自定义提示
              // remindTemplate: '', // 自定义提示模板, { }之间写要替换的字段名
              // batchLabel: '数据' // 批量删除条数后拼接描述
            },
            add: {}
          },
          // 表格列
          columns: [],
          queryFormChange: (fileName, val) => {},
          isPaginationLimited: false,
          // 自定义提示语
          customLimitTips: '',
          formButtonStyle:{} // 查询/重置等按钮的样式
        }
      }
    },
    columnSelected:{
      type:Boolean,
      default(){
        return false
      }
    }
  },
  provide() {
		return {
			CRUD_PROVIDE: this,
		};
	},
  activated(){
    this.refreshTableLayout()
  },
  data() {
    return {
      ids: [],
      editDialogTitle:'',
      // 查询表单提交的值
      queryParams: {
        [this.keyMap.pageNumber]: 1,
        [this.keyMap.pageSize]: 10,
        order: '',
        sort: ''
      },
      queryParamsResult: {
        [this.keyMap.pageNumber]: 1,
        [this.keyMap.pageSize]: 10,
        order: '',
        sort: ''
      },

      checkRecords: [], // 表格中当前选中的记录
      records: [], // 接口返回的记录列表
      total: 0, // 分页的总条数
      realTotal: 0, // 接口返回的总条数

    }
  },
  computed: {
    queryResetBtnSpan(){
      return this.option.isHideQueryResetBtn ? 0 : (this.option.queryFormColNum ? 24/this.option.queryFormColNum : 8)
    },
    totalFormItemSpan(){
      let total = 0
      this.queryFormFieldExcludeTree.forEach(item=>{
        total += item.span ? item.span : (this.option.queryFormColNum ? 24 / this.option.queryFormColNum : 8)
      })
      return total
    },
    tableProps(){
      return {
        data: 'data',
        total: 'total',
        current: this.keyMap.pageNumber,
        size: this.keyMap.pageSize,
      }
    },
    cols() {
      const res = []
      this.option.columns.forEach((col) => {
        if (!col.tableHide) {
          col.prop = col.field || col.prop
          res.push(col)
        }
      })
      return res
    },
    asyncSlot() {
      // 过滤当前组件定义的slot，透传其他slot到hos-biz-table中
			const slotArr = ['operation','form','buttonLeftOnTable','buttonRightOnTable','top','bottom'];
			const _slot = {};
			Object.keys(this.$scopedSlots)
				.filter((ele) => !slotArr.includes(ele))
				.forEach((ele) => {
					_slot[ele] = this.$scopedSlots[ele];
				});
			return _slot;
		},
    // 左侧树形查询条件
    queryFormTreeField() {
      var treeField = this.option.queryFormFields.find((item) => item['inputType'] === 'anji-tree')
      return treeField
    },
    // 查询条件里是否有树形控件
    hasTreeFieldInQueryForm() {
      return this.isNotBlank(this.queryFormTreeField)
    },
    // 树形控件是否内嵌到其他容器展示， 默认否
    isEmbedTreeFieldInQueryForm() {
      if (this.isNotBlank(this.queryFormTreeField)) {
        if (this.queryFormTreeField.anjiTreeOption.isEmbed) {
          return true
        } else {
          return false
        }
      }

      return false
    },
    // 不包含树形控件的查询条件
    queryFormFieldExcludeTree() {
      var treeFields = this.option.queryFormFields.filter((item) => item['inputType'] !== 'anji-tree')

      return treeFields
    },
    // 主键的列名
    primaryKeyFieldName() {
      var primaryKey = this.option.columns.find((item) => item['primaryKey'] === true)
      if (primaryKey != null) {
        return primaryKey['field']
      } else {
        return null
        // console.warn(
        //   '在columns中查找primaryKey=true失败，会导致查询详情和删除失败'
        // )
      }
    },

    // 表格中可展开的列
    tableExpandColumns() {
      var expandColumns = this.option.columns.filter((item) => item['columnType'] === 'expand')
      return expandColumns
    },

    // 是否可以批量删除
    disableBatchDelete() {
      return this.checkRecords.length <= 0
    }
  },
  created() {
    // 为查询框中所有input加上默认值
    this.option.queryFormFields.forEach((item) => {
      // 动态添加属性
      this.$set(this.queryParams, item.field, item.defaultValue === undefined ? null : item.defaultValue)
    })
  },
  mounted() {

  },
  methods: {
    calcSpan(span){
      let originSpan = span ? span : (this.option.queryFormColNum ? 24 / this.option.queryFormColNum : 8)
      if(this.totalFormItemSpan < (24 - this.queryResetBtnSpan)){
        // 不超过一行的
        return parseInt(originSpan * 24 / this.totalFormItemSpan)
      }else{
        // 超过了一行的,把原来总行24的换算成扣除了查询重置按钮的占比
        return parseInt(originSpan * 24 / (24 - this.queryResetBtnSpan))
      }
    },
    // 强制刷新布局，防止固定的操作列错位问题
    refreshTableLayout:_debounce(function(tableData) {
      this.records = tableData
      if(this.$refs.hosBizTable && this.$refs.hosBizTable.$refs['hos-table-'+this.uid] && this.$refs.hosBizTable.$refs['hos-table-'+this.uid].$refs['hos-table-lq']){
        this.$refs.hosBizTable.$refs['hos-table-'+this.uid].$refs['hos-table-lq'].doLayout()
      }
    }),
    queryFormFieldSpan(item) {
      if (item.span != null) {
        return item.span
      } else {
        return 6
      }
      // let rowLength = this.option.queryFormFields.length;
      // console.log(rowLength, "ss")
      // console.log(rowLength % 3)
      // if (rowLength <= 3) {
      //   return 6
      // }
      // else if (rowLength % 3 == 0) {
      //   return 8
      // } else if (rowLength > 6) {
      //   return 8
      // }
    },
    // 查询按钮
    handleQueryForm(from) {
      // 如果是点查询按钮，把树的查询属性去掉
      if (from === 'query') {
        if (this.hasTreeFieldInQueryForm) {
          delete this.queryParams[this.queryFormTreeField.field]
        }
      }
      // 如果是点树查询，把查询区里的属性去掉
      if (from === 'tree') {
        if (this.hasTreeFieldInQueryForm) {
          var treeVal = this.queryParams[this.queryFormTreeField.field]
          this.queryParams = {
            [this.keyMap.pageNumber]: 1,
            [this.keyMap.pageSize]: 10
          }
          this.queryParams[this.queryFormTreeField.field] = treeVal
        }
      }
      // 默认的排序
      if (this.isBlank(this.queryParams['order']) && this.isNotBlank(this.option.buttons.query.order)) {
        this.queryParams['sort'] = this.option.buttons.query.sort
        this.queryParams['order'] = this.option.buttons.query.order
      }
      this.queryParams[this.keyMap.pageNumber] = 1
      this.handleQueryPageList()
    },
    // 列表查询
    async handleQueryPageList(ifPagination = false) {
      if (ifPagination == false) {
        this.queryParamsResult = this.deepClone(this.queryParams)
      } else {
        this.queryParamsResult[this.keyMap.pageNumber] = this.queryParams[this.keyMap.pageNumber]
        this.queryParamsResult[this.keyMap.pageSize] = this.queryParams[this.keyMap.pageSize]
      }
      var params = ifPagination ? this.queryParamsResult : this.queryParams
      const { data, code } = await this.option.buttons.query.api(params)
      if (code != 200) return
      this.records = data.records
      this.$refs['hosBizTable']?.setTableData(data)
      // 修复手动调用刷新未更新深度分页页码问题
      this.$refs['hosBizTable'].params.pagination = {
        [this.keyMap.pageNumber]: this.queryParams[this.keyMap.pageNumber],
        [this.keyMap.pageSize]: this.queryParams[this.keyMap.pageSize]
      }
      this.realTotal = data.total
      if (this.option.isPaginationLimited) {
        this.total = data.total > 100 ? 100 : data.total
        this.sizeList = data.total > 100 ? [10, 20, 30, 40, 50] : this.$pageSizeAll
      } else {
        this.total = data.total
        this.sizeList = this.$pageSizeAll
      }
      // 这里加判断 如果删除了最后一条数据导致当前页码大于最大页码，则改完最大页码重新请求
      let size = this.queryParams.pageSize || data.size
      if (size <= 0) {
        size = 10
      }
      let maxPage = Math.ceil(data.total / size)
      if (maxPage <= 0) {
        maxPage = 1
      }
      if (this.queryParams[this.keyMap.pageNumber] > maxPage) {
        this.queryParams[this.keyMap.pageNumber] = maxPage
        this.handleQueryPageList()
      }
    },
    reset() {
      this.option.queryFormFields.forEach((item) => {
        // 动态添加属性
        this.$set(this.queryParams, item.field, item.defaultValue === undefined ? null : item.defaultValue)
      })
      this.queryParams[this.keyMap.pageNumber] = 1,
      this.queryParams[this.keyMap.pageSize] = 10
      this.$emit('reset')
      this.$store.commit('UPDATE_TABLE', { _uid: this.uid })
    },
    // 新增
    addDialog() {
        this.editDialogTitle = this.$t("operation.add")
        this.$store.commit("OPEN_DIALOG", {
            component: CrudEditComp,
            _uid: this.uid+"EditDialog",
            props: {
              uid:this.uid+"EditDialog",
              id: "",
              status: "add:",
            },
        });
    },
    // 多选框选中数据
    changeSelection(rows) {
      this.handleSelectionChange(rows)
        this.ids = rows.map((item) => item.id);
        this.$emit('selection-change',rows)
    },
    rowClick(row, column, event) {
      if (this.option.enableRowClick) {
        this.$refs['hosBizTable'].clearSelection()
        this.$refs['hosBizTable'].toggleRowSelection(row, true)
        this.checkRecords = [row]
        this.$emit('select-single-row', this.checkRecords[0])
      }
    },
    //修改页面
    editDialog( row ) {
      this.editDialogTitle = this.$t("operation.edit")
      this.$store.commit( "OPEN_DIALOG", {
          component: CrudEditComp,
          _uid: this.uid+"EditDialog",
          props: {
              uid:this.uid+"EditDialog",
              id: row.id,
              status: "edit:",
          },
      } );
    },
    // 批量删除
    deletion() {
        if (this.ids != undefined) {
            if (this.ids.length > 0) {
              let remindLabel = this.$t('是否删除已选中的数据？')
              if (this.option.buttons.delete.showRemind) {
                remindLabel = this.$t('是否删除已选中的') + this.ids.length + `${this.option.buttons.delete.batchLabel || this.$t('数据')}` + '?'
              }
                this.$confirm(
                  remindLabel,
                  this.$t('common.tips'),
                  { type: "delete" }
                )
                    .then(() => {
                      this.option.buttons.delete.api(this.ids).then((res) => {
                            if (res && res.code == "200") {
                                this.$store.commit("UPDATE_TABLE", {
                                    _uid: this.uid,
                                });
                                this.$message.success(
                                    res.msg
                                );
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                    })
                    .catch(() => {
                    });
            } else {
                this.$message.warning(this.$t('common.noDataSelected'));
            }
        } else {
            this.$message.warning(this.$t('common.noDataSelected'));
        }
    },
    //单个删除
    deleteRow( row ) {
      let remindLabel = this.option.buttons.delete.remindTemplate || ''
      if (this.option.buttons.delete.showRemind) {
        const regex = /{([^}]*)}/g;
        let matches;
        while ((matches = regex.exec(this.option.buttons.delete.remindTemplate)) !== null) {
          remindLabel = remindLabel.replace(`{${matches[1]}}`,row[matches[1]])
        }
      }
          this.$confirm(
            this.$t(`是否删除`) + remindLabel + '?',
              this.$t( 'common.delete' ),
              { type: "delete" }
          ).then( () => {
              if ( row ) {
                this.option.buttons.delete.api([row.id]).then( ( res ) => {
                      if ( res && res.code == "200" ) {
                          this.$message.success( res.msg );
                          this.$store.commit( "UPDATE_TABLE", {
                              _uid: this.uid,
                          } );
                      } else {
                          this.$message.error( res.msg );
                      }
                  } );
              }
          } );
      },

    // 选择项改变时
    handleSelectionChange(val) {
      this.checkRecords = val
    },
    queryFormChange(fileName, fieldVal) {
      if (typeof this.option.queryFormChange === 'function') {
        this.option.queryFormChange(this.queryParams, fileName, fieldVal)
      }
    }
  }
}
</script>
