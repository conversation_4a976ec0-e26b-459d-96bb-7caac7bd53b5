export const queryListApi = (params) => {
    return {
        url: 'search/data-storage-config/page',
        method: 'get',
        params
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'search/data-storage-config/deletion',
        method: 'post',
        data
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'search/data-storage-config/detail/' + id,
        method: 'get',
    }
}

export const addApi = (data) => {
    return {
        url: 'search/data-storage-config/insert',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'search/data-storage-config/update',
        method: 'post',
        data
    }
}

export const saveStorage = (data) => {
    return {
        url: `search/data-storage-config/save-db-config/${data.specIndexId}`,
        method: 'post',
        data
    }
}

export const getStoragePattern = () => {
    return {
        url: 'search/data-storage-config/type',
        method: 'get'
    }
}

export const checkDbConnect = (data) => {
    return {
        url: 'search/data-storage-config/verify-db-config',
        method: 'post',
        data
    }
}