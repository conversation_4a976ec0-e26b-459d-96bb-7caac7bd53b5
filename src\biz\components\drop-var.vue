<template>
  <div class="drop-var">
    <div v-if="varList.length==0" class="var-empty">{{ placeholder ? placeholder : $t('拖入变量') + '(' + $t('变量数') + `${isOnlyOne? '=': '>=' }1)` }}</div>
    <draggable v-model="varList" v-bind="{
        group: 'var',
        ghostClass: 'ghost',
        animation: 200
    }" @add="handleVarAdd">
      <transition-group name="fade" tag="div" class="drop-var-list">
        <hos-tag v-for="(item, index) in varList" :key="item.uuid" :title="`${item.type}`" class="var-item"
          :type="varTypeColor[item.type] || 'info'" size="small" :closable="true" @close="closeVar(index)">
          {{ item.name }}</hos-tag>
      </transition-group>
    </draggable>

  </div>
</template>

<script>
  import Draggable from "vuedraggable"
  export default {
    components: {
      Draggable
    },
    props: {
      // 是否仅拖拽1个（拖入变量超过1个时自动替换原来的）
      isOnlyOne: {
        type: Boolean,
        default () {
          return false
        }
      },
      // 最大拖入变量个数（拖入变量超过时不能放入）
      maxVarCount: {
        type: Number
      },
      // 自定义拖入前判断是否允许拖入
      canDropFunc: {
        type: Function,
      },
      placeholder: {
        type: String,
        default () {
          return '' // 拖入变量
        }
      },
      value: {
        type: Array,
        default () {
          return []
        }
      }
    },
    data() {
      return {
        varList: this.value,
        varTypeColor: {
          "定性": "success",
          "定量": "primary",
          "异常": "danger"
        }
      }
    },
    watch: {
      value(new_val, old_val) {
        this.varList = new_val
      },
      varList(new_val, old_val) {
        this.$emit('input', new_val)
        this.$store.dispatch('updateDropVar', { uid: this._uid, varList: new_val })
      }
    },
    beforeDestroy() {
      this.$store.dispatch('deleteDropVar', this._uid)
    },
    methods: {
      closeVar(index) {
        this.varList.splice(index, 1)
        // this.$emit('input', this.varList)
        this.$emit('close', index)
        this.$emit('change', this.varList)
      },
      handleVarAdd(evt) {
        const newIndex = evt.newIndex
        const curVar = this.varList[newIndex]
        if (this.canDropFunc) {
          const isCanDrop = this.canDropFunc(curVar, this.varList, evt)
          if (isCanDrop !== true) {
            this.varList.splice(newIndex, 1)
            return
          }
        }
        // 仅拖入一个变量，拖入第二个时替换原来的
        if (this.isOnlyOne) {
          const oldList = [...this.varList]
          this.varList.splice(0, this.varList.length)
          this.varList.push(curVar)
          if (oldList.length >= 2 && oldList.filter(item => item.name == curVar.name).length >= 2) {
            // 如果替换时跟之前的相等时，直接return，不触发change和add事件
            return
          }
        }
        // 超过最大可拖入变量时，移除最新拖入的
        if (this.maxVarCount > 0 && this.varList.length > this.maxVarCount) {
          this.$message.warning(this.$t('最大添加') + this.maxVarCount + this.$('个') + this.$t('变量'))
          this.varList.splice(newIndex, 1)
          return
        }
        // 判断重复拖入变量
        if (this.varList.length > 1 && this.varList.filter(item => item.name === curVar.name).length >= 2) {
          this.$message.warning(this.$t('变量') + '[' + curVar.name + ']' + this.$t('已存在'))
          this.varList.splice(newIndex, 1)
          return
        }
        // this.$emit('input', this.varList)
        this.$emit('add', curVar)
        this.$emit('change', this.varList)
      },
    }
  }

</script>
