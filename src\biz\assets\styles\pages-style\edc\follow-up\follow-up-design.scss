.follow-up-design {
	background-color: white;
	height: 100%;
	overflow: auto;

	// padding: 10px;
	.follow-up-card {
		position: relative;
		overflow: visible;
	}

	.m-b-10 {
		margin-bottom: 10px;
		display: flex !important;
		align-items: center;
	}

	.m-r-10 {
		margin-right: 10px;
	}

	.item-close {
		position: absolute;
		top: 0;
		right: 10px;
		transform: translateY(-50%) translateX(100%);
		cursor: pointer;
		color: #fbc4c4;
		background-color: #fff;
		z-index: 5;
		font-size: 18px;
	}

	.item-close:hover {
		color: #f56c6c;
	}

	.saveBtn {
		width: 80px;
		position: fixed;
		right: 40px;
		bottom: 20px;
		z-index: 1000;
	}

	.previewBtn {
		position: fixed;
		right: 310px;
		bottom: 20px;
		z-index: 1000;
	}

	.templateBtn {
		position: fixed;
		right: 220px;
		bottom: 20px;
		z-index: 1000;
	}

	.selectTemplateBtn {
		position: fixed;
		right: 130px;
		bottom: 20px;
		z-index: 1000;
	}
}