﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_rolestatus";

//页面选中数据
var selRoleID = "";

$(function() {
    initBTN();
    document.onkeydown = documentOnKeyDown;

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        if (!getIsCareCAOnByRole(selOrgID)) {
            return;
        }
        initRoleGrid(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        if (!getIsCareCAOnByRole(selOrgID)) {
            return;
        }
        initRoleGrid(selOrgID);
    }
})

function initBTN() {
    $("#btnStartRole").click(function(){startRole();});
    $("#btnStopRole").click(function(){stopRole();});
    $("#btnQueryRole").click(function(){queryRole();});
}

function getIsCareCAOnByRole(selOrgID) {
    var result = false;
    var errmsg = "";
    var data = {
        action: "GET_SYSOPTION",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        var arr = json.data;
        if (arr["isCareCAOnByRole"] !== "1") {
            errmsg = "当前组织机构配置中未开启【是否开启角色管理】，当前页面不可用！"
        } else if (arr["allCareCAOn"] == "1") {
            errmsg = "当前组织机构已配置【全院开启医护签名】，所有安全组都为启用状态！"
        } else {
            result = true;
        }
    } else {
        $.messager.alert("提示","获取通用配置数据错误!");
    }

    if (!result) {
        $.messager.alert("提示",errmsg);
        setEnable(false,errmsg);
    } else {
        setEnable(true);
    }
    return result;
}

function setEnable(enable,errmsg) {
    if (enable) {
        $("#role").show();
        $("#caTip").hide();
    } else {
        $("#role").hide();
        $("#caTip").show();
        document.getElementById("caTip").innerHTML = errmsg;
    }
}

function queryRole() {
    var roleDesc = $("#roleDesc").val();
    var signStatus = $HUI.combobox("#roleStatus").getValue();

    var queryParams = {
        action: "GET_ROLELIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            status: signStatus,
            roleDesc: roleDesc
        }
    };
    $("#dgRole").datagrid("load",queryParams);
}

function startRole() {
    selRoleID = selRoleID || "";
    if (selRoleID == "") {
        $.messager.alert("提示","请选中要开启的角色");
        return;
    }

    var data = {
        action: "SET_ROLECAON",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            roleID: selRoleID
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "开启角色CA成功",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
                queryRole();
            } else {
                $.messager.alert("提示", "开启角色CA失败！", "error")
            }
        } else {
            $.messager.alert("提示", "开启角色CA失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function stopRole() {
    selRoleID = selRoleID || "";
    if (selRoleID == "") {
        $.messager.alert("提示","请选中要停用的安全组");
        return;
    }

    var data = {
        action: "SET_ROLECAOFF",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            roleID: selRoleID
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "关闭角色CA成功",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
                queryRole();
            } else {
                $.messager.alert("提示", "关闭角色CA失败！", "error")
            }
        } else {
            $.messager.alert("提示", "关闭角色CA失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

function initRoleGrid(selOrgID) {
    var param = {
        action: "GET_ROLELIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            status: "ALL",
            ctLocDesc: ""
        }
    };

    $("#dgRole").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbRole",
        url: ca_common_tools.getAppPath("GET_ROLELIST"),
        queryParams: param,
        idField:"roleID",
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:50,
        pageList:[2,10,30,50],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"roleID",title:"角色ID",hidden:true},
            {field:"roleCode",title:"角色代码"},
            {field:"roleDesc",title:"角色名称",width:200},
            {
                field:"isCareCAOn",
                title:"是否已开启医护签名",
                align: "center",
                formatter: function(value,row,index)
                {
                    if (value == "1") {
                        return "<span style='color:green'>已开启</span>";
                    } else {
                        return "<span style='color:red'>未开启</span>";
                    }
                }
            }
        ]],
        onLoadError:function() {
            $.messager.alert("提示","角色列表加载失败");
        },
        onSelect:function(rowIndex,row){
            selRoleID = row.roleID;
            if (row.isCareCAOn == 1) {
                $("#btnStartRole").linkbutton("disable");
                $("#btnStopRole").linkbutton("enable");
            } else {
                $("#btnStopRole").linkbutton("disable");
                $("#btnStartRole").linkbutton("enable");
            }
        },
        onLoadSuccess:function(data){
            $("#dgRole").datagrid("clearSelections");
            $("#btnStartRole").linkbutton("disable");
            $("#btnStopRole").linkbutton("disable");
            selRoleID = "";
        }
    });
}

function documentOnKeyDown(e) {
    if (window.event) {
        var keyCode=window.event.keyCode;
    } else {
        var keyCode=e.which;
    }

    if (keyCode==13) {
        queryRole();
    }
}
