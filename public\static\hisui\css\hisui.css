.panel {
  overflow: hidden;
  text-align: left;
  margin: 0;
  border: 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.panel-header,
.panel-body {
  border-width: 1px;
  border-style: solid;
}
.panel-header {
  padding: 5px;
  position: relative;
}
.panel-title {
  background: url('images/blank.gif') no-repeat;
}
.panel-header-noborder {
  border-width: 0 0 1px 0;
}
.panel-body {
  overflow: auto;
  border-top-width: 0;
  padding: 0;
}
.panel-body-noheader {
  border-top-width: 1px;
}
.panel-body-noborder {
  border-width: 0px;
}
.panel-with-icon {
  padding-left: 18px;
}
.panel-icon,
.panel-tool {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  height: 16px;
  overflow: hidden;
}
.panel-icon {
  left: 5px;
  width: 16px;
}
.panel-tool {
  right: 5px;
  width: auto;
}
.panel-tool a {
  display: inline-block;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  filter: alpha(opacity=60);
  margin: 0 0 0 2px;
  vertical-align: top;
}
.panel-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #eaf2ff;
  -moz-border-radius: 3px 3px 3px 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}
.panel-loading {
  padding: 11px 0px 10px 30px;
}
.panel-noscroll {
  overflow: hidden;
}
.panel-fit,
.panel-fit body {
  height: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  overflow: hidden;
}
.panel-loading {
  background: url('images/loading.gif') no-repeat 10px 10px;
}
.panel-tool-close {
  background: url('images/panel_tools.png') no-repeat -16px 0px;
}
.panel-tool-min {
  background: url('images/panel_tools.png') no-repeat 0px 0px;
}
.panel-tool-max {
  background: url('images/panel_tools.png') no-repeat 0px -16px;
}
.panel-tool-restore {
  background: url('images/panel_tools.png') no-repeat -16px -16px;
}
.panel-tool-collapse {
  background: url('images/panel_tools.png') no-repeat -32px 0;
}
.panel-tool-expand {
  background: url('images/panel_tools.png') no-repeat -32px -16px;
}
.panel-header,
.panel-body {
  border-color: #95B8E7;
}
.panel-header {
  background-color: #E0ECFF;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -o-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: linear-gradient(to bottom, #EFF5FF 0, #E0ECFF 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#E0ECFF, GradientType=0);
}
.panel-body {
  background-color: #ffffff;
  color: #000000;
  font-size: 12px;
}
.panel-title {
  font-size: 12px;
  font-weight: bold;
  color: #0E2D5F;
  height: 16px;
  line-height: 16px;
}
.accordion {
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.accordion .accordion-header {
  border-width: 0 0 1px;
  cursor: pointer;
}
.accordion .accordion-body {
  border-width: 0 0 1px;
}
.accordion-noborder {
  border-width: 0;
}
.accordion-noborder .accordion-header {
  border-width: 0 0 1px;
}
.accordion-noborder .accordion-body {
  border-width: 0 0 1px;
}
.accordion-collapse {
  background: url('images/accordion_arrows.png') no-repeat 0 0;
}
.accordion-expand {
  background: url('images/accordion_arrows.png') no-repeat -16px 0;
}
.accordion {
  background: #ffffff;
  border-color: #95B8E7;
}
.accordion .accordion-header {
  background: #E0ECFF;
  filter: none;
}
.accordion .accordion-header-selected {
  background: #ffe48d;
}
.accordion .accordion-header-selected .panel-title {
  color: #000000;
}
.window {
  overflow: hidden;
  padding: 5px;
  border-width: 1px;
  border-style: solid;
}
.window .window-header {
  background: transparent;
  padding: 0px 0px 6px 0px;
}
.window .window-body {
  border-width: 1px;
  border-style: solid;
  border-top-width: 0px;
}
.window .window-body-noheader {
  border-top-width: 1px;
}
.window .window-header .panel-icon,
.window .window-header .panel-tool {
  top: 50%;
  margin-top: -11px;
}
.window .window-header .panel-icon {
  left: 1px;
}
.window .window-header .panel-tool {
  right: 1px;
}
.window .window-header .panel-with-icon {
  padding-left: 18px;
}
.window-proxy {
  position: absolute;
  overflow: hidden;
}
.window-proxy-mask {
  position: absolute;
  filter: alpha(opacity=5);
  opacity: 0.05;
}
.window-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  filter: alpha(opacity=40);
  opacity: 0.40;
  font-size: 1px;
  *zoom: 1;
  overflow: hidden;
}
.window,
.window-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.window-shadow {
  background: #ccc;
  -moz-box-shadow: 2px 2px 3px #cccccc;
  -webkit-box-shadow: 2px 2px 3px #cccccc;
  box-shadow: 2px 2px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2, MakeShadow=false, ShadowOpacity=0.2);
}
.window,
.window .window-body {
  border-color: #95B8E7;
}
.window {
  background-color: #E0ECFF;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #E0ECFF 20%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #E0ECFF 20%);
  background: -o-linear-gradient(top, #EFF5FF 0, #E0ECFF 20%);
  background: linear-gradient(to bottom, #EFF5FF 0, #E0ECFF 20%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#E0ECFF, GradientType=0);
}
.window-proxy {
  border: 1px dashed #95B8E7;
}
.window-proxy-mask,
.window-mask {
  background: #ccc;
}
.dialog-content {
  overflow: auto;
}
.dialog-toolbar {
  padding: 2px 5px;
}
.dialog-tool-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.dialog-button {
  padding: 5px;
  text-align: right;
}
.dialog-button .l-btn {
  margin-left: 5px;
}
.dialog-toolbar,
.dialog-button {
  background: #F4F4F4;
}
.dialog-toolbar {
  border-bottom: 1px solid #dddddd;
}
.dialog-button {
  border-top: 1px solid #dddddd;
}
.textbox[type=text] {
  border: 1px solid #95B8E7;
  vertical-align: middle;
}
.combo {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.combo .combo-text {
  font-size: 12px;
  border: 0px;
  line-height: 20px;
  height: 20px;
  margin: 0;
  padding: 0px 2px;
  *margin-top: -1px;
  *height: 18px;
  *line-height: 18px;
  _height: 18px;
  _line-height: 18px;
  vertical-align: baseline;
}
.combo-arrow {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.combo-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.combo-panel {
  overflow: auto;
}
.combo-arrow {
  background: url('images/combo_arrow.png') no-repeat center center;
}
.combo,
.combo-panel {
  background-color: #ffffff;
}
.combo {
  border-color: #95B8E7;
  background-color: #ffffff;
}
.combo-arrow {
  background-color: #E0ECFF;
}
.combo-arrow-hover {
  background-color: #eaf2ff;
}
.combobox-item,
.combobox-group {
  font-size: 12px;
  padding: 3px;
  padding-right: 0px;
}
.combobox-item-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.combobox-gitem {
  padding-left: 10px;
}
.combobox-group {
  font-weight: bold;
}
.combobox-item-hover {
  background-color: #eaf2ff;
  color: #000000;
}
.combobox-item-selected {
  background-color: #ffe48d;
  color: #000000;
}
.layout {
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 0;
}
.layout-panel {
  position: absolute;
  overflow: hidden;
}
.layout-panel-east,
.layout-panel-west {
  z-index: 2;
}
.layout-panel-north,
.layout-panel-south {
  z-index: 3;
}
.layout-expand {
  position: absolute;
  padding: 0px;
  font-size: 1px;
  cursor: pointer;
  z-index: 1;
}
.layout-expand .panel-header,
.layout-expand .panel-body {
  background: transparent;
  filter: none;
  overflow: hidden;
}
.layout-expand .panel-header {
  border-bottom-width: 0px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  position: absolute;
  font-size: 1px;
  display: none;
  z-index: 5;
}
.layout-split-proxy-h {
  width: 5px;
  cursor: e-resize;
}
.layout-split-proxy-v {
  height: 5px;
  cursor: n-resize;
}
.layout-mask {
  position: absolute;
  background: #fafafa;
  filter: alpha(opacity=10);
  opacity: 0.10;
  z-index: 4;
}
.layout-button-up {
  background: url('images/layout_arrows.png') no-repeat -16px -16px;
}
.layout-button-down {
  background: url('images/layout_arrows.png') no-repeat -16px 0;
}
.layout-button-left {
  background: url('images/layout_arrows.png') no-repeat 0 0;
}
.layout-button-right {
  background: url('images/layout_arrows.png') no-repeat 0 -16px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  background-color: #aac5e7;
}
.layout-split-north {
  border-bottom: 5px solid #E6EEF8;
}
.layout-split-south {
  border-top: 5px solid #E6EEF8;
}
.layout-split-east {
  border-left: 5px solid #E6EEF8;
}
.layout-split-west {
  border-right: 5px solid #E6EEF8;
}
.layout-expand {
  background-color: #E0ECFF;
}
.layout-expand-over {
  background-color: #E0ECFF;
}
.tabs-container {
  overflow: hidden;
}
.tabs-header {
  border-width: 1px;
  border-style: solid;
  border-bottom-width: 0;
  position: relative;
  padding: 0;
  padding-top: 2px;
  overflow: hidden;
}
.tabs-header-plain {
  border: 0;
  background: transparent;
}
.tabs-scroller-left,
.tabs-scroller-right {
  position: absolute;
  top: auto;
  bottom: 0;
  width: 18px;
  font-size: 1px;
  display: none;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.tabs-scroller-left {
  left: 0;
}
.tabs-scroller-right {
  right: 0;
}
.tabs-tool {
  position: absolute;
  bottom: 0;
  padding: 1px;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.tabs-header-plain .tabs-tool {
  padding: 0 1px;
}
.tabs-wrap {
  position: relative;
  left: 0;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}
.tabs-scrolling {
  margin-left: 18px;
  margin-right: 18px;
}
.tabs-disabled {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.tabs {
  list-style-type: none;
  height: 26px;
  margin: 0px;
  padding: 0px;
  padding-left: 4px;
  width: 50000px;
  border-style: solid;
  border-width: 0 0 1px 0;
}
.tabs li {
  float: left;
  display: inline-block;
  margin: 0 4px -1px 0;
  padding: 0;
  position: relative;
  border: 0;
}
.tabs li a.tabs-inner {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0 10px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  white-space: nowrap;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 0 0;
  -webkit-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.tabs li.tabs-selected a.tabs-inner {
  font-weight: bold;
  outline: none;
}
.tabs li.tabs-selected a:hover.tabs-inner {
  cursor: default;
  pointer: default;
}
.tabs li a.tabs-close,
.tabs-p-tool {
  position: absolute;
  font-size: 1px;
  display: block;
  height: 12px;
  padding: 0;
  top: 50%;
  margin-top: -6px;
  overflow: hidden;
}
.tabs li a.tabs-close {
  width: 12px;
  right: 5px;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs-p-tool {
  right: 16px;
}
.tabs-p-tool a {
  display: inline-block;
  font-size: 1px;
  width: 12px;
  height: 12px;
  margin: 0;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs li a:hover.tabs-close,
.tabs-p-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  cursor: hand;
  cursor: pointer;
}
.tabs-with-icon {
  padding-left: 18px;
}
.tabs-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 10px;
  top: 50%;
  margin-top: -8px;
}
.tabs-title {
  font-size: 12px;
}
.tabs-closable {
  padding-right: 8px;
}
.tabs-panels {
  margin: 0px;
  padding: 0px;
  border-width: 1px;
  border-style: solid;
  border-top-width: 0;
  overflow: hidden;
}
.tabs-header-bottom {
  border-width: 0 1px 1px 1px;
  padding: 0 0 2px 0;
}
.tabs-header-bottom .tabs {
  border-width: 1px 0 0 0;
}
.tabs-header-bottom .tabs li {
  margin: -1px 4px 0 0;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  -moz-border-radius: 0 0 5px 5px;
  -webkit-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
}
.tabs-header-bottom .tabs-tool {
  top: 0;
}
.tabs-header-bottom .tabs-scroller-left,
.tabs-header-bottom .tabs-scroller-right {
  top: 0;
  bottom: auto;
}
.tabs-panels-top {
  border-width: 1px 1px 0 1px;
}
.tabs-header-left {
  float: left;
  border-width: 1px 0 1px 1px;
  padding: 0;
}
.tabs-header-right {
  float: right;
  border-width: 1px 1px 1px 0;
  padding: 0;
}
.tabs-header-left .tabs-wrap,
.tabs-header-right .tabs-wrap {
  height: 100%;
}
.tabs-header-left .tabs {
  height: 100%;
  padding: 4px 0 0 4px;
  border-width: 0 1px 0 0;
}
.tabs-header-right .tabs {
  height: 100%;
  padding: 4px 4px 0 0;
  border-width: 0 0 0 1px;
}
.tabs-header-left .tabs li,
.tabs-header-right .tabs li {
  display: block;
  width: 100%;
  position: relative;
}
.tabs-header-left .tabs li {
  left: auto;
  right: 0;
  margin: 0 -1px 4px 0;
  float: right;
}
.tabs-header-right .tabs li {
  left: 0;
  right: auto;
  margin: 0 0 4px -1px;
  float: left;
}
.tabs-header-left .tabs li a.tabs-inner {
  display: block;
  text-align: left;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.tabs-header-right .tabs li a.tabs-inner {
  display: block;
  text-align: left;
  -moz-border-radius: 0 5px 5px 0;
  -webkit-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
}
.tabs-panels-right {
  float: right;
  border-width: 1px 1px 1px 0;
}
.tabs-panels-left {
  float: left;
  border-width: 1px 0 1px 1px;
}
.tabs-header-noborder,
.tabs-panels-noborder {
  border: 0px;
}
.tabs-header-plain {
  border: 0px;
  background: transparent;
}
.tabs-scroller-left {
  background: #E0ECFF url('images/tabs_icons.png') no-repeat 1px center;
}
.tabs-scroller-right {
  background: #E0ECFF url('images/tabs_icons.png') no-repeat -15px center;
}
.tabs li a.tabs-close {
  background: url('images/tabs_icons.png') no-repeat -34px center;
}
.tabs li a.tabs-inner:hover {
  background: #eaf2ff;
  color: #000000;
  filter: none;
}
.tabs li.tabs-selected a.tabs-inner {
  background-color: #ffffff;
  color: #0E2D5F;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #ffffff 100%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #ffffff 100%);
  background: -o-linear-gradient(top, #EFF5FF 0, #ffffff 100%);
  background: linear-gradient(to bottom, #EFF5FF 0, #ffffff 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#ffffff, GradientType=0);
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(top, #ffffff 0, #EFF5FF 100%);
  background: -moz-linear-gradient(top, #ffffff 0, #EFF5FF 100%);
  background: -o-linear-gradient(top, #ffffff 0, #EFF5FF 100%);
  background: linear-gradient(to bottom, #ffffff 0, #EFF5FF 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#EFF5FF, GradientType=0);
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(left, #EFF5FF 0, #ffffff 100%);
  background: -moz-linear-gradient(left, #EFF5FF 0, #ffffff 100%);
  background: -o-linear-gradient(left, #EFF5FF 0, #ffffff 100%);
  background: linear-gradient(to right, #EFF5FF 0, #ffffff 100%);
  background-repeat: repeat-y;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#ffffff, GradientType=1);
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(left, #ffffff 0, #EFF5FF 100%);
  background: -moz-linear-gradient(left, #ffffff 0, #EFF5FF 100%);
  background: -o-linear-gradient(left, #ffffff 0, #EFF5FF 100%);
  background: linear-gradient(to right, #ffffff 0, #EFF5FF 100%);
  background-repeat: repeat-y;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#EFF5FF, GradientType=1);
}
.tabs li a.tabs-inner {
  color: #0E2D5F;
  background-color: #E0ECFF;
  background: -webkit-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -moz-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: -o-linear-gradient(top, #EFF5FF 0, #E0ECFF 100%);
  background: linear-gradient(to bottom, #EFF5FF 0, #E0ECFF 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#EFF5FF, endColorstr=#E0ECFF, GradientType=0);
}
.tabs-header,
.tabs-tool {
  background-color: #E0ECFF;
}
.tabs-header-plain {
  background: transparent;
}
.tabs-header,
.tabs-scroller-left,
.tabs-scroller-right,
.tabs-tool,
.tabs,
.tabs-panels,
.tabs li a.tabs-inner,
.tabs li.tabs-selected a.tabs-inner,
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner,
.tabs-header-left .tabs li.tabs-selected a.tabs-inner,
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-color: #95B8E7;
}
.tabs-p-tool a:hover,
.tabs li a:hover.tabs-close,
.tabs-scroller-over {
  background-color: #eaf2ff;
}
.tabs li.tabs-selected a.tabs-inner {
  border-bottom: 1px solid #ffffff;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  border-top: 1px solid #ffffff;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  border-right: 1px solid #ffffff;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-left: 1px solid #ffffff;
}
.l-btn {
  text-decoration: none;
  display: inline-block;
  margin: 0;
  padding: 0;
  cursor: pointer;
  outline: none;
  text-align: center;
  vertical-align: middle;
}
.l-btn-plain {
  border: 0;
  padding: 1px;
}
.l-btn-disabled {
  color: #ccc;
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
.l-btn-left {
  display: inline-block;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  vertical-align: top;
}
.l-btn-text {
  display: inline-block;
  vertical-align: top;
  width: auto;
  line-height: 24px;
  font-size: 12px;
  padding: 0;
  margin: 0 4px;
}
.l-btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  font-size: 1px;
}
.l-btn span span .l-btn-empty {
  display: inline-block;
  margin: 0;
  width: 16px;
  height: 24px;
  font-size: 1px;
  vertical-align: top;
}
.l-btn span .l-btn-icon-left {
  padding: 0 0 0 20px;
  background-position: left center;
}
.l-btn span .l-btn-icon-right {
  padding: 0 20px 0 0;
  background-position: right center;
}
.l-btn-icon-left .l-btn-text {
  margin: 0 4px 0 24px;
}
.l-btn-icon-left .l-btn-icon {
  left: 4px;
}
.l-btn-icon-right .l-btn-text {
  margin: 0 24px 0 4px;
}
.l-btn-icon-right .l-btn-icon {
  right: 4px;
}
.l-btn-icon-top .l-btn-text {
  margin: 20px 4px 0 4px;
}
.l-btn-icon-top .l-btn-icon {
  top: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-icon-bottom .l-btn-text {
  margin: 0 4px 20px 4px;
}
.l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-left .l-btn-empty {
  margin: 0 4px;
  width: 16px;
}
.l-btn-plain:hover {
  padding: 0;
}
.l-btn-focus {
  outline: #0000FF dotted thin;
}
.l-btn-large .l-btn-text {
  line-height: 40px;
}
.l-btn-large .l-btn-icon {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-top: -16px;
}
.l-btn-large .l-btn-icon-left .l-btn-text {
  margin-left: 40px;
}
.l-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.l-btn-large .l-btn-icon-top .l-btn-text {
  margin-top: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-top .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-left .l-btn-empty {
  margin: 0 4px;
  width: 32px;
}
.l-btn {
  color: #444;
  background: #fafafa;
  border: 1px solid #bbb;
  background: -webkit-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -moz-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -o-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: linear-gradient(to bottom, #ffffff 0, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#eeeeee, GradientType=0);
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn:hover {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  filter: none;
}
.l-btn-plain {
  background: transparent;
  border: 0;
  filter: none;
}
.l-btn-plain:hover {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn-disabled,
.l-btn-disabled:hover {
  background: #fafafa;
  color: #444;
  background: -webkit-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -moz-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: -o-linear-gradient(top, #ffffff 0, #eeeeee 100%);
  background: linear-gradient(to bottom, #ffffff 0, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#eeeeee, GradientType=0);
  filter: alpha(opacity=50) progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff, endColorstr=#eeeeee, GradientType=0);
}
.l-btn-plain-disabled,
.l-btn-plain-disabled:hover {
  background: transparent;
  filter: alpha(opacity=50);
}
.l-btn-selected,
.l-btn-selected:hover {
  background: #ddd;
  filter: none;
}
.l-btn-plain-selected,
.l-btn-plain-selected:hover {
  background: #ddd;
}
.datagrid .panel-body {
  overflow: hidden;
  position: relative;
}
.datagrid-view {
  position: relative;
  overflow: hidden;
}
.datagrid-view1,
.datagrid-view2 {
  position: absolute;
  overflow: hidden;
  top: 0;
}
.datagrid-view1 {
  left: 0;
}
.datagrid-view2 {
  right: 0;
}
.datagrid-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  filter: alpha(opacity=30);
  display: none;
}
.datagrid-mask-msg {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  padding: 10px 5px 10px 30px;
  width: auto;
  height: 16px;
  border-width: 2px;
  border-style: solid;
  display: none;
}
.datagrid-sort-icon {
  padding: 0;
}
.datagrid-toolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.datagrid .datagrid-pager {
  display: block;
  margin: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.datagrid .datagrid-pager-top {
  border-width: 0 0 1px 0;
}
.datagrid-header {
  overflow: hidden;
  cursor: default;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-header-inner {
  float: left;
  width: 10000px;
}
.datagrid-header-row,
.datagrid-row {
  height: 25px;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-width: 0 1px 1px 0;
  border-style: dotted;
  margin: 0;
  padding: 0;
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  margin: 0;
  padding: 0 4px;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
}
.datagrid-header .datagrid-cell {
  height: auto;
}
.datagrid-header .datagrid-cell span {
  font-size: 12px;
}
.datagrid-cell-group {
  text-align: center;
}
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  width: 25px;
  text-align: center;
  margin: 0;
  padding: 0;
}
.datagrid-body {
  margin: 0;
  padding: 0;
  overflow: auto;
  zoom: 1;
}
.datagrid-view1 .datagrid-body-inner {
  padding-bottom: 20px;
}
.datagrid-view1 .datagrid-body {
  overflow: hidden;
}
.datagrid-footer {
  overflow: hidden;
}
.datagrid-footer-inner {
  border-width: 1px 0 0 0;
  border-style: solid;
  width: 10000px;
  float: left;
}
.datagrid-row-editing .datagrid-cell {
  height: auto;
}
.datagrid-header-check,
.datagrid-cell-check {
  padding: 0;
  width: 27px;
  height: 18px;
  font-size: 1px;
  text-align: center;
  overflow: hidden;
}
.datagrid-header-check input,
.datagrid-cell-check input {
  margin: 0;
  padding: 0;
  width: 15px;
  height: 18px;
}
.datagrid-resize-proxy {
  position: absolute;
  width: 1px;
  height: 10000px;
  top: 0;
  cursor: e-resize;
  display: none;
}
.datagrid-body .datagrid-editable {
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable table {
  width: 100%;
  height: 100%;
}
.datagrid-body .datagrid-editable td {
  border: 0;
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable .datagrid-editable-input {
  margin: 0;
  padding: 2px;
  border-width: 1px;
  border-style: solid;
}
.datagrid-sort .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/blue/datagrid_icons.png') no-repeat -64px center;
}
.datagrid-sort-desc .datagrid-sort-icon {
  padding: 0 13px 0 0;
  background: url('images/blue/datagrid_icons.png') no-repeat -16px center;
}
.datagrid-sort-asc .datagrid-sort-icon {
  padding: 0 13px 0 0;
  background: url('images/blue/datagrid_icons.png') no-repeat 0px center;
}
.datagrid-row-collapse {
  background: url('images/blue/datagrid_icons.png') no-repeat -48px center;
}
.datagrid-row-expand {
  background: url('images/blue/datagrid_icons.png') no-repeat -32px center;
}
.datagrid-mask-msg {
  background: #ffffff url('images/blue/loading.gif') no-repeat scroll 5px center;
}
.datagrid-header,
.datagrid-td-rownumber {
  background-color: #efefef;
  background: -webkit-linear-gradient(top, #F9F9F9 0, #efefef 100%);
  background: -moz-linear-gradient(top, #F9F9F9 0, #efefef 100%);
  background: -o-linear-gradient(top, #F9F9F9 0, #efefef 100%);
  background: linear-gradient(to bottom, #F9F9F9 0, #efefef 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F9F9F9, endColorstr=#efefef, GradientType=0);
}
.datagrid-cell-rownumber {
  color: #000000;
}
.datagrid-resize-proxy {
  background: #aac5e7;
}
.datagrid-mask {
  background: #ccc;
}
.datagrid-mask-msg {
  border-color: #95B8E7;
}
.datagrid-toolbar,
.datagrid-pager {
  background: #F4F4F4;
}
.datagrid-header,
.datagrid-toolbar,
.datagrid-pager,
.datagrid-footer-inner {
  border-color: #dddddd;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #ccc;
}
.datagrid-htable,
.datagrid-btable,
.datagrid-ftable {
  color: #000000;
  border-collapse: separate;
}
.datagrid-row-alt {
  background: #fafafa;
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #eaf2ff;
  color: #000000;
  cursor: default;
}
.datagrid-row-selected {
  background: #ffe48d;
  color: #000000;
}
.datagrid-body .datagrid-editable .datagrid-editable-input {
  border-color: #95B8E7;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  padding-bottom: 1px;
  border-width: 0 1px 0 0;
}
.propertygrid .datagrid-group {
  height: 21px;
  overflow: hidden;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.propertygrid .datagrid-group span {
  font-weight: bold;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  border-color: #dddddd;
}
.propertygrid .datagrid-view1 .datagrid-group {
  border-color: #E0ECFF;
}
.propertygrid .datagrid-view2 .datagrid-group {
  border-color: #dddddd;
}
.propertygrid .datagrid-group,
.propertygrid .datagrid-view1 .datagrid-body,
.propertygrid .datagrid-view1 .datagrid-row-over,
.propertygrid .datagrid-view1 .datagrid-row-selected {
  background: #E0ECFF;
}
.pagination {
  zoom: 1;
}
.pagination table {
  float: left;
  height: 30px;
}
.pagination td {
  border: 0;
}
.pagination-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 3px 1px;
}
.pagination .pagination-num {
  border-width: 1px;
  border-style: solid;
  margin: 0 2px;
  padding: 2px;
  width: 2em;
  height: auto;
}
.pagination-page-list {
  margin: 0px 6px;
  padding: 1px 2px;
  width: auto;
  height: auto;
  border-width: 1px;
  border-style: solid;
}
.pagination-info {
  float: right;
  margin: 0 6px 0 0;
  padding: 0;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
.pagination span {
  font-size: 12px;
}
.pagination-link .l-btn-text {
  width: 24px;
  text-align: center;
  margin: 0;
}
.pagination-first {
  background: url('images/blue/pagination_icons.png') no-repeat 0 center;
}
.pagination-prev {
  background: url('images/blue/pagination_icons.png') no-repeat -16px center;
}
.pagination-next {
  background: url('images/blue/pagination_icons.png') no-repeat -32px center;
}
.pagination-last {
  background: url('images/blue/pagination_icons.png') no-repeat -48px center;
}
.pagination-load {
  background: url('images/blue/pagination_icons.png') no-repeat -64px center;
}
.pagination-loading {
  background: url('images/blue/loading.gif') no-repeat center center;
}
.pagination-page-list,
.pagination .pagination-num {
  border-color: #95B8E7;
}
.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 12px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 12px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 22px;
}
.calendar-title {
  text-align: center;
  height: 22px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 2px;
  padding: 0 3px;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  width: 14px;
  height: 14px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth {
  left: 20px;
  background: url('images/calendar_arrows.png') no-repeat -18px -2px;
}
.calendar-nextmonth {
  right: 20px;
  background: url('images/calendar_arrows.png') no-repeat -34px -2px;
}
.calendar-prevyear {
  left: 3px;
  background: url('images/calendar_arrows.png') no-repeat -1px -2px;
}
.calendar-nextyear {
  right: 3px;
  background: url('images/calendar_arrows.png') no-repeat -49px -2px;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.calendar-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 12px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 40px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  margin: 0;
  padding: 2px;
  font-weight: bold;
  font-size: 12px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 21px;
  height: 21px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 2px 2px;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -45px 2px;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-body th,
.calendar-menu-month {
  color: #4d4d4d;
}
.calendar-day {
  color: #000000;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #95B8E7;
}
.calendar {
  border-color: #95B8E7;
}
.calendar-header {
  background: #E0ECFF;
}
.calendar-body,
.calendar-menu {
  background: #ffffff;
}
.calendar-body th {
  background: #F4F4F4;
  padding: 2px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #eaf2ff;
  color: #000000;
}
.calendar-hover {
  border: 1px solid #b7d2ff;
  padding: 0;
}
.calendar-selected {
  background-color: #ffe48d;
  color: #000000;
  border: 1px solid #ffab3f;
  padding: 0;
}
.datebox-calendar-inner {
  height: 180px;
}
.datebox-button {
  height: 18px;
  padding: 2px 5px;
  text-align: center;
}
.datebox-button a {
  font-size: 12px;
  font-weight: bold;
  text-decoration: none;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.datebox-button a:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.datebox-current,
.datebox-close {
  float: left;
}
.datebox-close {
  float: right;
}
.datebox .combo-arrow {
  background-image: url('images/datebox_arrow.png');
  background-position: center center;
}
.datebox-button {
  background-color: #F4F4F4;
}
.datebox-button a {
  color: #444;
}
.numberbox {
  border: 1px solid #95B8E7;
  margin: 0;
  padding: 0 2px;
  vertical-align: middle;
}
.spinner {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.spinner .spinner-text {
  font-size: 12px;
  border: 0px;
  line-height: 20px;
  height: 20px;
  margin: 0;
  padding: 0 2px;
  *margin-top: -1px;
  *height: 18px;
  *line-height: 18px;
  _height: 18px;
  _line-height: 18px;
  vertical-align: baseline;
}
.spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 0.6;
  filter: alpha(opacity=60);
  display: block;
  font-size: 1px;
  width: 18px;
  height: 10px;
}
.spinner-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-arrow-up {
  background: url('images/spinner_arrows.png') no-repeat 1px center;
}
.spinner-arrow-down {
  background: url('images/spinner_arrows.png') no-repeat -15px center;
}
.spinner {
  border-color: #95B8E7;
}
.spinner-arrow {
  background-color: #E0ECFF;
}
.spinner-arrow-hover {
  background-color: #eaf2ff;
}
.progressbar {
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  overflow: hidden;
  position: relative;
}
.progressbar-text {
  text-align: center;
  position: absolute;
}
.progressbar-value {
  position: relative;
  overflow: hidden;
  width: 0;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.progressbar {
  border-color: #95B8E7;
}
.progressbar-text {
  color: #000000;
  font-size: 12px;
}
.progressbar-value .progressbar-text {
  background-color: #ffe48d;
  color: #000000;
}
.searchbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.searchbox .searchbox-text {
  font-size: 12px;
  border: 0;
  margin: 0;
  padding: 0 2px;
  *margin-top: -1px;
  vertical-align: top;
}
.searchbox .searchbox-prompt {
  font-size: 12px;
  color: #ccc;
}
.searchbox-button {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.searchbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.searchbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox-button {
  background: url('images/searchbox_button.png') no-repeat center center;
}
.searchbox {
  border-color: #95B8E7;
  background-color: #fff;
}
.searchbox .l-btn-plain {
  background: #E0ECFF;
}
.searchbox .l-btn-plain-disabled,
.searchbox .l-btn-plain-disabled:hover {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-h {
  height: 22px;
}
.slider-v {
  width: 22px;
}
.slider-inner {
  position: relative;
  height: 6px;
  top: 7px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
}
.slider-handle {
  position: absolute;
  display: block;
  outline: none;
  width: 20px;
  height: 20px;
  top: -7px;
  margin-left: -10px;
}
.slider-tip {
  position: absolute;
  display: inline-block;
  line-height: 12px;
  font-size: 12px;
  white-space: nowrap;
  top: -22px;
}
.slider-rule {
  position: relative;
  top: 15px;
}
.slider-rule span {
  position: absolute;
  display: inline-block;
  font-size: 0;
  height: 5px;
  border-width: 0 0 0 1px;
  border-style: solid;
}
.slider-rulelabel {
  position: relative;
  top: 20px;
}
.slider-rulelabel span {
  position: absolute;
  display: inline-block;
  font-size: 12px;
}
.slider-v .slider-inner {
  width: 6px;
  left: 7px;
  top: 0;
  float: left;
}
.slider-v .slider-handle {
  left: 3px;
  margin-top: -10px;
}
.slider-v .slider-tip {
  left: -10px;
  margin-top: -6px;
}
.slider-v .slider-rule {
  float: left;
  top: 0;
  left: 16px;
}
.slider-v .slider-rule span {
  width: 5px;
  height: 'auto';
  border-left: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.slider-v .slider-rulelabel {
  float: left;
  top: 0;
  left: 23px;
}
.slider-handle {
  background: url('images/slider_handle.png') no-repeat;
}
.slider-inner {
  border-color: #95B8E7;
  background: #E0ECFF;
}
.slider-rule span {
  border-color: #95B8E7;
}
.slider-rulelabel span {
  color: #000000;
}
.menu {
  position: absolute;
  margin: 0;
  padding: 2px;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
}
.menu-item {
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.menu-text {
  height: 20px;
  line-height: 20px;
  float: left;
  padding-left: 28px;
}
.menu-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 2px;
  top: 50%;
  margin-top: -8px;
}
.menu-rightarrow {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 0;
  top: 50%;
  margin-top: -8px;
}
.menu-line {
  position: absolute;
  left: 26px;
  top: 0;
  height: 2000px;
  font-size: 1px;
}
.menu-sep {
  margin: 3px 0px 3px 25px;
  font-size: 1px;
}
.menu-active {
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.menu-item-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
.menu-text,
.menu-text span {
  font-size: 12px;
}
.menu-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #ccc;
  -moz-box-shadow: 2px 2px 3px #cccccc;
  -webkit-box-shadow: 2px 2px 3px #cccccc;
  box-shadow: 2px 2px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2, MakeShadow=false, ShadowOpacity=0.2);
}
.menu-rightarrow {
  background: url('images/blue/menu_arrows.png') no-repeat -32px center;
}
.menu-line {
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
}
.menu-sep {
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #fff;
}
.menu {
  background-color: #fafafa;
  border-color: #ddd;
  color: #444;
}
.menu-content {
  background: #ffffff;
}
.menu-item {
  border-color: transparent;
  _border-color: #fafafa;
}
.menu-active {
  border-color: #b7d2ff;
  color: #000000;
  background: #eaf2ff;
}
.menu-active-disabled {
  border-color: transparent;
  background: transparent;
  color: #444;
}
.m-btn-downarrow,
.s-btn-downarrow {
  display: inline-block;
  position: absolute;
  width: 16px;
  height: 16px;
  font-size: 1px;
  right: 0;
  top: 50%;
  margin-top: -8px;
}
.m-btn-active,
.s-btn-active {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  filter: none;
}
.m-btn-plain-active,
.s-btn-plain-active {
  background: transparent;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.m-btn .l-btn-left .l-btn-text {
  margin-right: 20px;
}
.m-btn .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.m-btn .l-btn-icon-right .l-btn-icon {
  right: 20px;
}
.m-btn .l-btn-icon-top .l-btn-text {
  margin-right: 4px;
  margin-bottom: 14px;
}
.m-btn .l-btn-icon-bottom .l-btn-text {
  margin-right: 4px;
  margin-bottom: 34px;
}
.m-btn .l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 20px;
}
.m-btn .l-btn-icon-top .m-btn-downarrow,
.m-btn .l-btn-icon-bottom .m-btn-downarrow {
  top: auto;
  bottom: 0px;
  left: 50%;
  margin-left: -8px;
}
.m-btn-line {
  display: inline-block;
  position: absolute;
  font-size: 1px;
  display: none;
}
.m-btn .l-btn-left .m-btn-line {
  right: 0;
  width: 16px;
  height: 500px;
  border-style: solid;
  border-color: #aac5e7;
  border-width: 0 0 0 1px;
}
.m-btn .l-btn-icon-top .m-btn-line,
.m-btn .l-btn-icon-bottom .m-btn-line {
  left: 0;
  bottom: 0;
  width: 500px;
  height: 16px;
  border-width: 1px 0 0 0;
}
.m-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 56px;
}
.m-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 50px;
}
.m-btn-downarrow,
.s-btn-downarrow {
  background: url('images/blue/menu_arrows.png') no-repeat 0 center;
}
.m-btn-plain-active,
.s-btn-plain-active {
  border-color: #b7d2ff;
  background-color: #eaf2ff;
  color: #000000;
}
.s-btn:hover .m-btn-line,
.s-btn-active .m-btn-line,
.s-btn-plain-active .m-btn-line {
  display: inline-block;
}
.l-btn:hover .s-btn-downarrow,
.s-btn-active .s-btn-downarrow,
.s-btn-plain-active .s-btn-downarrow {
  border-style: solid;
  border-color: #aac5e7;
  border-width: 0 0 0 1px;
}
.messager-body {
  padding: 10px;
  overflow: hidden;
}
.messager-button {
  text-align: center;
  padding-top: 10px;
}
.messager-button .l-btn {
  width: 70px;
}
.messager-icon {
  float: left;
  width: 32px;
  height: 32px;
  margin: 0 10px 10px 0;
}
.messager-error {
  background: url('images/messager_icons.png') no-repeat scroll -64px 0;
}
.messager-info {
  background: url('images/messager_icons.png') no-repeat scroll 0 0;
}
.messager-question {
  background: url('images/messager_icons.png') no-repeat scroll -32px 0;
}
.messager-warning {
  background: url('images/messager_icons.png') no-repeat scroll -96px 0;
}
.messager-progress {
  padding: 10px;
}
.messager-p-msg {
  margin-bottom: 5px;
}
.messager-body .messager-input {
  width: 100%;
  padding: 1px 0;
  border: 1px solid #95B8E7;
}
.tree {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.tree li {
  white-space: nowrap;
}
.tree li ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.tree-node {
  height: 18px;
  white-space: nowrap;
  cursor: pointer;
}
.tree-hit {
  cursor: pointer;
}
.tree-expanded,
.tree-collapsed,
.tree-folder,
.tree-file,
.tree-checkbox,
.tree-indent {
  display: inline-block;
  width: 16px;
  height: 18px;
  vertical-align: top;
  overflow: hidden;
}
.tree-expanded {
  background: url('images/blue/tree_icons.png') no-repeat -18px 0px;
}
.tree-expanded-hover {
  background: url('images/blue/tree_icons.png') no-repeat -50px 0px;
}
.tree-collapsed {
  background: url('images/blue/tree_icons.png') no-repeat 0px 0px;
}
.tree-collapsed-hover {
  background: url('images/blue/tree_icons.png') no-repeat -32px 0px;
}
.tree-lines .tree-expanded,
.tree-lines .tree-root-first .tree-expanded {
  background: url('images/blue/tree_icons.png') no-repeat -144px 0;
}
.tree-lines .tree-collapsed,
.tree-lines .tree-root-first .tree-collapsed {
  background: url('images/blue/tree_icons.png') no-repeat -128px 0;
}
.tree-lines .tree-node-last .tree-expanded,
.tree-lines .tree-root-one .tree-expanded {
  background: url('images/blue/tree_icons.png') no-repeat -80px 0;
}
.tree-lines .tree-node-last .tree-collapsed,
.tree-lines .tree-root-one .tree-collapsed {
  background: url('images/blue/tree_icons.png') no-repeat -64px 0;
}
.tree-line {
  background: url('images/blue/tree_icons.png') no-repeat -176px 0;
}
.tree-join {
  background: url('images/blue/tree_icons.png') no-repeat -192px 0;
}
.tree-joinbottom {
  background: url('images/blue/tree_icons.png') no-repeat -160px 0;
}
.tree-folder {
  background: url('images/blue/tree_icons.png') no-repeat -208px 0;
}
.tree-folder-open {
  background: url('images/blue/tree_icons.png') no-repeat -224px 0;
}
.tree-file {
  background: url('images/blue/tree_icons.png') no-repeat -240px 0;
}
.tree-loading {
  background: url('images/loading.gif') no-repeat center center;
}
.tree-checkbox0 {
  background: url('images/blue/tree_icons.png') no-repeat -208px -18px;
}
.tree-checkbox1 {
  background: url('images/blue/tree_icons.png') no-repeat -224px -18px;
}
.tree-checkbox2 {
  background: url('images/blue/tree_icons.png') no-repeat -240px -18px;
}
.tree-title {
  font-size: 12px;
  display: inline-block;
  text-decoration: none;
  vertical-align: top;
  white-space: nowrap;
  padding: 0 2px;
  height: 18px;
  line-height: 18px;
}
.tree-node-proxy {
  font-size: 12px;
  line-height: 20px;
  padding: 0 2px 0 20px;
  border-width: 1px;
  border-style: solid;
  z-index: 9900000;
}
.tree-dnd-icon {
  display: inline-block;
  position: absolute;
  width: 16px;
  height: 18px;
  left: 2px;
  top: 50%;
  margin-top: -9px;
}
.tree-dnd-yes {
  background: url('images/tree_icons.png') no-repeat -256px 0;
}
.tree-dnd-no {
  background: url('images/tree_icons.png') no-repeat -256px -18px;
}
.tree-node-top {
  border-top: 1px dotted red;
}
.tree-node-bottom {
  border-bottom: 1px dotted red;
}
.tree-node-append .tree-title {
  border: 1px dotted red;
}
.tree-editor {
  border: 1px solid #ccc;
  font-size: 12px;
  height: 14px !important;
  height: 18px;
  line-height: 14px;
  padding: 1px 2px;
  width: 80px;
  position: absolute;
  top: 0;
}
.tree-node-proxy {
  background-color: #ffffff;
  color: #000000;
  border-color: #95B8E7;
}
.tree-node-hover {
  background: #eaf2ff;
  color: #000000;
}
.tree-node-selected {
  background: #ffe48d;
  color: #000000;
}
.validatebox-invalid {
  background-image: url('images/validatebox_warning.png');
  background-repeat: no-repeat;
  background-position: right center;
  border-color: #ffa8a8;
  background-color: #fff3f3;
  color: #000;
}
.tooltip {
  position: absolute;
  display: none;
  z-index: 9900000;
  outline: none;
  opacity: 1;
  filter: alpha(opacity=100);
  padding: 5px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.tooltip-content {
  font-size: 12px;
}
.tooltip-arrow-outer,
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 0;
  border-style: solid;
  border-width: 6px;
  border-color: transparent;
  _border-color: tomato;
  _filter: chroma(color=tomato);
}
.tooltip-right .tooltip-arrow-outer {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -13px;
}
.tooltip-right .tooltip-arrow {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -12px;
}
.tooltip-left .tooltip-arrow-outer {
  right: 0;
  top: 50%;
  margin: -6px -13px 0 0;
}
.tooltip-left .tooltip-arrow {
  right: 0;
  top: 50%;
  margin: -6px -12px 0 0;
}
.tooltip-top .tooltip-arrow-outer {
  bottom: 0;
  left: 50%;
  margin: 0 0 -13px -6px;
}
.tooltip-top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin: 0 0 -12px -6px;
}
.tooltip-bottom .tooltip-arrow-outer {
  top: 0;
  left: 50%;
  margin: -13px 0 0 -6px;
}
.tooltip-bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin: -12px 0 0 -6px;
}
.tooltip {
  background-color: #ffffff;
  border-color: #95B8E7;
  color: #000000;
}
.tooltip-right .tooltip-arrow-outer {
  border-right-color: #95B8E7;
}
.tooltip-right .tooltip-arrow {
  border-right-color: #ffffff;
}
.tooltip-left .tooltip-arrow-outer {
  border-left-color: #95B8E7;
}
.tooltip-left .tooltip-arrow {
  border-left-color: #ffffff;
}
.tooltip-top .tooltip-arrow-outer {
  border-top-color: #95B8E7;
}
.tooltip-top .tooltip-arrow {
  border-top-color: #ffffff;
}
.tooltip-bottom .tooltip-arrow-outer {
  border-bottom-color: #95B8E7;
}
.tooltip-bottom .tooltip-arrow {
  border-bottom-color: #ffffff;
}
/*好多hover的颜色*/
/*0.7;*/
/*add 2019-12-23*/
/*#f4faff;*/
/*main-color*/
/*main-hover-color*/
/*tabs-gray*/
/*#378ec4;*/
/*#378ec4;*/
/*#E7E7E7;*/
/*#d2eafe;*/
/*#d9e7f1;#509de1;*/
/*main-hover-color*/
/*variables-end*/
body {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  padding: 10px;
  font-size: 14px;
  margin: 0;
  background-color: #FFFFFF;
}
body * {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  font-size: 14px;
}
h2 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  margin-bottom: 15px;
}
.demo-info {
  padding: 0 0 12px 0;
}
.demo-tip {
  display: none;
}
.r-label {
  text-align: right;
  padding-right: 10px;
}
.required-label:before {
  content: '*';
  color: red;
  vertical-align: bottom;
  line-height: 100%;
}
a {
  color: #017bce;
}
/*chrome默认了outline*/
input,
textarea {
  outline-width: 0;
}
#z-q-container {
  position: absolute;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid #9ed2f2;
  z-index: 9000;
}
/*create icon*/
/*create white icon*/
/*create big icon*/
.icon-blank {
  background: url('icons/blank.gif') no-repeat center center;
}
.icon-funnel-eye {
  background: url('icons/default/funnel_eye.png') no-repeat center center;
}
.icon-funnel-half {
  background: url('icons/default/funnel_half.png') no-repeat center center;
}
.icon-funnel-empty {
  background: url('icons/default/funnel_empty.png') no-repeat center center;
}
/*default icon*/
.icon-add {
  background: url('icons/edit_add.png') no-repeat center center;
}
.icon-edit {
  background: url('icons/pencil.png') no-repeat center center;
}
.icon-remove {
  background: url('icons/edit_remove.png') no-repeat center center;
}
.icon-save {
  background: url('icons/filesave.png') no-repeat center center;
}
.icon-cut {
  background: url('icons/cut.png') no-repeat center center;
}
.icon-ok {
  background: url('icons/ok.png') no-repeat center center;
}
.icon-no {
  background: url('icons/no.png') no-repeat center center;
}
.icon-cancel {
  background: url('icons/cancel.png') no-repeat center center;
}
.icon-reload {
  background: url('icons/reload.png') no-repeat center center;
}
.icon-search {
  background: url('icons/search.png') no-repeat center center;
}
.icon-print {
  background: url('icons/print.png') no-repeat center center;
}
.icon-help {
  background: url('icons/help.png') no-repeat center center;
}
.icon-undo {
  background: url('icons/undo.png') no-repeat center center;
}
.icon-redo {
  background: url('icons/redo.png') no-repeat center center;
}
.icon-back {
  background: url('icons/back.png') no-repeat center center;
}
.icon-sum {
  background: url('icons/sum.png') no-repeat center center;
}
.icon-tip {
  background: url('icons/tip.png') no-repeat center center;
}
.icon-filter {
  background: url('icons/filter.png') no-repeat center center;
}
.icon-mini-add {
  background: url('icons/mini_add.png') no-repeat center center;
}
.icon-mini-edit {
  background: url('icons/mini_edit.png') no-repeat center center;
}
.icon-mini-refresh {
  background: url('icons/mini_refresh.png') no-repeat center center;
}
.icon-large-picture {
  background: url('icons/large_picture.png') no-repeat center center;
}
.icon-large-clipart {
  background: url('icons/large_clipart.png') no-repeat center center;
}
.icon-large-shapes {
  background: url('icons/large_shapes.png') no-repeat center center;
}
.icon-large-smartart {
  background: url('icons/large_smartart.png') no-repeat center center;
}
.icon-large-chart {
  background: url('icons/large_chart.png') no-repeat center center;
}
.icon-noread-report {
  background: url('icons/noread_report.png') no-repeat center center;
}
.icon-bed {
  background: url('icons/bed.png') no-repeat center center;
}
.icon-house {
  background: url('icons/house.png') no-repeat center center;
}
.icon-person {
  background: url('icons/person.png') no-repeat center center;
}
.icon-add-note {
  background: url('icons/add_note.png') no-repeat center center;
}
.icon-patient {
  background: url('icons/outhosp_patient.png') no-repeat center center;
}
.icon-outhosp-patient {
  background: url('icons/outhosp_patient.png') no-repeat center center;
}
.icon-stethoscope {
  background: url('icons/stethoscope.png') no-repeat center center;
}
.icon-write-order {
  background: url('icons/write_order.png') no-repeat center center;
}
.icon-replace-order {
  background: url('icons/replace_order.png') no-repeat center center;
}
.icon-stop-order {
  background: url('icons/stop_order.png') no-repeat center center;
}
.icon-cancel-order {
  background: url('icons/cancel_order.png') no-repeat center center;
}
.icon-abort-order {
  background: url('icons/abort_order.png') no-repeat center center;
}
.icon-uncheckin {
  background: url('icons/uncheckin.png') no-repeat center center;
}
.icon-end-adm {
  background: url('icons/end_adm.png') no-repeat center center;
}
.icon-change-loc {
  background: url('icons/change_loc.png') no-repeat center center;
}
.icon-resort {
  background: url('icons/resort.png') no-repeat center center;
}
.icon-doctor {
  background: url('icons/doctor.png') no-repeat center center;
}
.icon-clear-screen {
  background: url('icons/clear_screen.png') no-repeat center center;
}
.icon-clear {
  background: url('icons/clear_screen.png') no-repeat center center;
}
.icon-read-card {
  background: url('icons/read_card.png') no-repeat center center;
}
.icon-update {
  background: url('icons/update.png') no-repeat center center;
}
.icon-upload-cloud {
  background: url('icons/upload_cloud.png') no-repeat center center;
}
.icon-unload-cloud {
  background: url('icons/unload_cloud.png') no-repeat center center;
}
.icon-reset {
  background: url('icons/reset.png') no-repeat center center;
}
.icon-arrow-top {
  background: url('icons/arrow_top.png') no-repeat center center;
}
.icon-arrow-bottom {
  background: url('icons/arrow_bottom.png') no-repeat center center;
}
.icon-arrow-right {
  background: url('icons/arrow_right.png') no-repeat center center;
}
.icon-arrow-left {
  background: url('icons/arrow_left.png') no-repeat center center;
}
.icon-close {
  background: url('icons/close.png') no-repeat center center;
}
.icon-save {
  background: url('icons/save.png') no-repeat center center;
}
.icon-other {
  background: url('icons/other.png') no-repeat center center;
}
.icon-patient-info {
  background: url('icons/patient_info.png') no-repeat center center;
}
.icon-apply-adm {
  background: url('icons/apply_adm.png') no-repeat center center;
}
.icon-del-diag {
  background: url('icons/del_diag.png') no-repeat center center;
}
.icon-apply-opr {
  background: url('icons/apply_opr.png') no-repeat center center;
}
.icon-add-diag {
  background: url('icons/add_diag.png') no-repeat center center;
}
.icon-save-tmpl {
  background: url('icons/save_tmpl.png') no-repeat center center;
}
.icon-apply-check {
  background: url('icons/apply_check.png') no-repeat center center;
}
.icon-check-reg {
  background: url('icons/check_reg.png') no-repeat center center;
}
.icon-exe-order {
  background: url('icons/exe_order.png') no-repeat center center;
}
.icon-emr-cri {
  background: url('icons/emr_cri.png') no-repeat center center;
}
.icon-copy-drug {
  background: url('icons/copy_drug.png') no-repeat center center;
}
.icon-copy-sos {
  background: url('icons/copy_sos.png') no-repeat center center;
}
.icon-copy-prn {
  background: url('icons/copy_prn.png') no-repeat center center;
}
.icon-copy-prn {
  background: url('icons/copy_prn.png') no-repeat center center;
}
.icon-lt-rt-37 {
  background: url('icons/lt_rt_37.png') no-repeat center center;
}
.icon-lt-rt-55 {
  background: url('icons/lt_rt_55.png') no-repeat center center;
}
.icon-lt-rt-73 {
  background: url('icons/lt_rt_73.png') no-repeat center center;
}
.icon-lt-rt-19 {
  background: url('icons/lt_rt_19.png') no-repeat center center;
}
.icon-lt-rt-46 {
  background: url('icons/lt_rt_46.png') no-repeat center center;
}
.icon-lt-rt-64 {
  background: url('icons/lt_rt_64.png') no-repeat center center;
}
.icon-arrow-left-top {
  background: url('icons/arrow_left_top.png') no-repeat center center;
}
.icon-arrow-right-top {
  background: url('icons/arrow_right_top.png') no-repeat center center;
}
.icon-bold {
  background: url('icons/bold.png') no-repeat center center;
}
.icon-font {
  background: url('icons/font.png') no-repeat center center;
}
.icon-strikethrough {
  background: url('icons/strikethrough.png') no-repeat center center;
}
.icon-underline {
  background: url('icons/underline.png') no-repeat center center;
}
.icon-incline {
  background: url('icons/incline.png') no-repeat center center;
}
.icon-subscript {
  background: url('icons/subscript.png') no-repeat center center;
}
.icon-superscript {
  background: url('icons/superscript.png') no-repeat center center;
}
.icon-indentation {
  background: url('icons/indentation.png') no-repeat center center;
}
.icon-align-left {
  background: url('icons/align_left.png') no-repeat center center;
}
.icon-align-center {
  background: url('icons/align_center.png') no-repeat center center;
}
.icon-align-right {
  background: url('icons/align_right.png') no-repeat center center;
}
.icon-cut-blue {
  background: url('icons/cut_blue.png') no-repeat center center;
}
.icon-paste {
  background: url('icons/paste.png') no-repeat center center;
}
.icon-paste-board {
  background: url('icons/paste_board.png') no-repeat center center;
}
.icon-copy {
  background: url('icons/copy.png') no-repeat center center;
}
.icon-add {
  background: url('icons/add.png') no-repeat center center;
}
.icon-paper-cfg {
  background: url('icons/paper_cfg.png') no-repeat center center;
}
.icon-export {
  background: url('icons/export.png') no-repeat center center;
}
.icon-omega {
  background: url('icons/omega.png') no-repeat center center;
}
.icon-eye {
  background: url('icons/eye.png') no-repeat center center;
}
.icon-fee-arrow {
  background: url('icons/fee_arrow.png') no-repeat center center;
}
.icon-paper-arrow {
  background: url('icons/paper_arrow.png') no-repeat center center;
}
.icon-checkin {
  background: url('icons/checkin.png') no-repeat center center;
}
.icon-paper {
  background: url('icons/paper.png') no-repeat center center;
}
.icon-fee {
  background: url('icons/fee.png') no-repeat center center;
}
.icon-paper-tri {
  background: url('icons/paper_tri.png') no-repeat center center;
}
.icon-outpatient {
  background: url('icons/outpatient.png') no-repeat center center;
}
.icon-inpatient {
  background: url('icons/inpatient.png') no-repeat center center;
}
.icon-emergency {
  background: url('icons/emergency.png') no-repeat center center;
}
.icon-star-light-yellow {
  background: url('icons/star_light_yellow.png') no-repeat center center;
}
.icon-star-yellow {
  background: url('icons/star_yellow.png') no-repeat center center;
}
.icon-book {
  background: url('icons/book.png') no-repeat center center;
}
.icon-all-select {
  background: url('icons/all_select.png') no-repeat center center;
}
.icon-cancel-select-grant {
  background: url('icons/cancel_select_grant.png') no-repeat center center;
}
.icon-select-grant {
  background: url('icons/select_grant.png') no-repeat center center;
}
.icon-refuse-select-grant {
  background: url('icons/refuse_select_grant.png') no-repeat center center;
}
.icon-double-quotes {
  background: url('icons/double_quotes.png') no-repeat center center;
}
.icon-transfer {
  background: url('icons/transfer.png') no-repeat center center;
}
.icon-align-justify {
  background: url('icons/align_justify.png') no-repeat center center;
}
.icon-unindent {
  background: url('icons/unindent.png') no-repeat center center;
}
.icon-stamp {
  background: url('icons/stamp.png') no-repeat center center;
}
.icon-paper-stamp {
  background: url('icons/paper_stamp.png') no-repeat center center;
}
.icon-batch-cfg {
  background: url('icons/batch_cfg.png') no-repeat center center;
}
.icon-batch-add {
  background: url('icons/batch_add.png') no-repeat center center;
}
.icon-import-xls {
  background: url('icons/import_xls.png') no-repeat center center;
}
.icon-init {
  background: url('icons/init.png') no-repeat center center;
}
.icon-inv-search {
  background: url('icons/inv_search.png') no-repeat center center;
}
.icon-submit {
  background: url('icons/submit.png') no-repeat center center;
}
.icon-chart-year {
  background: url('icons/chart_year.png') no-repeat center center;
}
.icon-chart-sum {
  background: url('icons/chart_sum.png') no-repeat center center;
}
.icon-attachment {
  background: url('icons/attachment.png') no-repeat center center;
}
.icon-import-reset {
  background: url('icons/import_reset.png') no-repeat center center;
}
.icon-stamp-cancel {
  background: url('icons/stamp_cancel.png') no-repeat center center;
}
.icon-stamp-pass {
  background: url('icons/stamp_pass.png') no-repeat center center;
}
.icon-mnypaper-cfg {
  background: url('icons/mnypaper_cfg.png') no-repeat center center;
}
.icon-mnypaper-run {
  background: url('icons/mnypaper_run.png') no-repeat center center;
}
.icon-mnypaper-ok {
  background: url('icons/mnypaper_ok.png') no-repeat center center;
}
.icon-mnypaper-no {
  background: url('icons/mnypaper_no.png') no-repeat center center;
}
.icon-mnypaper-down {
  background: url('icons/mnypaper_down.png') no-repeat center center;
}
.icon-mnypaper-down2 {
  background: url('icons/mnypaper_down2.png') no-repeat center center;
}
.icon-paper-money {
  background: url('icons/paper_money.png') no-repeat center center;
}
.icon-line-paid {
  background: url('icons/line-paid.png') no-repeat center center;
}
.icon-paper-arrow-down {
  background: url('icons/paper_arrow_down.png') no-repeat center center;
}
.icon-paper-arrow-up {
  background: url('icons/paper_arrow_up.png') no-repeat center center;
}
.icon-split {
  background: url('icons/split.png') no-repeat center center;
}
.icon-alert {
  background: url('icons/alert.png') no-repeat center center;
}
.icon-star {
  background: url('icons/star.png') no-repeat center center;
}
.icon-star-half {
  background: url('icons/star_half.png') no-repeat center center;
}
.icon-user {
  background: url('icons/user.png') no-repeat center center;
}
.icon-money-down {
  background: url('icons/money_down.png') no-repeat center center;
}
.icon-max-no {
  background: url('icons/max_no.png') no-repeat center center;
}
.icon-paper-link {
  background: url('icons/paper_link.png') no-repeat center center;
}
.icon-excel {
  background: url('icons/excel.png') no-repeat center center;
}
.icon-img {
  background: url('icons/img.png') no-repeat center center;
}
.icon-save-to {
  background: url('icons/save_to.png') no-repeat center center;
}
.icon-save-sure {
  background: url('icons/save_sure.png') no-repeat center center;
}
.icon-star-empty {
  background: url('icons/star_empty.png') no-repeat center center;
}
.icon-paper-info {
  background: url('icons/paper_info.png') no-repeat center center;
}
.icon-export-data {
  background: url('icons/export_data.png') no-repeat center center;
}
.icon-export-all {
  background: url('icons/export_all.png') no-repeat center center;
}
.icon-set-col {
  background: url('icons/set_col.png') no-repeat center center;
}
.icon-template {
  background: url('icons/template.png') no-repeat center center;
}
.icon-adjust-inventory {
  background: url('icons/adjust_inventory.png') no-repeat center center;
}
.icon-cancel-money {
  background: url('icons/cancel_money.png') no-repeat center center;
}
.icon-set-zero {
  background: url('icons/set_zero.png') no-repeat center center;
}
.icon-set-paper {
  background: url('icons/set_paper.png') no-repeat center center;
}
.icon-accept-money {
  background: url('icons/accept_money.png') no-repeat center center;
}
.icon-accept {
  background: url('icons/accept.png') no-repeat center center;
}
.icon-show-set {
  background: url('icons/show_set.png') no-repeat center center;
}
.icon-camera {
  background: url('icons/camera.png') no-repeat center center;
}
.icon-download {
  background: url('icons/download.png') no-repeat center center;
}
.icon-import {
  background: url('icons/import.png') no-repeat center center;
}
.icon-all-unselect {
  background: url('icons/all_unselect.png') no-repeat center center;
}
.icon-blue-edit {
  background: url('icons/blue_edit.png') no-repeat center center;
}
.icon-gray-edit {
  background: url('icons/gray_edit.png') no-repeat center center;
}
.icon-pat-write {
  background: url('icons/pat_write.png') no-repeat center center;
}
.icon-run {
  background: url('icons/run.png') no-repeat center center;
}
.icon-key {
  background: url('icons/key.png') no-repeat center center;
}
.icon-down {
  background: url('icons/down.png') no-repeat center center;
}
.icon-up {
  background: url('icons/up.png') no-repeat center center;
}
.icon-fx {
  background: url('icons/fx.png') no-repeat center center;
}
.icon-muti-key {
  background: url('icons/muti_key.png') no-repeat center center;
}
.icon-pause {
  background: url('icons/pause.png') no-repeat center center;
}
.icon-forbid {
  background: url('icons/forbid.png') no-repeat center center;
}
.icon-make-oppointment {
  background: url('icons/make_oppointment.png') no-repeat center center;
}
.icon-unuse {
  background: url('icons/unuse.png') no-repeat center center;
}
.icon-ignore {
  background: url('icons/ignore.png') no-repeat center center;
}
.icon-re-ignore {
  background: url('icons/re_ignore.png') no-repeat center center;
}
.icon-arrow-up {
  background: url('icons/arrow_up.png') no-repeat center center;
}
.icon-scanning {
  background: url('icons/scanning.png') no-repeat center center;
}
.icon-return {
  background: url('icons/return.png') no-repeat center center;
}
.icon-doc-caseload {
  background: url('icons/doc_caseload.png') no-repeat center center;
}
.icon-snowflake-blue {
  background: url('icons/snowflake_blue.png') no-repeat center center;
}
.icon-speci-mt {
  background: url('icons/speci_mt.png') no-repeat center center;
}
.icon-print-box {
  background: url('icons/print_box.png') no-repeat center center;
}
.icon-tube-add {
  background: url('icons/tube_add.png') no-repeat center center;
}
.icon-tube-del {
  background: url('icons/tube_del.png') no-repeat center center;
}
.icon-wrench-blue {
  background: url('icons/wrench_blue.png') no-repeat center center;
}
.icon-gen {
  background: url('icons/gen.png') no-repeat center center;
}
.icon-paper-submit {
  background: url('icons/paper_submit.png') no-repeat center center;
}
.icon-open-book {
  background: url('icons/open_book.png') no-repeat center center;
}
.icon-export-paper {
  background: url('icons/export_paper.png') no-repeat center center;
}
.icon-track {
  background: url('icons/track.png') no-repeat center center;
}
.icon-paper-eye {
  background: url('icons/paper_eye.png') no-repeat center center;
}
.icon-run-red {
  background: url('icons/run_red.png') no-repeat center center;
}
.icon-compare-no {
  background: url('icons/compare_no.png') no-repeat center center;
}
.icon-compare-yes {
  background: url('icons/compare_yes.png') no-repeat center center;
}
.icon-compare {
  background: url('icons/compare.png') no-repeat center center;
}
.icon-adm-add {
  background: url('icons/adm_add.png') no-repeat center center;
}
.icon-adm-same {
  background: url('icons/adm_same.png') no-repeat center center;
}
.icon-alert-red {
  background: url('icons/alert_red.png') no-repeat center center;
}
.icon-allergy-word {
  background: url('icons/allergy_word.png') no-repeat center center;
}
.icon-all-screen {
  background: url('icons/all_screen.png') no-repeat center center;
}
.icon-analysis {
  background: url('icons/analysis.png') no-repeat center center;
}
.icon-arrow-blue {
  background: url('icons/arrow_blue.png') no-repeat center center;
}
.icon-arrow-zoom {
  background: url('icons/arrow_zoom.png') no-repeat center center;
}
.icon-audit-x {
  background: url('icons/audit_x.png') no-repeat center center;
}
.icon-base-info {
  background: url('icons/base_info.png') no-repeat center center;
}
.icon-base-word {
  background: url('icons/base_word.png') no-repeat center center;
}
.icon-blue-drug-ok {
  background: url('icons/blue_drug_ok.png') no-repeat center center;
}
.icon-blue-move {
  background: url('icons/blue_move.png') no-repeat center center;
}
.icon-book-green {
  background: url('icons/book_green.png') no-repeat center center;
}
.icon-bottle-drug {
  background: url('icons/bottle_drug.png') no-repeat center center;
}
.icon-cal-pen {
  background: url('icons/cal_pen.png') no-repeat center center;
}
.icon-cancel-ref {
  background: url('icons/cancel_ref.png') no-repeat center center;
}
.icon-cancel-top {
  background: url('icons/cancel_top.png') no-repeat center center;
}
.icon-check {
  background: url('icons/check.png') no-repeat center center;
}
.icon-checkbox {
  background: url('icons/checkbox.png') no-repeat center center;
}
.icon-chg-doctor-grant {
  background: url('icons/chg_doctor_grant.png') no-repeat center center;
}
.icon-clock {
  background: url('icons/clock.png') no-repeat center center;
}
.icon-clock-blod {
  background: url('icons/clock_blod.png') no-repeat center center;
}
.icon-clock-record {
  background: url('icons/clock_record.png') no-repeat center center;
}
.icon-clock-black {
  background: url('icons/clock_black.png') no-repeat center center;
}
.icon-close-word {
  background: url('icons/close_word.png') no-repeat center center;
}
.icon-complex-word {
  background: url('icons/complex_word.png') no-repeat center center;
}
.icon-contain {
  background: url('icons/contain.png') no-repeat center center;
}
.icon-doctor-green-no {
  background: url('icons/doctor_green_no.png') no-repeat center center;
}
.icon-down-arrow-box {
  background: url('icons/down_arrow_box.png') no-repeat center center;
}
.icon-drug {
  background: url('icons/drug.png') no-repeat center center;
}
.icon-drug-arrow-red {
  background: url('icons/drug_arrow_red.png') no-repeat center center;
}
.icon-drug-audit {
  background: url('icons/drug_audit.png') no-repeat center center;
}
.icon-drug-clock {
  background: url('icons/drug_clock.png') no-repeat center center;
}
.icon-drug-link {
  background: url('icons/drug_link.png') no-repeat center center;
}
.icon-dsh-water {
  background: url('icons/dsh_water.png') no-repeat center center;
}
.icon-durg-freq {
  background: url('icons/durg_freq.png') no-repeat center center;
}
.icon-durg-ref {
  background: url('icons/durg_ref.png') no-repeat center center;
}
.icon-face-red {
  background: url('icons/face_red.png') no-repeat center center;
}
.icon-find-adm {
  background: url('icons/find_adm.png') no-repeat center center;
}
.icon-fire {
  background: url('icons/fire.png') no-repeat center center;
}
.icon-format-line {
  background: url('icons/format_line.png') no-repeat center center;
}
.icon-format-line-dott {
  background: url('icons/format_line_dott.png') no-repeat center center;
}
.icon-format-line-num {
  background: url('icons/format_line_num.png') no-repeat center center;
}
.icon-gen-barcode {
  background: url('icons/gen_barcode.png') no-repeat center center;
}
.icon-green-chart {
  background: url('icons/green_chart.png') no-repeat center center;
}
.icon-home-black {
  background: url('icons/home_black.png') no-repeat center center;
}
.icon-home-gray {
  background: url('icons/home_gray.png') no-repeat center center;
}
.icon-icd {
  background: url('icons/icd.png') no-repeat center center;
}
.icon-injector {
  background: url('icons/injector.png') no-repeat center center;
}
.icon-knw-submit {
  background: url('icons/knw_submit.png') no-repeat center center;
}
.icon-list-word {
  background: url('icons/list_word.png') no-repeat center center;
}
.icon-location {
  background: url('icons/location.png') no-repeat center center;
}
.icon-lock {
  background: url('icons/lock.png') no-repeat center center;
}
.icon-move-left-most {
  background: url('icons/move_left_most.png') no-repeat center center;
}
.icon-move-up-most {
  background: url('icons/move_up_most.png') no-repeat center center;
}
.icon-mtpaper-add {
  background: url('icons/mtpaper_add.png') no-repeat center center;
}
.icon-mtpaper-arrw-lftp {
  background: url('icons/mtpaper_arrw_lftp.png') no-repeat center center;
}
.icon-mtpaper-redo {
  background: url('icons/mtpaper_redo.png') no-repeat center center;
}
.icon-mtpaper-undo {
  background: url('icons/mtpaper_undo.png') no-repeat center center;
}
.icon-mttext {
  background: url('icons/mttext.png') no-repeat center center;
}
.icon-mutpaper-tri {
  background: url('icons/mutpaper_tri.png') no-repeat center center;
}
.icon-mutpaper-x {
  background: url('icons/mutpaper_x.png') no-repeat center center;
}
.icon-nail {
  background: url('icons/nail.png') no-repeat center center;
}
.icon-no-conatin {
  background: url('icons/no_conatin.png') no-repeat center center;
}
.icon-other-yellow {
  background: url('icons/other_yellow.png') no-repeat center center;
}
.icon-paper-bed {
  background: url('icons/paper_bed.png') no-repeat center center;
}
.icon-paper-blue-line {
  background: url('icons/paper_blue_line.png') no-repeat center center;
}
.icon-paper-stat {
  background: url('icons/paper_stat.png') no-repeat center center;
}
.icon-paper-chart {
  background: url('icons/paper_chart.png') no-repeat center center;
}
.icon-paper-drug {
  background: url('icons/paper_drug.png') no-repeat center center;
}
.icon-paper-link-pen {
  background: url('icons/paper_link_pen.png') no-repeat center center;
}
.icon-paper-new {
  background: url('icons/paper_new.png') no-repeat center center;
}
.icon-paper-no {
  background: url('icons/paper_no.png') no-repeat center center;
}
.icon-paper-ok {
  background: url('icons/paper_ok.png') no-repeat center center;
}
.icon-paper-opr-record {
  background: url('icons/paper_opr_record.png') no-repeat center center;
}
.icon-paper-save {
  background: url('icons/paper_save.png') no-repeat center center;
}
.icon-paper-table {
  background: url('icons/paper_table.png') no-repeat center center;
}
.icon-paper-upgrade {
  background: url('icons/paper_upgrade.png') no-repeat center center;
}
.icon-paper-upgrade-add {
  background: url('icons/paper_upgrade_add.png') no-repeat center center;
}
.icon-paper-x {
  background: url('icons/paper_x.png') no-repeat center center;
}
.icon-pat-add-red {
  background: url('icons/pat_add_red.png') no-repeat center center;
}
.icon-pat-alert-red {
  background: url('icons/pat_alert_red.png') no-repeat center center;
}
.icon-pat-house {
  background: url('icons/pat_house.png') no-repeat center center;
}
.icon-pat-opr {
  background: url('icons/pat_opr.png') no-repeat center center;
}
.icon-private-word {
  background: url('icons/private_word.png') no-repeat center center;
}
.icon-public-word {
  background: url('icons/public_word.png') no-repeat center center;
}
.icon-radio {
  background: url('icons/radio.png') no-repeat center center;
}
.icon-ref {
  background: url('icons/ref.png') no-repeat center center;
}
.icon-repeat-drug {
  background: url('icons/repeat_drug.png') no-repeat center center;
}
.icon-right-arrow {
  background: url('icons/right_arrow.png') no-repeat center center;
}
.icon-switch {
  background: url('icons/switch.png') no-repeat center center;
}
.icon-tel {
  background: url('icons/tel.png') no-repeat center center;
}
.icon-text {
  background: url('icons/text.png') no-repeat center center;
}
.icon-text-word {
  background: url('icons/text_word.png') no-repeat center center;
}
.icon-tip-blue {
  background: url('icons/tip_blue.png') no-repeat center center;
}
.icon-top-green {
  background: url('icons/top_green.png') no-repeat center center;
}
.icon-translate-word {
  background: url('icons/translate_word.png') no-repeat center center;
}
.icon-trans-pat {
  background: url('icons/trans_pat.png') no-repeat center center;
}
.icon-tube {
  background: url('icons/tube.png') no-repeat center center;
}
.icon-unlock {
  background: url('icons/unlock.png') no-repeat center center;
}
.icon-upload {
  background: url('icons/upload.png') no-repeat center center;
}
.icon-virus {
  background: url('icons/virus.png') no-repeat center center;
}
.icon-virus-drug {
  background: url('icons/virus_drug.png') no-repeat center center;
}
.icon-water-drop {
  background: url('icons/water_drop.png') no-repeat center center;
}
.icon-yellow-qa {
  background: url('icons/yellow_qa.png') no-repeat center center;
}
.icon-down-blue {
  background: url('icons/down_blue.png') no-repeat center center;
}
.icon-export-report {
  background: url('icons/export_report.png') no-repeat center center;
}
.icon-add-report {
  background: url('icons/add_report.png') no-repeat center center;
}
.icon-finish-report {
  background: url('icons/finish_report.png') no-repeat center center;
}
.icon-paper-plane {
  background: url('icons/paper_plane.png') no-repeat center center;
}
.icon-undo-paper-plane {
  background: url('icons/undo_paper_plane.png') no-repeat center center;
}
.icon-h24-stat {
  background: url('icons/h24_stat.png') no-repeat center center;
}
.icon-stat {
  background: url('icons/stat.png') no-repeat center center;
}
.icon-paper-pen {
  background: url('icons/paper_pen.png') no-repeat center center;
}
.icon-wating {
  background: url('icons/wating.png') no-repeat center center;
}
.icon-done {
  background: url('icons/done.png') no-repeat center center;
}
.icon-user-black {
  background: url('icons/user_black.png') no-repeat center center;
}
.icon-report-check-black {
  background: url('icons/report_check_black.png') no-repeat center center;
}
.icon-up-gray {
  background: url('icons/up_gray.png') no-repeat center center;
}
.icon-down-gray {
  background: url('icons/down_gray.png') no-repeat center center;
}
.icon-pat-info {
  background: url('icons/pat_info.png') no-repeat center center;
}
.icon-tooth {
  background: url('icons/tooth.png') no-repeat center center;
}
.icon-paid {
  background: url('icons/paid.png') no-repeat center center;
}
.icon-return-paid {
  background: url('icons/return_paid.png') no-repeat center center;
}
.icon-int-bill {
  background: url('icons/int_bill.png') no-repeat center center;
}
.icon-cancel-int-bill {
  background: url('icons/cancel_int_bill.png') no-repeat center center;
}
.icon-reprint-inv {
  background: url('icons/reprint_inv.png') no-repeat center center;
}
.icon-print-inv {
  background: url('icons/print_inv.png') no-repeat center center;
}
.icon-rebill {
  background: url('icons/rebill.png') no-repeat center center;
}
.icon-pat-fee-det {
  background: url('icons/pat_fee_det.png') no-repeat center center;
}
.icon-find-paid-det {
  background: url('icons/find_paid_det.png') no-repeat center center;
}
.icon-find-ord-det {
  background: url('icons/find_ord_det.png') no-repeat center center;
}
.icon-find-fee-itm {
  background: url('icons/find_fee_itm.png') no-repeat center center;
}
.icon-red-cancel-paper {
  background: url('icons/red_cancel_paper.png') no-repeat center center;
}
.icon-skip-no {
  background: url('icons/skip_no.png') no-repeat center center;
}
.icon-paper-ques {
  background: url('icons/paper_ques.png') no-repeat center center;
}
.icon-injector-water {
  background: url('icons/injector_water.png') no-repeat center center;
}
.icon-alert-pen {
  background: url('icons/alert_pen.png') no-repeat center center;
}
.icon-board-alert {
  background: url('icons/board_alert.png') no-repeat center center;
}
.icon-paper-key {
  background: url('icons/paper_key.png') no-repeat center center;
}
.icon-doctor-green-pen {
  background: url('icons/doctor_green_pen.png') no-repeat center center;
}
.icon-paper-group {
  background: url('icons/paper_group.png') no-repeat center center;
}
.icon-minus {
  background: url('icons/minus.png') no-repeat center center;
}
.icon-alarm {
  background: url('icons/alarm.png') no-repeat center center;
}
.icon-ip-cfg {
  background: url('icons/ip_cfg.png') no-repeat center center;
}
.icon-have-son-node {
  background: url('icons/have_son_node.png') no-repeat center center;
}
.icon-mater-info {
  background: url('icons/mater_info.png') no-repeat center center;
}
.icon-send-msg {
  background: url('icons/send_msg.png') no-repeat center center;
}
.icon-add-item {
  background: url('icons/add_item.png') no-repeat center center;
}
.icon-price-maint {
  background: url('icons/price_maint.png') no-repeat center center;
}
.icon-wax-tat-stat {
  background: url('icons/wax_tat_stat.png') no-repeat center center;
}
.icon-wax-stat {
  background: url('icons/wax_stat.png') no-repeat center center;
}
.icon-sort {
  background: url('icons/sort.png') no-repeat center center;
}
.icon-house-maint {
  background: url('icons/house_maint.png') no-repeat center center;
}
.icon-slice-tat-stat {
  background: url('icons/slice_tat_stat.png') no-repeat center center;
}
.icon-slice-stat {
  background: url('icons/slice_stat.png') no-repeat center center;
}
.icon-take-report {
  background: url('icons/take_report.png') no-repeat center center;
}
.icon-change-pay-way {
  background: url('icons/change_pay_way.png') no-repeat center center;
}
.icon-sample-stat {
  background: url('icons/sample_stat.png') no-repeat center center;
}
.icon-ga-maint {
  background: url('icons/ga_maint.png') no-repeat center center;
}
.icon-house-posi-maint {
  background: url('icons/house_posi_maint.png') no-repeat center center;
}
.icon-qua-pro-dis {
  background: url('icons/qua_pro_dis.png') no-repeat center center;
}
.icon-qua-pro-sort {
  background: url('icons/qua_pro_sort.png') no-repeat center center;
}
.icon-person-key-yel {
  background: url('icons/person_key_yel.png') no-repeat center center;
}
.icon-key-switch {
  background: url('icons/key_switch.png') no-repeat center center;
}
.icon-report-switch {
  background: url('icons/report_switch.png') no-repeat center center;
}
.icon-report-blue-shie-key {
  background: url('icons/report_blue_shie_key.png') no-repeat center center;
}
.icon-rectangle-flow {
  background: url('icons/rectangle_flow.png') no-repeat center center;
}
.icon-paper-switch {
  background: url('icons/paper_switch.png') no-repeat center center;
}
.icon-pat-house-switch {
  background: url('icons/pat_house_switch.png') no-repeat center center;
}
.icon-qua-pro-blue {
  background: url('icons/qua_pro_blue.png') no-repeat center center;
}
.icon-slice-only {
  background: url('icons/slice_only.png') no-repeat center center;
}
.icon-barbell {
  background: url('icons/barbell.png') no-repeat center center;
}
.icon-target-arrow {
  background: url('icons/target_arrow.png') no-repeat center center;
}
.icon-big-switch {
  background: url('icons/big_switch.png') no-repeat center center;
}
.icon-disabler {
  background: url('icons/disabler.png') no-repeat center center;
}
.icon-produce {
  background: url('icons/produce.png') no-repeat center center;
}
.icon-children {
  background: url('icons/children.png') no-repeat center center;
}
.icon-lung {
  background: url('icons/lung.png') no-repeat center center;
}
.icon-high {
  background: url('icons/high.png') no-repeat center center;
}
.icon-spirit {
  background: url('icons/spirit.png') no-repeat center center;
}
.icon-old {
  background: url('icons/old.png') no-repeat center center;
}
.icon-poor {
  background: url('icons/poor.png') no-repeat center center;
}
.icon-sugar {
  background: url('icons/sugar.png') no-repeat center center;
}
.icon-free {
  background: url('icons/free.png') no-repeat center center;
}
.icon-out-poverty {
  background: url('icons/out_poverty.png') no-repeat center center;
}
.icon-pregnant-woman {
  background: url('icons/pregnant_woman.png') no-repeat center center;
}
.icon-bell-blue-no {
  background: url('icons/bell_blue_no.png') no-repeat center center;
}
.icon-green-line-eye {
  background: url('icons/green_line_eye.png') no-repeat center center;
}
.icon-clock-orange {
  background: url('icons/clock_orange.png') no-repeat center center;
}
.icon-paper-clock-bue {
  background: url('icons/paper_clock_bue.png') no-repeat center center;
}
.icon-star-orange-border {
  background: url('icons/star_orange_border.png') no-repeat center center;
}
.icon-have-message {
  background: url('icons/have_message.png') no-repeat center center;
}
.icon-funnel-on {
  background: url('icons/funnel_on.png') no-repeat center center;
}
.icon-three-cuboid-green {
  background: url('icons/three_cuboid_green.png') no-repeat center center;
}
.icon-dustbin-red {
  background: url('icons/dustbin_red.png') no-repeat center center;
}
.icon-bell-blue {
  background: url('icons/bell_blue.png') no-repeat center center;
}
.icon-paper-pen-blue {
  background: url('icons/paper_pen_blue.png') no-repeat center center;
}
.icon-star-orange-body {
  background: url('icons/star_orange_body.png') no-repeat center center;
}
.icon-arrow-le-bo-gray {
  background: url('icons/arrow_le_bo_gray.png') no-repeat center center;
}
.icon-print-arr-bo {
  background: url('icons/print_arr_bo.png') no-repeat center center;
}
.icon-print-arr-bo-gray {
  background: url('icons/print_arr_bo_gray.png') no-repeat center center;
}
.icon-gear-gray {
  background: url('icons/gear_gray.png') no-repeat center center;
}
.icon-triangle-green-right {
  background: url('icons/triangle_green_right.png') no-repeat center center;
}
.icon-triangle-gray-right {
  background: url('icons/triangle_gray_right.png') no-repeat center center;
}
.icon-paper-pay {
  background: url('icons/paper_pay.png') no-repeat center center;
}
.icon-paper-pay-gray {
  background: url('icons/paper_pay_gray.png') no-repeat center center;
}
.icon-compu-torus {
  background: url('icons/compu_torus.png') no-repeat center center;
}
.icon-compu-torus-gray {
  background: url('icons/compu_torus_gray.png') no-repeat center center;
}
.icon-two-recta-gear {
  background: url('icons/two_recta_gear.png') no-repeat center center;
}
.icon-two-recta-gear-gray {
  background: url('icons/two_recta_gear_gray.png') no-repeat center center;
}
.icon-compu-run {
  background: url('icons/compu_run.png') no-repeat center center;
}
.icon-compu-run-gray {
  background: url('icons/compu_run_gray.png') no-repeat center center;
}
.icon-paper-blue-add {
  background: url('icons/paper_blue_add.png') no-repeat center center;
}
.icon-paper-blue-add-gray {
  background: url('icons/paper_blue_add_gray.png') no-repeat center center;
}
.icon-trian-recta-right {
  background: url('icons/trian_recta_right.png') no-repeat center center;
}
.icon-trian-recta-right-gray {
  background: url('icons/trian_recta_right_gray.png') no-repeat center center;
}
.icon-triangle-green-left {
  background: url('icons/triangle_green_left.png') no-repeat center center;
}
.icon-triangle-gray-left {
  background: url('icons/triangle_gray_left.png') no-repeat center center;
}
.icon-box-red-add {
  background: url('icons/box_red_add.png') no-repeat center center;
}
.icon-box-red-add-gray {
  background: url('icons/box_red_add_gray.png') no-repeat center center;
}
.icon-gray-chart {
  background: url('icons/gray_chart.png') no-repeat center center;
}
.icon-report-eye {
  background: url('icons/report_eye.png') no-repeat center center;
}
.icon-report-eye-gray {
  background: url('icons/report_eye_gray.png') no-repeat center center;
}
.icon-trian-recta-left {
  background: url('icons/trian_recta_left.png') no-repeat center center;
}
.icon-trian-recta-left-gray {
  background: url('icons/trian_recta_left_gray.png') no-repeat center center;
}
.icon-refresh-gray {
  background: url('icons/refresh_gray.png') no-repeat center center;
}
.icon-clock-pen {
  background: url('icons/clock_pen.png') no-repeat center center;
}
.icon-clock-pen-gray {
  background: url('icons/clock_pen_gray.png') no-repeat center center;
}
.icon-drug-eye {
  background: url('icons/drug_eye.png') no-repeat center center;
}
.icon-drug-eye-gray {
  background: url('icons/drug_eye_gray.png') no-repeat center center;
}
.icon-trigger-box {
  background: url('icons/trigger_box.png') no-repeat center center;
}
.icon-card {
  background: url('icons/card.png') no-repeat center center;
}
.icon-copy-blue {
  background: url('icons/copy_blue.png') no-repeat center center;
}
.icon-img-blue {
  background: url('icons/img_blue.png') no-repeat center center;
}
.icon-folder {
  background: url('icons/folder.png') no-repeat center center;
}
.icon-miss-img {
  background: url('icons/miss_img.png') no-repeat center center;
}
.icon-person-seal {
  background: url('icons/person_seal.png') no-repeat center center;
}
.icon-out {
  background: url('icons/out.png') no-repeat center center;
}
.icon-change {
  background: url('icons/change.png') no-repeat center center;
}
.icon-eye-deepgrade {
  background: url('icons/eye_deepgrade.png') no-repeat center center;
}
.icon-bell-yellow {
  background: url('icons/bell_yellow.png') no-repeat center center;
}
.icon-pat-alert-yellow {
  background: url('icons/pat_alert_yellow.png') no-repeat center center;
}
.icon-herb-back {
  background: url('icons/herb_back.png') no-repeat center center;
}
.icon-herb-pre {
  background: url('icons/herb_pre.png') no-repeat center center;
}
.icon-herb-next {
  background: url('icons/herb_next.png') no-repeat center center;
}
.icon-herb-no {
  background: url('icons/herb_no.png') no-repeat center center;
}
.icon-herb-ok {
  background: url('icons/herb_ok.png') no-repeat center center;
}
.icon-decoct-herb {
  background: url('icons/decoct_herb.png') no-repeat center center;
}
.icon-decoct-change {
  background: url('icons/decoct_change.png') no-repeat center center;
}
.icon-paper-eye-r {
  background: url('icons/paper_eye_r.png') no-repeat center center;
}
.icon-paper-pre {
  background: url('icons/paper_pre.png') no-repeat center center;
}
.icon-date {
  background: url('icons/date.png') no-repeat center center;
}
.icon-no-dot {
  background: url('icons/no_dot.png') no-repeat center center;
}
.icon-verify {
  background: url('icons/verify.png') no-repeat center center;
}
.icon-ca-green {
  background: url('icons/ca_green.png') no-repeat center center;
}
.icon-ster-again {
  background: url('icons/ster_again.png') no-repeat center center;
}
.icon-ster-bat {
  background: url('icons/ster_bat.png') no-repeat center center;
}
.icon-ster-bio {
  background: url('icons/ster_bio.png') no-repeat center center;
}
.icon-ster-cancel {
  background: url('icons/ster_cancel.png') no-repeat center center;
}
.icon-ster-finish {
  background: url('icons/ster_finish.png') no-repeat center center;
}
.icon-ster-ok {
  background: url('icons/ster_ok.png') no-repeat center center;
}
.icon-ster-bd {
  background: url('icons/ster_bd.png') no-repeat center center;
}
.icon-ster-leak {
  background: url('icons/ster_leak.png') no-repeat center center;
}
.icon-pause-red {
  background: url('icons/pause_red.png') no-repeat center center;
}
.icon-alarm-key {
  background: url('icons/alarm_key.png') no-repeat center center;
}
.icon-align-center-blue {
  background: url('icons/align_center_blue.png') no-repeat center center;
}
.icon-align-left-blue {
  background: url('icons/align_left_blue.png') no-repeat center center;
}
.icon-align-right-blue {
  background: url('icons/align_right_blue.png') no-repeat center center;
}
.icon-same-height-blue {
  background: url('icons/same_height_blue.png') no-repeat center center;
}
.icon-same-size-blue {
  background: url('icons/same_size_blue.png') no-repeat center center;
}
.icon-same-width-blue {
  background: url('icons/same_width_blue.png') no-repeat center center;
}
.icon-valign-bottom-blue {
  background: url('icons/valign_bottom_blue.png') no-repeat center center;
}
.icon-valign-middle-blue {
  background: url('icons/valign_middle_blue.png') no-repeat center center;
}
.icon-valign-top-blue {
  background: url('icons/valign_top_blue.png') no-repeat center center;
}
.icon-barcode-blue {
  background: url('icons/barcode_blue.png') no-repeat center center;
}
.icon-qrcode-blue {
  background: url('icons/qrcode_blue.png') no-repeat center center;
}
.icon-line {
  background: url('icons/line.png') no-repeat center center;
}
.icon-move {
  background: url('icons/move.png') no-repeat center center;
}
.icon-table-blue {
  background: url('icons/table_blue.png') no-repeat center center;
}
.icon-table-col {
  background: url('icons/table_col.png') no-repeat center center;
}
.icon-stamp-mess {
  background: url('icons/stamp_mess.png') no-repeat center center;
}
.icon-paper-lightning {
  background: url('icons/paper_lightning.png') no-repeat center center;
}
.icon-cale-3day {
  background: url('icons/cale_3day.png') no-repeat center center;
}
.icon-stamp-add {
  background: url('icons/stamp_add.png') no-repeat center center;
}
.icon-stamp-switch {
  background: url('icons/stamp_switch.png') no-repeat center center;
}
.icon-stamp-undo {
  background: url('icons/stamp_undo.png') no-repeat center center;
}
.icon-template-down {
  background: url('icons/template_down.png') no-repeat center center;
}
.icon-paper-minus {
  background: url('icons/paper_minus.png') no-repeat center center;
}
.icon-paper-lightning {
  background: url('icons/paper_lightning.png') no-repeat center center;
}
.icon-fishbone-diagram {
  background: url('icons/fishbone_diagram.png') no-repeat center center;
}
.icon-ice-water {
  background: url('icons/ice_water.png') no-repeat center center;
}
.icon-circle-down {
  background: url('icons/circle_down.png') no-repeat center center;
}
.icon-sure-readed {
  background: url('icons/sure_readed.png') no-repeat center center;
}
.icon-read-details {
  background: url('icons/read_details.png') no-repeat center center;
}
.icon-macpw {
  background: url('icons/macpw.png') no-repeat center center;
}
.icon-macpworder {
  background: url('icons/macpworder.png') no-repeat center center;
}
.icon-ring-blue {
  background: url('icons/ring_blue.png') no-repeat center center;
}
.icon-bag {
  background: url('icons/bag.png') no-repeat center center;
}
.icon-bag-x {
  background: url('icons/bag_x.png') no-repeat center center;
}
.icon-disp-x {
  background: url('icons/disp_x.png') no-repeat center center;
}
.icon-disp-back {
  background: url('icons/disp_back.png') no-repeat center center;
}
.icon-outInstc-mgr {
  background: url('icons/outInstc_mgr.png') no-repeat center center;
}
.icon-book-rep {
  background: url('icons/book_rep.png') no-repeat center center;
}
.icon-book-rep-v1 {
  background: url('icons/book_rep_v1.png') no-repeat center center;
}
.icon-book-pen {
  background: url('icons/book_pen.png') no-repeat center center;
}
.icon-paper-settings {
  background: url('icons/paper_settings.png') no-repeat center center;
}
.icon-book-settings {
  background: url('icons/book_settings.png') no-repeat center center;
}
.icon-eye-scan-box {
  background: url('icons/eye_scan_box.png') no-repeat center center;
}
.icon-org-frame {
  background: url('icons/org_frame.png') no-repeat center center;
}
.icon-alert-pen-gray {
  background: url('icons/alert_pen_gray.png') no-repeat center center;
}
.icon-paper-set-qus {
  background: url('icons/paper_set_qus.png') no-repeat center center;
}
.icon-bk-mgr {
  background: url('icons/bk_mgr.png') no-repeat center center;
}
.icon-msg-unread {
  background: url('icons/msg_unread.png') no-repeat center center;
}
.icon-msg-unread-unprocessed {
  background: url('icons/msg_unread_unprocessed.png') no-repeat center center;
}
.icon-msg-read {
  background: url('icons/msg_read.png') no-repeat center center;
}
.icon-msg-read-unprocessed {
  background: url('icons/msg_read_unprocessed.png') no-repeat center center;
}
.icon-msg-read-processed {
  background: url('icons/msg_read_processed.png') no-repeat center center;
}
.icon-sound {
  background: url('icons/sound.png') no-repeat center center;
}
.icon-evaluate-red {
  background: url('icons/evaluate_red.png') no-repeat center center;
}
.icon-evaluate-green {
  background: url('icons/evaluate_green.png') no-repeat center center;
}
.icon-pc {
  background: url('icons/pc.png') no-repeat center center;
}
.icon-pc-v1 {
  background: url('icons/pc_v1.png') no-repeat center center;
}
.icon-pc-v2 {
  background: url('icons/pc_v2.png') no-repeat center center;
}
.icon-ecg-adm {
  background: url('icons/ecg_adm.png') no-repeat center center;
}
.icon-key2 {
  background: url('icons/key2.png') no-repeat center center;
}
.icon-data-stat {
  background: url('icons/data_stat.png') no-repeat center center;
}
.icon-user-settings {
  background: url('icons/user_settings.png') no-repeat center center;
}
.icon-video {
  background: url('icons/video.png') no-repeat center center;
}
.icon-share {
  background: url('icons/share.png') no-repeat center center;
}
.icon-share-no {
  background: url('icons/share_no.png') no-repeat center center;
}
.icon-paper-share {
  background: url('icons/paper_share.png') no-repeat center center;
}
.icon-home-back {
  background: url('icons/home_back.png') no-repeat center center;
}
.icon-paper-plane-clock {
  background: url('icons/paper_plane_clock.png') no-repeat center center;
}
.icon-file-open {
  background: url('icons/file_open.png') no-repeat center center;
}
.icon-file {
  background: url('icons/file.png') no-repeat center center;
}
.icon-paper-unlink {
  background: url('icons/paper_unlink.png') no-repeat center center;
}
.icon-shopping-cart-ok {
  background: url('icons/shopping_cart_ok.png') no-repeat center center;
}
.icon-person-ok {
  background: url('icons/person_ok.png') no-repeat center center;
}
.icon-physics-monitor {
  background: url('icons/physics_monitor.png') no-repeat center center;
}
.icon-change-x-virus {
  background: url('icons/change_x_virus.png') no-repeat center center;
}
.icon-paper-print {
  background: url('icons/paper_print.png') no-repeat center center;
}
.icon-multi-del {
  background: url('icons/multi_del.png') no-repeat center center;
}
.icon-pda-execution-rate {
  background: url('icons/pda_execution_rate.png') no-repeat center center;
}
.icon-insert-local-image {
  background: url('icons/insert_local_image.png') no-repeat center center;
}
.icon-edit-picture {
  background: url('icons/edit_picture.png') no-repeat center center;
}
.icon-creating-a-pedigree-map {
  background: url('icons/creating_a_pedigree_map.png') no-repeat center center;
}
.icon-edit-pedigree-chart {
  background: url('icons/edit_pedigree_chart.png') no-repeat center center;
}
.icon-image-properties {
  background: url('icons/image_properties.png') no-repeat center center;
}
.icon-quality {
  background: url('icons/quality.png') no-repeat center center;
}
/*big icon*/
.icon-big-save {
  background: url('icons/big/save.png') no-repeat top center;
}
.icon-big-print {
  background: url('icons/big/print.png') no-repeat top center;
}
.icon-big-del {
  background: url('icons/big/del.png') no-repeat top center;
}
.icon-big-favorite {
  background: url('icons/big/favorite.png') no-repeat top center;
}
.icon-big-favorite-add {
  background: url('icons/big/favorite_add.png') no-repeat top center;
}
.icon-big-img {
  background: url('icons/big/img.png') no-repeat top center;
}
.icon-big-omega {
  background: url('icons/big/omega.png') no-repeat top center;
}
.icon-big-position {
  background: url('icons/big/position.png') no-repeat top center;
}
.icon-big-refresh {
  background: url('icons/big/refresh.png') no-repeat top center;
}
.icon-big-unlock {
  background: url('icons/big/unlock.png') no-repeat top center;
}
.icon-big-book-arrow {
  background: url('icons/big/book_arrow.png') no-repeat top center;
}
.icon-big-book-eye {
  background: url('icons/big/book_eye.png') no-repeat top center;
}
.icon-big-book-ref {
  background: url('icons/big/book_ref.png') no-repeat top center;
}
.icon-big-book-arrow-rt {
  background: url('icons/big/book_arrow_rt.png') no-repeat top center;
}
.icon-big-book-to-book {
  background: url('icons/big/book_to_book.png') no-repeat top center;
}
.icon-big-doctor-green {
  background: url('icons/big/doctor_green.png') no-repeat top center;
}
.icon-big-lt-rt-37 {
  background: url('icons/big/lt_rt_37.png') no-repeat top center;
}
.icon-big-lt-rt-55 {
  background: url('icons/big/lt_rt_55.png') no-repeat top center;
}
.icon-big-lt-rt-73 {
  background: url('icons/big/lt_rt_73.png') no-repeat top center;
}
.icon-big-lt-rt-19 {
  background: url('icons/big/lt_rt_19.png') no-repeat top center;
}
.icon-big-lt-rt-46 {
  background: url('icons/big/lt_rt_46.png') no-repeat top center;
}
.icon-big-lt-rt-28 {
  background: url('icons/big/lt_rt_28.png') no-repeat top center;
}
.icon-big-lt-rt-82 {
  background: url('icons/big/lt_rt_82.png') no-repeat top center;
}
.icon-big-lt-rt-64 {
  background: url('icons/big/lt_rt_64.png') no-repeat top center;
}
.icon-big-card-reader {
  background: url('icons/big/card_reader.png') no-repeat top center;
}
.icon-big-clear {
  background: url('icons/big/clear.png') no-repeat top center;
}
.icon-big-doctor-adm {
  background: url('icons/big/doctor_adm.png') no-repeat top center;
}
.icon-big-fee-arrow {
  background: url('icons/big/fee_arrow.png') no-repeat top center;
}
.icon-big-paper-arrow {
  background: url('icons/big/paper_arrow.png') no-repeat top center;
}
.icon-big-paper-pen {
  background: url('icons/big/paper_pen.png') no-repeat top center;
}
.icon-big-paper-search {
  background: url('icons/big/paper_search.png') no-repeat top center;
}
.icon-big-ring {
  background: url('icons/big/ring.png') no-repeat top center;
}
.icon-big-ring-blue {
  background: url('icons/big/ring_blue.png') no-repeat top center;
}
.icon-big-skip-no {
  background: url('icons/big/skip_no.png') no-repeat top center;
}
.icon-big-home {
  background: url('icons/big/home.png') no-repeat top center;
}
.icon-big-stamp {
  background: url('icons/big/stamp.png') no-repeat top center;
}
.icon-big-tooth {
  background: url('icons/big/tooth.png') no-repeat top center;
}
.icon-big-close-eye {
  background: url('icons/big/close_eye.png') no-repeat top center;
}
.icon-big-open-eye {
  background: url('icons/big/open_eye.png') no-repeat top center;
}
.icon-big-delete-col {
  background: url('icons/big/delete_col.png') no-repeat top center;
}
.icon-big-delete-row {
  background: url('icons/big/delete_row.png') no-repeat top center;
}
.icon-big-delete-table {
  background: url('icons/big/delete_table.png') no-repeat top center;
}
.icon-big-insert-col {
  background: url('icons/big/insert_col.png') no-repeat top center;
}
.icon-big-insert-row {
  background: url('icons/big/insert_row.png') no-repeat top center;
}
.icon-big-insert-table {
  background: url('icons/big/insert_table.png') no-repeat top center;
}
.icon-big-split-cells {
  background: url('icons/big/split_cells.png') no-repeat top center;
}
.icon-big-help {
  background: url('icons/big/help.png') no-repeat top center;
}
.icon-big-paper {
  background: url('icons/big/paper.png') no-repeat top center;
}
.icon-big-question {
  background: url('icons/big/question.png') no-repeat top center;
}
.icon-big-bar {
  background: url('icons/big/bar.png') no-repeat top center;
}
.icon-big-card {
  background: url('icons/big/card.png') no-repeat top center;
}
.icon-big-change-account {
  background: url('icons/big/change_account.png') no-repeat top center;
}
.icon-big-disuse {
  background: url('icons/big/disuse.png') no-repeat top center;
}
.icon-big-inspect {
  background: url('icons/big/inspect.png') no-repeat top center;
}
.icon-big-maint {
  background: url('icons/big/maint.png') no-repeat top center;
}
.icon-big-meterage {
  background: url('icons/big/meterage.png') no-repeat top center;
}
.icon-big-return {
  background: url('icons/big/return.png') no-repeat top center;
}
.icon-big-start {
  background: url('icons/big/start.png') no-repeat top center;
}
.icon-big-stop {
  background: url('icons/big/stop.png') no-repeat top center;
}
.icon-big-tip {
  background: url('icons/big/tip.png') no-repeat top center;
}
.icon-big-rad {
  background: url('icons/big/rad.png') no-repeat top center;
}
.icon-big-balance {
  background: url('icons/big/balance.png') no-repeat top center;
}
.icon-big-open-file {
  background: url('icons/big/open_file.png') no-repeat top center;
}
.icon-big-waxblock-return {
  background: url('icons/big/waxblock_return.png') no-repeat top center;
}
.icon-big-cells-smear {
  background: url('icons/big/cells_smear.png') no-repeat top center;
}
.icon-big-slide-filed {
  background: url('icons/big/slide_filed.png') no-repeat top center;
}
.icon-big-slide-send {
  background: url('icons/big/slide_send.png') no-repeat top center;
}
.icon-big-dyeing {
  background: url('icons/big/dyeing.png') no-repeat top center;
}
.icon-big-slide-add {
  background: url('icons/big/slide_add.png') no-repeat top center;
}
.icon-big-embed {
  background: url('icons/big/embed.png') no-repeat top center;
}
.icon-big-slide-made {
  background: url('icons/big/slide_made.png') no-repeat top center;
}
.icon-big-slide-return {
  background: url('icons/big/slide_return.png') no-repeat top center;
}
.icon-big-book-yellow {
  background: url('icons/big/book_yellow.png') no-repeat top center;
}
.icon-big-med-bag {
  background: url('icons/big/med_bag.png') no-repeat top center;
}
.icon-big-next {
  background: url('icons/big/next.png') no-repeat top center;
}
.icon-big-pre {
  background: url('icons/big/pre.png') no-repeat top center;
}
.icon-big-print-box {
  background: url('icons/big/print_box.png') no-repeat top center;
}
.icon-big-save-add {
  background: url('icons/big/save_add.png') no-repeat top center;
}
.icon-big-save-next {
  background: url('icons/big/save_next.png') no-repeat top center;
}
.icon-big-print-run {
  background: url('icons/big/print_run.png') no-repeat top center;
}
.icon-big-paper-time {
  background: url('icons/big/paper_time.png') no-repeat top center;
}
.icon-big-alert-yellow {
  background: url('icons/big/alert_yellow.png') no-repeat top center;
}
.icon-big-pre-audit {
  background: url('icons/big/pre_audit.png') no-repeat top center;
}
.icon-big-paper-gray {
  background: url('icons/big/paper_gray.png') no-repeat top center;
}
.icon-big-msg {
  background: url('icons/big/msg.png') no-repeat top center;
}
.icon-big-equi-cfg {
  background: url('icons/big/equi_cfg.png') no-repeat top center;
}
.icon-big-med-equi {
  background: url('icons/big/med_equi.png') no-repeat top center;
}
.icon-big-alert {
  background: url('icons/big/alert.png') no-repeat top center;
}
.icon-big-idcard {
  background: url('icons/big/idcard.png') no-repeat top center;
}
.icon-big-paid {
  background: url('icons/big/paid.png') no-repeat top center;
}
.icon-big-pat-list {
  background: url('icons/big/pat_list.png') no-repeat top center;
}
.icon-big-read-card {
  background: url('icons/big/read_card.png') no-repeat top center;
}
.icon-big-search-pat {
  background: url('icons/big/search_pat.png') no-repeat top center;
}
.icon-big-cardiogram {
  background: url('icons/big/cardiogram.png') no-repeat top center;
}
.icon-big-conical-bottle {
  background: url('icons/big/conical_bottle.png') no-repeat top center;
}
.icon-big-patient-mach {
  background: url('icons/big/patient_mach.png') no-repeat top center;
}
.icon-big-movie-mach {
  background: url('icons/big/movie_mach.png') no-repeat top center;
}
.icon-big-chopsticks-bowl {
  background: url('icons/big/chopsticks_bowl.png') no-repeat top center;
}
.icon-big-rectangle-tree {
  background: url('icons/big/rectangle_tree.png') no-repeat top center;
}
.icon-big-message-clock {
  background: url('icons/big/message_clock.png') no-repeat top center;
}
.icon-big-message-cate {
  background: url('icons/big/message_cate.png') no-repeat top center;
}
.icon-big-message-colum {
  background: url('icons/big/message_colum.png') no-repeat top center;
}
.icon-big-message-pen {
  background: url('icons/big/message_pen.png') no-repeat top center;
}
.icon-big-miss-img {
  background: url('icons/big/miss_img.png') no-repeat top center;
}
.icon-big-paper-yellow {
  background: url('icons/big/paper_yellow.png') no-repeat top center;
}
.icon-big-mach-blue-red {
  background: url('icons/big/mach_blue_red.png') no-repeat top center;
}
.icon-big-first-second {
  background: url('icons/big/first_second.png') no-repeat top center;
}
.icon-big-clock-back-blue {
  background: url('icons/big/clock_back_blue.png') no-repeat top center;
}
.icon-big-clock-back-gree {
  background: url('icons/big/clock_back_gree.png') no-repeat top center;
}
.icon-big-person-green {
  background: url('icons/big/person_green.png') no-repeat top center;
}
.icon-big-report-yel-pen {
  background: url('icons/big/report_yel_pen.png') no-repeat top center;
}
.icon-big-two-pill-gray {
  background: url('icons/big/two_pill_gray.png') no-repeat top center;
}
.icon-big-paper-orange {
  background: url('icons/big/paper_orange.png') no-repeat top center;
}
.icon-big-blue-white-circle {
  background: url('icons/big/blue_white_circle.png') no-repeat top center;
}
.icon-big-three-blue-bar {
  background: url('icons/big/three_blue_bar.png') no-repeat top center;
}
.icon-big-blue-frame-ok {
  background: url('icons/big/blue_frame_ok.png') no-repeat top center;
}
.icon-big-white-p-red {
  background: url('icons/big/white_p_red.png') no-repeat top center;
}
.icon-big-book-arrow-ok {
  background: url('icons/big/book_arrow_ok.png') no-repeat top center;
}
.icon-big-eye-deepgrade {
  background: url('icons/big/eye_deepgrade.png') no-repeat top center;
}
.icon-big-paper-box {
  background: url('icons/big/paper_box.png') no-repeat top center;
}
.icon-big-ca-green {
  background: url('icons/big/ca_green.png') no-repeat top center;
}
.icon-big-redlabel-refresh {
  background: url('icons/big/redlabel_refresh.png') no-repeat top center;
}
.icon-big-paper-print {
  background: url('icons/big/paper_print.png') no-repeat top center;
}
.icon-big-drug-ok {
  background: url('icons/big/drug_ok.png') no-repeat top center;
}
.icon-big-drug-paper {
  background: url('icons/big/drug_paper.png') no-repeat top center;
}
.icon-big-drug-x {
  background: url('icons/big/drug_x.png') no-repeat top center;
}
.icon-big-drug-forbid {
  background: url('icons/big/drug_forbid.png') no-repeat top center;
}
.icon-big-card-money {
  background: url('icons/big/card_money.png') no-repeat top center;
}
.icon-big-medibottle {
  background: url('icons/big/medibottle.png') no-repeat top center;
}
.icon-big-drug-all-ok {
  background: url('icons/big/drug_all_ok.png') no-repeat top center;
}
.icon-big-drug-back {
  background: url('icons/big/drug_back.png') no-repeat top center;
}
.icon-big-printer-refresh {
  background: url('icons/big/printer_refresh.png') no-repeat top center;
}
.icon-big-medibottle-run {
  background: url('icons/big/medibottle_run.png') no-repeat top center;
}
.icon-big-insert-local-image {
  background: url('icons/big/insert_local_image.png') no-repeat top center;
}
.icon-big-edit-picture {
  background: url('icons/big/edit_picture.png') no-repeat top center;
}
.icon-big-creating-a-pedigree-map {
  background: url('icons/big/creating_a_pedigree_map.png') no-repeat top center;
}
.icon-big-edit-pedigree-chart {
  background: url('icons/big/edit_pedigree_chart.png') no-repeat top center;
}
.icon-big-image-properties {
  background: url('icons/big/image_properties.png') no-repeat top center;
}
/*white icon*/
.icon-w-plus {
  background: url('icons/white/plus.png') no-repeat center center;
}
.icon-w-add {
  background: url('icons/white/add.png') no-repeat center center;
}
.icon-w-arrow-down {
  background: url('icons/white/arrow_down.png') no-repeat center center;
}
.icon-w-arrow-up {
  background: url('icons/white/arrow_up.png') no-repeat center center;
}
.icon-w-back {
  background: url('icons/white/back.png') no-repeat center center;
}
.icon-w-cal {
  background: url('icons/white/cal.png') no-repeat center center;
}
.icon-w-cancel {
  background: url('icons/white/cancel.png') no-repeat center center;
}
.icon-w-card {
  background: url('icons/white/card.png') no-repeat center center;
}
.icon-w-clean {
  background: url('icons/white/clean.png') no-repeat center center;
}
.icon-w-close {
  background: url('icons/white/close.png') no-repeat center center;
}
.icon-w-config {
  background: url('icons/white/config.png') no-repeat center center;
}
.icon-w-edit {
  background: url('icons/white/edit.png') no-repeat center center;
}
.icon-w-epr {
  background: url('icons/white/epr.png') no-repeat center center;
}
.icon-w-file {
  background: url('icons/white/file.png') no-repeat center center;
}
.icon-w-file-open {
  background: url('icons/white/file_open.png') no-repeat center center;
}
.icon-w-find {
  background: url('icons/white/find.png') no-repeat center center;
}
.icon-w-home {
  background: url('icons/white/home.png') no-repeat center center;
}
.icon-w-list {
  background: url('icons/white/list.png') no-repeat center center;
}
.icon-w-new {
  background: url('icons/white/new.png') no-repeat center center;
}
.icon-w-other {
  background: url('icons/white/other.png') no-repeat center center;
}
.icon-w-paper {
  background: url('icons/white/paper.png') no-repeat center center;
}
.icon-w-plus {
  background: url('icons/white/plus.png') no-repeat center center;
}
.icon-w-print {
  background: url('icons/white/print.png') no-repeat center center;
}
.icon-w-save {
  background: url('icons/white/save.png') no-repeat center center;
}
.icon-w-switch {
  background: url('icons/white/switch.png') no-repeat center center;
}
.icon-w-update {
  background: url('icons/white/update.png') no-repeat center center;
}
.icon-w-import {
  background: url('icons/white/import.png') no-repeat center center;
}
.icon-w-export {
  background: url('icons/white/export.png') no-repeat center center;
}
.icon-w-star {
  background: url('icons/white/star.png') no-repeat center center;
}
.icon-w-msg {
  background: url('icons/white/msg.png') no-repeat center center;
}
.icon-w-copy {
  background: url('icons/white/copy.png') no-repeat center center;
}
.icon-w-stamp {
  background: url('icons/white/stamp.png') no-repeat center center;
}
.icon-w-batch-cfg {
  background: url('icons/white/batch_cfg.png') no-repeat center center;
}
.icon-w-inv {
  background: url('icons/white/inv.png') no-repeat center center;
}
.icon-w-batch-add {
  background: url('icons/white/batch_add.png') no-repeat center center;
}
.icon-w-eye {
  background: url('icons/white/eye.png') no-repeat center center;
}
.icon-w-arrow-left {
  background: url('icons/white/arrow_left.png') no-repeat center center;
}
.icon-w-arrow-right {
  background: url('icons/white/arrow_right.png') no-repeat center center;
}
.icon-w-calc {
  background: url('icons/white/calc.png') no-repeat center center;
}
.icon-w-submit {
  background: url('icons/white/submit.png') no-repeat center center;
}
.icon-w-run {
  background: url('icons/white/run.png') no-repeat center center;
}
.icon-w-rent {
  background: url('icons/white/rent.png') no-repeat center center;
}
.icon-w-takes {
  background: url('icons/white/takes.png') no-repeat center center;
}
.icon-w-line-key {
  background: url('icons/white/line_key.png') no-repeat center center;
}
.icon-w-clock {
  background: url('icons/white/clock.png') no-repeat center center;
}
.icon-w-key {
  background: url('icons/white/key.png') no-repeat center center;
}
.icon-w-setting {
  background: url('icons/white/setting.png') no-repeat center center;
}
.icon-w-img {
  background: url('icons/white/img.png') no-repeat center center;
}
.icon-w-ok {
  background: url('icons/white/ok.png') no-repeat center center;
}
.icon-w-filter {
  background: url('icons/white/filter.png') no-repeat center center;
}
.icon-w-predrug {
  background: url('icons/white/predrug.png') no-repeat center center;
}
.icon-w-trigger-box {
  background: url('icons/white/trigger_box.png') no-repeat center center;
}
.icon-w-paid {
  background: url('icons/white/paid.png') no-repeat center center;
}
.icon-w-zoom {
  background: url('icons/white/zoom.png') no-repeat center center;
}
.icon-w-book {
  background: url('icons/white/book.png') no-repeat center center;
}
.icon-w-upload {
  background: url('icons/white/upload.png') no-repeat center center;
}
.icon-w-pen-paper {
  background: url('icons/white/pen_paper.png') no-repeat center center;
}
.icon-w-volume-up {
  background: url('icons/white/volume_up.png') no-repeat center center;
}
.icon-w-download {
  background: url('icons/white/download.png') no-repeat center center;
}
.icon-w-reset {
  background: url('icons/white/reset.png') no-repeat center center;
}
.icon-w-pause-circle {
  background: url('icons/white/pause_circle.png') no-repeat center center;
}
.icon-w-skip-no {
  background: url('icons/white/skip_no.png') no-repeat center center;
}
.icon-w-ca {
  background: url('icons/white/ca.png') no-repeat center center;
}
.icon-w-ster-bd {
  background: url('icons/white/ster_bd.png') no-repeat center center;
}
.icon-w-ster-leak {
  background: url('icons/white/ster_leak.png') no-repeat center center;
}
.icon-w-scan-code {
  background: url('icons/white/scan_code.png') no-repeat center center;
}
.icon-w-canceldrug {
  background: url('icons/white/canceldrug.png') no-repeat center center;
}
.icon-w-camera {
  background: url('icons/white/camera.png') no-repeat center center;
}
.icon-w-stop {
  background: url('icons/white/stop.png') no-repeat center center;
}
.pic-pat-man {
  background: url('icons/blue/pat/man.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-woman {
  background: url('icons/blue/pat/woman.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-pat-unknown-gender {
  background: url('icons/blue/pat/unknown_gender.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-doctor {
  background: url('icons/blue/usr/doctor.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-nurse {
  background: url('icons/blue/usr/nurse.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-surgeon {
  background: url('icons/blue/usr/surgeon.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-clothing-worker {
  background: url('icons/blue/usr/clothing_worker.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-medi-worker {
  background: url('icons/blue/usr/medi_worker.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-dep-director {
  background: url('icons/blue/usr/dep_director.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-hosp-director {
  background: url('icons/blue/usr/hosp_director.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-usr-doctor-woman {
  background: url('icons/blue/usr/doctor_woman.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-adm-out {
  background: url('icons/blue/adm/out.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-adm-em {
  background: url('icons/blue/adm/em.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-adm-in {
  background: url('icons/blue/adm/in.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-cert {
  background: url('icons/blue/logon/cert.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-default {
  background: url('icons/blue/logon/default.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-face {
  background: url('icons/blue/logon/face.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-phone {
  background: url('icons/blue/logon/phone.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-pin {
  background: url('icons/blue/logon/pin.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-sound {
  background: url('icons/blue/logon/sound.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-logon-ukey {
  background: url('icons/blue/logon/ukey.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-about-dhcc-digitalmed {
  background: url('icons/blue/about/dhcc_digitalmed.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-about-imedical-logo {
  background: url('icons/blue/about/imedical_logo.png') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-e403 {
  background: url('icons/blue/sysst/e403.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-e404 {
  background: url('icons/blue/sysst/e404.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-e500 {
  background: url('icons/blue/sysst/e500.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-nodata {
  background: url('icons/blue/sysst/nodata.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-nodata-region {
  background: url('icons/blue/sysst/nodata_region.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-nodata-msg {
  background: url('icons/blue/sysst/nodata_msg.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-timeout-relogon {
  background: url('icons/blue/sysst/timeout_relogon.gif') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
.pic-sysst-welcome {
  background: url('icons/blue/sysst/welcome.svg') no-repeat center center;
  background-size: contain;
  width: 100%;
  height: 100%;
}
/*2018-6-28 增大图标与左边间距*/
.panel-icon {
  left: 10px;
  width: 16px;
}
.panel-header {
  padding: 3px 5px;
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.panel-status-collapse .panel-header {
  border-radius: 4px;
}
.panel-title {
  background-image: none;
}
.panel-tool a:hover {
  background-color: #cccccc;
}
.panel-header,
.panel-body {
  border-color: #509de1;
}
.panel-header {
  background: #509de1;
  filter: none;
}
.panel-body {
  font-size: 14px;
  -moz-border-radius: 0 0 4px 4px;
  -webkit-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}
.panel-body.panel-body-noheader {
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}
.panel-title {
  font-size: 14px;
  font-weight: normal;
  color: #ffffff;
  height: 26px;
  line-height: 26px;
  padding-left: 5px;
}
.panel-title.panel-with-icon {
  padding-left: 32px;
}
.panel-header.panel-header-big {
  padding: 8px 5px 8px 5px;
}
.panel-header.panel-header-big .panel-title {
  padding-left: 0px;
  font-size: 16px;
}
.panel-header.panel-header-big .panel-title.panel-with-icon {
  padding-left: 32px;
}
.panel-header.panel-header-big .panel-icon {
  left: 10px;
}
.panel-header-gray {
  background: #f9f9fa;
  border-color: #cccccc;
}
.panel-header-gray + .panel-body {
  border-color: #cccccc;
}
.panel-header-gray .panel-title {
  color: #000000;
}
.panel-header-white {
  background: #ffffff;
  border-color: #cccccc;
}
.panel-header-white + .panel-body {
  border-color: #cccccc;
}
.panel-header-white .panel-title {
  color: #000000;
}
.panel-header-blue {
  border-color: #509de1;
  background: #b4dbf5;
}
.panel-header-blue + .panel-body {
  border-color: #509de1;
}
.panel-header-blue .panel-title {
  color: #000000;
}
.panel-header-acc {
  border-color: #509de1;
  background: #40a2de;
}
.panel-header-acc + .panel-body {
  border-color: #509de1;
}
.panel-header-card {
  width: 80px;
  border: none;
  top: 11px;
  left: 25px;
  z-index: 1;
  padding: 0;
  margin-top: -14px;
  border-bottom: 1px solid #509de1;
  background-color: #ffffff;
  -moz-border-radius: 0 0 8px 8px;
  -webkit-border-radius: 0 0 8px 8px;
  border-radius: 0 0 8px 8px;
}
.panel-header-card .panel-title {
  color: #000000;
  text-align: center;
  padding-left: 0px;
  font-weight: normal;
}
.panel-header-card + .panel-body {
  border-top: 1px solid #509de1;
  border-color: #509de1;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.panel-header-card-gray {
  width: 80px;
  border: none;
  top: 11px;
  left: 25px;
  z-index: 1;
  padding: 0;
  margin-top: -14px;
  border-bottom: 1px solid #E3E3E3;
  background-color: #ffffff;
  -moz-border-radius: 0 0 8px 8px;
  -webkit-border-radius: 0 0 8px 8px;
  border-radius: 0 0 8px 8px;
}
.panel-header-card-gray .panel-title {
  color: #000000;
  text-align: center;
  padding-left: 0px;
  font-weight: normal;
}
.panel-header-card-gray + .panel-body {
  border: 1px solid #E3E3E3;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.panel-tool a {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.panel-tool-close {
  background: url('images/panel_tools_1.png') no-repeat -16px 0px;
}
.panel-tool-min {
  background: url('images/panel_tools_1.png') no-repeat 0px 0px;
}
.panel-tool-max {
  background: url('images/panel_tools_1.png') no-repeat 0px -16px;
}
.panel-tool-restore {
  background: url('images/panel_tools_1.png') no-repeat -16px -16px;
}
.panel-tool-collapse {
  background: url('images/panel_tools_1.png') no-repeat -32px 0;
}
.panel-tool-expand {
  background: url('images/panel_tools_1.png') no-repeat -32px -16px;
}
.panel-tool a:hover {
  background-color: #e5e5e5;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.panel-tool a.panel-tool-close:hover {
  background-color: #ff0000;
  background-image: url(images/panel_tools_2.png);
}
.panel.lookup-p > .panel-header,
.panel.combo-p > .panel-header,
.panel.window > .panel-header,
.layout > .panel > .panel-header,
.accordion > .panel > .panel-header,
.tabs-panels > .panel > .panel-header {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  padding: 5px 5px;
}
.panel.lookup-p > .panel-body,
.panel.combo-p > .panel-body,
.panel.window > .panel-body,
.layout > .panel > .panel-body,
.accordion > .panel > .panel-body,
.tabs-panels > .panel > .panel-body {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.panel-header-gray.panel-body.panel-body-noheader {
  background: #ffffff;
  border-color: #cccccc;
}
/*bodyCls:'panel-body-gray'*/
.panel-body-gray {
  background: #ffffff;
  border-color: #cccccc;
}
/*自适应layout在医为浏览器会出现横纵滚动条问题，且滚动幅度刚好为滚动条宽度
* 应该是宽高计算正确了，但是横纵滚动条互相影响就互相出现了，将类panel-scroll禁止滚动
* 原来设计上panel-noscroll也是禁止滚动，但是莫名的panel-body的优先了
 */
.panel-body.panel-noscroll {
  overflow: hidden;
}
.accordion .accordion-body {
  /*border-width: 0 0 1px;*/
  /*wanghc*/
  border: none;
}
.accordion {
  border-color: #509de1;
}
.accordion .accordion-header {
  padding: 3px 5px 5px 7px;
  background: #509DE1;
  border-bottom: 0;
  border-top: 3px solid #ffffff;
  /* add wanghc */
}
.accordion .accordion-header-selected .panel-title {
  color: #ffffff;
}
/*add wanghc*/
.accordion-green .accordion-header {
  background: #21BA45;
  filter: none;
}
.accordion-body {
  background-color: #f2f2f2;
  color: #7a7875;
}
.accordion-header .panel-title {
  padding-left: 5px;
  color: #ffffff;
}
.accordion-header .panel-with-icon {
  padding-left: 31px;
}
.accordion-header .panel-icon {
  left: 10px;
}
.accordion-header .panel-tool {
  margin-top: -11px;
  height: 22px;
}
.accordion-header .panel-tool a {
  display: inline-block;
  width: 22px;
  height: 22px;
  opacity: 1.0;
  filter: alpha(opacity=100);
  vertical-align: top;
  margin: 0;
}
.accordion-header .panel-tool a:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.accordion-header .panel-tool .accordion-collapse {
  background: url(images/accordion_arrows.png) no-repeat -22px 0;
}
.accordion-header .panel-tool .accordion-expand {
  background: url(images/accordion_arrows.png) no-repeat 0 0;
}
.accordion.accordion-noborder .panel:first-child .accordion-header {
  border-top-width: 0px;
}
.accordion-gray.accordion.accordion-noborder .panel:first-child .accordion-header {
  border-top-width: 1px;
  margin-top: 0px;
}
.accordion-gray.accordion .accordion-body {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #cccccc;
  border-top: 0px;
}
.accordion-gray.accordion .accordion-header {
  margin-top: 4px;
  border: 1px #cccccc solid;
  background: #f7f7f7;
  filter: none;
}
.accordion-gray.accordion .accordion-header .panel-title {
  color: #000000;
}
.accordion-gray.accordion .accordion-header .panel-tool .accordion-collapse {
  background: url(images/accordion_arrows-black.png) no-repeat -22px 0;
}
.accordion-gray.accordion .accordion-header .panel-tool .accordion-expand {
  background: url(images/accordion_arrows-black.png) no-repeat 0 0;
}
.window,
.window-shadow {
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}
.window {
  padding: 0px;
  border-bottom: 0px;
  /*background-color:@window-body-bgcolor;*/
  background-image: none;
  background-color: transparent;
}
.window .window-header {
  border-color: #556983;
  background: #556983;
  /*transparent;*/
  padding: 6px 1px 6px 1px;
}
.window .window-header .panel-icon {
  left: 20px;
  /*1px*/
}
.window .window-header .panel-tool {
  right: 12px;
  /*1px*/
}
.window .window-header .panel-tool .panel-tool-close,
.window .window-header .panel-tool .panel-tool-max,
.window .window-header .panel-tool .panel-tool-restore,
.window .window-header .panel-tool .panel-tool-min,
.window .window-header .panel-tool .panel-tool-collapse,
.window .window-header .panel-tool .panel-tool-expand {
  background-image: url(images/window_tools.png);
}
.window .window-header .panel-tool .panel-tool-close {
  background-image: url(images/panel_tools_2.png);
}
.window .window-header .panel-with-icon {
  padding-left: 40px;
  /*18px*/
}
.window .window-body {
  border-width: 1px;
  border-style: solid;
  border-top-width: 0px;
}
.window .window-body-noheader {
  border-top-width: 1px;
}
.window-shadow {
  background: #666666;
  -moz-box-shadow: 2px 3px 10px #666666;
  -webkit-box-shadow: 2px 3px 10px #666666;
  box-shadow: 2px 3px 10px #666666;
}
.window,
.window .window-body {
  border: none;
}
.window-proxy {
  border: 1px dashed #cccccc;
}
.window-proxy-mask,
.window-mask {
  background: #000000;
}
.window .window-header .panel-icon,
.window .window-header .panel-tool {
  top: 50%;
  margin-top: -8px;
}
.window.panel > .panel-header > .panel-title {
  color: #ffffff;
}
.dialog-content {
  overflow: auto;
}
.dialog-toolbar {
  padding: 2px 5px;
}
.dialog-tool-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #ccc;
  border-right: 1px solid #fff;
  margin: 2px 1px;
}
.dialog-button {
  padding: 10px;
  /*text-align: right;*/
  text-align: center;
}
.dialog-button .l-btn {
  margin-left: 10px;
}
.dialog-button .l-btn:first-child {
  margin-left: 0px;
}
.dialog-toolbar,
.dialog-button {
  background: #F4F4F4;
}
.dialog-toolbar {
  border-bottom: 1px solid #dddddd;
}
/* 2018-6-28*/
.dialog-button {
  /*border-top: 1px solid #dddddd;*/
  border: none;
  background: #FFFFFF;
}
/*dialog内容区panel 不要圆角*/
.panel-body.panel-body-noheader.dialog-content {
  border-radius: 0;
}
input[type=text],
textarea {
  border-color: #9ed2f2;
  width: 148px;
  /*websys.css-width:130px;*/
}
input.textbox,
textarea.textbox,
select.textbox,
.textareabox-text,
.textbox[type=text] {
  width: 148px;
  /*--2018-11-22*/
  margin: 0;
  padding: 0 0 0 5px;
  box-sizing: content-box;
  border-radius: 2px;
  border: 1px solid #9ed2f2;
  color: #000000;
  font-size: 14px;
}
input.textbox:active,
textarea.textbox:active,
select.textbox:active,
.textareabox-text:active,
.textbox[type=text]:active {
  background-color: #ffe48d;
}
input.textbox:hover,
textarea.textbox:hover,
select.textbox:hover,
.textareabox-text:hover,
.textbox[type=text]:hover {
  background-color: #ffe48d;
}
input.textbox:focus,
textarea.textbox:focus,
select.textbox:focus,
.textareabox-text:focus,
.textbox[type=text]:focus {
  background-color: #ffe48d;
}
input.textbox {
  line-height: 28px;
  height: 28px;
}
.textbox:disabled,
.textareabox-text:disabled,
.validatebox-text:disabled,
.searchbox-text:disabled,
textarea:disabled,
input:not([type]):disabled,
input[type="text"]:disabled {
  background-color: #f7f7f7;
  border-color: #bbbbbb;
  color: #999;
}
.spinner .textbox {
  width: 151px;
}
.triggerbox .textbox {
  width: 151px;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999999;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #999999;
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #999999;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999999;
}
.combo .combo-text {
  font-size: 14px;
  border: 0;
  line-height: 28px;
  height: 28px;
  padding: 0px 0px 0px 5px;
  *height: 26px;
  *line-height: 26px;
  _height: 26px;
  _line-height: 26px;
}
.combo-arrow {
  width: 30px;
  height: 30px;
  opacity: 1;
  filter: alpha(opacity=100);
}
.combo-arrow {
  background: url(images/combo_arrow.png) no-repeat center center;
}
.combo,
.combo-panel {
  background-color: #ffffff;
}
.combo {
  border-radius: 2px;
  border-color: #9ed2f2;
  background-color: #ffffff;
}
/* focus-widthin--ie11也没支持，chrome可以
.combo:focus-within{
  border-color: @input-border-focus-color;
}*/
.combo-arrow.combo-arrow-hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #eaeaea;
}
.combo.disabled {
  border-color: #bbbbbb;
}
.combo.disabled .combo-arrow {
  background-color: #f7f7f7;
  background-image: url(images/combo_arrow_disable.png);
}
.combo-p > .combo-panel {
  border-color: #9ed2f2;
}
.combobox-item,
.combobox-group,
._hisui_combobox-selectall {
  font-size: 14px;
  padding: 3px;
  padding-right: 0px;
  padding-left: 5px;
  line-height: 24px;
}
._hisui_combobox-selectall {
  background: #f6f6f6;
  cursor: pointer;
  border-left: 1px #9ed2f2 solid;
  border-right: 1px #9ed2f2 solid;
  border-top: 1px #9ed2f2 solid;
  height: 26px;
  white-space: nowrap;
}
.btop > .combo-panel {
  border-top-width: 0;
}
.bbtm > .combo-panel {
  border-bottom-width: 0;
}
.bbtm > ._hisui_combobox-selectall {
  border-bottom: 1px #509de1 solid;
}
.combobox-gitem {
  padding-left: 10px;
}
.combobox-item-hover {
  background-color: #dbecf8;
  color: #000000;
}
.combobox-item-selected {
  background-color: #dbecf8;
  color: #000000;
}
.combobox-item .combobox-checkbox,
._hisui_combobox-selectall .combobox-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  background: url("images/blue/checkbox.png") no-repeat;
  background-position: 0 0;
  width: 24px;
  height: 24px;
}
.combobox-item-selected.combobox-item .combobox-checkbox,
._hisui_combobox-selectall.checked .combobox-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  background: url("images/blue/checkbox.png") no-repeat;
  background-position: -48px 0;
  width: 24px;
  height: 24px;
}
.combobox-item-disabled.combobox-item .combobox-checkbox {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  background: url("images/blue/checkbox.png") no-repeat;
  background-position: -72px 0;
  width: 24px;
  height: 24px;
}
.layout-expand-body-title > div {
  text-align: center;
  font-weight: bold;
  color: #fff;
}
/*改为只控制其子级panel-header 的panel-tool  不让其影响放在layout区域下面的panel cryze 2018-10-20*/
.layout-panel > .panel-header .panel-tool a:hover,
.layout-expand > .panel-header .panel-tool a:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.layout-split-north {
  border-bottom: 5px solid #ffffff;
}
.layout-split-south {
  border-top: 5px solid #ffffff;
}
.layout-split-east {
  border-left: 5px solid #ffffff;
}
.layout-split-west {
  border-right: 5px solid #ffffff;
}
.layout-expand {
  background-color: #509de1;
}
.layout-expand-over {
  background-color: #509de1;
}
/*改为只控制其子级panel-header 的panel-tool 不让其影响放在layout区域下面的panel cryze 2018-10-20*/
.layout-panel > .panel-header.panel-header-gray .layout-button-up,
.layout-expand > .panel-header.panel-header-gray .layout-button-up {
  background: url(images/layout_arrows_black.png) -16px -16px no-repeat;
}
.layout-panel > .panel-header.panel-header-gray .layout-button-left,
.layout-expand > .panel-header.panel-header-gray .layout-button-left {
  background: url(images/layout_arrows_black.png) no-repeat;
}
.layout-panel > .panel-header.panel-header-gray .layout-button-right,
.layout-expand > .panel-header.panel-header-gray .layout-button-right {
  background: url(images/layout_arrows_black.png) 0px -16px no-repeat;
}
.layout-panel > .panel-header.panel-header-gray .layout-button-down,
.layout-expand > .panel-header.panel-header-gray .layout-button-down {
  background: url(images/layout_arrows_black.png) -16px 0px no-repeat;
}
.layout-panel > .panel-header.panel-header-gray .panel-tool a:hover,
.layout-expand > .panel-header.panel-header-gray .panel-tool a:hover {
  background-color: #e5e5e5;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.layout-expand .panel-header.panel-header-gray {
  background-color: #f9f9fa;
}
.layout-expand .panel-header-gray + .panel-body {
  background-color: #f9f9fa;
}
.layout-expand .panel-header-gray + .panel-body .layout-expand-body-title > div {
  text-align: center;
  font-weight: bold;
  color: #000;
}
.layout > .panel.layout-panel > .panel-header {
  padding: 4px 5px;
}
.panel-header-gray-parent.layout-expand {
  border-radius: 4px;
}
.layout > .panel.layout-panel > .panel-header.panel-header-gray {
  border-radius: 4px 4px 0 0;
}
.layout > .panel > .panel-header-gray + .panel-body {
  border-radius: 0 0 4px 4px;
}
.layout > .panel > .panel-header-gray.panel-body.panel-body-noheader {
  border-radius: 4px;
}
/*
.layout-expand {
  background-color: #F7F7F7;
}
.layout-expand-over {
  background-color: #F9F9FF;
}
.layout-expand {
  .panel-body{
    border-color:#cccccc;
  }
  .panel-header{
    border-color: #cccccc;
  }
}

}*/
.tabs-scroller-left,
.tabs-scroller-right {
  position: absolute;
  top: auto;
  bottom: 0px;
  width: 19px;
  /*2018-6-28 加宽*/
  font-size: 1px;
  display: none;
  cursor: pointer;
  border: none;
  /*去边框---下面定义*/
}
.tabs-header {
  padding-top: 0px;
  border-top-width: 0;
}
.tabs {
  height: 36px;
  padding-left: 0px;
  border-width: 0 0 0px 0;
  overflow: hidden;
  /*tabs-brand no hover no pointer  cryze 2018-3-15*/
}
.tabs li {
  height: 35px;
  /*wanghc 2018-3-19  win7-ie11*/
  margin: 0px;
}
.tabs li:not(:last-child)::after {
  content: '';
  display: inline-block;
  height: 20px;
  width: 0;
  vertical-align: middle;
  border-left: 1px solid #2A72B0;
  border-right: 1px solid #70B5F1;
  margin-left: 1px;
}
.tabs li.tabs-selected a.tabs-inner {
  background: #ffffff;
  border-top: 3px solid #017BCE;
  color: #000000;
  font-weight: normal;
}
.tabs li.tabs-selected a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
  font-weight: normal;
}
.tabs li.tabs-selected::after {
  content: none;
}
.tabs li.tabs-selected a.tabs-close {
  background: url(images/tabs_icons_2.png) no-repeat 0 0;
}
.tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  color: #ffffff;
  background: #509DE1;
  border-width: 0;
  border-top: 3px solid #509DE1;
  filter: none;
}
.tabs li a.tabs-inner:hover {
  border-top-color: #017bce;
  background: #017bce;
  color: #ffffff;
  filter: none;
}
.tabs li a.tabs-inner .tabs-title.tabs-closable {
  padding-right: 28px;
}
.tabs li a.tabs-inner .tabs-icon {
  margin-top: -6px;
}
.tabs li a.tabs-inner .tabs-title.tabs-with-icon {
  padding-left: 20px;
}
.tabs li a.tabs-close {
  background: url(images/tabs_icons_2.png) no-repeat -18px 0;
  margin-top: -7px;
  right: 10px;
  height: 18px;
  width: 18px;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.tabs li a.tabs-close:hover {
  background: url(images/tabs_icons_2.png) no-repeat -36px 0;
}
.tabs li.tabs-brand a.tabs-inner {
  cursor: default;
}
.tabs li.tabs-brand a.tabs-inner:hover {
  border-top-color: #509DE1;
  background: #509DE1;
  color: #ffffff;
}
/*.tabs li a.tabs-inner {
  color:@tabs-header-font-color;
  background:@tabs-header-bgcolor;
}*/
.tabs-title {
  font-size: 14px;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.tabs-header-left .tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.tabs-header-right .tabs li a.tabs-inner {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.tabs-scroller-left {
  background: #509DE1 url('images/tabs_icons.png') no-repeat 4px center;
  /*2018-6-27 UI建议修改 1px->4px*/
}
.tabs-scroller-right {
  background: #509DE1 url('images/tabs_icons.png') no-repeat -12px center;
  /*2018-6-27 UI建议修改 -15px->-12px*/
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  background: #ffffff ;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  background: #ffffff;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  background: #ffffff;
}
.tabs-header,
.tabs-tool {
  background-color: #509DE1;
  /*modify*/
}
.tabs-header-plain {
  background: transparent;
}
.tabs-header,
.tabs-scroller-left,
.tabs-scroller-right,
.tabs-tool,
.tabs,
.tabs-panels,
.tabs li a.tabs-inner,
.tabs li.tabs-selected a.tabs-inner,
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner,
.tabs-header-left .tabs li.tabs-selected a.tabs-inner,
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-color: #509DE1;
}
.tabs li.tabs-selected a.tabs-inner {
  border-left: 0px solid #ffffff;
  border-bottom: 0px solid #ffffff;
}
.tabs li.tabs-selected a.tabs-inner {
  border-top: 3px solid #017BCE;
}
.tabs-p-tool a:hover {
  background-color: #017BCE;
}
.tabs li a:hover.tabs-color {
  background-color: #017BCE;
}
.tabs-scroller-over {
  background-color: #017BCE;
}
.tabs-header-bottom {
  padding: 0;
}
.tabs-header-bottom.tabs-header {
  border-bottom-width: 0;
}
.tabs-header-bottom .tabs {
  height: 36px;
  padding: 0px;
  border-width: 0 0 0px 0;
}
.tabs-header-bottom .tabs li {
  height: 35px;
  /*wanghc 2018-3-19  win7-ie11*/
  margin: 0px;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  border-top: 0px;
  border-bottom: 3px solid #509DE1;
}
.tabs-header-bottom .tabs li a.tabs-inner:hover {
  border-top: 0px solid #eaf2ff;
  border-bottom-color: #017bce;
  background: #017bce;
  color: #ffffff;
  filter: none;
}
.tabs-header-bottom .tabs li a.tabs-inner .tabs-icon {
  margin-top: -9px;
}
.tabs-header-bottom .tabs li a.tabs-close {
  margin-top: -10px;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  border-left: 0px solid #ffffff;
  border-top: 0px solid #ffffff;
  border-top: 0px;
  border-bottom: 3px solid #017BCE;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
}
.tabs-header-left {
  padding: 0;
}
.tabs-header-left.tabs-header {
  border-left: 0;
  border-top: 1px solid #509DE1;
}
.tabs-header-left .tabs {
  padding: 0px;
  border-width: 0 0 0px 0;
}
.tabs-header-left .tabs li {
  height: 35px;
  /*wanghc 2018-3-19  win7-ie11*/
  float: left;
  margin: 0px;
}
.tabs-header-left .tabs li::after {
  content: none;
}
.tabs-header-left .tabs li a.tabs-inner {
  border: 0 solid #509DE1;
  border-left: 3px solid #509DE1;
}
.tabs-header-left .tabs li a.tabs-inner:hover {
  border-right: 0px solid #eaf2ff;
  border-left-color: #017bce;
  background: #017bce;
  color: #ffffff;
  filter: none;
}
.tabs-header-left .tabs li a.tabs-inner .tabs-icon {
  margin-top: -8px;
}
.tabs-header-left .tabs li a.tabs-close {
  margin-top: -9px;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  border: 0 solid #ffffff;
  border-left: 3px solid #017BCE;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
}
.tabs-header-right {
  padding: 0;
}
.tabs-header-right.tabs-header {
  border-right: 0;
  border-top: 1px solid #509DE1;
}
.tabs-header-right .tabs {
  /*  height: @tabs-header-height;*/
  padding: 0px;
  border-width: 0 0 0px 0;
}
.tabs-header-right .tabs li {
  height: 35px;
  /*wanghc 2018-3-19  win7-ie11*/
  float: right;
  margin: 0px;
}
.tabs-header-right .tabs li::after {
  content: none;
}
.tabs-header-right .tabs li a.tabs-inner {
  border: 0 solid #509DE1;
  border-right: 3px solid #509DE1;
}
.tabs-header-right .tabs li a.tabs-inner:hover {
  border-left: 0px solid #eaf2ff;
  border-right-color: #017bce;
  background: #017bce;
  color: #ffffff;
  filter: none;
}
.tabs-header-right .tabs li a.tabs-inner .tabs-icon {
  margin-top: -8px;
}
.tabs-header-right .tabs li a.tabs-close {
  margin-top: -9px;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border: 0 solid #ffffff;
  border-right: 3px solid #017BCE;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
}
/*--tabs-gray--*/
.tabs-container.tabs-gray .tabs-scroller-left {
  border-color: #F7F7F7;
  background: #F7F7F7 url('images/tabs_icons_gray.png') no-repeat 4px center;
  /*2018-6-27 UI建议修改 1px->4px*/
  border-bottom: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-scroller-right {
  border-color: #F7F7F7;
  background: #F7F7F7 url('images/tabs_icons_gray.png') no-repeat -12px center;
  /*2018-6-27 UI建议修改 -15px->-12px*/
  border-bottom: 1px solid #cccccc;
  border-left: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-p-tool a:hover {
  background-color: #cccccc;
}
.tabs-container.tabs-gray .tabs-scroller-over {
  background-color: #cccccc;
}
.tabs-container.tabs-gray .tabs-tool {
  border-left: 0px;
  border-color: #CCC;
  border-right: 1px solid #F7F7F7;
}
.tabs-container.tabs-gray .tabs-tool .l-btn {
  margin: 1px;
}
.tabs-container.tabs-gray .tabs-header,
.tabs-container.tabs-gray .tabs-tool {
  background-color: #F7F7F7;
}
.tabs-container.tabs-gray .tabs-panels,
.tabs-container.tabs-gray .tabs-header {
  border-color: #CCC;
}
.tabs-container.tabs-gray .tabs-header {
  border: 1px solid #CCC;
  border-bottom: 0;
}
.tabs-container.tabs-gray .tabs-header .tabs {
  border: 0;
  border-bottom: 1px solid #CCC;
}
.tabs-container.tabs-gray .tabs {
  overflow: visible;
  /*tabs-brand no hover no pointer  cryze 2018-3-15*/
}
.tabs-container.tabs-gray .tabs li {
  border: 0;
  height: 35px;
  overflow: hidden;
  /*2020-2-27 overflow-y -> overflow*/
  border-right: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs li::after {
  content: none;
}
.tabs-container.tabs-gray .tabs li a.tabs-inner {
  color: #000000;
  background-color: #F7F7F7;
  /*modify*/
  border-color: #F7F7F7;
}
.tabs-container.tabs-gray .tabs li a.tabs-inner:hover {
  background: #CCC;
  color: #000000;
  border-color: #CCC;
  filter: none;
}
.tabs-container.tabs-gray .tabs li a.tabs-close {
  background: url(images/tabs_icons_2.png) no-repeat 0 0;
}
.tabs-container.tabs-gray .tabs li .tabs-close:hover {
  background: url(images/tabs_icons_2.png) no-repeat -36px 0;
}
.tabs-container.tabs-gray .tabs li.tabs-selected {
  height: 36px;
}
.tabs-container.tabs-gray .tabs li.tabs-selected a.tabs-inner {
  color: #017BCE;
  background-color: #ffffff;
  /*modify*/
  border-color: #017BCE;
}
.tabs-container.tabs-gray .tabs li.tabs-selected a.tabs-inner:hover {
  border-color: #017BCE;
}
.tabs-container.tabs-gray .tabs li.tabs-selected a.tabs-close {
  background: url(images/tabs_icons_2.png) no-repeat 0 0;
}
.tabs-container.tabs-gray .tabs li.tabs-selected .tabs-close:hover {
  background: url(images/tabs_icons_2.png) no-repeat -36px 0;
}
.tabs-container.tabs-gray .tabs li.tabs-brand a.tabs-inner {
  cursor: default;
}
.tabs-container.tabs-gray .tabs li.tabs-brand a.tabs-inner:hover {
  border-color: #F7F7F7;
  background: #F7F7F7;
  color: #000000;
}
/*--tabs-gray--*/
.tabs-container.tabs-gray .tabs-header-bottom {
  padding: 0;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs-scroller-left {
  border-color: #F7F7F7;
  background: #F7F7F7 url('images/tabs_icons_gray.png') no-repeat 4px center;
  /*2018-6-27 UI建议修改 1px->4px*/
  border-top: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs-scroller-right {
  border-color: #F7F7F7;
  background: #F7F7F7 url('images/tabs_icons_gray.png') no-repeat -12px center;
  /*2018-6-27 UI建议修改 -15px->-12px*/
  border-top: 1px solid #cccccc;
  border-left: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-header-bottom.tabs-header {
  border: 1px solid #CCC;
  border-top: 0;
}
.tabs-container.tabs-gray .tabs-header-bottom.tabs-header .tabs {
  border: 0;
  border-top: 1px solid #CCC;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs {
  height: 36px;
  padding: 0;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li {
  border: 0;
  margin: 0;
  height: 35px;
  overflow: hidden;
  border-right: 1px solid #cccccc;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li::after {
  content: none;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li.tabs-selected {
  height: 36px;
  margin-top: -1px;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  color: #017BCE;
  background-color: #ffffff;
  /*modify*/
  border-color: #017BCE;
}
.tabs-container.tabs-gray .tabs-header-bottom .tabs li.tabs-selected a.tabs-inner:hover {
  border-color: #017BCE;
}
.tabs-container.tabs-gray .tabs-header-left {
  padding: 0;
}
.tabs-container.tabs-gray .tabs-header-left.tabs-header {
  border: 1px solid #CCC;
  border-right: 0;
  border-radius: 0;
}
.tabs-container.tabs-gray .tabs-header-left.tabs-header .tabs {
  border: 0;
  border-right: 1px solid #CCC;
  height: 100%;
}
.tabs-container.tabs-gray .tabs-header-left .tabs {
  height: 36px;
  padding: 0px;
}
.tabs-container.tabs-gray .tabs-header-left .tabs li {
  height: 36px;
  float: left;
  margin: 0px;
  border: 0;
  border-bottom: 1px solid #CCC;
  border-right: 1px solid #CCC;
}
.tabs-container.tabs-gray .tabs-header-left .tabs li::after {
  content: none;
}
.tabs-container.tabs-gray .tabs-header-left .tabs li.tabs-selected {
  border-right: 1px solid #fff;
}
.tabs-container.tabs-gray .tabs-header-right {
  padding: 0;
}
.tabs-container.tabs-gray .tabs-header-right.tabs-header {
  border: 1px solid #CCC;
  border-left: 0;
  border-radius: 0;
}
.tabs-container.tabs-gray .tabs-header-right.tabs-header .tabs {
  border: 0;
  border-left: 1px solid #CCC;
  height: 100%;
}
.tabs-container.tabs-gray .tabs-header-right .tabs {
  height: 36px;
  padding: 0px;
}
.tabs-container.tabs-gray .tabs-header-right .tabs li {
  height: 36px;
  float: left;
  margin: 0 0 0 -1px;
  border: 0;
  border-bottom: 1px solid #CCC;
  border-left: 1px solid #CCC;
}
.tabs-container.tabs-gray .tabs-header-right .tabs li::after {
  content: none;
}
.tabs-container.tabs-gray .tabs-header-right .tabs li.tabs-selected {
  border-left: 1px solid #fff;
}
.tabs-gray .tabs-header {
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.tabs-gray .tabs-header.tabs-header-bottom {
  -moz-border-radius: 0 0 4px 4px ;
  -webkit-border-radius: 0 0 4px 4px ;
  border-radius: 0 0 4px 4px ;
}
.tabs-gray .tabs-header.tabs-header-bottom.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-header.tabs-header-left {
  -moz-border-radius: 4px 0 0 4px ;
  -webkit-border-radius: 4px 0 0 4px ;
  border-radius: 4px 0 0 4px ;
}
.tabs-gray .tabs-header.tabs-header-left.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-header.tabs-header-right {
  -moz-border-radius: 0 4px 4px 0 ;
  -webkit-border-radius: 0 4px 4px 0 ;
  border-radius: 0 4px 4px 0 ;
}
.tabs-gray .tabs-header.tabs-header-right.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-header.tabs-header-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels {
  -moz-border-radius: 0 0 4px 4px ;
  -webkit-border-radius: 0 0 4px 4px ;
  border-radius: 0 0 4px 4px ;
}
.tabs-gray .tabs-panels.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-top {
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.tabs-gray .tabs-panels.tabs-panels-top.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-right {
  -moz-border-radius: 0 4px 4px 0 ;
  -webkit-border-radius: 0 4px 4px 0 ;
  border-radius: 0 4px 4px 0 ;
}
.tabs-gray .tabs-panels.tabs-panels-right.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-left {
  -moz-border-radius: 4px 0 0 4px ;
  -webkit-border-radius: 4px 0 0 4px ;
  border-radius: 4px 0 0 4px ;
}
.tabs-gray .tabs-panels.tabs-panels-left.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-gray .tabs-panels.tabs-panels-noborder {
  /*border:false*/
  border: 0;
}
.tabs-keywords .tabs-header {
  background-color: transparent;
  border: 0px;
}
.tabs-keywords .tabs-panels {
  border: 0px;
}
.tabs-keywords .tabs li::after {
  content: none;
}
.tabs-keywords .tabs li a.tabs-inner {
  border-radius: 4px;
  border: 0px;
  background-color: transparent;
  color: #ff4401;
}
.tabs-keywords .tabs li a.tabs-inner:hover {
  background: #ffe9db;
}
.tabs-keywords .tabs li.tabs-selected a.tabs-inner {
  background: #ffe9db;
}
/*gray 且选中蓝条在下方*/
.tabs-gray-btm .tabs-header,
.tabs-gray-btm .tabs-tool {
  background-color: #F7F7F7;
  border-color: #F7F7F7;
}
.tabs-gray-btm .tabs-panels {
  border-color: #ffffff;
}
.tabs-gray-btm .tabs {
  border-bottom: 1px solid #cccccc;
  /*[3173471] [3195938]*/
}
.tabs-gray-btm .tabs li {
  height: auto;
  /*以便关闭按钮在不同高度的tabheight下居中*/
}
.tabs-gray-btm .tabs li a.tabs-inner {
  background-color: #F7F7F7;
  border-top: 0px solid #F7F7F7;
  border-bottom: 3px solid #F7F7F7;
  color: #000000;
}
.tabs-gray-btm .tabs li a.tabs-inner:hover {
  background: #ffffff;
  color: #000000;
  filter: none;
  border-bottom: 3px solid #017BCE;
}
.tabs-gray-btm .tabs li.tabs-selected a.tabs-inner {
  border-top: 0px solid #F7F7F7;
  border-bottom: 3px solid #017BCE;
  background: #ffffff;
}
.tabs-gray-btm .tabs li:not(:last-child)::after {
  content: '';
  display: inline-block;
  height: 20px;
  width: 0;
  vertical-align: middle;
  border-left: 1px solid #CCC;
  border-right: 1px solid #F7F7F7;
  margin-left: 1px;
}
.tabs-gray-btm .tabs li a.tabs-close {
  background-position-x: 0;
  margin-top: -9px;
  /*以便关闭按钮在不同高度的tabheight下居中*/
}
.tabs-gray-btm .tabs li a.tabs-close:hover {
  background: url(images/tabs_icons_2.png) no-repeat -36px 0;
}
.l-btn-text {
  min-width: 0;
  /*@linkbutton-text-width;*/
  line-height: 30px;
  font-size: 14px;
  padding: 0 15px;
  margin: 0px;
}
.l-btn-icon {
  width: 30px;
  height: 30px;
  line-height: 30px;
  position: absolute;
  background-color: #378ec4;
  top: 0px;
  margin-top: 0;
  /*top: @top-4IE8@x9;*/
  /*实测ie8不需要*/
}
.l-btn-left.l-btn-icon-left {
  /*在style加width时，图标偏移*/
  width: 100%;
  width: auto\9;
  /*IE6,7,8*/
  /*2018-11-14*/
}
.l-btn-icon-left .l-btn-text {
  margin: 0 0 0 30px;
}
.l-btn-plain .l-btn-icon-left .l-btn-text {
  margin: 0 4px 0 22px;
  min-width: 0px;
}
.l-btn-icon-left .l-btn-icon {
  left: 0px;
}
.l-btn-icon-right .l-btn-text {
  margin: 0 4px 0 0;
}
.l-btn-plain .l-btn-icon-left .l-btn-text.l-btn-empty {
  margin: 0;
}
.l-btn-left .l-btn-empty {
  width: 30px;
  min-width: 30px;
  margin: 0;
  padding: 0;
}
.l-btn-plain:hover {
  background: #eaf2ff;
  color: #000000;
  border: 1px solid #b7d2ff;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
}
.l-btn {
  color: #ffffff;
  background: #40A2DE;
  filter: none;
  border: 0px solid #40A2DE;
  border-radius: 0;
}
.l-btn.l-btn-active,
.l-btn.m-btn-active,
.l-btn.s-btn-active,
.l-btn:hover {
  background: #0063a7;
  color: #ffffff;
  border: 0px solid #0063a7;
  filter: none;
}
.l-btn.l-btn-active .l-btn-icon,
.l-btn.m-btn-active .l-btn-icon,
.l-btn.s-btn-active .l-btn-icon,
.l-btn:hover .l-btn-icon {
  background-color: #0063a7;
}
.l-btn.l-btn-focus {
  outline: #0063a7 dotted thin;
  background: #0063a7;
  color: #ffffff;
  border: 0px solid #0063a7;
  filter: none;
}
.l-btn.l-btn-focus .l-btn-icon {
  background-color: #0063a7;
}
.l-btn.l-btn-plain.l-btn-focus {
  border: 0;
  padding: 0;
  background-color: #eaf2ff;
}
.l-btn.l-btn-selected {
  background: #0063a7;
  color: #ffffff;
}
.l-btn.l-btn-plain.l-btn-focus {
  color: #000000;
}
/*特殊情况（按钮在蓝色病人信息条上时）：hover背景色为#0063a7*/
.l-btn.hover-dark:hover {
  background: #0063a7;
}
.l-btn.hover-dark:hover .l-btn-icon {
  background-color: #0063a7;
}
.l-btn-plain {
  color: #000000;
  background: transparent;
  border: 0px solid transparent;
  padding: 0px;
  filter: none;
}
.l-btn-plain:hover {
  background: #eaf2ff;
  color: #000000;
  border: 0px solid #b7d2ff;
  padding: 0px;
}
.l-btn-plain .l-btn-left .l-btn-icon {
  background-color: transparent;
}
.l-btn-plain.l-btn-plain-selected {
  background: #eaf2ff;
  color: #000000;
}
.l-btn-disabled,
.l-btn-disabled:hover,
.l-btn.hover-dark.l-btn-disabled:hover,
.l-btn.hover-dark.l-btn-disabled {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background: #bbbbbb;
  color: #ffffff;
  border-color: #bbbbbb;
}
.l-btn-disabled .l-btn-icon,
.l-btn-disabled:hover .l-btn-icon,
.l-btn.hover-dark.l-btn-disabled:hover .l-btn-icon,
.l-btn.hover-dark.l-btn-disabled .l-btn-icon {
  background-color: #999999;
}
.l-btn-plain.l-btn-disabled {
  background-color: transparent;
  border-color: transparent;
}
.l-btn-plain.l-btn-disabled .l-btn-left {
  background-color: transparent;
  color: #444;
  border-color: transparent;
}
.l-btn-plain.l-btn-disabled .l-btn-left .l-btn-icon {
  background-color: transparent;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.l-btn-plain.l-btn-disabled .l-btn-left .l-btn-text {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
/*
.l-btn-disabled,
.l-btn-disabled:hover {
  background:#ffffff;
  color: #444;
}
.hisui-linkbutton.l-btn-disabled,.hisui-linkbutton.l-btn-disabled:hover {
    background:@linkbutton-bgcolor;
    color: #444;    
}*/
/*.linkbutton-plain{
  color: #000000;
  background: transparent;
  border: 1px solid transparent;
  padding: 1px;
  filter: none;
  .l-btn-left .l-btn-icon {
    background-color: transparent;
  }
}*/
a.l-btn-link {
  color: #40A2DE;
}
a.l-btn-link:hover {
  color: #378ec4;
}
/*
.l-btn.big.l-btn-plain{
  .l-btn-icon-left {
    .l-btn-icon{
      left: 50%;
      margin-left: -14px;
    }
   .l-btn-text{
      margin:0 4px 0 0;
      padding:24px 2px 2px 0;
    }
  }
}*/
/*
wanghc 20180516 .big后移，在ie7下这种选择器会变成最后一个类选择
.l-btn.l-btn-plain.big <==> .big
*/
.l-btn.l-btn-plain.big {
  padding: 0;
  border: 0;
}
.l-btn.l-btn-plain.big .l-btn-icon-left {
  padding: 4px 10px;
  width: auto;
}
.l-btn.l-btn-plain.big .l-btn-icon-left .l-btn-icon {
  height: 28px;
  line-height: 28px;
  top: 4px;
  position: absolute;
  width: 100%;
}
.l-btn.l-btn-plain.big .l-btn-icon-left .l-btn-text {
  padding: 38px 0 0px;
  /*43--38px 20180929*/
  line-height: 14px;
  margin: 0;
  text-align: center;
}
.l-btn.l-btn-plain.big:hover {
  border: 0;
  padding: 0;
  background: #dcebf9;
}
.l-btn.l-btn-plain.big:hover .l-btn-text {
  color: #378ec4;
  /*#21ba45;*/
}
.datagrid-btn-separator.big {
  float: none;
  height: 65px;
  border-left: 1px solid #ccc;
  margin: 0px 4px;
  display: inline-block;
  vertical-align: middle;
}
/*增加其它色系按钮*/
.l-btn.l-btn-small.green {
  background-color: #2ab66a;
}
.l-btn.l-btn-small.green.l-btn-disabled {
  background: #bbbbbb;
}
.l-btn.l-btn-small.green:hover,
.l-btn.l-btn-small.green.l-btn-focus {
  background-color: #239e5b;
}
.l-btn.l-btn-small.green:hover.l-btn-disabled,
.l-btn.l-btn-small.green.l-btn-focus.l-btn-disabled {
  background: #bbbbbb;
}
.l-btn.l-btn-small.yellow {
  background-color: #ffb746;
}
.l-btn.l-btn-small.yellow.l-btn-disabled {
  background: #bbbbbb;
}
.l-btn.l-btn-small.yellow:hover,
.l-btn.l-btn-small.yellow.l-btn-focus {
  background-color: #ff9c00;
}
.l-btn.l-btn-small.yellow:hover.l-btn-disabled,
.l-btn.l-btn-small.yellow.l-btn-focus.l-btn-disabled {
  background: #bbbbbb;
}
.l-btn.l-btn-small.red {
  background-color: #f16e57;
}
.l-btn.l-btn-small.red.l-btn-disabled {
  background: #bbbbbb;
}
.l-btn.l-btn-small.red:hover,
.l-btn.l-btn-small.red.l-btn-focus {
  background-color: #d35b46;
}
.l-btn.l-btn-small.red:hover.l-btn-disabled,
.l-btn.l-btn-small.red.l-btn-focus.l-btn-disabled {
  background: #bbbbbb;
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  font-size: 14px;
}
.datagrid-header .datagrid-cell span {
  font-size: 14px;
}
.datagrid-header .datagrid-header-autowrap .datagrid-cell {
  white-space: pre-wrap;
  word-break: break-all;
}
/*---*/
.datagrid-header-row,
.datagrid-row {
  height: 35px;
}
.datagrid-row {
  height: 34px;
}
.datagrid-header,
.datagrid-td-rownumber {
  background: #f4f6f5;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #ddd;
  border-style: solid;
}
.datagrid-row-selected,
.datagrid-row-over.datagrid-row-selected {
  /* selected>hover */
  background: #ffe48d;
  /*color: @datagrid-row-select-color;*/
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #dcf0ff;
  color: #000000;
  cursor: default;
}
.datagrid-cell,
.datagrid-cell-group {
  /*text-overflow: ellipsis;*/
  /*2018-12-4 showTip*/
  padding: 0 8px;
}
/*toolbar 一点改动*/
.datagrid-toolbar {
  background-color: #ffffff;
  border-color: #cccccc;
}
.datagrid-toolbar .l-btn-plain {
  padding: 0 0 0 ;
  border: 0;
  margin-top: 3px;
  margin-bottom: 3px;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-text {
  line-height: 22px;
  padding: 0 10px 0 31px;
  margin: 0 0 0 ;
  color: #666666;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-empty {
  margin: 0;
  width: 22px;
  padding: 0 5px 0 0;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-icon {
  left: 5px;
  height: 22px;
  width: 26px;
}
.datagrid-toolbar .l-btn-plain .l-btn-icon-left .l-btn-empty + .l-btn-icon {
  left: 0;
}
.datagrid-toolbar .l-btn-plain:hover {
  padding: 0 0 0 ;
  border: 0;
  background-color: #dbedf9;
}
.datagrid-toolbar .l-btn-plain:hover .l-btn-text {
  color: #0379d0;
}
/*toolbar 一点改动*/
.datagrid-btoolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
  background: #F4F4F4;
  border-color: #dddddd;
  background-color: #ffffff;
}
.datagrid-btoolbar .l-btn-plain {
  padding: 0 0 0 ;
  border: 0;
  margin-top: 3px;
  margin-bottom: 3px;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-text {
  line-height: 22px;
  padding: 0 10px 0 26px;
  margin: 0 0 0 ;
  color: #666666;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-empty {
  margin: 0;
  width: 22px;
  padding: 0 5px 0 0;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-icon {
  left: 5px;
  height: 22px;
  width: 26px;
}
.datagrid-btoolbar .l-btn-plain .l-btn-icon-left .l-btn-empty + .l-btn-icon {
  left: 0;
}
.datagrid-btoolbar .l-btn-plain:hover {
  padding: 0 0 0 ;
  border: 0;
  background-color: #dbedf9;
}
.datagrid-btoolbar .l-btn-plain:hover .l-btn-text {
  color: #0379d0;
}
.datagrid-body .datagrid-editable {
  padding: 1px;
  /*2942041*/
}
.datagrid-body .datagrid-editable .validatebox-text {
  border-radius: 2px;
}
.datagrid-body .datagrid-editable .datagrid-editable-input {
  border: 1px solid #9ed2f2;
  margin: 0;
  padding: 0 0 0 5px;
  line-height: 28px;
  border-radius: 2px;
}
.datagrid-body .datagrid-editable .datagrid-editable-input:active {
  background-color: #f4faff;
}
/*celltextarea覆盖datagrid-header问题*/
/*celltextarea编辑时背景*/
.datagrid-body .datagrid-editable-input.celltextarea {
  background-color: #ffe48d;
}
td.datagrid-value-changed {
  background: url('images/dirty.gif') no-repeat 0 0;
}
.datagrid-filter-htable .datagrid-cell-filter {
  height: 24px;
  line-height: 24px;
  width: 100%;
  padding: 0px 5px;
  border-width: 1px;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  padding-bottom: 1px;
  border-width: 0 1px 0 0;
}
.propertygrid .datagrid-group {
  height: 21px;
  overflow: hidden;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.propertygrid .datagrid-group span {
  font-weight: bold;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  border-color: #dddddd;
}
.propertygrid .datagrid-view1 .datagrid-group {
  border-color: #E0ECFF;
}
.propertygrid .datagrid-view2 .datagrid-group {
  border-color: #dddddd;
}
.propertygrid .datagrid-group,
.propertygrid .datagrid-view1 .datagrid-body,
.propertygrid .datagrid-view1 .datagrid-row-over,
.propertygrid .datagrid-view1 .datagrid-row-selected {
  background: #E0ECFF;
}
.pagination span.l-btn-text.l-btn-empty {
  line-height: 24px;
  width: 16px;
  min-width: 16px;
  margin: 0 4px;
}
.pagination span.l-btn-icon {
  width: 16px;
  height: 16px;
  top: 50%;
  top: 8px\9;
  margin-top: -8px;
  left: 4px;
}
.pagination .l-btn-plain .l-btn-icon-left .l-btn-text.l-btn-empty {
  margin: 0 0 0 6px ;
}
.pagination .pagination-num:active,
.pagination .pagination-num:focus,
.pagination .pagination-num:hover {
  background-color: #ffe48d;
}
.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 12px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 12px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 22px;
}
.calendar-title {
  text-align: center;
  height: 22px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 2px;
  padding: 0 3px;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  width: 14px;
  height: 14px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth {
  left: 20px;
  background: url('images/calendar_arrows.png') no-repeat -18px -2px;
}
.calendar-nextmonth {
  right: 20px;
  background: url('images/calendar_arrows.png') no-repeat -34px -2px;
}
.calendar-prevyear {
  left: 3px;
  background: url('images/calendar_arrows.png') no-repeat -1px -2px;
}
.calendar-nextyear {
  right: 3px;
  background: url('images/calendar_arrows.png') no-repeat -49px -2px;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-other-month {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.calendar-disabled {
  opacity: 0.2;
  filter: alpha(opacity=20);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 12px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 40px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  margin: 0;
  padding: 2px;
  font-weight: bold;
  font-size: 12px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 21px;
  height: 21px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 2px 2px;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -45px 2px;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-body th,
.calendar-menu-month {
  color: #4d4d4d;
}
.calendar-day {
  color: #000000;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #95B8E7;
}
.calendar {
  border-color: #95B8E7;
}
.calendar-header {
  background: #E0ECFF;
}
.calendar-body,
.calendar-menu {
  background: #ffffff;
}
.calendar-body th {
  background: #F4F4F4;
  padding: 2px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #eaf2ff;
  color: #000000;
}
.calendar-hover {
  border: 1px solid #b7d2ff;
  padding: 0;
}
.calendar-selected {
  background-color: #ffe48d;
  color: #000000;
  border: 1px solid #ffab3f;
  padding: 0;
}
/*日历最下面的today close*/
.datebox-button a {
  font-size: 12px;
}
.datebox-button {
  background-color: #F4F4F4;
}
.datebox-button a {
  color: #444;
  font-weight: 700;
  text-decoration: none;
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.datebox .combo-arrow {
  background-image: url('images/datebox_arrow.png');
}
.combo.disabled.datebox .combo-arrow {
  background-image: url('images/datebox_arrow_disable.png');
  background-color: #f7f7f7;
}
/*.numberbox {
  border: 1px solid #95B8E7;
  margin: 0;
  padding: 0 2px;
  vertical-align: middle;
}
*/
.spinner {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.spinner.disabled {
  border-color: #bbbbbb;
}
.spinner.disabled .spinner-arrow {
  background-color: #f7f7f7;
}
.spinner.disabled .spinner-arrow .spinner-arrow-up {
  background-image: url(images/spinner_arrows_disable.png);
}
.spinner.disabled .spinner-arrow .spinner-arrow-down {
  background-image: url(images/spinner_arrows_disable.png);
}
.spinner .spinner-text {
  font-size: 14px;
  border: 0px;
  line-height: 28px;
  height: 28px;
  margin: 0;
  padding: 0 0 0 5px;
  *margin-top: -1px;
  *height: 26px;
  *line-height: 26px;
  _height: 26px;
  _line-height: 26px;
  vertical-align: baseline;
}
.spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 1;
  filter: alpha(opacity=100);
  display: block;
  font-size: 1px;
  width: 22px;
  height: 14px;
}
.spinner-arrow-up {
  background: url(images/spinner_arrows.png) -21px center no-repeat;
}
.spinner-arrow-down {
  background: url(images/spinner_arrows.png) 0px center no-repeat;
}
.spinner {
  border-color: #9ed2f2;
}
.progressbar {
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  overflow: hidden;
  position: relative;
}
.progressbar-text {
  text-align: center;
  position: absolute;
}
.progressbar-value {
  position: relative;
  overflow: hidden;
  width: 0;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.progressbar {
  border-color: #95B8E7;
}
.progressbar-text {
  color: #000000;
  font-size: 12px;
}
.progressbar-value .progressbar-text {
  background-color: #ffe48d;
  color: #000000;
}
.searchbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.searchbox.disabled {
  border-color: #bbbbbb;
}
.searchbox.disabled .searchbox-button {
  background-color: #f7f7f7;
  background-image: url('images/searchbox_button_disable.png');
}
.searchbox-text:focus + span > span {
  /*20190710*/
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #ffe48d;
}
.searchbox .searchbox-text {
  font-size: 14px;
  border: 0;
  margin: 0;
  padding: 0 0 0 5px;
  *margin-top: -1px;
  vertical-align: top;
}
.searchbox .searchbox-text:active {
  background-color: #ffe48d;
}
.searchbox .searchbox-text:disabled {
  background-color: #f7f7f7;
}
.searchbox .searchbox-text:focus {
  background-color: #ffe48d;
}
.searchbox .searchbox-prompt {
  font-size: 14px;
  color: #ccc;
}
.searchbox-button {
  width: 30px;
  height: 28px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 1;
  filter: alpha(opacity=100);
}
.searchbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox-button {
  background: url('images/searchbox_button.png') no-repeat center center;
}
.searchbox {
  border-color: #9ed2f2;
  /*#95B8E7;*/
  background-color: none;
}
.searchbox .l-btn-plain {
  background: #E0ECFF;
}
.searchbox .l-btn-plain-disabled,
.searchbox .l-btn-plain-disabled:hover {
  opacity: 1;
  filter: alpha(opacity=100);
}
.slider-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-h {
  height: 22px;
}
.slider-v {
  width: 22px;
}
.slider-inner {
  position: relative;
  height: 6px;
  top: 7px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
}
.slider-handle {
  position: absolute;
  display: block;
  outline: none;
  width: 20px;
  height: 20px;
  top: -7px;
  margin-left: -10px;
}
.slider-tip {
  position: absolute;
  display: inline-block;
  line-height: 12px;
  font-size: 12px;
  white-space: nowrap;
  top: -22px;
}
.slider-rule {
  position: relative;
  top: 15px;
}
.slider-rule span {
  position: absolute;
  display: inline-block;
  font-size: 0;
  height: 5px;
  border-width: 0 0 0 1px;
  border-style: solid;
}
.slider-rulelabel {
  position: relative;
  top: 20px;
}
.slider-rulelabel span {
  position: absolute;
  display: inline-block;
  font-size: 12px;
}
.slider-v .slider-inner {
  width: 6px;
  left: 7px;
  top: 0;
  float: left;
}
.slider-v .slider-handle {
  left: 3px;
  margin-top: -10px;
}
.slider-v .slider-tip {
  left: -10px;
  margin-top: -6px;
}
.slider-v .slider-rule {
  float: left;
  top: 0;
  left: 16px;
}
.slider-v .slider-rule span {
  width: 5px;
  height: 'auto';
  border-left: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.slider-v .slider-rulelabel {
  float: left;
  top: 0;
  left: 23px;
}
.slider-handle {
  background: url('images/slider_handle.png') no-repeat;
}
.slider-inner {
  border-color: #95B8E7;
  background: #E0ECFF;
}
.slider-rule span {
  border-color: #95B8E7;
}
.slider-rulelabel span {
  color: #000000;
}
.menu {
  padding: 0;
  background-color: #fff;
  border-color: #40a2de;
  color: #000;
}
.menu-item {
  height: 36px;
  border: none;
}
.menu-text {
  height: 35px;
  line-height: 35px;
  float: left;
  padding-left: 41px ;
  /*28px;*/
}
.menu-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 15px;
  top: 50%;
  margin-top: -8px;
}
.menu-line {
  display: none;
}
.menu-active {
  border-radius: 0;
}
.menu-text,
.menu-text span {
  font-size: 14px;
}
/*.menu-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #ccc;
  -moz-box-shadow: 2px 2px 3px #cccccc;
  -webkit-box-shadow: 2px 2px 3px #cccccc;
  box-shadow: 2px 2px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2, MakeShadow=false, ShadowOpacity=0.2); 
}*/
.menu-active {
  border-color: #dbecf8;
  color: #000000;
  background: #dbecf8;
}
.menu-rightarrow {
  background: url(images/calendar_arrows.png) no-repeat -32px center;
}
.menu-rightarrow {
  right: 15px;
}
.menu-no-icon .menu-text {
  padding-left: 15px;
}
.l-btn-plain.m-btn .l-btn-text {
  color: #000000;
}
.m-btn-downarrow,
.s-btn-downarrow {
  background: url(images/arrow_down_white.png) center 0px no-repeat;
}
.l-btn-plain .m-btn-downarrow,
.l-btn-plain .s-btn-downarrow {
  background: url('images/blue/panel_tools_1.png') -32px -16px no-repeat;
}
.m-btn .l-btn-text {
  min-width: 40px;
}
.m-btn .l-btn-left .l-btn-text {
  margin-right: 32px;
  margin-left: 0px;
}
.m-btn .l-btn-left.l-btn-icon-left .l-btn-text {
  margin-right: 32px;
  margin-left: 18px;
}
.m-btn .l-btn-left .m-btn-line {
  width: 30px;
}
.m-btn-downarrow,
.s-btn-downarrow {
  width: 30px;
  height: 16px;
  top: 8px;
  margin-top: 0;
}
.m-btn-plain-active,
.s-btn-plain-active {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  background-color: #eaf2ff;
}
/*ie11加了兼容模式后,
  内核成ie7
  .menubutton-blue.l-btn.l-btn-plain .l-btn-left .l-btn-icon{display:none;}
  会选中 .l-btn-plain .l-btn-left .l-btn-icon{display:none;}

*/
.l-btn.l-btn-plain.menubutton-blue {
  border: 0;
  padding: 0;
  background-color: #40a2de;
}
.l-btn.l-btn-plain.menubutton-blue:hover,
.l-btn.l-btn-plain.menubutton-blue.m-btn-plain-active {
  background-color: #0063a7;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left {
  width: 100%;
  text-align: left;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .l-btn-text {
  color: #fff;
  margin-left: 0;
  padding-left: 15px;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .l-btn-icon {
  display: none;
}
.l-btn.l-btn-plain.menubutton-blue .l-btn-left .m-btn-downarrow {
  background: url(images/arrow_down_white.png) center 0px no-repeat;
}
.menu.menubutton-blue .menu-item {
  background-color: #6fc2f4;
  height: 30px;
  border-bottom: 1px solid #40a2de;
}
.menu.menubutton-blue .menu-item:hover {
  background-color: #378ec4;
}
.menu.menubutton-blue .menu-item .menu-text {
  color: #fff;
  padding-left: 15px;
  line-height: 30px;
}
.menu.menubutton-blue .menu-item .menu-icon {
  display: none;
}
.menu.menubutton-blue .menu-item:last-child {
  border-bottom: 0px;
}
.menu.menubutton-blue .menu-sep {
  display: none;
}
.l-btn.l-btn-plain.menubutton-toolbar {
  border: 0;
  padding: 0;
  background-color: transparent;
}
.l-btn.l-btn-plain.menubutton-toolbar:hover {
  background-color: #dbedf9;
}
.l-btn.l-btn-plain.menubutton-toolbar.l-btn-focus {
  background-color: #dbedf9;
}
.l-btn.l-btn-plain.menubutton-toolbar.m-btn-plain-active {
  background-color: #dbedf9;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left {
  width: 100%;
  text-align: left;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .l-btn-text {
  line-height: 22px;
  padding: 0 36px 0 31px;
  margin: 0 0 0 ;
  color: #666666;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .l-btn-icon {
  left: 10px;
  height: 22px;
  width: 16px;
}
.l-btn.l-btn-plain.menubutton-toolbar .l-btn-left .m-btn-downarrow {
  height: 16px;
  width: 16px;
  background: url('images/blue/panel_tools_1.png') -32px -16px no-repeat;
  top: 3px;
  right: 10px;
}
.menu.menubutton-toolbar .menu-item {
  background-color: #fff;
  height: 35px;
  /*26px*/
  border: 0px;
}
.menu.menubutton-toolbar .menu-item:hover {
  background-color: #dbedf9;
}
.menu.menubutton-toolbar .menu-item .menu-text {
  color: #000;
  padding-left: 15px;
  /*10px*/
  line-height: 35px;
  /*26px*/
}
.menu.menubutton-toolbar .menu-item .menu-icon {
  display: none;
}
.menu.menubutton-toolbar .menu-item:last-child {
  border-bottom: 0px;
}
.menu.menubutton-toolbar .menu-sep {
  margin-left: 0px;
  /*要求顶二头2937859*/
}
.s-btn:hover .m-btn-line,
.s-btn-active .m-btn-line,
.s-btn-plain-active .m-btn-line {
  display: inline-block;
}
.l-btn:hover .s-btn-downarrow,
.s-btn-active .s-btn-downarrow,
.s-btn-plain-active .s-btn-downarrow {
  border-style: solid;
  border-color: #aac5e7;
  border-width: 0 0 0 1px;
}
.hisui-splitbutton.l-btn-plain .m-btn-downarrow,
.hisui-splitbutton .s-btn-downarrow {
  width: 24px;
}
.messager-body {
  padding: 10px;
  overflow: hidden;
  word-wrap: break-word;
}
.messager-button {
  text-align: center;
  padding-top: 10px;
}
.messager-button .l-btn {
  width: 70px;
}
.messager-icon {
  float: left;
  width: 32px;
  height: 32px;
  margin: 0 10px 10px 0;
}
.messager-info {
  background: url('images/messager_icons.png') no-repeat scroll 0 0;
}
.messager-question {
  background: url('images/messager_icons.png') no-repeat scroll -32px 0;
}
.messager-error {
  background: url('images/messager_icons.png') no-repeat scroll -64px 0;
}
.messager-warning {
  background: url('images/messager_icons.png') no-repeat scroll -96px 0;
}
.messager-success {
  background: url('images/messager_icons.png') no-repeat scroll -128px 0;
}
.messager-progress {
  padding: 10px;
}
.messager-p-msg {
  margin-bottom: 5px;
}
.messager-body .messager-input {
  width: 96%;
  border-radius: 2px;
  padding: 1px 0 1px 10px;
  border: 1px solid #9ed2f2;
  height: 26px;
}
/*--popover--*/
.messager-popover {
  position: absolute;
  border-radius: 5px;
  display: block;
  padding: 9px 15px;
  font-weight: bold;
  font-size: 14px;
  z-index: 10000;
}
.messager-popover.success {
  color: #3c763d;
  background: #e2ffde;
  border: 1px #aae3a2 solid;
}
.messager-popover.info {
  color: #1278b8;
  background: #e3f7ff;
  border: 1px #c0e2f7 solid;
}
.messager-popover.alert {
  color: #ff7e00;
  background: #fff3dd;
  border: 1px #f7d199 solid;
}
.messager-popover.error {
  color: #ff3d2c;
  background: #ffe3e3;
  border: 1px #fec0c0 solid;
}
.messager-popover-icon {
  float: left;
  width: 16px;
  height: 16px;
  margin-right: 10px;
  margin-top: 1px;
}
.messager-popover-icon.success {
  background: url('images/blue/messager_popover.png') no-repeat scroll 0 0;
}
.messager-popover-icon.alert {
  background: url('images/blue/messager_popover.png') no-repeat scroll -16px 0;
}
.messager-popover-icon.error {
  background: url('images/blue/messager_popover.png') no-repeat scroll -32px 0;
}
.messager-popover-icon.info {
  background: url('images/blue/messager_popover.png') no-repeat scroll -48px 0;
}
.messager-popover .content {
  height: 18px;
  float: left;
  margin-top: -1px;
}
.messager-popover .close {
  float: right;
  width: 16px;
  height: 16px;
  display: block;
}
.messager-popover.success .close {
  background: url('images/blue/messager_popover.png') no-repeat scroll -64px 0;
}
.messager-popover.alert .close {
  background: url('images/blue/messager_popover.png') no-repeat scroll -80px 0;
}
.messager-popover.error .close {
  background: url('images/blue/messager_popover.png') no-repeat scroll -96px 0;
}
.messager-popover.info .close {
  background: url('images/blue/messager_popover.png') no-repeat scroll -112px 0;
}
.messager-popover .close:hover {
  background: url('images/blue/messager_popover.png') no-repeat scroll -128px 0;
  cursor: pointer;
}
.tree-node {
  height: auto;
}
.tree-title {
  font-size: 14px;
  height: auto;
  line-height: 28px;
  padding: 0 5px;
  /*2942043*/
}
/*增加.tree>li>ul解决 大节点也变色背景问题 */
.tree > li > ul .tree-node-selected .tree-title {
  background: #ffe48d;
}
.tree-checkbox,
.tree-collapsed,
.tree-expanded,
.tree-file,
.tree-folder,
.tree-indent {
  margin-top: 5px;
}
/*线条*/
.tree-lines {
  /*高度不固定时  tree-folder tree-file tree-folder-open   都加了一个类 tree-icon-lines*/
}
.tree-lines .tree-collapsed,
.tree-lines .tree-expanded,
.tree-lines .tree-indent {
  margin-top: 0;
  height: auto;
  height: 28px;
}
.tree-lines .tree-line {
  background: url('images/blue/tree_lines.png') no-repeat -113px center;
}
.tree-lines .tree-join {
  background: url('images/blue/tree_lines.png') no-repeat -129px center;
}
.tree-lines .tree-expanded {
  background: url('images/blue/tree_lines.png') no-repeat -81px center;
}
.tree-lines .tree-collapsed {
  background: url('images/blue/tree_lines.png') no-repeat -65px center;
}
.tree-lines .tree-root-first .tree-expanded {
  background: url('images/blue/tree_lines.png') no-repeat -200px center;
}
.tree-lines .tree-root-first .tree-collapsed {
  background: url('images/blue/tree_lines.png') no-repeat -184px center;
}
.tree-lines .tree-node-last .tree-expanded {
  background: url('images/blue/tree_lines.png') no-repeat -17px center;
}
.tree-lines .tree-node-last .tree-collapsed {
  background: url('images/blue/tree_lines.png') no-repeat -1px center;
}
.tree-lines .tree-root-one .tree-expanded {
  background: url('images/blue/tree_lines.png') no-repeat -49px center;
}
.tree-lines .tree-root-one .tree-collapsed {
  background: url('images/blue/tree_lines.png') no-repeat -33px center;
}
.tree-lines .tree-joinbottom {
  background: url('images/blue/tree_lines.png') no-repeat -97px center;
}
.tree-lines .tree-folder.tree-folder-open.tree-icon-lines {
  margin: 0;
  background: url('images/blue/tree_lines.png') no-repeat -151px center;
}
.tree-lines .tree-folder.tree-icon-lines,
.tree-lines .tree-file.tree-icon-lines {
  margin: 0;
  background: url('images/blue/tree_lines.png') no-repeat -167px center;
}
/*将树的样式整成手风琴 加个类accordiontree 尝试下*/
.accordiontree.tree {
  border: 1px solid #0e8bdd;
}
.accordiontree.tree > li > .tree-node {
  height: 36px;
  line-height: 36px;
  background-color: #0e8bdd;
  position: relative;
}
.accordiontree.tree > li > .tree-node .tree-hit {
  position: absolute;
  height: 14px;
  width: 14px;
  top: 50%;
  margin-top: -7px;
  right: 10px;
}
.accordiontree.tree > li > .tree-node .tree-hit.tree-collapsed {
  background: url(images/accordion_arrows2.png) -19px center no-repeat;
}
.accordiontree.tree > li > .tree-node .tree-hit.tree-expanded {
  background: url(images/accordion_arrows2.png) -2px center no-repeat;
}
.accordiontree.tree > li > .tree-node .tree-hit:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.accordiontree.tree > li > .tree-node .tree-icon {
  display: none;
}
.accordiontree.tree > li > .tree-node .tree-title {
  height: 36px;
  line-height: 36px;
  color: #fff;
  padding: 0 34px 0 15px;
}
.accordiontree.tree > li > .tree-node.tree-node-hover {
  background-color: #3ea2e4;
}
.accordiontree.tree > li > ul {
  background-color: #f9f9f9;
}
.accordiontree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 36px;
  line-height: 36px;
  position: relative;
}
.accordiontree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #e3e3e3;
}
.accordiontree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #e3e3e3;
}
.accordiontree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  /*2019-12-12 解决选中行变黄底色问题 */
  background: #e3e3e3;
}
.accordiontree.tree > li > ul .tree-node .tree-hit {
  width: 15px;
  height: 18px;
  margin-top: 9px;
}
.accordiontree.tree > li > ul .tree-node .tree-indent {
  width: 15px;
}
.accordiontree.tree > li > ul .tree-node > span.tree-indent:first-child {
  width: 0px;
}
.accordiontree.tree > li > ul .tree-node .tree-icon {
  display: none;
}
.accordiontree.tree > li > ul .tree-node .tree-title {
  height: 36px;
  line-height: 36px;
  color: #000;
  padding: 0 10px 0 0;
}
.treegrid-tr-tree-div-hidden {
  display: none;
}
.validatebox-text {
  height: 28px;
  line-height: 28px;
  margin: 0;
  padding: 0 0 0 5px;
  box-sizing: content-box;
  border: 1px solid #9ed2f2;
  color: #000000;
  font-size: 14px;
  vertical-align: middle;
}
.validatebox-text:active {
  background-color: #ffe48d;
}
.validatebox-text:focus {
  background-color: #ffe48d;
}
.validatebox-text:focus + span > span,
.validatebox-text:focus + input + span > span {
  /*20190710 numberspinner--input+input+span*/
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #ffe48d;
}
.validatebox-text:hover {
  background-color: #ffe48d;
}
.validatebox-text.validatebox-invalid {
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
  border-color: red;
}
.validatebox-text.validatebox-invalid::-webkit-input-placeholder {
  color: #ff6356;
}
textarea.validatebox-text.validatebox-invalid {
  background-position: right 10px bottom 10px;
  background-position-x: 90%\9;
  background-position-y: 50%\9;
}
input[type=text]::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
input::-ms-reveal {
  display: none;
  width: 0px;
  height: 0px;
}
input:-webkit-input-placeholder {
  /* WebKit 核心浏览器 如谷歌Chrome */
  color: #999;
}
input:-moz-placeholder {
  /* 火狐浏览器 */
  color: #999;
}
input:-ms-input-placeholder {
  /* IE 10+ */
  color: #999;
}
.tooltip {
  padding: 5px 8px 5px;
}
.tooltip-content {
  font-size: 14px;
  word-break: break-all;
}
.tooltip-arrow-outer,
.tooltip-arrow {
  _border-color: #4f75aa;
  _filter: chroma(color=#4f75aa);
}
.tooltip {
  background-color: rgba(0, 0, 0, 0.7);
  border-color: #4f75aa;
  border-width: 0;
  color: #ffffff;
}
.tooltip-right .tooltip-arrow-outer {
  border-right-color: #4f75aa;
  border-width: 0;
}
.tooltip-right .tooltip-arrow {
  border-right-color: #ffffff;
}
.tooltip-left .tooltip-arrow-outer {
  border-left-color: #4f75aa;
  border-width: 0;
}
.tooltip-left .tooltip-arrow {
  border-left-color: #ffffff;
}
.tooltip-top .tooltip-arrow-outer {
  border-top-color: #4f75aa;
  border-width: 0;
}
.tooltip-top .tooltip-arrow {
  border-top-color: #ffffff;
}
.tooltip-bottom .tooltip-arrow-outer {
  border-bottom-color: #4f75aa;
  border-width: 0;
}
.tooltip-bottom .tooltip-arrow {
  border-bottom-color: #ffffff;
}
label.checkbox,
label.radio {
  background: url('images/blue/checkbox-v.png') no-repeat;
  padding-left: 21px;
  cursor: pointer;
  line-height: 22px;
  height: 21px;
  vertical-align: middle;
  display: inline-block;
  display: inline\0;
  /*兼容IE8,IE9下,文字超长折行重叠问题*/
}
label.checkbox.right,
label.radio.right {
  padding-left: 0px;
  padding-right: 24px;
}
label.radio {
  background-position-x: -6px;
  background-position-y: -120px;
}
label.radio:hover {
  background-position-y: -144px;
}
label.radio.hover {
  background-position-y: -144px;
}
label.radio.checked {
  background-position-y: -168px;
}
label.radio.disabled {
  background-position-y: -192px;
}
label.radio.checked.disabled {
  background-position-y: -216px;
}
label.radio.invalid {
  background-position-y: -264px;
}
label.radio.right {
  background-position-x: right;
}
label.checkbox,
label.hischeckbox_square-blue.radio {
  background-position-x: -6px;
  background-position-y: 0;
}
label.checkbox:hover,
label.hischeckbox_square-blue.radio:hover {
  background-position-y: -24px;
}
label.checkbox.hover,
label.hischeckbox_square-blue.radio.hover {
  background-position-y: -24px;
}
label.checkbox.checked,
label.hischeckbox_square-blue.radio.checked {
  background-position-y: -48px;
}
label.checkbox.disabled,
label.hischeckbox_square-blue.radio.disabled {
  background-position-y: -72px;
}
label.checkbox.checked.disabled,
label.hischeckbox_square-blue.radio.checked.disabled {
  background-position-y: -96px;
}
label.checkbox.invalid,
label.hischeckbox_square-blue.radio.invalid {
  background-position-y: -240px;
}
label.checkbox.right,
label.hischeckbox_square-blue.radio.right {
  background-position-x: right;
}
.datagrid-header-row label.checkbox {
  margin-top: -4px;
  margin-left: 2px;
}
.has-switch {
  display: inline-block;
  cursor: pointer;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 4px;
  height: 26px;
  border: none;
  border-color: transparent;
  background-color: transparent;
  position: relative;
  text-align: left;
  overflow: hidden;
  line-height: 8px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  vertical-align: middle;
  min-width: 80px;
}
.has-switch.switch-mini {
  min-width: 56px;
  height: 20px;
}
.has-switch.switch-mini i.switch-mini-icons {
  height: 1.20em;
  line-height: 9px;
  vertical-align: text-top;
  text-align: center;
  transform: scale(0.6);
  margin-top: -1px;
  margin-bottom: -1px;
}
.has-switch.switch-small {
  min-width: 80px;
}
.has-switch.switch-large {
  min-width: 120px;
}
.has-switch.deactivate {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default !important;
}
.has-switch.deactivate label,
.has-switch.deactivate span {
  cursor: default !important;
}
.has-switch > div {
  display: inline-block;
  width: 150%;
  position: relative;
  top: 0;
}
.has-switch > div.switch-animate {
  -webkit-transition: left 0.5s;
  -moz-transition: left 0.5s;
  -o-transition: left 0.5s;
  transition: left 0.5s;
}
.has-switch > div.switch-off {
  background-color: #bdbdbd;
  left: -50%;
  /*-49->-50 */
}
.has-switch > div.switch-off label {
  border: 1px solid #bdbdbd;
}
.has-switch > div.switch-on {
  background-color: #21ba45;
  left: 0%;
}
.has-switch > div.switch-on label {
  border: 1px solid #21ba45;
}
.has-switch input[type=radio],
.has-switch input[type=checkbox] {
  display: none;
}
.has-switch span,
.has-switch label {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  display: inline-block;
  height: 26px;
  padding-bottom: 3px;
  padding-top: 2px;
  font-size: 14px;
  line-height: 20px;
}
.has-switch span.switch-mini,
.has-switch label.switch-mini {
  padding-bottom: 4px;
  padding-top: 3px;
  font-size: 10px;
  line-height: 12px;
  height: 20px;
}
.has-switch span.switch-small,
.has-switch label.switch-small {
  padding-bottom: 3px;
  padding-top: 3px;
  font-size: 14px;
  line-height: 18px;
}
.has-switch span.switch-large,
.has-switch label.switch-large {
  padding-bottom: 1px;
  padding-top: 1px;
  font-size: 16px;
  line-height: normal;
}
.has-switch label {
  text-align: center;
  margin-top: 0;
  margin-bottom: 0;
  z-index: 100;
  width: 34%;
  color: #333333;
  border-radius: 4px;
  background-color: #fff;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch label.disabled,
.has-switch label[disabled] {
  color: #333333;
  background-color: #e6e6e6;
  *background-color: #d9d9d9;
}
.has-switch label:active,
.has-switch label.active {
  background-color: #cccccc;
}
.has-switch label i {
  color: #000;
  line-height: 18px;
  pointer-events: none;
}
.has-switch span {
  text-align: center;
  z-index: 1;
  width: 33%;
}
.has-switch span.switch-left {
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  border-bottom-left-radius: 4px;
}
.has-switch span.switch-right {
  color: #fff;
  position: relative;
  top: 0;
  background-color: #bdbdbd;
  border-color: #ffffff #ffffff #d9d9d9;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #ffffff;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-right.disabled,
.has-switch span.switch-right[disabled] {
  color: #333333;
  background-color: #ffffff;
  *background-color: #f2f2f2;
}
.has-switch span.switch-primary,
.has-switch span.switch-left {
  color: #ffffff;
  position: relative;
  top: 0;
  background-color: #21ba45;
  border-color: #0088cc #0088cc #005580;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #0088cc;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-info {
  color: #ffffff;
  background-color: #41a7c5;
  background-image: -moz-linear-gradient(top, #2f96b4, #5bc0de);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#2f96b4), to(#5bc0de));
  background-image: -webkit-linear-gradient(top, #2f96b4, #5bc0de);
  background-image: -o-linear-gradient(top, #2f96b4, #5bc0de);
  background-image: linear-gradient(to bottom, #2f96b4, #5bc0de);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff2f96b4', endColorstr='#ff5bc0de', GradientType=0);
  border-color: #5bc0de #5bc0de #28a1c5;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #5bc0de;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-info:hover,
.has-switch span.switch-info:focus,
.has-switch span.switch-info:active,
.has-switch span.switch-info.active,
.has-switch span.switch-info.disabled,
.has-switch span.switch-info[disabled] {
  color: #ffffff;
  background-color: #5bc0de;
  *background-color: #46b8da;
}
.has-switch span.switch-info:active,
.has-switch span.switch-info.active {
  background-color: #31b0d5;
}
.has-switch span.switch-success {
  color: #ffffff;
  background-color: #58b058;
  background-image: -moz-linear-gradient(top, #51a351, #62c462);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#51a351), to(#62c462));
  background-image: -webkit-linear-gradient(top, #51a351, #62c462);
  background-image: -o-linear-gradient(top, #51a351, #62c462);
  background-image: linear-gradient(to bottom, #51a351, #62c462);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff51a351', endColorstr='#ff62c462', GradientType=0);
  border-color: #62c462 #62c462 #3b9e3b;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #62c462;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-success:hover,
.has-switch span.switch-success:focus,
.has-switch span.switch-success:active,
.has-switch span.switch-success.active,
.has-switch span.switch-success.disabled,
.has-switch span.switch-success[disabled] {
  color: #ffffff;
  background-color: #62c462;
  *background-color: #4fbd4f;
}
.has-switch span.switch-success:active,
.has-switch span.switch-success.active {
  background-color: #42b142;
}
.has-switch span.switch-warning {
  color: #ffffff;
  background-color: #f9a123;
  background-image: -moz-linear-gradient(top, #f89406, #fbb450);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f89406), to(#fbb450));
  background-image: -webkit-linear-gradient(top, #f89406, #fbb450);
  background-image: -o-linear-gradient(top, #f89406, #fbb450);
  background-image: linear-gradient(to bottom, #f89406, #fbb450);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff89406', endColorstr='#fffbb450', GradientType=0);
  border-color: #fbb450 #fbb450 #f89406;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #fbb450;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-warning:hover,
.has-switch span.switch-warning:focus,
.has-switch span.switch-warning:active,
.has-switch span.switch-warning.active,
.has-switch span.switch-warning.disabled,
.has-switch span.switch-warning[disabled] {
  color: #ffffff;
  background-color: #fbb450;
  *background-color: #faa937;
}
.has-switch span.switch-warning:active,
.has-switch span.switch-warning.active {
  background-color: #fa9f1e;
}
.has-switch span.switch-danger {
  color: #ffffff;
  background-color: #d14641;
  background-image: -moz-linear-gradient(top, #bd362f, #ee5f5b);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#bd362f), to(#ee5f5b));
  background-image: -webkit-linear-gradient(top, #bd362f, #ee5f5b);
  background-image: -o-linear-gradient(top, #bd362f, #ee5f5b);
  background-image: linear-gradient(to bottom, #bd362f, #ee5f5b);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffbd362f', endColorstr='#ffee5f5b', GradientType=0);
  border-color: #ee5f5b #ee5f5b #e51d18;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #ee5f5b;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-danger:hover,
.has-switch span.switch-danger:focus,
.has-switch span.switch-danger:active,
.has-switch span.switch-danger.active,
.has-switch span.switch-danger.disabled,
.has-switch span.switch-danger[disabled] {
  color: #ffffff;
  background-color: #ee5f5b;
  *background-color: #ec4844;
}
.has-switch span.switch-danger:active,
.has-switch span.switch-danger.active {
  background-color: #e9322d;
}
.has-switch span.switch-default {
  color: #333333;
  background-color: #f0f0f0;
  background-image: -moz-linear-gradient(top, #e6e6e6, #ffffff);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e6e6e6), to(#ffffff));
  background-image: -webkit-linear-gradient(top, #e6e6e6, #ffffff);
  background-image: -o-linear-gradient(top, #e6e6e6, #ffffff);
  background-image: linear-gradient(to bottom, #e6e6e6, #ffffff);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe6e6e6', endColorstr='#ffffffff', GradientType=0);
  border-color: #ffffff #ffffff #d9d9d9;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #ffffff;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-default:hover,
.has-switch span.switch-default:focus,
.has-switch span.switch-default:active,
.has-switch span.switch-default.active,
.has-switch span.switch-default.disabled,
.has-switch span.switch-default[disabled] {
  color: #333333;
  background-color: #ffffff;
  *background-color: #f2f2f2;
}
.has-switch span.switch-default:active,
.has-switch span.switch-default.active {
  background-color: #e6e6e6;
}
/*增加灰色 2018-6-28*/
.has-switch span.switch-gray {
  color: #ffffff;
  background-color: #bdbdbd;
  background-repeat: repeat-x;
  border-color: #bdbdbd #bdbdbd #bdbdbd;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  *background-color: #bdbdbd;
  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.has-switch span.switch-gray:hover,
.has-switch span.switch-gray:focus,
.has-switch span.switch-gray:active,
.has-switch span.switch-gray.active,
.has-switch span.switch-gray.disabled,
.has-switch span.switch-gray[disabled] {
  color: #ffffff;
  background-color: #bdbdbd;
  *background-color: #bdbdbd;
}
.has-switch span.switch-gray:active,
.has-switch span.switch-gray.active {
  background-color: #bdbdbd;
}
.filebox {
  position: relative;
  border: 1px solid #9ed2f2;
  background-color: #fff;
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  -moz-border-radius: 2px 0 0 2px;
  -webkit-border-radius: 2px 0 0 2px;
  border-radius: 2px 2px 2px 2px;
}
.filebox.filebox-left {
  -moz-border-radius: 0 2px 2px 0;
  -webkit-border-radius: 0 2px 2px 0;
  border-radius: 2px 2px 2px 2px;
}
.filebox.filebox-left.filebox-no-txet {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.filebox.filebox-no-txet {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.filebox.disabled {
  border-color: #bbbbbb;
  background-color: #f7f7f7;
}
.filebox.disabled .filebox-text {
  background-color: #f7f7f7;
  color: #999;
}
.filebox .filebox-button {
  position: absolute;
  top: -1px;
  padding: 0;
  vertical-align: top;
  border-radius: 0 0 0 0;
}
.filebox .filebox-button-right {
  right: 0;
}
.filebox .filebox-button-left {
  left: 0;
}
.filebox .filebox-text {
  border: 0;
  margin: 0;
  padding: 0 0 0 5px;
  white-space: normal;
  vertical-align: top;
  outline-style: none;
  resize: none;
}
.filebox .filebox-value {
  vertical-align: top;
  position: absolute;
  top: 0;
  left: -5000px;
}
.filebox.filebox-plain .filebox-button.l-btn {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn .l-btn-text {
  background: #40A2DE;
}
.filebox.filebox-plain .filebox-button.l-btn .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn .l-btn-icon {
  background-color: transparent;
  opacity: 1;
  /*0.7;*/
  filter: alpha(opacity=70);
}
.filebox.filebox-plain .filebox-button.l-btn:hover .l-btn-text {
  background: #0063a7;
}
.filebox.filebox-plain .filebox-button.l-btn:hover .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn:hover .l-btn-icon {
  opacity: 1;
  filter: alpha(opacity=100);
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-text {
  background: #bbbbbb;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-icon {
  background-color: transparent;
  opacity: 1;
  filter: alpha(opacity=100);
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled .l-btn-icon.icon-folder {
  background: url(icons/folder_disabled.png) no-repeat center center;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled:hover .l-btn-text {
  background: #bbbbbb;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled:hover .l-btn-text.l-btn-empty {
  background-color: transparent;
}
.filebox.filebox-plain .filebox-button.l-btn.l-btn-disabled:hover .l-btn-icon {
  opacity: 1;
  filter: alpha(opacity=100);
}
.filebox-label {
  display: inline-block;
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: pointer;
  left: 0;
  top: 0;
  z-index: 10;
}
.webui-popover-content {
  display: none;
}
.webui-popover-rtl {
  direction: rtl;
  text-align: right;
}
.webui-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 8999;
  /*9999;*/
  display: none;
  min-width: 50px;
  min-height: 32px;
  padding: 0px;
  text-align: left;
  white-space: normal;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.webui-popover.top,
.webui-popover.top-left,
.webui-popover.top-right {
  margin-top: -10px;
}
.webui-popover.right,
.webui-popover.right-top,
.webui-popover.right-bottom {
  margin-left: 10px;
}
.webui-popover.bottom,
.webui-popover.bottom-left,
.webui-popover.bottom-right {
  margin-top: 10px;
}
.webui-popover.left,
.webui-popover.left-top,
.webui-popover.left-bottom {
  margin-left: -10px;
}
.webui-popover.pop {
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  -o-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.pop-out {
  -webkit-transition-property: "opacity,transform";
  -o-transition-property: "opacity,transform";
  transition-property: "opacity,transform";
  -webkit-transition: .15s linear;
  -o-transition: .15s linear;
  transition: .15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.fade,
.webui-popover.fade-out {
  -webkit-transition: opacity .15s linear;
  -o-transition: opacity .15s linear;
  transition: opacity .15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.out {
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.in {
  -webkit-transform: none;
  -o-transform: none;
  transform: none;
  opacity: 1;
  filter: alpha(opacity=100);
}
.webui-popover .webui-popover-content {
  padding: 9px 10px;
  /*14px->10px*/
  overflow: auto;
  display: block;
}
.webui-popover .webui-popover-content > div:first-child {
  width: 99%;
}
.webui-popover-inner .close {
  font-family: arial;
  margin: 8px 10px 0 0;
  float: right;
  font-size: 16px;
  font-weight: 700;
  line-height: 16px;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .2;
  filter: alpha(opacity=20);
  text-decoration: none;
}
.webui-popover-inner .close:hover,
.webui-popover-inner .close:focus {
  opacity: .5;
  filter: alpha(opacity=50);
}
.webui-popover-inner .close:after {
  content: "\00D7";
  width: .8em;
  height: .8em;
  padding: 4px;
  position: relative;
}
.webui-popover-title {
  padding: 8px 10px;
  margin: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #f2f2f2;
  border-bottom-color: #e4e4e4;
  border-radius: 5px 5px 0 0;
  display: block;
  color: #000000;
}
.webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
  display: none;
}
.webui-popover-inverse {
  background-color: #333;
  color: #eee;
}
.webui-popover-inverse .webui-popover-title {
  background: #333;
  border-bottom: 1px solid #3b3b3b;
  color: #eee;
}
.webui-no-padding .webui-popover-content {
  padding: 0;
}
.webui-no-padding .list-group-item {
  border-right: none;
  border-left: none;
}
.webui-no-padding .list-group-item:first-child {
  border-top: 0;
}
.webui-no-padding .list-group-item:last-child {
  border-bottom: 0;
}
.webui-popover > .webui-arrow,
.webui-popover > .webui-arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.webui-popover > .webui-arrow {
  border-width: 11px;
}
.webui-popover > .webui-arrow:after {
  border-width: 10px;
  content: "";
}
.webui-popover.top > .webui-arrow,
.webui-popover.top-right > .webui-arrow,
.webui-popover.top-left > .webui-arrow {
  bottom: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-color: #999;
  border-top-color: rgba(0, 0, 0, 0.25);
  border-bottom-width: 0;
}
.webui-popover.top > .webui-arrow:after,
.webui-popover.top-right > .webui-arrow:after,
.webui-popover.top-left > .webui-arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-top-color: #fff;
  border-bottom-width: 0;
}
.webui-popover.right > .webui-arrow,
.webui-popover.right-top > .webui-arrow,
.webui-popover.right-bottom > .webui-arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.right > .webui-arrow:after,
.webui-popover.right-top > .webui-arrow:after,
.webui-popover.right-bottom > .webui-arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #fff;
}
.webui-popover.bottom > .webui-arrow,
.webui-popover.bottom-right > .webui-arrow,
.webui-popover.bottom-left > .webui-arrow {
  top: -11px;
  left: 50%;
  margin-left: -11px;
  border-bottom-color: #999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  border-top-width: 0;
}
.webui-popover.bottom > .webui-arrow:after,
.webui-popover.bottom-right > .webui-arrow:after,
.webui-popover.bottom-left > .webui-arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #fff;
  border-top-width: 0;
}
.webui-popover.left > .webui-arrow,
.webui-popover.left-top > .webui-arrow,
.webui-popover.left-bottom > .webui-arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.left > .webui-arrow:after,
.webui-popover.left-top > .webui-arrow:after,
.webui-popover.left-bottom > .webui-arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #fff;
  bottom: -10px;
}
.webui-popover-inverse.top > .webui-arrow,
.webui-popover-inverse.top-left > .webui-arrow,
.webui-popover-inverse.top-right > .webui-arrow,
.webui-popover-inverse.top > .webui-arrow:after,
.webui-popover-inverse.top-left > .webui-arrow:after,
.webui-popover-inverse.top-right > .webui-arrow:after {
  border-top-color: #333;
}
.webui-popover-inverse.right > .webui-arrow,
.webui-popover-inverse.right-top > .webui-arrow,
.webui-popover-inverse.right-bottom > .webui-arrow,
.webui-popover-inverse.right > .webui-arrow:after,
.webui-popover-inverse.right-top > .webui-arrow:after,
.webui-popover-inverse.right-bottom > .webui-arrow:after {
  border-right-color: #333;
}
.webui-popover-inverse.bottom > .webui-arrow,
.webui-popover-inverse.bottom-left > .webui-arrow,
.webui-popover-inverse.bottom-right > .webui-arrow,
.webui-popover-inverse.bottom > .webui-arrow:after,
.webui-popover-inverse.bottom-left > .webui-arrow:after,
.webui-popover-inverse.bottom-right > .webui-arrow:after {
  border-bottom-color: #333;
}
.webui-popover-inverse.left > .webui-arrow,
.webui-popover-inverse.left-top > .webui-arrow,
.webui-popover-inverse.left-bottom > .webui-arrow,
.webui-popover-inverse.left > .webui-arrow:after,
.webui-popover-inverse.left-top > .webui-arrow:after,
.webui-popover-inverse.left-bottom > .webui-arrow:after {
  border-left-color: #333;
}
.webui-popover i.icon-refresh:before {
  content: "";
}
.webui-popover i.icon-refresh {
  display: block;
  width: 30px;
  height: 30px;
  font-size: 20px;
  top: 50%;
  left: 50%;
  position: absolute;
  margin-left: -15px;
  margin-right: -15px;
  background: url(../img/loading.gif) no-repeat;
}
@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
.webui-popover-backdrop {
  background-color: rgba(0, 0, 0, 0.65);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8998;
  /*9998 应该小于window-mask的z-index:9000,不然messager.alert显示有问题*/
}
.webui-popover .dropdown-menu {
  display: block;
  position: relative;
  top: 0;
  border: none;
  box-shadow: none;
  float: none;
}
.comboq {
  box-sizing: border-box;
  height: 30px;
}
input.comboq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
.comboq.disabled {
  background-image: url('images/combo_arrow_disable.png');
  background-color: #f7f7f7;
  color: #ccc;
}
.comboq {
  background-image: url(images/combo_arrow.png);
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
input.comboq.textbox {
  padding-right: 30px;
  width: 118px;
  /*textbox样式为content-box, 减去附加的padding,保持与非q系统一样长*/
}
.comboq.bginone {
  background-image: none;
  padding-right: 0px;
}
.lookup.disabled {
  background-image: url('images/lookup_arrow_disable.png');
  background-color: #f7f7f7;
  color: #ccc;
}
.lookup {
  background-image: url(images/lookup_arrow.png);
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
.kw-chapter {
  clear: both;
  font-size: 16px;
  font-weight: bold;
  color: #017bce;
  padding: 15px 0 5px;
}
.kw-chapter a {
  border-right: 5px #017bce solid;
  width: 0px;
  height: 30px;
  padding: 0px;
  margin-right: 5px;
}
.kw-line {
  border-bottom: 1px #eeeeee solid;
  margin: 5px 0;
}
.kw-section {
  margin-bottom: 5px;
  padding-top: 5px;
  clear: both;
}
.kw-section-header {
  font-weight: 600;
  margin-bottom: 5px;
}
.keywords li {
  list-style: none;
  padding: 0;
  margin: 0;
}
ul.kw-section-list {
  padding: 0px;
  margin: 0px;
}
.kw-section-list > li {
  float: left;
  margin: 5px 5px 5px 0;
}
.kw-section-list > li a {
  border-radius: 4px;
  display: block;
  height: 26px;
  line-height: 26px;
  text-align: center;
  background: #efefef;
  color: #666666;
  text-decoration: none;
  padding: 0 10px;
}
.kw-section-list > li a:hover {
  cursor: pointer;
  background: #d8efff;
  color: #017bce;
}
.kw-section-list > li.selected a {
  background: #40a2de;
  color: #feffff;
}
.keywords-labelred .kw-section-list > li a {
  background: transparent;
  color: #ff4401;
}
.keywords-labelred .kw-section-list > li a:hover {
  cursor: pointer;
  background: #ffe9db;
}
.keywords-labelred .kw-section-list > li.selected a {
  background: #ffe9db;
}
.triggerbox {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.triggerbox.disabled {
  border-color: #bbbbbb;
  background-color: #f7f7f7;
}
.triggerbox.disabled .triggerbox-button {
  background-color: #bbbbbb;
}
.triggerbox .triggerbox-text {
  font-size: 14px;
  border: 0;
  margin: 0;
  padding: 0 0 0 5px;
  *margin-top: -1px;
  vertical-align: top;
  /*focus bgcolor*/
}
.triggerbox .triggerbox-text:active,
.triggerbox .triggerbox-text:focus {
  background-color: #ffe48d;
}
.triggerbox .triggerbox-text:active + span > span,
.triggerbox .triggerbox-text:focus + span > span {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #ffe48d;
}
.triggerbox .triggerbox-text:disabled {
  background-color: #f7f7f7;
}
.triggerbox .triggerbox-prompt {
  font-size: 14px;
  color: #ccc;
}
.triggerbox-button {
  width: 30px;
  height: 28px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.triggerbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.triggerbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.triggerbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.triggerbox {
  border-color: #9ed2f2;
  /*#95B8E7;*/
  background-color: #40a2de;
}
.triggerbox.triggerbox-plain {
  background-color: #ffffff;
}
.triggerbox.triggerbox-plain.disabled {
  background-color: #f7f7f7;
}
.triggerbox.triggerbox-plain.disabled .triggerbox-button.icon-trigger-box {
  background: url(icons/trigger_box_disabled.png) center center no-repeat;
}
.triggerbox.triggerbox-plain.disabled .triggerbox-button.icon-copy-blue {
  background: url(icons/copy_blue_disabled.png) center center no-repeat;
}
.triggerbox.triggerbox-plain.disabled .triggerbox-button.icon-folder {
  background: url(icons/folder_disabled.png) center center no-repeat;
}
.triggerbox.triggerbox-plain.disabled .triggerbox-button.icon-img-blue {
  background: url(icons/img_blue_disabled.png) center center no-repeat;
}
.triggerbox.triggerbox-plain .triggerbox-button {
  opacity: 1;
  /*0.7;*/
  filter: alpha(opacity=70);
}
.triggerbox.triggerbox-plain .triggerbox-button.triggerbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background-color: #eaeaea;
}
.triggerbox .l-btn-plain {
  background: #E0ECFF;
}
.triggerbox .l-btn-plain-disabled,
.triggerbox .l-btn-plain-disabled:hover {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.dateboxq.disabled {
  background-image: url('images/datebox_arrow_disable.png');
  background-color: #f7f7f7;
  color: #ccc;
}
.dateboxq {
  background-image: url(images/datebox_arrow.png);
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
input.dateboxq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
.datetimeboxq.disabled {
  background-image: url('images/datebox_arrow_disable.png');
  background-color: #f7f7f7;
  color: #ccc;
}
.datetimeboxq {
  background-image: url(images/datebox_arrow.png);
  background-repeat: no-repeat;
  background-position: right 8px center;
}
input.datetimeboxq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
/*common css*/
.hstep-container {
  font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Wenquanyi Micro Hei", "Microsoft Yahei", Arial, sans-serif;
  display: inline-block;
  position: relative;
  color: #000;
  width: 700px;
  height: 60px;
  font-size: 18px;
}
.hstep-container ul.hstep-container-steps {
  list-style: none;
  position: absolute;
  top: 2px;
  z-index: 10;
}
.hstep-container ul.hstep-container-steps li {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  float: left;
  width: 100px;
  height: 85px;
}
.hstep-container ul.hstep-container-steps li .cnode {
  background-image: url('images/lite/hstep.png');
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  margin: 10px 0px 0px 6px;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
.hstep-container ul.hstep-container-steps li.done {
  color: #000000;
}
.hstep-container ul.hstep-container-steps li.done .cnode {
  background-position: 0px -60px;
}
.hstep-container ul.hstep-container-steps li.undone {
  color: #999999;
}
.hstep-container ul.hstep-container-steps li.undone .cnode {
  background-position: 0px -80px;
}
.hstep-container ul.hstep-container-steps li.active {
  color: #13AE37;
  background-color: transparent;
}
.hstep-container ul.hstep-container-steps li.active .cnode {
  margin-top: 7px;
  background-position: 0px -40px;
  margin-bottom: 3px;
}
.hstep-container ul.hstep-container-steps li.active .cntt {
  color: #13AE37;
}
.hstep-container ul.hstep-container-steps li.hover {
  color: #339EFF;
}
.hstep-container ul.hstep-container-steps li.hover .cnode {
  margin-top: 6px;
  background-position: 0px -100px;
  margin-bottom: 4px;
}
.hstep-container ul.hstep-container-steps li.hover .cntt {
  color: #339EFF;
}
.hstep-container .hstep-progress-highlight {
  background: #18af66;
}
.hstep-container .hstep-progress {
  width: 400px;
  height: 1px;
  position: absolute;
  top: 35px;
  left: 15px;
  float: left;
  margin-right: 10px;
  overflow: hidden;
}
.hstep-container .hstep-progress .hstep-progress-bar {
  width: 400px;
  height: 20px;
  background: #e4e4e4;
  display: inline-block;
}
.hstep-container .hstep-progress .hstep-progress-bar .hstep-progress-highlight {
  height: 20px;
  display: block;
}
.hstep-container ul,
.hstep-container li,
.hstep-container p {
  margin: 0;
  padding: 0;
}
/*common css*/
.vstep-container {
  font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Wenquanyi Micro Hei", "Microsoft Yahei", Arial, sans-serif;
  display: inline-block;
  position: relative;
  color: #000;
  height: 700px;
  font-size: 18px;
}
.vstep-container ul.vstep-container-steps {
  list-style: none;
  position: absolute;
  top: 2px;
  z-index: 10;
}
.vstep-container ul.vstep-container-steps li {
  font-family: "Microsoft Yahei", verdana, helvetica, arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  height: 85px;
}
.vstep-container ul.vstep-container-steps li * {
  float: left;
  margin: 5px 5px 0 0px;
}
.vstep-container ul.vstep-container-steps li .cnode {
  background-image: url('images/lite/hstep.png');
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  margin: 10px 0px 0px 10px;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
}
.vstep-container ul.vstep-container-steps li .title {
  margin-right: 20px;
  margin-left: 5px;
}
.vstep-container ul.vstep-container-steps li.done {
  color: #000000;
}
.vstep-container ul.vstep-container-steps li.done .cnode {
  background-position: 0px -60px;
}
.vstep-container ul.vstep-container-steps li.undone {
  color: #999999;
}
.vstep-container ul.vstep-container-steps li.undone .cnode {
  background-position: 0px -80px;
}
.vstep-container ul.vstep-container-steps li.active {
  color: #13AE37;
  background-color: transparent;
}
.vstep-container ul.vstep-container-steps li.active .cnode {
  margin-top: 7px;
  background-position: 0px -40px;
  margin-bottom: 3px;
}
.vstep-container ul.vstep-container-steps li.active .cntt {
  color: #13AE37;
}
.vstep-container ul.vstep-container-steps li.hover {
  color: #339EFF;
}
.vstep-container ul.vstep-container-steps li.hover .cnode {
  margin-top: 6px;
  background-position: 0px -100px;
  margin-bottom: 4px;
}
.vstep-container ul.vstep-container-steps li.hover .cntt {
  color: #339EFF;
}
.vstep-container .vstep-progress-highlight {
  background: #18af66;
}
.vstep-container .vstep-progress {
  width: 1px;
  height: 400px;
  position: absolute;
  top: 20px;
  left: 17px;
  float: left;
  margin-right: 10px;
  overflow: hidden;
}
.vstep-container .vstep-progress .vstep-progress-bar {
  width: 20px;
  height: 400px;
  background: #e4e4e4;
  display: inline-block;
}
.vstep-container .vstep-progress .vstep-progress-bar .vstep-progress-highlight {
  height: 20px;
  display: block;
}
.vstep-container ul,
.vstep-container li,
.vstep-container p {
  margin: 0;
  padding: 0;
}
.timeboxq.disabled {
  background-image: none;
  background-color: #f7f7f7;
  color: #ccc;
}
.timeboxq {
  background-image: none;
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-position-x: 96%\9;
  background-position-y: 50%\9;
}
input.timeboxq::-ms-clear {
  display: none;
  width: 0px;
  height: 0px;
}
/*2019-07-10 span:hover---输入框与arrow都变色*/
.searchbox:hover input,
.lookup:hover input,
.combo:hover input,
.spinner:hover input,
.triggerbox:hover input,
.searchbox:hover span > span,
.lookup:hover span > span,
.combo:hover span > span,
.spinner:hover span > span,
.triggerbox:hover span > span {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #ffe48d;
}
.searchbox span.searchbox-button.searchbox-button-hover,
.combo span.combo-arrow.combo-arrow-hover,
.triggerbox span.triggerbox-button.triggerbox-button-hover,
.spinner .spinner-arrow span.spinner-arrow-hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #eaeaea;
}
/**蓝色根节点**/
/**蓝色标题 灰色根节点**/
/*0-32 中间为白色搜索图标*/
/* 176-192 向左折叠*/
/* 192-208 向右展开*/
/* 256-272 白色下箭头*/
/* 272-288 白色上箭头*/
/* 112-128 灰色下箭头*/
/* 128-144 灰色上箭头*/
/* 144-160 小灰色右箭头*/
/* 160-176 小灰色上箭头*/
.menutree-default-width {
  width: 192px;
  height: 1px;
  padding: 0;
  margin: 0;
  position: absolute;
  top: -100px;
}
.menutree-default-min-width {
  width: 38px;
  height: 1px;
  padding: 0;
  margin: 0;
  position: absolute;
  top: -100px;
}
/*menutree*/
.menutree .menutree-hidden {
  /*用于控制元素的隐藏*/
  display: none;
}
.menutree > .panel-body {
  border-radius: 0;
  background-color: transparent;
  overflow: hidden;
}
.menutree .menutree-collapse-wrap {
  height: 26px;
  margin-bottom: 4px;
  text-align: center;
  background-color: #F2F2F2;
  border: 1px solid #ddd;
}
.menutree .menutree-collapse-wrap .menutree-collapse {
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-top: 6px;
  cursor: pointer;
}
.menutree .menutree-collapse-wrap .menutree-collapse.menutree-expanded {
  background: url('images/menutree.png') -176px center no-repeat;
}
.menutree .menutree-collapse-wrap .menutree-collapse.menutree-collapsed {
  background: url('images/menutree.png') -192px center no-repeat;
}
.menutree .menutree-searchbox-wrap {
  margin-bottom: 4px;
}
.menutree .menutree-searchbox-wrap .searchbox {
  border-color: #40a2de;
}
.menutree .menutree-searchbox-wrap .searchbox .searchbox-button {
  background: url('images/menutree.png') -1px center no-repeat;
  background-color: #40a2de;
}
.menutree .menutree-searchbox-wrap .searchbox .searchbox-button.searchbox-button-hover {
  background: url('images/menutree.png') -1px center no-repeat;
  background-color: #40a2de;
}
.menutree .menutree-tree-wrap {
  border: 1px solid #509de1;
  background-color: #f9f9f9;
  overflow: auto;
}
.menutree .menutree-tree-wrap .menutree-tree.tree {
  border: 0px solid #0e8bdd;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  height: 35px;
  background-color: #0e8bdd;
  line-height: 35px;
  font-weight: bold;
  color: #ffffff;
  position: relative;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit {
  position: absolute;
  height: 16px;
  width: 16px;
  top: 50%;
  margin-top: -8px;
  right: 10px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  background: url('images/menutree.png') -256px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  background: url('images/menutree.png') -272px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-icon {
  display: none;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-title {
  height: 35px;
  line-height: 35px;
  color: #ffffff;
  padding: 0 34px 0 13px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #0e8bdd;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul {
  background-color: transparent;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 35px;
  line-height: 35px;
  position: relative;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #e3e3e3;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover .tree-title {
  background: #e3e3e3;
  color: #000000;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #e3e3e3;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  background: #e3e3e3;
  color: #000000;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit {
  width: 16px;
  height: 16px;
  margin-top: 9px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-collapsed {
  background: url('images/menutree.png') -144px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-collapsed.tree-collapsed-hover {
  background: url('images/menutree.png') -144px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-expanded {
  background: url('images/menutree.png') -160px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-hit.tree-expanded.tree-expanded-hover {
  background: url('images/menutree.png') -160px center no-repeat;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-indent {
  width: 15px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node > span.tree-indent:first-child {
  width: 0px;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-icon {
  display: none;
}
.menutree .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-title {
  height: 35px;
  line-height: 35px;
  color: #000;
  padding: 0 10px 0 0;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .menutree-tip-count {
  display: block;
  position: absolute;
  right: 20px;
  top: 10px;
  width: auto;
  padding-left: 5px;
  padding-right: 5px;
  height: 16px;
  background-color: #dddddd;
  border-radius: 4px;
  line-height: 16px;
  text-align: center;
  font-size: 12px;
  color: #666666;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .menutree-reg-word {
  background-color: yellow;
  color: #000;
}
.menutree .menutree-tree-wrap .menutree-tree.tree .menutree-node-hidden {
  display: none;
}
.menutree .menutree-tree-wrap.menutree-tree-norootcollapse .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree .menutree-tree-wrap.menutree-tree-collapsible .menutree-tree.tree > li > .tree-node .tree-icon {
  display: inline-block;
  height: 35px;
  margin: 0;
  width: 16px;
  margin-left: 13px;
}
.menutree .menutree-tree-wrap.menutree-tree-collapsible .menutree-tree.tree > li > .tree-node .tree-title {
  padding-left: 10px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree-title {
  height: 35px;
  line-height: 35px;
  background-color: #0e8bdd;
  color: #ffffff;
  padding-left: 13px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree {
  border: 0px solid #0e8bdd;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  height: 35px;
  background-color: #F2F2F2;
  line-height: 35px;
  font-weight: normal;
  color: #000000;
  position: relative;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit {
  position: absolute;
  height: 16px;
  width: 16px;
  top: 50%;
  margin-top: -8px;
  right: 10px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  background: url('images/menutree.png') -112px center no-repeat;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  background: url('images/menutree.png') -128px center no-repeat;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit:hover {
  background-color: rgba(255, 255, 255, 0.3);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-title {
  height: 35px;
  line-height: 35px;
  color: #000000;
  padding: 0 34px 0 13px;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #e3e3e3;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-selected {
  background-color: #e3e3e3;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul {
  background-color: transparent;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 35px;
  line-height: 35px;
  position: relative;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #d8efff;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-hover .tree-title {
  background: #d8efff;
  color: #017BCE;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #d8efff;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  background: #d8efff;
  color: #017BCE;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul .tree-node .tree-title {
  color: #000;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree .menutree-tip-count {
  background-color: #E4E4E4;
  color: #666666;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree .tree-node-selected .menutree-tip-count,
.menutree .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree .tree-node-hover .menutree-tip-count {
  background-color: #FFE9E9;
  color: #EE0F0F;
}
.menutree .menutree-tree-wrap.menutree-tree-withtitle.menutree-tree-norootcollapse .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-min > .panel-body {
  border-radius: 0;
  background-color: transparent;
  overflow: hidden;
}
.menutree.menutree-min .menutree-searchbox-wrap {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap {
  background-color: #F2F2F2;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree {
  border: 0px;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-title {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li.menutree-root-hover > .tree-node {
  background-color: #e3e3e3;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-hover {
  background-color: #e3e3e3;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-selected {
  background-color: #e3e3e3;
}
.menutree.menutree-min .menutree-tree-wrap .menutree-tree.tree > li > ul {
  background-color: #ffffff;
  position: absolute;
  display: none!important;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle {
  background-color: #F2F2F2;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree-title {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree {
  border: 0px;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-title {
  display: none;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #F2F2F2;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node.tree-node-selected {
  background-color: #F2F2F2;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > .tree-node .tree-icon {
  margin-left: 10px;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li > ul {
  background-color: #ffffff;
  display: none!important;
  position: absolute;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li.menutree-root-hover > .tree-node {
  background-color: #e3e3e3;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-hover {
  background-color: #e3e3e3;
}
.menutree.menutree-min .menutree-tree-wrap.menutree-tree-withtitle .menutree-tree.tree > li.menutree-root-hover > .tree-node.tree-node-selected {
  background-color: #e3e3e3;
}
.menutree.menutree-sp > .panel-body {
  border-radius: 0;
  background-color: transparent;
  overflow: hidden;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node {
  /*第一层节点 根节点*/
  height: 35px;
  background-color: #e3e3e3;
  line-height: 35px;
  font-weight: normal;
  color: #000000;
  position: relative;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-collapsed {
  display: none;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-hit.tree-expanded {
  display: none;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node .tree-title {
  height: 35px;
  line-height: 35px;
  color: #000000;
  padding: 0 34px 0 13px;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node.tree-node-hover {
  background-color: #e3e3e3;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > .tree-node.tree-node-selected {
  background-color: #e3e3e3;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul {
  background-color: transparent;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node {
  padding-left: 5px;
  height: 35px;
  line-height: 35px;
  position: relative;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover {
  background-color: #d8efff;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-hover .tree-title {
  background: #d8efff;
  color: #017BCE;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected {
  background-color: #d8efff;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node.tree-node-selected .tree-title {
  background: #d8efff;
  color: #017BCE;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree > li > ul .tree-node .tree-title {
  color: #000;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree .menutree-tip-count {
  background-color: #E4E4E4;
  color: #666666;
}
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree .tree-node-selected .menutree-tip-count,
.menutree.menutree-sp .menutree-tree-wrap .menutree-tree.tree .tree-node-hover .menutree-tip-count {
  background-color: #FFE9E9;
  color: #EE0F0F;
}
.z-q-clearbtnicon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  background: url(images/tabs_icons_2.png) no-repeat 0px -1px;
}
.z-q-clearbtnicon:hover,
.z-q-clearbtnicon:focus {
  background-position-x: -36px;
  color: red;
  cursor: pointer;
}
