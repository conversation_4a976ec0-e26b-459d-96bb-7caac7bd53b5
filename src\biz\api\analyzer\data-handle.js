export const getProcessListBySubSetId = (subsetId) => {
  return {
    url: `/analyzer/process/list/${subsetId}`,
    method: 'get',
  }
}

// 异常值处理
export const abnormalValue = (data) => {
  return {
    url: `/analyzer/process/fill_numeric_outliers`,
    method: 'post',
    data
  }
}

// 极值处理
export const extreme = (data) => {
  return {
    url: `/analyzer/process/data_process_extreme`,
    method: 'post',
    data
  }
}

export const getExtremeOutliers = (data) => {
  return {
    url: `/analyzer/process/data_process_extreme/by`,
    method: 'post',
    data
  }
}

// 去掉特殊符号
export const specialSymbol = (data) => {
  return {
    url: `/analyzer/process/fill_symbol_outliers`,
    method: 'post',
    data
  }
}

// 无效数据处理
export const invalid = (data) => {
  return {
    url: `/analyzer/process/invalid_data`,
    method: 'post',
    data
  }
}

// 缺失值处理
export const valueMissing = (data) => {
  return {
    url: `/analyzer/process/miss_value`,
    method: 'post',
    data
  }
}

// 自定义变量
export const mathCalculate = (data) => {
  return {
    url: `/analyzer/process/calculate`,
    method: 'post',
    data
  }
}

// 离散化处理
export const classifyByRanges = (data) => {
  return {
    url: `/analyzer/process/classify_by_ranges`,
    method: 'post',
    data
  }
}

// 独特编码
export const classifyByOnehot = (data) => {
  return {
    url: `/analyzer/process/classify_by_onehot`,
    method: 'post',
    data
  }
}

// 特征分类编码
export const classifyByCategories = (data) => {
  return {
    url: `/analyzer/process/classify_by_categories`,
    method: 'post',
    data
  }
}
export const getVarCategory = ({varName, subsetId}) => {
  return {
    url: `/analyzer/subset/variable/category/${subsetId}`,
    method: 'get',
    params: {
      varName
    }
  }
}

// 数据标准化
export const normalize = (data) => {
  return {
    url: `/analyzer/process/normalize`,
    method: 'post',
    data
  }
}
