
export const queryListApi = (params) => {
  return {
    url: 'edc/message-record/page',
    method: 'get',
    params,
  }
}

export const queryAll = (params) => {
  return {
    url: 'edc/message-record/list',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/message-record/deletion',
    method: 'post',
    data
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/message-record/detail/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/message-record/insert',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/message-record/update',
    method: 'post',
    data
  }
}


