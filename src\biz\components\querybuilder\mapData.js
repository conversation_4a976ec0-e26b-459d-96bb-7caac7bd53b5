import i18n from "@base/i18n/index.js"
// 数据类型
// export const datatypeMap = {
//     1: "数字",
//     2: "文本",
//     3: "日期",
//     4: "时间",
//     5: "日期时间",
//     6: "单选下拉框",
//     7: "字典",
//     8: "关键词",
// };

// 关系词类型
export const relationMap2 = {
  1: { name: i18n.t('biz.search.web.common.eq'), code: "eq" },
  2: { name: i18n.t('biz.search.web.common.greaterThan'), code: "gt" },
  3: { name: i18n.t('biz.search.web.common.lessThan'), code: "lt" },
  4: { name: i18n.t('biz.search.web.common.greaterOrEq'), code: "gte" },
  5: { name: i18n.t('biz.search.web.common.lessOrEq'), code: "lte" },
  6: { name: i18n.t('biz.search.web.common.notEq'), code: "ne" },
  7: { name: i18n.t('biz.search.web.common.containes'), code: "include" },
  8: { name: i18n.t('biz.search.web.common.notContain'), code: "not_include" },
  9: { name: i18n.t('biz.search.web.common.semanticInclude'), code: "include2" },
  10: { name: i18n.t('biz.search.web.common.multiple'), code: "in" },
  11: { name: i18n.t('biz.search.web.common.null'), code: "is_null" },
  12: { name: i18n.t('biz.search.web.common.notNull'), code: "not_null" },
  23: { name: i18n.t('biz.search.web.common.multipleLike'), code: "in_and_like" },
  24: { name: i18n.t('biz.search.web.common.multipleParentAllLike'), code: "in_and_parent_like" },
  25: { name: i18n.t('biz.search.web.common.multiple2'), code: "in_and_parent" },
  26: { name: i18n.t('biz.search.web.common.containLike'), code: "include_like" },
  27: { name: i18n.t('biz.search.web.common.regexp'), code: "regexp" },
}
// 阴阳性关系词
export const filterTypeList = [
  { name: i18n.t('biz.search.web.common.positiveInclude'), code: "positive_include" },
  { name: i18n.t('biz.search.web.common.negativeInclude'), code: "negative_include" }
]

// 数据类型下拉选项
// export const typeOptions = [
//     { label: '数字', value: 1 },
//     { label: '文本', value: 2 },
//     { label: '日期', value: 3 },
//     { label: '时间', value: 4 },
//     { label: '日期时间', value: 5 },
//     { label: '单选下拉框', value: 6 },
//     { label: '字典', value: 7 },
//     { label: '关键词', value: 8 },
// ];

// 关系词下拉选项
export const relatedOptions = [
  { label: i18n.t('biz.search.web.common.eq'), value: "eq" },
  { label: i18n.t('biz.search.web.common.greaterThan'), value: "gt" },
  { label: i18n.t('biz.search.web.common.lessThan'), value: "lt" },
  { label: i18n.t('biz.search.web.common.greaterOrEq'), value: "gte" },
  { label: i18n.t('biz.search.web.common.lessOrEq'), value: "lt" },
  { label: i18n.t('biz.search.web.common.notEq'), value: "ne" },
  { label: i18n.t('biz.search.web.common.containes'), value: "include" },
  { label: i18n.t('biz.search.web.common.notContain'), value: "not_include" },
  { label: i18n.t('biz.search.web.common.openInterval'), value: "between_open" },
    // { label: '多值匹配(同时包含)', value: "in_and_parent" },
]
export default { relatedOptions }
