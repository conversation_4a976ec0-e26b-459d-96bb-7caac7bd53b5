.advancedSearch-index {
  position: relative;
  .highLight-text {
    color: #409eff;
  }

  .searchHistory-wrapper {
    position: relative;
    float: right; 
    right: 0;
    z-index: 10;
    // 检索历史
    .searchHistory {
      line-height: 35px;
      padding: 0 10px;
      cursor: pointer;
      font-size: 14px;
      color: #999999;
      &:hover {
        color: #999999;
      }
    }
  }


  .search-content {
    margin: 0 !important;
    display: flex;
  }
  .search-content-filter {
    // display: none; // 左侧隐藏
    width: 300px;
    margin-right: 10px;
    // float: left;
    // padding-top: 10px;
    // margin-left: 5px;
    background-color: #fff;
    min-height: calc(100vh - 425px);
    border-radius: 5px;
  }
  .search-result {
    // width: calc(100% - 330px); // 左侧暂时隐藏,把宽度调整为100%
    width: 100%;
    // float: left;
    // margin-left: 10px; // 左侧暂时隐藏,不设置margin-left
    padding: 10px;
    background-color: #fff;
    border-radius: 5px;

    .search-address {
      padding: 20px 20px 5px 20px;
      font-size: 14px;

      .result-type {
        float: right;

        .hos-radio {
          margin-right: 10px;
        }
      }
    }
  }
}
