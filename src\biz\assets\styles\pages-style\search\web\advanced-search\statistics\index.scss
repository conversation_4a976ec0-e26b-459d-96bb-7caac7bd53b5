.analysis-dialog.hos-dialog__wrapper.full-height-dialog {
  &>.hos-dialog>.hos-dialog__header {
    padding: 0;
  }

  &>.hos-dialog>.hos-dialog__body {
    height: calc(100% - 36px);
    max-height: unset !important;
    overflow-y: auto;
  }
                     
  .hos-dialog__headerbtn.hos-dialog__headerbtn--collapse {
    display: none;
  }

  .titleBodyPlace {
    display: flex;
    flex-wrap: nowrap;

    .cardInside {
      flex: 1;
      margin-top: 10px;

      // text-align: center;
      // align-items: center;
      .cardInsideL {
        font-size: 16px;
      }

      .cardInsideR {
        color: rgb(25, 154, 242);
        font-size: 18px;
        font-weight: 900;
      }
    }
  }

  // 相关患者统计的内置卡片样式
  .top10CardOutSider {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .hos-col {
      border: 1px solid rgb(236, 236, 236);
      border-radius: 5px;
      margin: 10px 10px 0 0;
      width: 31%;
      min-width: 300px;
      height: 400px;
      box-sizing: border-box;

      .top10Card {
        height: 400px;
      }

      // background-color:red
    }

    .hos-empty {
      width: 100%;
    }
  }

  .index-container {
    // position:absolute;
    // top:-10px;
    // left:0;
    // width: calc(100% - 20px);
    min-height: calc(100vh - 50px);
    margin: 0 10px;

    // background-color: rgb(236,236,236);
    // background: #fff;
    .trend-container,
    .adm-distribution-container,
    .pat-distribution-container {
      margin: 0 0 15px 0;
    }

    .top-container {
      margin: 15px 0;
      height: 100px;
    }

    .info-card {
      height: 100px;
    }

    .info-icon {
      font-size: 50px;
      padding: 10px;
      border: solid 1px #beddfd;
      border-radius: 50px;
      background-color: #beddfd;
      color: #fff;
      margin-left: 20px;
    }

    .count-icon {
      font-size: 50px;
      padding: 10px;
      border: none;
      color: #beddfd;
      margin-left: 20px;
    }

    .count-label {
      font-size: 50px;
      font-weight: bolder;
    }

    .count-label-small {
      font-size: 35px;
      font-weight: bolder;
    }

    .index-card,
    .trend-container,
    .adm-distribution-container,
    .pat-distribution-container {
      padding: 10px;
      padding-bottom: 30px;
      border-radius: 5px;
      color: #666;
      background: #fff;

      .header {
        margin-bottom: 10px;
      }
    }
  }

  // 可供点击的
  .clickable {
    cursor: pointer;
  }

  // 格栏标题
  .blockTitle {
    font-size: 18px;
    font-weight: 900;
    color: #666;
  }
}
