.grade3-box {
  height: 100%;

  // border: 1px solid #d9d9d9;
  // border-radius: 4px;
  .grade3-out-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;

    .grade3-box-left {
      .left-menu {
        width: 120px;
        padding-right: 5px;
        overflow-x: hidden;
        box-sizing: border-box;
        // height: fit-content;
        // max-height: 100%;
        height: 100%;
        overflow-y: scroll;
        padding-left: 5px;
        display: flex;
        flex-direction: column;

        span {
          display: inline-block;
          width: 100%;
          height: 40px;
          line-height: 40px;
          white-space: nowrap;
          text-overflow: ellipsis;
          padding-left: 10px;
          border-left: 2px solid #cacaca;
          cursor: pointer;

          &:hover {
            color: #589cfc;
          }
        }

        span.is-active {
          color: #589cfc;
          border-left: 2px solid #589cfc;
        }
      }

      .left-menu.scroll::-webkit-scrollbar {
        display: none; // 隐藏menu滚动条
        // width: 2px;
        // background-color:#F5F5F5;
      }

      /*定义滚动条轨道：内阴影+圆角*/
      .left-menu.scroll::-webkit-scrollbar-track {
        // background-color:#F5F5F5;
      }

      /*定义滑块：内阴影+圆角*/
      .left-menu.scroll::-webkit-scrollbar-thumb {
        // border-radius:10px;
        // background-color:#555;
      }
    }

    .grade3-box-right {
      width: calc(100% - 130px);
      height: 100%;
      position: relative;
      margin-left: 5px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;

      .title {
        height: 40px;
        box-sizing: border-box;
        line-height: 40px;
        padding-left: 30px;
        border-bottom: 1px solid #d9d9d9;
        background-color: #f5f7fa;
      }

      .search {
        width: 250px;
        height: 48px;
        padding: 10px;
        box-sizing: border-box;
      }

      .field-content {
        display: flex;
        width: 100%;
        height: calc(100% - 128px);
        overflow-y: auto;
        padding-left: 10px;

        .load-more-line {
          text-align: center;
          color: #589cfc;

          span {
            cursor: pointer;

            i {
              margin-left: 3px;
            }
          }
        }

        .check-all-div {
          position: absolute;
          top: 10px;
          left: 5px;
        }

        .hos-tag {
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: pointer;
        }

        .hos-tag.is-selected {
          color: white;
          background-color: #589cfc;
        }

        .hos-tag.disable {
          color: white;
          border-color: #909399;
          background-color: #909399;
          cursor: not-allowed;
        }
      }

      .garde012-btn-container {
        height: 40px;
        padding-right: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}