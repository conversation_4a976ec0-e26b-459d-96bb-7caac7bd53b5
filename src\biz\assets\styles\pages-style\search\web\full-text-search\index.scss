.advanced-retrieval {
  .box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0px auto;
    height: 100%;
    width: 100%;
    text-align: center;
    background-color: #fff;
    padding-bottom: 100px;
  }

  .box .search-history-text {
    margin-top: 20px;
    color: #999999;
    text-decoration: underline;
    cursor: pointer;
  }

  .box.show-result {
    padding-top: 20px;
  }

  .search-group {
    // top:200px;
    margin: 0 auto;
    position: relative;
    display: inline-flex;

    .hos-input .hos-input__inner {
      line-height: 28px;
      padding-left: 10px !important;
      height: 30px !important;
      border: none !important;
      font-size: 16px !important;
      background: #fff !important;
    }

    .hos-input .hos-input__inner:focus {
      box-shadow: none;
    }
  }

  .keyword-type,
  .search-input,
  .search-button {
    outline: none;
    height: 50px;
    line-height: 50px;
  }

  .keyword-type {
    color: #777;
    user-select: none;
    border: 1px solid #409eff;
    border-right: none;
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px;
    width: 100px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    /* Firefox */
    -webkit-box-sizing: border-box;
    /* Safari */
    position: relative;

    .filter-select {
      cursor: pointer;
    }

    .filter-select:after {
      content: "";
      position: absolute;
      left: auto;
      top: 15px;
      right: 0;
      height: 40%;
      width: 1px;
      background-color: #999;
    }
  }

  .filter-list {
    position: absolute;
    min-width: 108px;
    cursor: pointer;
    border: 1px solid #ddd;
    background-color: #fff;
    white-space: nowrap;
    -o-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -ms-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  }

  .filter-list ul {
    padding: 0;
  }

  .filter-list ul li {
    list-style-type: none;
    line-height: 32px;
    text-align: left;
    padding-left: 15px;
  }

  .filter-list ul li:hover {
    background-color: #f2f1fa;
  }

  .filter-list ul li.active {
    background-color: #409eff;
  }

  .search-input {
    width: 467px;
    border: 1px solid #409eff;
    border-left: none;
    // border-bottom-left-radius: 5px;
    // border-top-left-radius: 5px;
    border-right: none;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    /* Firefox */
    -webkit-box-sizing: border-box;
    /* Safari */
    font-size: 16px;
    color: #222;
    padding: 0 10px;
  }

  .search-button {
    width: 120px;
    line-height: 48px;
    cursor: pointer;
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
    border-right: none;
    color: #fff;
    background: none repeat scroll 0 0 #409eff;
    border: 0;
    padding: 0;
    vertical-align: baseline !important;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    margin-bottom: 0;
    font-weight: 500;
    font-size: 16px;
    display: inline-block;
    position: relative;

    i {
      vertical-align: middle;
    }
  }
}