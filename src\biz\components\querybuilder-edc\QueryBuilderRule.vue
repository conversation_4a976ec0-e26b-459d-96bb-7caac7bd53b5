<!-- 单个条件 -->
<template>
  <div class="rule-container">
    <div class="rule-header">
      <div class="rule-filter-container">
        <hos-input v-model="input_name" :disabled="queryBuilderObj.disabled" :class="{'is-error':fieldError}"
                  size="small" @blur="input_name= getFullInputName_edc(query.fieldItemInfo)"
        >
          <i slot="suffix" :title="$t('点击选择指标')" class="search-open-icon hos-input__icon icon hos-icon-search"
             @click="openSearchItem"
          />
        </hos-input>
      </div>
      <div class="rule-operator-container">
        <hos-select v-model="query.relative" :disabled="queryBuilderObj.disabled" :class="{'is-error':relativeError}"
                   size="small"
        >
          <hos-option v-for="operator in queryOperatorOption" :key="operator.value" :value="operator.value"
                     :label="operator.label"
          />
        </hos-select>
      </div>
      <div class="rule-value-container">
        <!--文本框-->
        <hos-input v-if="query.inputType === 'text' || query.inputType === 'keyword' || query.inputType === 'string' || query.inputType === 'input' " v-model="query.value"
                  :disabled="queryBuilderObj.disabled"
                  size="small" type="text" :class="{'is-error':valueError}" clearable
        />
        <!--数值框-->
        <hos-input-number v-else-if="query.inputType === 'number'" v-model="query.value"
                         :disabled="queryBuilderObj.disabled" :class="{'is-error':valueError}" size="small"
        />
        <!--日期时间选择框-->
        <hos-date-picker v-else-if="query.inputType === 'date' || query.inputType === 'datetime'" v-model="query.value"
                        :disabled="queryBuilderObj.disabled" size="small"
                        value-format="timestamp" :editable="false" style="width:100%" :class="{'is-error':valueError}"
                        :type="query.inputType"
        />
        <!--多选框-->
        <!-- <hos-checkbox-group v-model="query.value"
                           v-else-if="query.inputType === 'checkbox'">
          <hos-checkbox :label="choice" :disabled="queryBuilderObj.disabled"
                       v-for="choice in query.choices"
                       :key="choice"
                       :value="choice"></hos-checkbox>
        </hos-checkbox-group> -->
        <!--单选-->
        <!-- <hos-radio-group v-model="query.value"
                        v-else-if="query.inputType === 'radio'">
          <hos-radio :label="choice" :disabled="queryBuilderObj.disabled"
                    v-for="choice in query.choices"
                    :key="choice"
                    :value="choice"></hos-radio>
        </hos-radio-group> -->
        <hos-input v-else v-model="query.value" :disabled="queryBuilderObj.disabled" type="text" size="small"
                  :class="{'is-error':valueError}" clearable
        />
      </div>

      <div class="btn-group rule-actions" style="display: inline-block;">
        <hos-button :disabled="queryBuilderObj.disabled" type="primary" size="mini" :title="$t('添加同级条件')"
          icon="hos-icon-plus" circle @click="add" />
        <hos-button v-if="depth <= maxDepth" :disabled="queryBuilderObj.disabled" type="primary" size="mini" :title="$t('添加二级条件')"
          icon="hos-icom-batch-add" circle @click="addGroup" />
        <hos-button :disabled="queryBuilderObj.disabled" type="info" size="mini" icon="hos-icon-close" circle :title="$t('删除')" @click="remove" />

      </div>
    </div>
  </div>
</template>

<script>
import {getFullInputName_edc} from '@/utils/utils'
  export default {
    name: 'QueryBuilderRule',
    components: {

    },
    props: {
      inputSize: {
        type: String,
        default() {
          return 'small'
        }
      },
      query: {
        type: Object,
        default() {
          return {}
        }
      },
      index: {
        type: Number,
        default() {
          return null
        }
      },
      // 全部关系词类型
      rules: {
        type: Array,
        default() {
          return []
        }
      },
      maxDepth: {
        type: Number,
        default() {
          return 3
        }
      },
      depth: {
        type: Number,
        default() {
          return 0
        }
      }
    },
    inject: ['queryBuilderObj'],
    data() {
      // debugger
      return {
        // 当前查询条件的字段编码
        selectSearchItemFieldEName: '',
        fieldItemInfo: this.query.rule || this.rules[0],
        hasError: false,
        fieldError: false,
        relativeError: false,
        valueError: false,
        input_name: this.getFullInputName_edc(this.query.fieldItemInfo),
        queryOperatorOption: [{
              label: this.$t('等于'),
              value: 'eq'
            }, {
              label: this.$t('不等于'),
              value: 'ne'
            }, {
              label: this.$t('大于'),
              value: 'gt'
            }, {
              label: this.$t('小于'),
              value: 'lt'
            }, {
              label: this.$t('大于等于'),
              value: 'gte'
            }, {
              label: this.$t('小于等于'),
              value: 'lte'
            }, {
              label: this.$t('包含'),
              value: 'include'
            }]
      }
    },
    watch: {
      // 监听当前条件查询字段的变化
      selectSearchItemFieldEName() {
        this.ruleChange()
      },
      // 原来的代码只监听了字段名,如果表单不同但字段相同,不会触发watch,故修改
      // 且初始状态时,input_name值会出现 '-undefined'情况,修改其赋值方式
      'query.fieldItemInfo'(val) {
        this.fieldError = false
        this.relativeError = false
        this.valueError = false
        this.input_name = this.getFullInputName_edc(this.query.fieldItemInfo)
      },
      'query.value'() {
        this.valueError = false
      }
    },
    mounted() {
      this.initValue()
    },
    methods: {
      getFullInputName_edc,
      add() {
        this.$emit('child-add-requested', this.index)
      },
      remove() {
        this.$emit('child-deletion-requested', this.index)
      },
      addGroup() {
        this.$emit('group-add-requested', this.index)
      },
      initValue() {
        if (this.query.value === null) {
          const updated_query = this.deepClone(this.query)
          // 适配输入框类型的值
          // if (this.selectedRuleObj.inputType === "checkbox") {
          //   updated_query.value = [];
          // }
          // if (
          //   this.selectedRuleObj.inputType === "select" ||
          //   this.selectedRuleObj.inputType === "radio"
          // ) {
          //   updated_query.value = this.selectedRuleObj.choices[0];
          // }
          // if (
          //   this.selectedRuleObj.inputType === "time" ||
          //   this.selectedRuleObj.inputType === "date" ||
          //   this.selectedRuleObj.inputType === "datetime"
          // ) {
          //   updated_query.value = Math.round(new Date());
          // }
          this.$emit('update:query', this.deepClone(updated_query))
        }
      },
      // 打开查询项弹出框
      openSearchItem() {
        this.$emit('open-search-dialog', this.index)
      },
      // rulex修改
      ruleChange() {

      },
      valid() {
        const b1 = this.validField()
        const b2 = this.validRelative()
        const b3 = this.validValue()
        return b1 || b2 || b3
      },
      validField() {
        const curFieldInfo = this.query.fieldItemInfo

        if (!curFieldInfo || !curFieldInfo.fieldName) {
          this.fieldError = true
        } else {
          this.fieldError = false
        }

        return this.fieldError
      },
      validRelative() {
        const curOp = this.query.relative
        if (!curOp || curOp.length === 0) {
          this.relativeError = true
        } else {
          this.relativeError = false
        }

        return this.relativeError
      },
      validValue() {
        const curV = this.query.value

        if (this.isNull(curV)) {
          this.valueError = true
        } else {
          this.valueError = false
        }

        return this.valueError
      }
    }
  }

</script>
