.project-follow-up-calendar {
	.hos-calendar__header {
		display: none;
	}
	.hos-calendar__title {
		opacity: 0;
		display: none;
	}

	.custom-handler-line {
		height: 30px;
		.hos-date-editor {
			position: absolute;
			left:50%;
			transform: translateX(-50%);
		}
		.week-btn-group {
			position: absolute;
			right: 5px;
			z-index: 2;
			display: flex;
			justify-content: center;

			.btn-div {
				height:26px;
				padding: 0 10px;
				border:1px solid;
				border-color:#e2e2e2;
				margin-left: 10px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				&:hover {
					background:#eff9ff;
					color:#339eff;
				}
			}
		}
	}
	.visit-box {
		display: block;
		.status-visited,
		.status-todo,
		.status-over {
			height: 28px;
			box-sizing: border-box;
			display: block;
			margin-top: 4px;
			text-wrap: nowrap;
			border-radius:2px;
			font-size: 12px;
			padding-left: 4px;
			display: flex;
			align-items: center;
			text-align: left;
		}
		.status-visited {
			background:#DFF5D9;
			border-left: 3px solid #28ba04;
			
		}
		.status-todo {
			background:#D6ECFF;
			border-left: 3px solid #349eff;
		}
		.status-over {
			background:#FFDBDC;
			border-left: 3px solid #ff1315;
		}
	}

	.calender-container {
		.hos-calendar {
			.hos-calendar__body {
				padding-bottom: 20px;
				.is-selected {
					box-sizing: border-box;
					.hos-calendar-day {
						height: 128px !important;
					}
				}
				.hos-calendar-day {
					padding: 0 !important;
					height: 132px !important;
					.day-info {
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						box-sizing: border-box;
						width: 100%;
						height: 100%;
						margin: 0;
						padding: 5px 8px;

						.date-num {
							padding: 5px;
							align-self: end;
						}
					}
					.is-selected {
						background-color: #e6a23c;
						color: white;
					}
					.disabled-day {
						color: #c0c4cc;
						cursor: not-allowed;
					}
				}
			}
		}
	}
	.hos-calendar-day {
		padding: 0 !important;
	}
}
