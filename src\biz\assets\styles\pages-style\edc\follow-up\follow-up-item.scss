.follow-up-item {
	position: relative;
	overflow: visible;

	.lg-width {
		width: 120px;
	}

	.mid-width {
		width: 60px;
	}

	.xl-width {
		width: 145px;
	}

	.m-r-5 {
		margin-right: 5px;
	}

	.m-r-10 {
		margin-right: 10px;
	}

	.m-b-5 {
		margin-bottom: 5px;
		display: flex;
		align-items: center;
	}

	.m-b-10 {
		margin-bottom: 10px;
		display: flex;
		align-items: center;
	}

	.m-b-15 {
		margin-bottom: 15px;
	}

	.m-b-5 div {
		margin-bottom: 5px;
	}

	.f-l {
		float: left;
	}

	.disp-i-b {
		display: inline-block;
	}

	.disp-f {
		display: flex;
	}

	.p-l-260 {
		padding-left: 160px;
	}

	.bottom-form {
		margin-top: 10px;
	}

	.item-close {
		position: absolute;
		top: 0px;
		right: 20px;
		transform: translateY(-50%) translateX(100%);
		cursor: pointer;
		color: #fbc4c4;
		background-color: #fff;
		z-index: 5;
		font-size: 18px;
	}

	.item-close:hover {
		color: #f56c6c;
	}

	.item-plus {
		position: absolute;
		top: 0;
		right: 28px;
		transform: translateY(-50%) translateX(100%);
		cursor: pointer;
		color: #c2e7b0;
		background-color: #fff;
		z-index: 5;
		font-size: 18px;
	}

	.item-plus:hover {
		color: #67c23a;
	}

	.hos-divider {
		background-color: #ebeef5;
	}

	.item-rank {
		position: absolute;
		bottom: 8px;
		right: 20px;
		transform: translateY(-50%) translateX(100%);
		cursor: pointer;
		color: #b3d8ff;
		background-color: #fff;
		z-index: 5;
		font-size: 18px;
	}

	.item-rank:hover {
		color: #409eff;
	}

	.hos-form-item {
		margin-bottom: 10px;
	}

	.condition-tips-wrap {
		position: relative;
		.hos-tooltip {
			position: absolute;
			left: -15px;
		}
	}

	.rule-item-delete {
		margin-top: 3px;
		cursor: pointer;
		color: #339eff;
		background-color: #fff;
		font-size: 18px;
		margin-left: 10px;
	}
}
