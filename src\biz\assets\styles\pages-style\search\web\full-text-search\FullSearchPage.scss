.fullSearchPage {
  height: calc(100vh - 130px);

  .search-main {
    display: flex;
    height: calc(100% - 52px);
    overflow-y: auto;

    .search-content {
      margin: 0 !important;
    }

    .search-content-filter {
      width: 320px;
      // padding-top: 10px;
      // margin-left: 5px;
      background-color: #fff;
      border-radius: 5px;
      overflow-y: auto;
    }

    // @include res(lg) {
    //   .search-content-filter {
    //     width: 300px;
    //     overflow-x: hidden;
    //   }
    // }

    .error-box {
      color: red;
      text-align: center;

      margin-top: 40px;
    }

    .search-result {
      width: calc(100% - 320px);
      // min-width: 900px;
      // margin-left: 10px;
      padding: 10px;
      background-color: #fff;
      border-radius: 5px;
      overflow-y: auto;

      .search-result-head {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .search-address {
          // padding: 20px 20px 5px 20px;
          font-size: 14px;
          color: #999999;

          .result-type {
            float: right;

            .hos-radio {
              margin-right: 10px;
            }
          }
        }


        // .btn {
        //   height: 30px;
        // }
      }

      .page {
        // height: 60px;
        height: 35px;
        margin-top: 15px;
      }
    }
  }

  .input-with-select {
    .hos-input-group__prepend {
      height: 40px;
    }

    .hos-select .hos-input .hos-input__inner {
      width: 105px !important;
    }

    .hos-input__inner {
      width: 400px;
      height: 42px;
    }
  }

  .search-box {
    padding: 10px;
    background-color: #fff;
    margin-bottom: 10px;
    border-radius: 5px;
    position: relative;

    .search-group {
      margin: 0 auto;
      position: relative;
      display: inline-flex;

      .hos-input .hos-input__inner {
        line-height: 28px;
        padding-left: 10px !important;
        height: 30px !important;
        border: none !important;
        font-size: 16px !important;
      }

      .keyword-type,
      .search-input,
      .search-button {
        outline: none;
        height: 42px;
        line-height: 42px;
      }

      .filter-list {
        position: absolute;
        min-width: 108px;
        cursor: pointer;
        border: 1px solid #ddd;
        background-color: #fff;
        white-space: nowrap;
        -o-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        -ms-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        -moz-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
      }

      .filter-list ul {
        padding: 0;
      }

      .filter-list ul li {
        list-style-type: none;
        line-height: 32px;
        text-align: left;
        padding-left: 15px;
      }

      .filter-list ul li:hover {
        background-color: #f2f1fa;
      }

      .filter-list ul li.active {
        background-color: #409eff;
      }

      .search-input {
        width: 400px;
        border: 1px solid #409eff;
        border-right: none;
        border-left: none;
        box-sizing: border-box;
        // border-bottom-left-radius: 5px;
        // border-top-left-radius: 5px;
        -moz-box-sizing: border-box;
        /* Firefox */
        -webkit-box-sizing: border-box;
        /* Safari */
        font-size: 16px;
        color: #222;
        padding: 0 10px;
      }

      .search-button {
        width: 75px;
        // line-height: 40px;
        height: 42px;
        cursor: pointer;
        border-bottom-right-radius: 5px;
        border-top-right-radius: 5px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-right: none;
        color: #fff;
        background: none repeat scroll 0 0 #409eff;
        border: 0;
        padding: 0;
        vertical-align: baseline !important;
        text-align: center;
        vertical-align: middle;
        white-space: nowrap;
        // margin-bottom: 0;
        font-weight: 500;
        font-size: 16px;
        display: inline-block;
        position: relative;
      }
    }

    .search-history-text {
        margin-top: 20px;
        color: #999999;
        text-decoration: underline;
        cursor: pointer;
        position: absolute;
        left: 600px;
        top: 15px;
      }

    .keyword-type {
      color: #777;
      user-select: none;
      border: 1px solid #409eff;
      border-right: none;
      border-bottom-left-radius: 5px;
      border-top-left-radius: 5px;
      width: 100px;
      box-sizing: border-box;
      -moz-box-sizing: border-box;
      /* Firefox */
      -webkit-box-sizing: border-box;
      /* Safari */
      position: relative;

      .filter-select {
        cursor: pointer;
        text-align: center;
      }

      .filter-select:after {
        content: "";
        position: absolute;
        left: auto;
        top: 15px;
        right: 0;
        height: 40%;
        width: 1px;
        background-color: #999;
      }
    }
  }
}
