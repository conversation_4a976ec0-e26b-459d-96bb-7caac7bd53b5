.doubt-content {
	// padding: 0 20px;
	height: calc(100% - 10px);
	.hos-tabs {
    height: 100%;
    .hos-tabs__content {
        height: calc(100% - 34px) !important;
        .hos-tab-pane {
            height: calc(100% - 14px);
        }
    }
}
	.doubt-dout {
		height: 8px;
		width: 8px;
		border-radius: 5px;
		margin-right: 2px;
		background-color: red;
		display: inline-block;
		position: relative;
		top: -5px;
	}
	.readBtn {
		float: right;
		margin-left: 5px;
		padding: 4px 10px;
	}
	.readBtn:hover {
		color: #005bb6;
	}
	.label-tag {
		background-color: #ff0000;
		color: #fff;
		font-size: 12px;
		font-weight: bolder;
		border-radius: 13px;
		padding: 2px 6px;
		margin-left: 5px;
	}
	.label-name {
		font-size: 16px;
		display: inline-block;
		width: 200px;
		text-align: center;
	}
	.field-li {
		cursor: pointer;
		border-bottom: 1px solid #f0f0f0;
		&:last-child {
			border-bottom: 0;
		}
	}
	.field-item {
		padding: 10px;
	}
	.field-item2 {
		padding: 5px 10px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #f0f0f0;
		&:last-child {
			border-bottom: 0;
		}
	}
	.field-item:hover,
	.field-item2:hover {
		background-color: #e8f3ff;
	}
	.doubt-item {
		padding: 5px;
		margin-bottom: 5px;
		margin-right: 10px;
		border-bottom: 1px solid #f0f0f0;

		&:last-child {
			border-bottom: 0px solid #f0f0f0;
		}

		&:hover {
			cursor: pointer;
			background: rgb(235, 235, 235);
		}

		color: #6c6c6c;

		.form-key {
			color: #409eff;
		}

		.form-before {
			color: #999;
		}

		.form-after {
			// color: #ff8181;
			color: #67c23a;
		}
	}

	.m-r-5 {
		margin-right: 5px;
	}
}
.hos-drawer-record .hos-drawer {
	.hos-drawer__header{
		margin-bottom: 10px;
	}
}
