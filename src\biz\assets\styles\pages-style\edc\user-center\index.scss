.user-center-page {
  .group-title {
    font-size: 18px;
    margin: 18px 0 10px;
    color: #666;
  }

  .m-t-10 {
    margin-top: 10px;
  }

  .m-r-5 {
    margin-right: 5px;
  }

  .emptyData {
    text-align: center;
    margin-top: 50px;
  }

  .add-btn,
  .no-sub {
    text-align: center;
    cursor: pointer;
    color: #999;
  }

  .no-sub :hover {
    cursor: auto;
  }

  .add-btn :hover {
    background-color: rgba(242, 242, 242, 1);
  }

  .w-50 {
    width: 50%;
  }

  .position-r {
    position: relative;

    .tag-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      // display: flex;

      .info {
        font-size: 12px;
        font-weight: bold;
        margin: 0 10px;
        color: #666666;
      }
    }
  }

  .tag1 {
    width: 55px;
    text-align: center;
    border-radius: 0;
  }

  .tag2 {
    border-radius: 0;
  }
  .tag3 {
    // position: absolute;
    margin-left: 10px;
    margin-top: 2px;
  }
  .center-form {
    height: 30px;
    margin-top: 10px;
    position: relative;
    .item-right {
      .hos-form-item {
        margin: 0;
      }
    }

    .filter-span {
      color: #606266;
      i {
        margin-right: 5px;
      }
    }
  }
  .load-more {
    padding: 10px;
    margin: 10px;
    font-size: 16px;
    text-align: center;
    &.more {
      &:hover {
        cursor: pointer;
        background: #eee;
        .more-text {
          color: #409eff;
        }
      }
    }
    .no-more {
      color: #999;
    }
  }
}
