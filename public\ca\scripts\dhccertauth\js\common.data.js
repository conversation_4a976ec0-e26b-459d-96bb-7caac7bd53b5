﻿var ca_common_url = {
    "GET_SYSOPTION"             :"caConfig/getSysOption",
    "GET_ALLCAREPRVTYPE"        :"caConfig/getAllCarePrvType",
    "GET_ALLSIGNTYPE"           :"caConfig/getAllSignType",
    "GET_ALLAUTHTYPE"           :"caConfig/getAllAuthType",
    "GET_ALLVENDER"             :"caConfig/getAllVender",
    "SAVE_SYSOPTION"            :"caConfig/saveSysOption",

    "GET_VENDERSIGNTYPEISREG"   :"caConfig/getVenderSignTypeIsReg",
    "GET_VENDERSERVICELIST"     :"caConfig/getVenderServiceList",
    "SAVE_VENDERSERVICE"        :"caConfig/saveVenderService",
    "DELETE_VENDERSERVICE"      :"caConfig/deleteVenderService",

    "GET_CTLOCLIST"             :"caConfig/getCtLocList",
    "SET_DEPTCAOFF"             :"caConfig/setDeptOff",
    "SET_DEPTCAON"              :"caConfig/setDeptOn",

    "GET_ROLELIST"              :"caConfig/getRoleList",
    "SET_ROLECAOFF"             :"caConfig/setRoleOff",
    "SET_ROLECAON"              :"caConfig/setRoleOn",

    "GET_SIGNMODELLIST"         :"caConfig/getSignModeList",
    "GET_SIGNACTIONLIST"        :"caConfig/getSignActionList",
    "GET_SIGNACTIONCONFIGS"     :"caConfig/getSignActionConfigs",
    "SAVE_SIGNACTIONCONFIG"     :"caConfig/saveSignActionConfig",

    "GET_ALLCTLOC"              :"caConfig/getAllCtLoc",
    "GET_CERTLIST"              :"caConfig/getCertList",
    "SET_CERTUNBIND"            :"caConfig/setCertUnbind",

    "GET_USERLIST"              :"caConfig/getUserList",
    "GET_OFFCAUSERLIST"         :"caConfig/getOffCaUserList",
    "SET_USERCAOFF"             :"caConfig/setUserCaOff",
    "SET_USERCAON"              :"caConfig/setUserCaOn",
    "GET_CERTDETAIL"            :"caConfig/getCertDetail",
    "BIND_USERCERT"             :"caConfig/bindUserCert",

    "GET_DEFAULTORGID"          :"caConfig/getDefaultOrgId",
    "GET_ORGLIST"               :"caConfig/getOrgList",
}

var ca_common_tools = {
    getServerConfig: function() {
        //return "APP_START_MICROSERVICES";     //微服务
        return "APP_START_MONOMER";     //单体
    },
    getParams: function() {
        //获取url中的请求
        //var localurl = location.href; //获取当前完整的URL地址信息(包含http://，域名，路径，具体文件和全部传递参数)
        var url = location.search; //获取url中"?"符后的字串
        if (url.length > 0) { //判断是否携带参数
            var params = {};
            if (url.indexOf("?") != -1) {
                this.isShwoInput = true;
                var str = url.substr(url.indexOf("?") + 1);
                var strs = str.split("&");
                for (var i = 0; i < strs.length; i++) {
                    params[strs[i].split("=")[0]] = strs[i].split("=")[1];
                }
            }
            return params;
        } else {
            //$.messager.alert("提示", "URL未传递参数", "error");
            return {};
        }
    },
    getMWToken: function() {
        try {
            if (typeof(websys_getMWToken) != "undefined")
                return websys_getMWToken();
            return "";
        } catch(e) {
            return "";
        }
    },
    trans: function(value) {
        if (typeof $g == "function") {
            value = $g(value);
        }
        return value;
    },
    getSession: function() {
        if (websys_getSession()) {
            var session = websys_getSession();
            return {
                UserID: session.userId || "",
                RoleID: session.roleId || "",
                //HospID: session.hospId || "",
                CTLocID: session.ctLocId || "",
                LangID: session.langId || ""
            };
        }
        return {};
    },
    //获取通用请求路径
    getAppPath: function (action) {
        var appPatch = "";
        var menu = websys_getMenuWin();
        if (typeof menu=="object"&&menu) {
            if (this.getServerConfig() == "APP_START_MONOMER"){
                appPatch = menu.baseAppPath + ca_common_url[action];
            } else {
                appPatch = "http://localhost:20004/hosca" + ca_common_url[action];     //todo:后期要考虑微服务时读取配置或者其他处理方式
                //appPatch = menu.baseAppPath + "hosca" + ca_common_url[action];
            }
            ///appPatch = menu.baseAppPath + "hosca/caconfig/interface"
            ///appPatch = menu.caApiPath + "hosca/caconfig/interface"
            //appPatch = "http://localhost:8090/his/" + "hosca/caconfig/interface"
        }
        return appPatch;
    }
}

// 自行实现获取组织机构下拉框方法  copyfrom基础平台
// opt 拓展配置数据，暂时不需要传
// paramObj json形式传入语言id，组织机构id等
function genOrgComp(paramObj,opt){
    var langID = (paramObj && paramObj['langID']) || "";
	var defaultOpt = { width: 350 };
	opt = $.extend(defaultOpt,opt||{});
	if ($("#_OrgList").length==0){
		$("<input id=\"_OrgList\" class=\"textbox\"/>").prependTo("body");
	}
	if ($("#_OrgListLabel").length==0){
		$("<label id='_OrgListLabel' style='color:red;margin:0 10px 0 10px' class='r-label'>"+"组织机构"+"</label>").prependTo("body");
	}

    var defOrgID = parent.indexDefOrgID || "";
    if (defOrgID == "") {
        var data = {
            action: "GET_DEFAULTORGID",
            params: {
                langID: langID
            }
        };
        var json = ajaxPOSTCommon(data,"",false);
        if (json.code == 200) {
            defOrgID = json.data.defaultOrgID || "";
        }
    }

    var param = {
        action: "GET_ORGLIST",
        params: {
            langID: langID
        }
    };
	var obj = $HUI.combogrid('#_OrgList',{
		delay: 500,
		blurValidValue:true,
		panelWidth:350,
		width:opt.width,
		editable:false,
		pagination:true,
		minQueryLen:1,
		value:defOrgID,
		isCombo:true,
		showPageList:false,
		showRefresh:false,
		displayMsg:"当前:{from}~{to},共{total}条",
        onBeforeLoad:function(param){
			param = $.extend(param,{desc:$("#_OrgList").lookup("getText")});
			return true;
		},
		queryParams:param,
		url: ca_common_tools.getAppPath("GET_ORGLIST"),
		idField: 'orgID',
		textField: 'orgDesc',
		columns: [[
			{field:"orgID",title:"组织机构id",align:"left",hidden:true,width:100},
			{field:"orgDesc",title:"组织机构名称",align:"left",width:300}
		]]
	});
	return obj;
}

// 前后台交互POST/GET方法
// data 命令数据json对象，例：{"action":"GET_ALLCAREPRVTYPE","params":{"organizationID":"2","langID":"20"}}
// successFunc 成功后回调函数
// errorFunc 失败后回调函数 -- 暂不增加，在此方法内处理错误，不再继续向外部传递
// async 异步true，同步false
function ajaxPOSTCommon(data, successFunc, async) {
    //同步请求时需将回调函数设为false;
    successFunc = async == false ? false : successFunc;
    
    //获取路径
    var baseURL = ca_common_tools.getAppPath(data.action);

    //修改9999的报错提示信息，9999为程序运行bug，不提示用户具体信息
    var callFunction = false;
    if (typeof(successFunc) == "function") {
        var callFunction = function(ret) {
            if (ret.code == "9999")
                ret.msg = "程序运行异常，请联系系统管理员排查问题。";
            successFunc(ret);
        }
    }

    var errData = "";
    var result = $ipost(baseURL , data, callFunction,function (e) {
        //请求失败
        console.log("post请求后台接口失败，action:" + data.action);
        //$.messager.alert("提示", "post请求后台接口失败，action:" + data.action, "error");
        errData = {"code":e.status, "msg":data.action + ":" + e.statusText};
    });
    if (!async) {
        if (errData != "")
            return errData;

        //修改9999的报错提示信息，9999为程序运行bug，不提示用户具体信息
        if (result.code == "9999")
            result.msg = "程序运行异常，请联系系统管理员排查问题。";
        return result;
    }
}

function ajaxGETCommon(data, successFunc, async) {
    //同步请求时需将回调函数设为false;
    successFunc = async == false ? false : successFunc;

    //获取路径
    var baseURL = ca_common_tools.getAppPath(data.action);

    //修改9999的报错提示信息，9999为程序运行bug，不提示用户具体信息
    var callFunction = false;
    if (typeof(successFunc) == "function") {
        var callFunction = function(ret) {
            if (ret.code == "9999")
                ret.msg = "程序运行异常，请联系系统管理员排查问题。";
            successFunc(ret);
        }
    }

    var errData = "";
    var result = $iget(baseURL , data, callFunction,function () {
        //请求失败
        console.log("get请求后台接口失败，action:" + data.action);
        //$.messager.alert("提示", "get请求后台接口失败，action:" + data.action, "error");
        errData = {"code":e.status, "msg":data.action + ":" + e.statusText};
    });
    if (!async) {
        if (errData != "")
            return errData;

        //修改9999的报错提示信息，9999为程序运行bug，不提示用户具体信息
        if (result.code == "9999")
            result.msg = "程序运行异常，请联系系统管理员排查问题。";
        return result;
    }
}

//combox初始化数据公用方法
function initCombobox(id,data,isMultiple,isEditable,fieId,fieText) {
    fieId = fieId || "ID";
    fieText = fieText || "Text";
    var panelHeight = 350;
    if (fieId == "ID") panelHeight = 70;
    $("#"+id).combobox({
        data:data,
        valueField:fieId,
        textField:fieText,
        width:160,
        panelWidth:200,
        panelHeight:panelHeight,
        multiple:isMultiple,
        selectOnNavigation:false,
        rowStyle:(isMultiple == true)?"checkbox":"",   //"checkbox", //显示成勾选行形式
        editable:(isEditable == true),
        onSelect:function(data) {
            //签名方式变化时，重新设置拓展项是否可选
            if ((data || "" != "")&&(typeof(data.signTypeCode) != "undefined"))
                initOptionValueStatus();
        }
    });

    if ((isEditable)){
        $("#"+id).combobox({
            filter: function(q, row){
                return ((row[fieId].toUpperCase().indexOf(q.toUpperCase()) >= 0)||(row[fieText].toUpperCase().indexOf(q.toUpperCase()) >= 0));
            }
        });
    }
}

/// 创建HISUI-Dialog弹窗
function createModalDialog(dialogId, dialogTitle, width, height, iframeId, iframeContent,callback,arr,maximi,minimi) {
    $("body").append("<div id='"+dialogId+"'</div>");
    if (isNaN(width)) width = 800;
    if (isNaN(height)) height = 500;
    if (maximi == undefined) maximi = false;
    if (minimi == undefined) minimi = false;
    $HUI.dialog("#"+dialogId,{
        title: dialogTitle,
        width: width,
        height: height,
        cache: false,
        collapsible: false,
        minimizable:minimi,
        maximizable:maximi,
        resizable: false,
        modal: true,
        closed: false,
        closable: true,
        isTopZindex: true,
        content: iframeContent,
        onBeforeClose: function() {
            var tempFrame = $("#"+iframeId)[0].contentWindow;
            if (tempFrame.dialogBeforeClose) {
                tempFrame.dialogBeforeClose();
            }
            if (tempFrame && tempFrame.returnValue) {
                returnValue = tempFrame.returnValue;
                if ((returnValue !== "") &&(typeof(callback) === "function"))
                {
                    callback(returnValue,arr);
                }
            }
        },
        onClose: function() {
            //$("#modalIframe").hide();
            $("#"+dialogId).dialog("destroy");
        }
    });
}
//关闭dialog,子页面调用
function closeDialog(dialogId) {
    $HUI.dialog("#"+dialogId).close();
}

//将日期格式转为年-月-日格式
function dateFormat(date) {
    var fmatdate = date;
    if (date == "" || date == undefined) return fmatdate;
    if (typeof(dtformat) != "undefined") {
        if (dtformat == "DMY") {
            var tmparr = date.split("/");
            fmatdate = tmparr[2]+"-"+tmparr[1]+"-"+tmparr[0];
        } else if (dtformat == "MDY") {
            var tmparr = date.split("/");
            fmatdate = tmparr[2]+"-"+tmparr[0]+"-"+tmparr[1];
        }
    }
    return fmatdate
}

//获取日期间隔(几天)
function getDateGap(startDate, endDate) {
    var startDate1 = dateFormat(startDate);
    var endDate1 = dateFormat(endDate);
    var date1 = new Date(startDate1);
    var date2 = new Date(endDate1);
    var date3 = date2.getTime()-date1.getTime();
    var days = Math.floor(date3/(24*3600*1000));
    return days;
}

//获取几天前的日期
function getStartDate(endDate, gapDays) {
    var endDate1 = dateFormat(endDate);
    var d = new Date(endDate1);
    var year = d.getFullYear();
    var mon = d.getMonth() + 1;
    var day = d.getDate();
    if (day <= gapDays) {
        if (mon > 1) {
            mon = mon - 1;
        } else {
            year = year - 1;
            mon = 12;
        }
    }
    d.setDate(d.getDate() - gapDays);
    year = d.getFullYear();
    mon = d.getMonth() + 1;
    day = d.getDate();
    s = year + "-" + (mon < 10 ? ("0" + mon) : mon) + "-" + (day < 10 ? ("0" + day) : day);
    return s;
}

//获取当前日期时间 格式 yyyy-MM-dd HH:MM:SS
function getCurrentTime() {
    var date = new Date();
    var seperator = ":";
    
    var currentdate = date.getHours() + seperator + date.getMinutes() + seperator + date.getSeconds();
    return currentdate;
}

function getTime() {
    var date = new Date();
    return date.getTime();
}