.ExportFilterDialog {
  .top-search {
    display: flex;
    align-items: center;
    margin: 20px 0;
    justify-content: center;
  }
  .search-item-input {
    width: 350px;
    // margin-left: 200px;
    // margin-right: 50px;
  }
  .tips {
    font-size: 12px;
  }
  .item-container {
    padding: 20px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
  .line-item-box {
    position: relative;
    margin-bottom: 10px;
    margin-right: 10px;
    // line-height: 15px;
    display: flex;
    align-items: center;
    &.active .line-item {
      background-color: #409eff;
    }
    .line-item-close {
      position: absolute;
      top: -5px;
      right: -5px;
      border-radius: 50%;
      background: #909399;
      color: #fff;
      padding: 2px;
      font-size: 12px;
      cursor: pointer;
      z-index: 2;
    }
    &.active .line-item-close {
      background: #409eff;
    }
    .line-item {
      background-color: #909399;
      color: #fff;
      border: 0;
      padding: 3px 8px;
      border-radius: 2px;
      font-size: 14px;
      line-height: 16px;
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      cursor: pointer;
      &:hover {
        background-color: #53b0f4;
      }
    }
    .line-item-select {
      .hos-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
}
