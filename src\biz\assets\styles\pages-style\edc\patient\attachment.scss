.mod-pat__attachment {
    $border: 1px solid #e4e7ed;
    $fullHeight: calc(100vh - 190px);

    .att-row {
        height: $fullHeight;
        width: calc(100% - 20px);
        margin-left: 5px !important;
    }

    .left-container {
        height: $fullHeight;

        .tips-dev {
            color: rgb(153, 153, 153);
            height: 100%;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            padding-bottom: 80px;
            border: 1px solid #E2E2E2;
        }

        .left-inner-container {
            height: 100%;
            position: relative;
            overflow-y: auto;

            .page {
                position: absolute;
                bottom: 10px;
            }
        }
    }

    .right-container {
        height: 100%;

        .right-inner-container {
            height: 100%;
            border-radius: 5px;
        }
    }
}