.AdvancedResultList {
  .pointer {
    cursor: pointer;
  }

  .page {
    height: 60px;
    margin-top: 15px;
  }

  .result-header {
    height: 30px;
    line-height: 30px;
    // padding: 5px 10px;
    background-color: #fff;
    color: #999999;

    .sort-list {
      float: right;
      font-size: 14px;
      font-weight: 400;

      .hos-button {
        color: #606266;
      }
    }
  }

  .block {
    background-color: #fff;
    margin-bottom: 30px;
    margin-top: 20px;
    margin-left: 15px;
  }

  .result-list {
    background-color: #fff;
    padding-left: 5px;
  }

  .result-item {
    position: relative;
    padding-top: 5px;

    .check-box {
      position: absolute;
      left: 10px;
      top: 20px;
      width: 20px;
      z-index: 99;
    }

    .item-box {
      border-bottom: 1px dashed #e4e4e4;
      position: relative;
      *zoom: 1;
      // padding: 0 0 0 10px;

      .title {
        font-size: 18px;
        font-weight: normal;
        color: #444;
        line-height: 30px;
        z-index: 9;
        // padding-left: 24px;
        padding-right: 10px;
        position: relative;
        font-weight: bold;
        display: block;

        a {
          color: #0a86e2;
          text-decoration: none;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-inline-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;

          .i-span {
            padding: 8px 0;
            margin-right: 10px;
            font-size: 16px;
          }

          .pointer {
            font-size: 16px;
          }
        }

        .statis-info {
          // width: 200px;
          text-align: right;
          color: #0a86e2;
          font-size: 18px;
          line-height: 30px;
          float: right;
        }
      }

      .is-hit > .i-span {
        color: #a7701e;
      }

      .child-item-abstarct {
        position: relative;
        color: #666666;
        font-size: 14px;
        margin-bottom: 8px;
        max-height: 48px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        // padding-left: 24px;
        padding-right: 10px;

        .i-span {
          padding: 8px 0;
          margin-right: 10px;
        }

        .adm-type {
          padding: 1px 5px 0 5px;
          border-radius: 2px;
          line-height: 20px;
          display: inline-block;
        }

        .adm-type-i {
          color: #fff;
          background-color: #459acb;
        }

        .adm-type-o {
          color: #fff;
          background-color: #02d0ca;
        }

        .adm-type-e {
          color: #fff;
          background-color: #ff8f97;
        }

        .adm-type-h {
          color: #fff;
          background-color: #9798ca;
        }
      }
    }
  }
}
