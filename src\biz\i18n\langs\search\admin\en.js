const t = {}

t.common = {}
t.common.belongMenu = 'Menu'
t.common.enableStatus = 'Enable Status'
t.common.menuCode = 'Menu Code'
t.common.menuName = 'Menu Name'
t.common.btnCode = 'Button Code'
t.common.btnName = 'Button Name'
t.common.sort = 'Sort'
t.common.creator = 'Creator'
t.common.createTime = 'Creation time'
t.common.modifier = 'Modified By'
t.common.modifyTime = 'Modification time'
t.common.modelName = 'Model name'
t.common.fieldCode = 'Field encoding'
t.common.fieldDataType = 'Field Data Type'
t.common.isAnalyzer = 'Partitioning'
t.common.yes = 'Yes'
t.common.no = 'No'
t.common.count = 'Number'
t.common.fieldName = 'Field name'
t.common.notDesensitized = 'Not desensitized'
t.common.nameDesensitized = 'Name desensitization'
t.common.idDesensitized = 'ID desensitization'
t.common.all = 'All'
t.common.maximum = 'Maximum'
t.common.minimum = 'Minimum'
t.common.firstTime = 'First'
t.common.lastTime = 'Last'
t.common.comparison = 'comparison operators'
t.common.equal = 'Equal'
t.common.contain = 'Contain(Like)'
t.common.containLike = 'Contain'
t.common.regexp = 'Regexp'
t.common.greaterThan = 'Greater than'
t.common.lessThan = 'Less than'
t.common.greaterEqual = 'Greater than or equal'
t.common.lessEqual = 'Less than or equal'
t.common.notEqual = 'Not equal to'
t.common.notContain = 'Not contain'
t.common.semanticInclusion = 'Semantic Inclusion'
t.common.multiple = 'Multivalued accurately matching(Any)'
t.common.multipleParentAll = 'Multivalued accurately matching(Including both)'
t.common.multipleLike = 'Multivalued likely matching(Any)'
t.common.multipleParentAllLike = 'Multivalued likely matching(Including both)'
t.common.null = 'Null'
t.common.isNull = 'Not null'
t.common.enable = 'Enable or not'
t.common.welcome = 'Welcome to the exploration system management backend~'

t.common.save = 'Save'
t.common.close = 'Close'
t.common.clearCache = 'Clear Cache'
t.common.add = 'Add'
t.common.submit = 'Submit'
t.common.confirm = 'Confirm'
t.common.cancel = 'Cancel'
t.common.handle = 'Operate'
t.common.edit = 'Edit'
t.common.delete = 'Delete'
t.common.query = 'Query'
t.common.clear = 'Clear'
t.common.authBatch = 'Batch authorization'
t.common.oneKeyAuth = 'All authorization'
t.common.cancelAuthBatch = 'Cancel authorization'
t.common.cancelAuthAll = 'Cancel All'
t.common.searchAuth = 'Query Authorization'
t.common.exportAuth = 'Export Authorization'

// 路由
t.route = {}
t.route.home = 'Home'
t.route.ESSupport = 'ES Support'
t.route.indexManage = 'Index'
t.route.viewData = 'View Data'
t.route.dslSearch = 'DSL'
t.route.modelManage = 'Model'
t.route.modelList = 'Model List'
t.route.tableManage = 'Table'
t.route.fieldManage = 'Field'
t.route.fieldRelation = 'Field Relation'
t.route.exportConfig = 'Export Config'
t.route.exportModelConfig = 'Export Model'
t.route.authorManage = 'Authority'
t.route.paramsConfig = 'Search Params'
t.route.caseDetailManage = 'Case Detail Manage'
t.route.dictManage = 'Dictionary'
t.route.tag = 'Tag'
t.route.dslConfig = 'DSL Search'
t.route.terminologyManage = 'Term'
t.route.cache = 'Cache'

// 提示消息
t.msg = {}
t.msg.needEnableStatus = 'Enabling status is required'
t.msg.remind = 'Prompt'
t.msg.updateSuccess = 'Update success'
t.msg.cancelUpdate = 'Cancel update'
t.msg.deleteSuccess = 'Successfully deleted'
t.msg.cancelDelete = 'Cancel delete'
t.msg.nothing = 'There is currently no data available'
t.msg.selectPlease = 'Please select'
t.msg.inputEsIndexName = 'Please enter the ES index name'
t.msg.limitSpecialChar = 'Special symbols such as spaces are not allowed to be entered'
t.msg.inputAliaName = 'Please enter ES index alias'
t.msg.inputReplaceNum = 'Please enter the number of copies'
t.msg.inputShardNumber = 'Please enter the number of shards'
t.msg.saveSuccess = 'Successfully saved!'
t.msg.inputFieldName = 'Please enter a field name'
t.msg.cancelHandle = 'Please enter a field name'
t.msg.askUpdate = 'Are you sure to modify it'
t.msg.uploadSuccess = 'Upload successful'
t.msg.uploadFail = 'Upload failed. Please try again later'

// 限制提示
t.limit = {}
t.limit.less16 = 'No more than 16 characters'
t.limit.less30 = 'No more than 30 characters'
t.limit.less32 = 'No more than 32 characters'
t.limit.less60 = 'No more than 60 characters'
t.limit.less64 = 'No more than 64 characters'
t.limit.less100 = 'No more than 100 characters'
t.limit.less128 = 'No more than 128 characters'
t.limit.less200 = 'No more than 200 characters'
t.limit.less512 = 'No more than 512 characters'

t.limit.lessNumber99 = 'No more than 99'

// 权限管理
t.authority = {}
t.authority.title = 'Rights Management'
t.authority.needTargetMenu = 'Target menu is required'
t.authority.needMenuName = 'Target menu name is required'
t.authority.needBtnName = 'Target button name is required'
t.authority.assignPermission = 'Assign permissions'
t.authority.assignForRole = 'Assign permissions to roles'

// 角色管理
t.roleManage = {}
t.roleManage.title = 'Roles'
t.roleManage.roleCode = 'Role Code'
t.roleManage.needRoleCode = 'Role code is required'
t.roleManage.roleName = 'Name'
t.roleManage.needRoleName = 'Role name is required'
t.roleManage.assignForUser = 'Assign roles to users'

// 用户管理
t.userManage = {}
t.userManage.title = 'User'
t.userManage.assignRole = 'Assigning roles'
t.userManage.loginName = 'Login Name'
t.userManage.needLoginName = 'Login name is required'
t.userManage.realName = 'Name'
t.userManage.needRealName = 'Real name required'
t.userManage.tel = 'Phone'
t.userManage.pwd = 'Password'
t.userManage.needPwd = 'Password required'
t.userManage.email = 'Email'
t.userManage.remark = 'Remark'
t.userManage.lastLoginTime = 'Last login time'
t.userManage.lastLoginIP = 'Last login IP'
t.userManage.pwdRemind = 'Initial password: 123456, please log in and modify it in a timely manner'
t.userManage.reFillLoginName = 'Login name already exists, please re-enter!'
t.userManage.handleFail = 'Operation failed!'

// DSL查询
t.dslConfig = {}
t.dslConfig.title = 'DSL Query'
t.dslConfig.conditionDesc = 'Condition description'
t.dslConfig.keyword = 'Keywords'
t.dslConfig.addDSLSearch = 'Add DSL query'
t.dslConfig.updateDSLSearch = 'Modify DSL Query'
t.dslConfig.searchName = 'Query Name'
t.dslConfig.needSearchName = 'Query name cannot be empty'
t.dslConfig.dslQuery = 'DSL statement'
t.dslConfig.needDslQuery = 'DSL statement cannot be empty'

// ES运维
t.ESSupport = {}
t.ESSupport.queryMethod = 'Request Method'
t.ESSupport.esIndex = 'ES index'
t.ESSupport.indexMethod = 'Index method'
t.ESSupport.resetJson = 'Reset JSON'
t.ESSupport.checkJson = 'Validate JSON'
t.ESSupport.checkIndexMethod = 'Please check the ES indexing method'
t.ESSupport.checkSuccess = 'Verification successful'
t.ESSupport.checkJsonFormat = 'Please check the JSON format'
t.ESSupport.updateDataCaution1 = 'This operation will update the values in the database. Do you want to continue?'
t.ESSupport.getTemplateErr = 'Error obtaining template'
t.ESSupport.runDslErr = 'Error executing dsl'
t.ESSupport.createIndex = 'Creating an index'
t.ESSupport.headPlugin = 'Head plugin'
t.ESSupport.sensePlugin = 'Sense plugin'
t.ESSupport.indexModelMap = 'View Index Model Mapping'
t.ESSupport.viewIndexModelMap = 'View Index Model Mapping'
t.ESSupport.createReIndexTask = 'Create Reindex Task'
t.ESSupport.viewReIndexTaskList = 'View Reindex Task List'
t.ESSupport.addTable = 'Add Data Table'
t.ESSupport.addField = 'Add Fields'
t.ESSupport.viewSetting = 'View settings'
t.ESSupport.viewMapping = 'View mapping'
t.ESSupport.indexName = 'Index name'
t.ESSupport.updateIndexModelInfo = 'Modifying Index Model Information'
t.ESSupport.mainPieceNum = 'Number of main shards'
t.ESSupport.replicas = 'Number of copies'
t.ESSupport.countDocs = 'Number of documents'
t.ESSupport.deletedDocs = 'Number of deleted documents'
t.ESSupport.totalStoreSize = 'Storage size'
t.ESSupport.primaryStoreSize = 'Main shard storage size'
t.ESSupport.health = 'Index Status'
t.ESSupport.caution1 = 'Are you sure you want to unbind the index? This will result in the index not being able to obtain the corresponding model information'
t.ESSupport.caution2 = 'Are you sure you want to perform index editing? This may result in the index being unable to obtain corresponding model information'
t.ESSupport.claim = 'Illustrate'
t.ESSupport.caution3 = 'Tables and fields must be maintained in the model before selection can be made'
t.ESSupport.selectTable = 'Select Data Table'
t.ESSupport.selectField = 'Select Field'
t.ESSupport.caution4 = 'ElasticSearch cannot be modified or deleted after adding a table. Please confirm if the field name is correct!'
t.ESSupport.caution5 = 'There are currently no available models available. Please create a model first'
t.ESSupport.selectDataModel = 'Select Data Model'
t.ESSupport.esIndexName = 'ES Index Name'
t.ESSupport.aliaName = 'ES index alias'
t.ESSupport.shardNumber = 'Number of shards'
t.ESSupport.toCreate = 'To create'
t.ESSupport.viewConfig = 'View Configuration'
t.ESSupport.viewMapping = 'View Map'
t.ESSupport.caution6 = 'The minimum number of copies is 0, and the maximum is 2'
t.ESSupport.caution7 = 'The minimum number of shards is 1, and the maximum number is 21'
t.ESSupport.attention = '*Attention'
t.ESSupport.caution8 = 'Tables must be maintained in the model before selection can be made'
t.ESSupport.selectTablePlease = 'Please select a table'
t.ESSupport.caution9 = 'ElasticSearch cannot be modified or deleted after adding a table. Please confirm if the table name is correct！'
t.ESSupport.reIndexList = 'Reindex List'
t.ESSupport.viewDetail = 'Detail'
t.ESSupport.stopTask = 'Terminate Task'
t.ESSupport.inputFieldCode = 'Please enter the field code'
t.ESSupport.selectFieldType = 'Please select a field type'
t.ESSupport.selectIsAnalyzer = 'Please choose whether to break the word'
t.ESSupport.taskDDocTotal = 'Total number of task documents'
t.ESSupport.refreshDocCnt = "Update the number of document entries"
t.ESSupport.addDocCnt = "Number of newly added documents"
t.ESSupport.deleteDocCnt = "Number of deleted documents"
t.ESSupport.esTaskStartTime = "Elastic Task Start Time"
t.ESSupport.esTaskRunTime = "Elastic task runtime"
t.ESSupport.esTaskFinishFlag = "Elastic task execution completion identifier"
t.ESSupport.sourceDataIndex = 'Source Data Index'
t.ESSupport.targetIndex = 'Target Index'
t.ESSupport.theme = 'Table'
t.ESSupport.toAdmList = 'To the visit list'
t.ESSupport.regno = 'Registration no'
t.ESSupport.inputRegno = 'Please enter the registration number'
t.ESSupport.admNo = 'Visit number'
t.ESSupport.inputAdmNo = 'Please enter the visit number'
t.ESSupport.claim2 = 'Please enter the visit number description: The purpose of the current function is to understand the data situation, and only some patient data can be viewed at most. If you need to view all data, the current function is currently not supported'

// 模型管理
t.modelManage = {}
t.modelManage.admDate = 'Visit Date Field'
t.modelManage.admDateTime = 'Visit Date Time Period'
t.modelManage.startDate = 'Start date'
t.modelManage.endDate = 'End date'
t.modelManage.admDepart = 'Visiting department fields'
t.modelManage.admDepartName = 'Name of visiting department'
t.modelManage.enterConfirm = 'Please enter the text and press<Enter>to confirm'
t.modelManage.selectField = 'Please select a field first'
t.modelManage.askAuth1 = 'This operation will authorize all fields. Do you want to continue?'
t.modelManage.authSuccess = 'All authorizations successful'
t.modelManage.askCancelAuth = 'Are you sure you want to cancel the authorization in bulk?'
t.modelManage.askCancelAuth2 = 'This operation will cancel all authorized tables. Do you want to continue？'
t.modelManage.allCancelSuccess = 'All cancelled successfully'
t.modelManage.tableName = 'Table Name'
t.modelManage.inputTableName = 'Please enter a table name'
t.modelManage.tableCode = 'Table encoding'
t.modelManage.inputTableCode = 'Please enter the table code'
t.modelManage.selectTable = 'Please select a table first'
t.modelManage.askAuth2 = 'This operation will authorize all tables. Do you want to continue?'
t.modelManage.tableLevelAuth = 'Table level authorization'
t.modelManage.fieldLevelAuth = 'Field level authorization'
t.modelManage.dataRangeAuth = 'Data scope authorization'
t.modelManage.selectRoleModel1st = 'Please select a model and role first'
t.modelManage.dictCateName = 'Dictionary classification name:'
t.modelManage.dictCateCode = 'Dictionary classification coding:'
t.modelManage.isFuzzyMatch = 'Fuzzy matching or not:'
t.modelManage.selectDictFather = 'Please select a dictionary parent class'
t.modelManage.inputName = 'Please fill in the name'
t.modelManage.inputCode = 'Please fill in the code'
t.modelManage.inputSort = 'Please fill in the sorting'
t.modelManage.inputNumber = 'Please enter a positive integer.'
t.modelManage.selectIsFuzzyMatch = 'Please choose whether to support fuzzy matching'
t.modelManage.dictCateInfo = 'Dictionary classification information'
t.modelManage.dictName = 'Dictionary Name'
t.modelManage.dictCode = 'Dictionary coding'
t.modelManage.selectDictCate1st = 'Please select the left dictionary classification first'
t.modelManage.caution1 = 'After deletion, the fields that have already been bound to the deletion item dictionary will not be able to match with dictionary items. After deletion, the dictionary needs to be rebound. Are you sure you want to perform the deletion operation?'
t.modelManage.caution2 = 'Please select the dictionary classification to delete first'
t.modelManage.dictItemManage = 'Dictionary item management'
t.modelManage.name = 'Name'
t.modelManage.needDictItemName = 'Dictionary item name is required'
t.modelManage.itemCode = 'Item encoding'
t.modelManage.needItemCode = 'Dictionary item code is required'
t.modelManage.pyCapital = 'Initial Pinyin'
t.modelManage.dictCateManage = 'Dictionary classification management'
t.modelManage.syncDict = ' Synchronize Dictionary'
t.modelManage.addDictCate1st = 'Please add a new dictionary classification first'
t.modelManage.dictCate = 'Dictionary classification'
t.modelManage.paramsConfig = 'Configuration'
t.modelManage.configName = 'Parameter'
t.modelManage.configNameRequired = 'Parameter name is required'
t.modelManage.configKey = 'Parameter coding'
t.modelManage.configKeyRequired = 'Parameter code is required'
t.modelManage.configDesc = 'Parameter description'
t.modelManage.configVal = 'Parameter value'
t.modelManage.configValRequired = 'Parameter value is required'

// 导出配置
t.exportConfig = {}
t.exportConfig.relateField = 'Associated Fields'
t.exportConfig.isDefault = 'Default or not'
t.exportConfig.selectRelateField = 'Please select the associated field'
t.exportConfig.filterPropConfig = 'Filter Item Attribute Configuration'
t.exportConfig.selectTheme = 'Please select a theme'
t.exportConfig.filterProp = 'Filter Properties'
t.exportConfig.needFilterProp = 'Filter attribute is required'
t.exportConfig.filterCode = 'Filter encoding'
t.exportConfig.needFilterCode = 'Filter code is required'
t.exportConfig.typeCode = 'Type code'
t.exportConfig.exportTypeCode = 'Export Type Encoding'
t.exportConfig.modelDesc = 'Model description'
t.exportConfig.inputModelName = 'Please enter a model name'
t.exportConfig.inputExportTypeCode = 'Please enter the export type code'
t.exportConfig.inputModelDesc = 'Please enter a model description'
t.exportConfig.exportSpecialVal = 'Export Special Values'
t.exportConfig.exportSpecialValName = 'Export Special Value Names'
t.exportConfig.relateFieldName = 'Associated Field Name'
t.exportConfig.comparisonValue = 'Comparison value'
t.exportConfig.expression = 'Expression'
t.exportConfig.timeField = 'Time field'
t.exportConfig.conditionField = 'Condition field'
t.exportConfig.periodDesc = 'Stage Description'
t.exportConfig.selectLeftTreeNode = 'Please select the left tree node first!'
t.exportConfig.obPeriodDesc = 'Observation Phase Description'
t.exportConfig.inputObPeriodDesc = 'Please enter a description of the observation stage'
t.exportConfig.caution1 = 'The comparison field of the observation stage time range, for example, if exporting data within 24 hours after admission, the time field can be set to "Admission start time"'
t.exportConfig.caution2 = 'The comparison field of observation stage events, for example, if exporting data after taking compound licorice tablets, the condition field can be set to "drug common name"'
t.exportConfig.inputRelateField = 'Please enter the associated field'
t.exportConfig.inputSpecialValName = 'Please enter the export special value name'
t.exportConfig.inputComparison = 'Please enter a comparison character'
t.exportConfig.obPeriodConfig = 'Observation stage configuration'
t.exportConfig.obFieldConfig = 'Observation Field Configuration'
t.exportConfig.exportFilterConfig = 'Export item filtering configuration'
t.exportConfig.exportSpecialValConf = 'Export item filtering configuration'
t.exportConfig.tabClaim = 'Tab Description'
t.exportConfig.obPeriodConfClaim = 'Observation Phase Configuration Description'
t.exportConfig.claim1 = 'Configure conditional filtering fields and time filtering fields for exporting observation stages'
t.exportConfig.obFieldConfConfClaim = 'Observation Field Configuration Description'
t.exportConfig.claim2 = 'Configure the fields related to the exported observation phase for filtering as time ranges'
t.exportConfig.specialValConfConfClaim = 'Special value configuration instructions'
t.exportConfig.claim3 = 'Maintenance of special value options for related topics during the configuration export phase'

// 字段管理
t.fieldManage = {}
t.fieldManage.themeId = 'Subject ID'
t.fieldManage.indexId = 'Index ID'
t.fieldManage.enUSCode = 'Field encoding'
t.fieldManage.tmType = 'Desensitization type'
t.fieldManage.tmTypeCode = 'Desensitization type code'
t.fieldManage.exportable = 'Exportable'
t.fieldManage.analyzable = 'Can analyze'
t.fieldManage.syncFieldSuggestion = 'Synchronization field suggestion word'
t.fieldManage.selectFieldOnlyOne = 'You can select only one field'
t.fieldManage.syncSuccess = 'Synchronization succeeded'
t.fieldManage.isMultiple = 'Multiple'
t.fieldManage.isNegative = 'Negative'
t.fieldManage.searchable = 'Searchable'
t.fieldManage.domainName = 'Principal Domain Name'
t.fieldManage.cnNameRequired = 'The Chinese name of the field cannot be empty'
t.fieldManage.dataTypeRequired = 'The field data type cannot be empty'
t.fieldManage.usCodeRequired = 'Field English code cannot be empty'
t.fieldManage.fieldAttr = 'Field Properties'
t.fieldManage.participleType = 'Participle type'
t.fieldManage.isDefaultExport = 'Default Export'
t.fieldManage.allowQueryString = 'Full-text Query'
t.fieldManage.boost = 'Weight'
t.fieldManage.bindDict = 'Bound Dictionary'
t.fieldManage.bindTag = 'Bound Labels'
t.fieldManage.selectField = 'Please select a dictionary'
t.fieldManage.undividedWord = 'Undivided word'
t.fieldManage.structuredParticiple = 'Structured participle'
t.fieldManage.selectParticipleType = 'Please select a participle type'
t.fieldManage.selectIsNegative = 'Please choose whether to support negative'
t.fieldManage.selectIsMultiple = 'Please select whether to match multiple values'
t.fieldManage.selectIsDefaultExport = 'Please choose whether to export by default'
t.fieldManage.selectAllowQueryString = 'Please choose whether to support full-text retrieval'
t.fieldManage.selectTmType = 'Please select the type of desensitization'
t.fieldManage.inputBoost = 'Please enter the weight'
t.fieldManage.regnoTm = 'Registration number desensitization'
t.fieldManage.phoneTm = 'Phone number desensitization'
t.fieldManage.emailTm = 'Phone number desensitization'
t.fieldManage.addressTm = 'Address desensitization'
t.fieldManage.allTm = 'Total desensitization'
t.fieldManage.cnNameTm = 'Chinese name desensitization'
t.fieldManage.fieldType = 'Field Type'
t.fieldManage.selectTreeNode1st = 'Please select a tree node first!'
t.fieldManage.caution1 = 'Are you sure you want to modify the field type? After modification, the yin and yang properties will become invalid'
t.fieldManage.caution2 = 'Only when the field type is [Text] can yin and yang be supported'
t.fieldManage.caution3 = 'Please select data first'
t.fieldManage.caution4 = 'This operation will permanently delete the file. Do you want to continue?'
t.fieldManage.caution5 = 'Must be greater than 0'
t.fieldManage.caution6 = 'Must be an integer'

// 字段关系
t.fieldRelation = {}
t.fieldRelation.relationType = 'Relationship word type'
t.fieldRelation.caution = 'Up to 100'
t.fieldRelation.inputNumber = 'Please enter a number!'

// 模型列表
t.modelList = {}
t.modelList.createModel = 'Create Model'
t.modelList.editModel = 'Modify Model'
t.modelList.selectModelName = 'Please select a model name'
t.modelList.switchSelect = 'Switch Selection'
t.modelList.switchInput = 'Switch inputs'
t.modelList.modelCode = 'Model encoding'
t.modelList.indexAlias = 'Index alias'
t.modelList.indexPrefix = 'ES index prefix'
t.modelList.isSplitIndex = 'Adopting a sub index strategy'
t.modelList.isSpecialDisease = 'Is it a specialized disease bank model'
t.modelList.isDefaultIndex = 'Default model or not'
t.modelList.onlyNumber = 'Numbers Only'
t.modelList.esIndexCodeRequired = 'ES index encoding cannot be empty'
t.modelList.modelNameRequired = 'Model name cannot be empty'
t.modelList.modelCodeRequired = 'Model code cannot be empty'
t.modelList.indexAliasRequired = 'Index alias cannot be empty'
t.modelList.selectEnable = 'Please select whether to enable or not'
t.modelList.exportModel = 'Exported model'
t.modelList.uploadModel = 'Upload Model'
t.modelList.syncData = 'Upload model synchronization data'
t.modelList.selectExportModel = 'Select the exported model'
t.modelList.confirmExport = 'Confirm Export'
t.modelList.indexReplicasNum = 'Number of indexed copies'
t.modelList.indexShardsNum = 'Number of index shards'
t.modelList.isSplitIndex = 'Number of index shards'
t.modelList.isSpecialDisease = 'Is it a specialized disease index'
t.modelList.uploadCaution = 'The uploaded file can only be in xls or xlsx format!'
t.modelList.selectModel1st = 'Please select the model first and then click on download!'
t.modelList.title = 'Model List'
t.modelList.exportSuccess = 'Export successful'
t.modelList.syncSuccess = 'Sync successful'
t.modelList.confirmDeleteModel = 'Confirm deleting the model?'

// 表管理
t.tableManage = {}
t.tableManage.title = 'Table management'
t.tableManage.themeName = 'Table Name'
t.tableManage.inputThemeName = 'Please enter a theme name'
t.tableManage.themeCode = 'Table encoding'
t.tableManage.inputThemeCode = 'Please enter the theme code'
t.tableManage.themeBusinessCode = 'Table Domain Name'
t.tableManage.inputThemeBusCode = 'Please enter the name of the topic field'
t.tableManage.tableLevel = 'Table level'
t.tableManage.selectTbLevel = 'Please select a table level'
t.tableManage.level1 = 'Level 1'
t.tableManage.level2 = 'Level 2'
t.tableManage.level3 = 'Level 3'
t.tableManage.themeParentName = 'Parent Table'
t.tableManage.selectTbParent = 'Please select a parent theme'
t.tableManage.noThemeParent = 'No Parent Table'
t.tableManage.enable = 'Enabled'
t.tableManage.disable = 'Disabled'

// 标签管理
t.tagManage = {}
t.tagManage.title = 'Label Management'
t.tagManage.tagName = 'Tag Name'
t.tagManage.tagNameRequired = 'Label name is required'
t.tagManage.searchItemTag = 'Query Item Label'
t.tagManage.searchTempTag = 'Query Template Label'
t.tagManage.exportTempTag = 'Export Template Labels'
t.tagManage.tagType = 'Label Type'
t.tagManage.tagTypeRequired = 'Label type is required'

// 术语管理
t.terminology = {}
t.terminology.concept = 'Concept Table'
t.terminology.semanticName = 'Semantic Name'
t.terminology.semanticNameRequired = 'Semantic name required'
t.terminology.semanticTag = 'semantic tagging'
t.terminology.semanticTagRequired = 'Semantic label required'
t.terminology.status = 'Status'
t.terminology.statusRequired = 'Status required'
t.terminology.invalid = 'Invalid'
t.terminology.effective = 'Effective'
t.terminology.termTable = 'Term Table'
t.terminology.relationTable = 'Relationship Table'
t.terminology.relationshipName = 'Relationship word name'
t.terminology.relationshipNameRequire = 'Relationship word name is required'
t.terminology.term = 'Term name'
t.terminology.termRequired = 'Term required'
t.terminology.conceptId = 'Concept ID'
t.terminology.firstTerm = 'Preferred Terminology'
t.terminology.pend1stTerm = 'Pending preferred terms'
t.terminology.commonTerm = 'Common Terminology'
t.terminology.termType = 'Term Type'
t.terminology.termTypeRequired = 'Term type is required'
t.terminology.semanticDesc = 'Semantic Description'
t.terminology.releaseDate = 'Release Date'

// 授权管理
t.authorManage = {}
t.authorManage.selectAdmDate = 'Please select the visit date field'
t.authorManage.selectAdmDept = 'Please select the visiting department field'

// 患者详情管理
t.caseDetailManage = {}
t.caseDetailManage.title = 'Case detail management'
t.caseDetailManage.configName = 'Configuration name'
t.caseDetailManage.configCode = 'Configuration code'
t.caseDetailManage.configData = 'Case detail configurator'
t.caseDetailManage.configType = 'Configuration type'
t.caseDetailManage.indexId = 'Model'
t.caseDetailManage.indexIdRequire = 'Please select a model'
t.caseDetailManage.configNameRequire = 'Please enter the model Name'
t.caseDetailManage.configCodeRequire = 'Please enter the configuration code'
t.caseDetailManage.configTypeRequire = 'Please select the model type'

export default t
