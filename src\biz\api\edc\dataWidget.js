// 数据组件管理）

export const pageListApi = (params) => {
    return {
        url: '/search/data-widget/page',
        method: 'GET',
        params
    }
}

export const detailApi = (id) => {
    return {
        url: '/search/data-widget/detail/' + id,
        method: 'GET'
    }
}

export const addApi = (data) => {
    return {
        url: '/search/data-widget/insert',
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: '/search/data-widget/update',
        method: 'POST',
        data
    }
}

export const deleteApi = (data) => {
    return {
        url: '/search/data-widget/deletion',
        method: 'POST',
        data
    }
}

