import { debounce } from "lodash"
/**
 * 函数节流
 */
export function _throttle(fn, delay) {
    let timer
    delay = delay || 1000 // 一秒内触发一次
    return function(...args) {
        const context = this
        const canExecute = !timer
        if (canExecute) {
            fn.apply(context, args)
        } else {
            clearTimeout(timer)
        }
        timer = setTimeout(() => {
            timer = null
        }, delay)
    }
}

/**
 * 函数防抖
 * @fn 执行函数
 * @delay 延迟时间
 */

export function _debounce(fn, delay) {
    const wait = delay || 500
    return debounce(fn, wait)
}

export default { _throttle, _debounce }
