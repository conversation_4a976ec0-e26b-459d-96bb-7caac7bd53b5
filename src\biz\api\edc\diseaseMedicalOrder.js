// 获取当前的医嘱条件列表
export const queryListApi = (params) => {
  return {
    url: 'edc/disease-medical/list',
    method: 'get',
    params,
  }
}

// 新增医嘱配置
export const addApi = (data) => {
  return {
    url: 'edc/disease-medical/insert',
    method: 'post',
    data,
  }
}

// 更新医嘱配置
export const editApi = (data) => {
  return {
    url: 'edc/disease-medical/update',
    method: 'put',
    data
  }
}

// 删除医嘱配置
export const deleteBatchApi = (data) => {
  return {
    url: 'edc/disease-medical/deletion',
    method: 'post',
    data
  }
}

// 获取全程医嘱执行记录
export const getOrderExecuteRecordApi = (params) => {
  return {
    url: 'edc/disease-patient/medical-execute',
    method: 'get',
    params
  }
}

// 获取医嘱执行明细
export const getOrderExecuteDetailApi = (params) => {
  return {
    url: 'edc/disease-patient/medical-detail',
    method: 'get',
    params
  }
}