
export const queryListApi = (params) => {
    return {
        url: 'edc/role-template/page',
        method: 'get',
        params,
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'edc/role-template/deletion',
        method: 'post',
        data
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'edc/role-template/detail/' + id,
        method: 'get',
    }
}

export const addApi = (data) => {
    return {
        url: 'edc/role-template/insert',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'edc/role-template/update',
        method: 'post',
        data
    }
}