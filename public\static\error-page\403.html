<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>403-page</title>
  </head>
  <style>
    body {
      background-color: #fff;
    }
    .exception {
      min-height: 500px;
      height: 80%;
      align-items: center;
      text-align: center;
      margin-top: 150px;
    }
    .img {
      display: inline-block;
      padding-right: 52px;
      zoom: 1;
    }
    .gif-image {
      width: 320px;
      height: 220px;
    }
    .content {
      display: inline-block;
      flex: auto;
    }
    h1 {
      color: #434e59;
      font-size: 72px;
      font-weight: 600;
      line-height: 72px;
      margin-bottom: 24px;
    }
    .desc {
      color: rgba(0, 0, 0, 0.45);
      font-size: 20px;
      line-height: 28px;
      margin-bottom: 16px;
    }
    .back-home-btn {
      display: inline-block;
      height: 30px;
      white-space: nowrap;
      -webkit-appearance: none;
      text-align: center;
      box-sizing: border-box;
      outline: 0;
      margin: 0;
      transition: 0.1s;
      font-weight: 400;
      -moz-user-select: none;
      -ms-user-select: none;
      padding: 5px 15px;
      font-size: 14px;
      border: 1px solid #339eff;
      border-radius: 5px;
      color: #fff;
      background-color: #339eff;
    }
    .back-home-btn:hover {
      cursor: pointer;
    }
  </style>
  <body>
    <div class="exception">
      <div class="img">
        <div class="gif-image" id="gif-image"></div>
      </div>
      <div class="content">
        <h1>403</h1>
        <div class="desc">抱歉，你无权访问该页面。</div>
        <div id="fromUrl" class="desc"></div>
        <div class="action">
          <button id="backHome" class="back-home-btn">返回首页</button>
        </div>
      </div>
    </div>
    <script>
      // let backgroundUrl = './static/error-page/403.gif'
      // if (window.__webpack_public_path__) {
      //   backgroundUrl = window.__webpack_public_path__ + backgroundUrl
      // }
      let backgroundUrl = window.__webpack_public_path__ + './static/error-page/403.gif'
      document.getElementById('gif-image').style.background = `url(${backgroundUrl}) no-repeat`
      document.getElementById('gif-image').style.backgroundSize = 'cover'
      document.getElementById('backHome').addEventListener('click', function () {
        if (window.VUE_APP_ROUTER_BASE) {
          window.location.href = window.VUE_APP_ROUTER_BASE + '/welcome'
        } else {
          window.location.href = '/welcome'
        }
      })
      function displayCachedData() {
        // 获取上次访问的页面的地址
        var dataContainer = document.getElementById('fromUrl')
        var cachedData = sessionStorage.getItem('pro__lastPath')
        if (cachedData !== null) {
          dataContainer.innerText = '您上次访问的页面是：' + cachedData
        }
        sessionStorage.removeItem('pro__lastPath')
      }
      function ifIframe() {
        // 判断当前页面是否在iframe中
        const element = document.getElementById('backHome')
        let style = element.style
        if (window !== window.parent) {
          style.display = 'none'
        }
      }
      displayCachedData()
      ifIframe()
    </script>
  </body>
</html>
