
export const addTemplate = (data) => {
  return {
    url: "/search/template/insert",
    method: "post",
    data
  }
}
export const search = (data) => {
  return {
    url: "search/advanced/search",
    method: "post",
    data
  }
}

export const searchAnalyzer = (data) => {
  return {
    url: "search/advanced/analyzer",
    method: "post",
    data
  }
}

export const getAggregationTop = (queryJson) => {
  return {
    url: "/search/advanced/aggregationTop",
    method: "post",
    data: query<PERSON>son
  }
}

export const getAggregationProfile = (queryJson) => {
  return {
    url: "/search/advanced/aggregation/select-patient-info",
    method: "post",
    data: queryJson
  }
}

export const clearFileList = (guid) => {
  return request({
    url: `es/excel/delete/${guid}`,
    method: "DELETE",
  })
}

export const uploadExcel = (data) => {
  return {
    url: "/es/excel/upload",
    method: "POST",
    data
  }
}


export const getQueryCondByGuid = (guid) => {
  return {
    url: "search/search-history/select-object/" + guid,
    method: "GET"
  }
}

export const getAdvancedSearchDsl = (data) => {
  return {
    url: 'search/advanced/dsl/select',
    method: 'POST',
    data,
    params: {
      level:data.level
    }
  }
}

// 新建数据集
export const addDataSet = (params) => {
  return {
    url: 'search/result-op/analysis/dataset/insert',
    method: 'POST',
    params
  }
}

