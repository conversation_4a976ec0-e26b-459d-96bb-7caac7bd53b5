<template>
  <div class="select-item">
    <div
      v-for="item in items"
      :key="item.value"
      class="select-item__option"
      :class="{ 'select-item__option--selected': curValue === item.value, 'is-disabled': disabled }"
      :style="{'background':item.bgColor,'color':item.color}"
      @click="selectItem(item.value)"
    >
      <img v-if="item.imgUrl" class="select-item__img" :src="item.imgUrl">
      {{ item.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: "SelectItem",
  props: {
    // 选项列表
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    // v-model 绑定的值
    value: {
      type: [String, Number],
      default: null,
    },
    disabled:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      curValue:this.value
    }
  },
  methods: {
    selectItem(value) {
      if(this.disabled){
        return
      }
      if(this.curValue == value){
        this.$emit("input", null);
      }else{
        this.$emit("input", value);
      }
    },
  },
  watch:{
    value(val){
      this.curValue = val
    }
  }
};
</script>

<style lang="scss" scoped>
.select-item {
  display: inline-block;
}

.select-item__option {
  display: inline-flex; /* 改为 flex 布局，便于控制图片和文字对齐 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  background: #f0f5ff;
  opacity: 1;
  margin: 0 10px 10px 0;
  border: 1px solid #f0f5ff;
  color: #409eff;
  font-weight: 400;
  box-shadow: none;
  border-radius: 5px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  padding: 4px 8px;
  font-size: 14px;
  line-height: 1;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  &:hover {
    font-weight: bold;
  }
  &.is-disabled{
    font-weight: 400;
    cursor: default;
  }

  .select-item__img {
    width: 18px;
    height: 18px;
    margin-right: 4px; /* 添加右边距，使图片与文字分开 */
    vertical-align: middle; /* 确保图片在行内时的对齐一致性 */
  }
}

.select-item__option--selected {
  border: 1px solid #409eff;
  font-weight: bold;
}
</style>
