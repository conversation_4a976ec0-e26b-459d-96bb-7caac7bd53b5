<template>
  <div class="superslide-container">
    <slot name="titCell"></slot>
    <slot></slot>
    <slot name="prev"></slot>
    <slot name="next"></slot>
    <slot name="pageStateCell"></slot>
  </div>
</template>

<script>
import $ from './jquery-vendor.js';
import './jquery.SuperSlide.2.1.3.source.js';

export default {
  name: "superslide",
  props: {
    options: {
      type: Object,
      default: () => ({})
    }
  },
  mounted: function () {
    this.create();
  },
  destroyed: function () {
  },
  methods: {
    create: function () {
      const superSlideOptions = Object.assign({}, this.options)
      $(this.$el).slide(superSlideOptions)
    }
  }
}
</script>

