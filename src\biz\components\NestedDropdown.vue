<template>
  <hos-dropdown 
    ref="childrenDropDown" 
    trigger="click" 
    :hide-on-click="false"
    @command="emitCommand"
    width="200px"
  >
    <div class="hos-dropdown-link">
      <span>{{ item.title }}</span>
      <i v-if="item.children" class="hos-icon-arrow-right"></i>
    </div>
    <hos-dropdown-menu 
      v-if="item.children" 
      slot="dropdown" 
      :visible-arrow="false" 
      class="lang-select-dropdown-chidren" 
      :append-to-body="false"
      :style="{width:item.childWidth}" 
    >
      <template v-for="(child, index) in item.children">
        <hos-dropdown-item 
          :key="index" 
          v-if="!child.children" 
          :command="child.value"
        >
          {{ child.title }}
        </hos-dropdown-item>
        <hos-dropdown-item 
          style="padding:0;" 
          :key="index + '_nested'" 
          v-else
        >
          <NestedDropdown 
            :item="child" 
            @close="emitCommand" 
          />
        </hos-dropdown-item>
      </template>
    </hos-dropdown-menu>
  </hos-dropdown>
</template>

<script>
export default {
  name: 'NestedDropdown',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  methods: {
    emitCommand(command) {
      // 传递子菜单的命令到父级
      this.$emit('close', command);
    },
  },
};
</script>
<style lang="scss" scoped>
.lang-select-dropdown-chidren {
  top: -10px !important;
  left: 112px !important;
}
.hos-dropdown-menu{
  min-width: 115px;
}
.hos-dropdown{
  width: 100%;
}
.hos-dropdown-menu__item:focus {
  .hos-dropdown-link {
    background-color: #28ba05;
    color: #fff;
  }
}
.hos-dropdown-link {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #000;
  &:hover,
  &:focus {
    background-color: #28ba05;
    color: #fff;
  }
}
</style>
