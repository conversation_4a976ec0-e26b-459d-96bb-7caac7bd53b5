
// 获取主题树
export const getItemTree = (indexName) => {
    return {
        url: `search/es-theme-metadata/tree-by-indexid/${indexName}`,
        method: 'get',
    }
}

// 获取所有索引
export const getAllIndex = () => {
    return {
        url: 'search/es-index-metadata/list',
        method: 'get'
    }
}

// 获取左侧树的数据
export const getTreeByIndex = (val) => {
    return {
        url: `search/es-theme-metadata/tree-by-indexid/${val}`,
        method: 'get',

    }
}

// 获取根据表id查询表全部字段
export const queryWordListById = (params) => {
    return {
        url: `search/es-property-metadata/list-by-theme/${params.themeId}`,
        method: 'GET',
        // params
    }
}