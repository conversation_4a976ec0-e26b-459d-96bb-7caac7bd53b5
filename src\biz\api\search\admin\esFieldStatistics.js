// ES字段统计配置


// 根据表id获取es字段列表
export const getESFieldsByTable = (params) => {
    return {
        url: '/search/es-theme-metadata/es-filed',
        method: 'get',
        params
    }
}

// 分页查询ES字段统计规则
export const commonPageList = (params) => {
    return {
        url: '/search/stats-rule/page',
        method: 'get',
        params
    }
}

// 查询详情
export const commonDetailApi = (id) => {
    return {
        url: '/search/stats-rule/detail/' + id,
        method: 'get'
    }
}

// 批量删除
export const deleteBatch = (data) => {
    return {
        url: '/search/stats-rule/deletion',
        method: 'post',
        data
    }
}

// 新增
export const commonAddApi = (data) => {
    return {
        url: '/search/stats-rule/insert',
        method: 'post',
        data
    }
}

// 修改
export const commonEditApi = (data) => {
    return {
        url: '/search/stats-rule/update',
        method: 'post',
        data
    }
}