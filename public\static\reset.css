/* reset.css（清除默认CSS样式） */
/* 请注意：具体情况下可能需要根据项目需求进行微调 */

/* 1. 设置所有元素的边框和内边距为0，以防止默认边框和内边距影响布局。 */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 2. 清除列表样式，将列表元素的项目符号和缩进去除。 */
ul,
ol {
  list-style: none;
}

/* 3. 清除超链接的默认样式，去除下划线和默认颜色。 */
a {
  text-decoration: none;
  color: inherit;
}

/* 4. 修正图片元素的默认样式，防止图片底部有空隙。 */
img {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 5. 修正表单元素的默认样式，使其在不同浏览器下显示一致。 */
input,
button,
textarea,
select {
  appearance: none;
  border: none;
  background: none;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  outline: none;
}

/* 6. 修正按钮元素的默认样式，使其在不同浏览器下显示一致。 */
button {
  cursor: pointer;
}

/* 7. 重置标题元素的默认样式，使其在不同浏览器下显示一致。 */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/* 8. 清除水平线的默认样式，去除水平线的默认颜色和高度。 */
hr {
  border: none;
  border-top: 1px solid #ccc;
  height: 0;
}

/* 9. 清除段落元素的默认间距，防止默认间距影响布局。 */
p {
  margin: 0;
}

/* 10. 清除按钮在某些浏览器中的内边距，使其显示一致。 */
button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/* 11. 修正块级元素的默认样式，防止部分浏览器为块级元素添加默认边距。 */
html,
body,
div,
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

/* 12. 清除清单样式，修正有序列表和无序列表的默认样式。 */
ol,
ul {
  padding: 0;
  list-style: none;
}

/* 13. 添加一些常用的全局样式，例如文本颜色、字体、行高等。 */
body {
  color: #333;
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

/* 14. 修正表格样式，去除表格的默认边框和间距。 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 15. 清除按钮在IE11及以下版本的默认内边距。 */
button,
input[type="reset"],
input[type="button"],
input[type="submit"] {
  padding: 0;
}

/* 隐藏滚动条 */
/* Webkit (Safari/Chrome) */
/* 注意：Chrome 64及更高版本已不再支持修改滚动条样式 */
body::-webkit-scrollbar {
  width: 0.5em;
}

body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Firefox */
/* 注意：这里使用了 -moz，但是在 Firefox 64 及更新版本中，该样式已被弃用 */
/* 在新版本的 Firefox 中，滚动条样式可能会变回默认样式 */
/* 更多信息请参考 Mozilla 文档 */
body {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.5) transparent;
}

/* Microsoft Edge 和 Internet Explorer 10+ */
body {
  -ms-overflow-style: none;
}

body::-ms-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
}