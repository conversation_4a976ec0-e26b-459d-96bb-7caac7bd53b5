;(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined'
    ? (module.exports = factory())
    : typeof define === 'function' && define.amd
    ? define(factory)
    : (global.__hos = factory())
})(this, function () {
  return {
    _: {},
    debug: false,
    isForceLocalLogin: false, // 是否强制使用单体登录，后端服务出现故障时手动开启
    isForceMAC: false, // 是否强制使用MAC地址，设置为true后登录时必须获取到MAC地址才能登录
    bpmBaseURL: 'http://114.251.235.9:8371', // 流程定义 baseUrl
    VUE_APP_BASE_URL: '/csm', // baseUrl
    VUE_APP_API_BASE_URL: '/csm', // apibaseUrl
    VUE_APP_PAGE_OFFICE_API: '/csm', // pageOffice链接
    VUE_APP_PAGE_PRINT: '/', // pageOffice静态文件路径
    VUE_APP_MSG_BELL: '1', // 右上角消息中心,type:String,'0':'关闭' '1':'开启'
    VUE_APP_HIDE_MEDICAL: false, // 是否隐藏download弹窗中的"医为"两个字. true:隐藏,false:显示
    VUE_EXCEL_PREFIX: true, // 导入导出请求地址是否添加前缀. true:开启,false:关闭
    VUE_APP_HIS_DBDIALOG: false, // 是否由his处理websys客户端弹窗提醒内容
    VUE_APP_WEBSYS_WS: false, // 新websys客户端,使用ws进行连接
    VUE_APP_LOGOUT_REDIRECT: false, // 是否启用登录重定向，true:启用,false:不启用
    VUE_APP_GET_COMMON_AUTH: true, // 是否在登录后获取页面预置权限公共元素.
    VUE_APP_TAB_SWITCH: false, // 是否开启页签切换确认提示
    VUE_LOGO_HOMEPAGE: 'base', // 首页显示方式,base:显示基础平台,portal:显示门户平台
    VUEA_APP_SHOW_SIDEBAR_RESIZER: true, // 是否显示侧边栏缩放bar
    VUEA_APP_SIDEBAR_MIN_WIDTH: 220, // 侧边栏最小宽度
    VUEA_APP_SIDEBAR_MAX_WIDTH: 500, // 侧边栏最大宽度
    // VUE_APP_USE_PAGE_OFFICE: true // 是否使用pageOffice
    // VUE_APP_TIME_OUT: 15000, // 超时时间
    // VUE_APP_HELP_URL: 'http://114.242.246.250:8034/', // 帮助文档地址
    // VUE_APP_OAUTH_URL: '/oauth/postMessage', // 统一登录测试地址 发送 跨域 token
    // VUE_APP_LOGIN_OAUTH_SERVER_URL: '/oauth/logout', // 登录类型为oauth类型时需要
    // VUE_APP_OAUTH_UPDATE_PWD_URL: '/setpassword', // 统一登录修改密码地址
    // VUE_APP_MSG_WS: '/ws' // 消息中心ws直连或代理路径
    VUE_APP_USE_PRINT: true, // 是否使用打印功能
    // BASE_URL: '/', // 打包部署时前端子路径 如果部署到域名的子目录下可以设置：例如 '/csm-front'
    /**
     * -----20250227 代码中使用process.env的地方 在以下引入-----start-----
     * VUE_APP_THEME: process.env.VUE_APP_SIMPLE_ONCE || '0', // UI主题 0极简 1 卡片 2纯净
     * VUE_APP_GLOBAL_CRYPT: process.env.VUE_APP_GLOBAL_CRYPT, // 是否启用全局加密
     * VUE_APP_GLOBAL_DECRYPT: process.env.VUE_APP_GLOBAL_DECRYPT, // 是否启用全局解密
     * VUE_APP_GLOBAL_PARAMS_STRINGIFY: process.env.VUE_APP_GLOBAL_PARAMS_STRINGIFY, // 是否启用全局参数序列化
     * VUE_APP_GLOBAL_SIGN : process.env.VUE_APP_GLOBAL_SIGN, // 是否启用全局签名
     * VUE_APP_CRYPT_TYPE: process.env.VUE_APP_CRYPT_TYPE, // 加密类型
     * VUE_APP_LOGIN_TYPE: process.env.VUE_APP_LOGIN_TYPE, // 登录类型
     * NODE_ENV: process.env.NODE_ENV, // 环境变量
     * BASE_URL: process.env.BASE_URL, // 基础路径
     * VUE_APP_ENV_TYPE: process.env.VUE_APP_ENV_TYPE, // 环境类型
     * VUE_APP_IS_FEATURE: process.env.VUE_APP_IS_FEATURE, // 是否显示系统特色，当 VUE_APP_SIMPLE_ONCE 为 0 时生效。 1:显示  0:不显示
     * VUE_APP_LOGIN_SOURCE: process.env.VUE_APP_LOGIN_SOURCE, // 统一登录平台的编码
     * VUE_APP_SSO: process.env.VUE_APP_SSO, // 是否启用单点登录
     * VUE_APP_BASE_API: process.env.VUE_APP_BASE_API, // 接口地址
     * VUE_APP_CAS_BASE_URL: process.env.VUE_APP_CAS_BASE_URL, // CAS地址
     * VUE_APP_NAV_SETUP: process.env.VUE_APP_NAV_SETUP, // 极简版导航栏是否显示设置按钮 0:否   1:是
     * -----20250227 代码中使用process.env的地方 在以上引入-----end-----
    */
    /* -----20250409 defaultSetting.js namespace 值在此定义-----start-----  */
    VUE_APP_NAMESPACE: 'pro__'
    /* -----20250409 defaultSetting.js namespace 值在此定义-----end-----  */
  }
})
