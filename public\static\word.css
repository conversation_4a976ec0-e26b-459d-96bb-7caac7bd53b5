body {
  display: flex;
  flex: 1;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  border: 1px solid #e2e2e2;
  text-align: center;
  background-color: #f8f8f8;
  padding: 0 6px;
}

td {
  /* padding: 0 6px; */
  text-align: center;
  border: 1px solid #ccc;
}

button {
  padding: 4px 12px;
  font-size: 14px;
  color: #fff;
  background-color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
}

button:hover {
  background-color: #0056b3;
}

#officeContent {
  width: 100%;
  height: 100vh;
}

#OfficeRight {
  flex: 1;
  min-width: 800px;
}

.submit-btn {
  margin-top: 10px;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}