.case-into-group-container {
  background-color: #fff;
  .case-radio {
    width: 100%;
    text-align: center;
    margin-bottom: 30px;
  }

  .case-content {
    .single-form-container {
      width: 550px;
      margin: 0 auto;

      @media (min-width: 768px) {
        width: 100%;
      }

      @media (min-width: 1024px) {
        width: 550px;
      }

      .hos-form {
        // 可以设置这个减小控件宽度
        margin-right: 0 !important;

        ::v-deep.hos-form-item {
          margin-bottom: 15px;
        }
      }

      .button-row {
        margin-top: 20px;
        padding: 5px 0 15px 0;
        display: flex;
        justify-content: center;

        button {
          width: 100px;
        }
      }
    }

    .expandsion-field {
      i {
        color: #66b1ff;
        position: relative;
        top: 2px;
      }

      text-align: right;
    }

    .mult-upload-container {
      padding: 40px;

      .top-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        span.gray-text {
          color: gray;
          vertical-align: text-bottom;
        }
      }

      .bottom-container {
        text-align: center;
        padding: 40px;

        ::v-deep .hos-upload {
          width: 100%;

          .hos-upload-dragger {
            width: 100%;
          }
        }

      }
    }

    .qc-disease-select {
      width: 50%;
      margin:auto;
      position: relative;
      top: 100px;

      .label {
        margin-right: 20px;
      }
    }
  }
}