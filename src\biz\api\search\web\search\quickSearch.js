// 获取同义词
export const getSameMeanWord = (keyword) => {
  return {
    url: `search/quick-search/synonym/${keyword}`,
    method: "GET",
  }
}

// 获取下位词
export const getMoreWord = (keyword) => {
  return {
    url: `search/quick-search/hyponym/${keyword}`,
    method: "GET",
  }
}

// 批量获取同义词
export const getBatchSameMeanWord = (keywordList) => {
  return {
    url: `search/term/synonym-top`,
    method: "POST",
    data: keywordList || []
  }
}

// 批量获取下位词
export const getBatchMoreWordList = (keywordList) => {
  return {
    url: `search/term/hyponym-top`,
    method: "POST",
    data: keywordList || []
  }
}

export const search = (data) => {
  return {
    url: "search/quick-search/search",
    method: "post",
    data
  }
}

export const getThemeList = (params) => {
  return {
    url: "search/es-theme-metadata/tree-by-indexid/" + params,
    method: "get",
  }
}

export const getAggregationTop = (queryJson) => {
  return {
    url: "search/quick-search/aggregation/select-top",
    method: "post",
    data: queryJson
  }
}

export const getAggregationProfile = (queryJson) => {
  return {
    url: "search/quick-search/aggregation/select-patient-info",
    method: "post",
    data: queryJson
  }
}
export const getAllFullField = () => {
  return {
    url: "search/quick-search/select-full-field/list",
    method: "get",
  }
}

// 搜索建议
export const querySearchAsync = (params) => {
  return {
    url: 'search/elastic-api/select-suggest',
    method: 'GET',
    params,
  }
}

export const pageListHistory = (params) => {
  return {
    url: 'search/quick-search/his-record/page',
    method: 'GET',
    params
  }
}

export const deleteHistoryBatch = (data) => {
  return {
    url: 'search/quick-search/his-record/deletion',
    method: 'POST',
    data
  }
}

