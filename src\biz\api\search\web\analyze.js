
export const getJsonData = (searchGuid) => {
  return {
    url: `search/search-history/select-object/${searchGuid}`,
    method: 'GET',
  }
}
// 患者分布统计
export const getAggregationProfile = (data) => {
  return {
    url: `search/advanced/aggregation/select-patient-info`,
    method: 'POST',
    data
  }
}
//  聚合查询（top10）
export const getAggregationTop = (data) => {
  return {
    url: `search/advanced/aggregationTop`,
    method: 'POST',
    data
  }
}

// 患者分布统计FullSearch
export const getAggregationProfileFullSearch = (data) => {
  return {
    url: `search/quick-search/aggregation/select-patient-info`,
    method: 'POST',
    data
  }
}
//  聚合查询FullSearch
export const getAggregationTopFullSearch = (data) => {
  return {
    url: `search/quick-search/aggregation/select-top`,
    method: 'POST',
    data
  }
}

// 全文检索-查询顶部病历质量统计数据
export const getFullSearchAggregationCount = (data) => {
  return {
    url: "search/quick-search/aggregation/select-patient-count",
    method: "POST",
    data
  }
}

// 高级检索-查询顶部病历质量统计数据
export const getAdvancedSearchAggregationCount = (data) => {
  return {
    url: "search/advanced/aggregation/select-count",
    method: "POST",
    data
  }
}


