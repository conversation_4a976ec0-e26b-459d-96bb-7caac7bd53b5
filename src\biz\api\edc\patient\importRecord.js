// 导入部分后台重构，暂未迁移接口
export const pageListApi = (params) => {
    return {
        url: `edc/pat-import-record/page`,
        method: 'GET',
        params
    }
}

export const deleteApi = (data) => {
    return {
        url: `edc/pat-import-record/deletion`,
        method: 'POST',
        data
    }
}

export const downloadApi = (params) => {
    return {
        url: `edc/pat-import-record/download`,
        method: 'GET',
        params,
        responseType: 'blob'
    }
}