console.log();
process.on('exit', () => {
    console.log();
});

if (!process.argv[2]) {
    console.error('[模块名]必填 - Please enter new component name');
    process.exit(1);
}

const name = process.argv[2];
const moduleName = 'hos-app-' + name;
let fs = require("fs");
const fileSave = require('file-save');
let compressing = require("compressing");
const path = require('path')

const basePath = '../src/sys/hos-app-base/'
const decompressPath = '../src/sys/'
const dirPath = "./"
const fileName = "template.zip"

// 解压文件
function unzip(){
    return new Promise((resolve, reject) => {
        compressing.zip.uncompress(path.join(__dirname, '../template.zip'), path.join(__dirname, decompressPath), {zipFileNameEncoding: 'GBK'})
            .then(res => {
                console.log("-------unzip success-----")
                resolve(res)
            })
            .catch(err => {
                console.log("-------unzip err-----")
                reject(error)
            })
    })

}

const moduleConfig = require('../src/sys/hos-app-base/axios/moduleConfig')
function generateModuleConfig(){
    if(moduleConfig[name]){
        console.error(`${name} 已存在.`);
        process.exit(1);
    }
    moduleConfig[name] = 'sys/' + moduleName;
    fileSave(path.join(__dirname, basePath + 'axios/moduleConfig.js'))
        .write('module.exports = ', 'utf8')
        .write(JSON.stringify(moduleConfig, null, '  '), 'utf8')
        .end('\n');

    console.log("-------generate moduleConfig success-----")
}

// 1. 重新输出 i18n/langs/index.js
function generateI18n(){
    let fileContent = "";
    //确保base 在第一位
    if(moduleConfig.base){
        fileContent = fileContent +
`// base i18n
import baseI18n from './base'
let zh = baseI18n.zh;
let en = baseI18n.en;
`;
    }
    if(moduleConfig.biz){
        fileContent = fileContent +
`

// biz i18n
import bizI18n from '@/i18n'
// 把 biz i18n 放到国际化的biz下
zh.biz = bizI18n['zh']
en.biz = bizI18n['en']
`;
    }
    Object.keys(moduleConfig).forEach(moduleName=>{
        switch (moduleName){
            case 'base':
                break;
            case 'biz':
                break;
            default:
                fileContent = fileContent +
`

// ${moduleName} i18n
import ${moduleName}I18n from '@core/hos-app-${moduleName}/i18n'
// 把 ${moduleName} i18n 放到国际化的biz下
zh.${moduleName} = ${moduleName}I18n['zh']
en.${moduleName} = ${moduleName}I18n['en']
`;
        }

    })
    fileContent = fileContent + `

    
// 准备翻译的语言环境信息
export default {
\tzh,
\ten,
}`;

    fileSave(path.join(__dirname, basePath + 'i18n/langs/index.js'))
        .write(fileContent,"utf8")

    console.log("-------generate i18n success-----")
}

// 重新输出模块的router文件
function generateModuleRouter(){
    let routerFileContent = `
/**
 * 静态普通路由
 * @type {{}}
 */
export const ${name}ConstantRouterMap = [
    // {
    //     path: '/staff',
    //     name: 'Staff',
    //     component: () =>
    //         import(/* webpackChunkName: "index" */ '@/views/staff/index'),
    // }
]


/**
 * 静态菜单路由
 * @type { *[] }
 */
export const ${name}ConstantMenuMap = [

]    
`
    fileSave(path.join(__dirname,decompressPath,moduleName,'router/index.js'))
        .write(routerFileContent,"utf8")
}

// 2. 重新输出 router/router.config.js
function generateRouter(){
    generateModuleRouter();

    let fileContent =
`import { LoginLayout, TabLayout,RouteView,BlankLayout } from '@components/layouts';
import {baseConstantRouterMap,baseConstantMenuMap } from "@base/router/base-router"
import {bizConstantRouterMap, bizConstantMenuMap} from "@/router/index";
`;

    Object.keys(moduleConfig).forEach(moduleName=>{
        switch (moduleName){
            case 'base':
                break;
            case 'biz':
                break;
            default:
                fileContent = fileContent +
                    `
// ${moduleName} Router
import {${name}ConstantRouterMap,${name}ConstantMenuMap} from '@core/hos-app-${name}/router';
`;
        }
    })

    fileContent = fileContent + `
/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/welcome',
    children: []
  },
  {
    path: '*', redirect: '/404', hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
`
    Object.keys(moduleConfig).forEach(moduleName=>{
        fileContent = fileContent +
        `
    // ${moduleName} 静态路由
    ...${moduleName}ConstantRouterMap,
`;
    })
    fileContent = fileContent + `
]

/**
 * 基础菜单路由
 * @type { *[] }
 */
export const constantMenuMap = [
`
    Object.keys(moduleConfig).forEach(moduleName=>{
        fileContent = fileContent +
            `
    // ${moduleName} 静态路由
    ...${moduleName}ConstantMenuMap,
`;
    })
    fileContent = fileContent + `
]`



    fileSave(path.join(__dirname, basePath + 'router/router.config.js'))
        .write(fileContent,"utf8")

    console.log("-------generate router success-----")
}

// 3. 重新输出 store/modules/index.js
function generateStore(){
    let fileContent =
`// base vuex
import base from './base'
// biz vuex
import biz from '@/store'`

    Object.keys(moduleConfig).forEach(moduleName=>{
        switch (moduleName){
            case 'base':
                break;
            case 'biz':
                break;
            default:
                fileContent = fileContent + `
// ${moduleName} vuex
import ${moduleName} from '@core/hos-app-${name}/store';`;
        }
    })

    fileContent = fileContent +  `

let modules = {`;

    Object.keys(moduleConfig).forEach(moduleName=>{
                fileContent = fileContent + `
    ...${moduleName},`;
    })

    fileContent = fileContent +  `
}

export default modules;      
`

    fileSave(path.join(__dirname, basePath + 'store/modules/index.js'))
        .write(fileContent,"utf8")
    console.log("-------generate store success-----")

}

// 4. 生成测试页面
function generateTestView() {
    let fileContent =
`<template>
  <div class="h-fit">
    <hos-biz-table
        uid="staffTable"
        :cols="staffTableCols"
        data="${name}.staff.selectPage"
        :form="formObj"
    >
      <!-- 搜索 -->
      <template #form>
        <hos-form-item label="姓名">
          <hos-input v-model="formObj.model.name"></hos-input>
        </hos-form-item>
        <hos-form-item label="编码">
          <hos-input v-model="formObj.model.id"></hos-input>
        </hos-form-item>
        <hos-form-item label="手机号">
          <hos-input v-model="formObj.model.phone"></hos-input>
        </hos-form-item>
        <hos-form-item label="邮箱">
          <hos-input v-model="formObj.model.email"></hos-input>
        </hos-form-item>
        <hos-form-item>
          <hos-biz-button type="primary" run="form.search">搜索</hos-biz-button>
          <hos-biz-button type="primary" run="form.reset">重置</hos-biz-button>
        </hos-form-item>
      </template>

      <!-- 工具栏  -->
      <template #toolbar>
        <hos-button-group class="hos">
          <hos-button v-has-permi="{key:'demo:staffList:add'}" icon="hos-icom-add" @click="addOrEditUser()">新增</hos-button>
        </hos-button-group>
      </template>

      <!-- 操作列 -->
      <template #operation="{ row }">
        <hos-tooltip v-has-permi="{key:'demo:staffList:edit'}" class="pl5 pr5" content="修改">
          <i class="hos-icom-edit" @click="addOrEditUser(row)"></i>
        </hos-tooltip>
        <hos-tooltip v-has-permi="{key:'demo:staffList:delete'}" class="pl5 pr5" content="删除">
          <i class="hos-icom-cancel" @click="delRow(row)"></i>
        </hos-tooltip>
      </template>
    </hos-biz-table>
    <hos-biz-dialog
        :title="title"
        uid="staffAddDialog"
        :close-on-click-modal="false"
    >
    </hos-biz-dialog>
  </div>
</template>
<script>
// 书写业务逻辑
  export default {
    props: [],
    components: {},
    data() {
      return {
        title: '新增员工',
        formObj:{
          labelWidth: "auto",
          inline: true,
          model:{
            name:"",
            email:"",
            phone:"",
            gender:"",
            age:""
          }
        },
        staffTableCols:[
          {
            type: "selection",
            align: "center",
            width: "50"
          },{
            prop: "name",
            label: "姓名",
            permiKey:'demo:staffList:tableCol:name'
          },{
            prop: "gender",
            label: "性别",
            permiKey:'demo:staffList:tableCol:gender'
          },{
            prop: "phone",
            label: "手机号",
            permiKey:'demo:staffList:tableCol:phone'
          },{
            prop: "email",
            label: "邮箱",
            permiKey:'demo:staffList:tableCol:email'
          },{
            prop: "description",
            label: "备注",
            permiKey:'demo:staffList:tableCol:des'
          },{
            label: "操作",
            width: "70",
            slotName:"operation"
          }
        ]
      }
    },
    mounted() {
      this.staffTableCols = this.$m.colAuthFilter(this.staffTableCols)
    },
    methods: {
      addOrEditUser(row) {
        if (row) {
          this.title = "修改员工";
          // 修改
          this.$store.commit("OPEN_DIALOG", {
            component: require("./staff-add.vue").default,
            _uid: "staffAddDialog",
            props: {
              id: row.id,
              status: 'edit'
            },
          });
        } else {
          this.title = "新增员工";
          // 新增
          this.$store.commit("OPEN_DIALOG", {
            component: require("./staff-add.vue").default,
            _uid: "staffAddDialog",
            props:{
              status: 'add'
            }
          });
        }
      },

      // 删除行
      delRow(row) {
        this.$api('${name}.staff.deleteById', row.id).then((response) => {
          if (response && response.code == 200) {
            this.$store.commit("UPDATE_TABLE",{_uid: 'staffTable'});
            this.$message.success('删除成功');
          }
        })
      }
    },
  }
</script>
<style lang="scss" scoped>
/* 书写组件样式，仅在组件内部生效 */
</style>
<style lang="scss">
/* 书写组件样式，全局生效 */
</style>
`
    fileSave(path.join(__dirname,decompressPath,moduleName,'views/staff/index.vue'))
        .write(fileContent,"utf8")

    let staffAddContent =
`<template>
  <div>
    <hos-form
        ref="dialogForm"
        :model="staffForm"
        label-width="auto"
        :rules="rules"
    >
      <hos-row>
        <hos-col :span="12">
          <hos-form-item v-has-permi="{key:'demo:staffForm:name',formRule: rules, elModel: 'name',status: status}" label="姓名:" prop="name">
            <hos-input :disabled="$m.isDisabled('demo:staffForm:name',status)" v-model="staffForm.name"></hos-input>
          </hos-form-item>
        </hos-col>
        <hos-col :span="12">
          <hos-form-item v-has-permi="{key:'demo:staffForm:gender',formRule: rules, elModel: 'gender',status: status}" label="性别:" prop="gender">
            <hos-select :disabled="$m.isDisabled('demo:staffForm:gender',status)" v-model="staffForm.gender">
              <hos-option label="男" value="男"></hos-option>
              <hos-option label="女" value="女"></hos-option>
            </hos-select>
          </hos-form-item>
        </hos-col>
        <hos-col :span="12">
          <hos-form-item v-has-permi="{key:'demo:staffForm:age',formRule: rules, elModel: 'age',status: status}" label="年龄:" prop="age">
            <hos-input-number
                v-model="staffForm.age"
                :controls="false"
                :min="0"
                style="width: 100%"
                :disabled="$m.isDisabled('demo:staffForm:age',status)"
            ></hos-input-number>
          </hos-form-item>
        </hos-col>
        <hos-col :span="12">
          <hos-form-item v-has-permi="{key:'demo:staffForm:phone',formRule: rules, elModel: 'phone',status: status}" label="手机号:" prop="phone">
            <hos-input :disabled="$m.isDisabled('demo:staffForm:phone',status)" v-model="staffForm.phone"></hos-input>
          </hos-form-item>
        </hos-col>
        <hos-col :span="12">
          <hos-form-item v-has-permi="{key:'demo:staffForm:email',formRule: rules, elModel: 'email',status: status}" label="邮箱:" prop="email">
            <hos-input :disabled="$m.isDisabled('demo:staffForm:email',status)" v-model="staffForm.email"></hos-input>
          </hos-form-item>
        </hos-col>
        <hos-col :span="24">
          <hos-form-item v-has-permi="{key:'demo:staffForm:des',formRule: rules, elModel: 'description',status: status}" label="描述:">
            <hos-input
                :disabled="$m.isDisabled('demo:staffForm:des',status)"
                v-model="staffForm.description"
                type="textarea"
                :rows="2"
            ></hos-input>
          </hos-form-item>
        </hos-col>
      </hos-row>
    </hos-form>
    <div slot="footer" class="dialog-footer">
      <hos-button type="primary" @click="cancel">取消</hos-button>
      <hos-button type="success" @click="save" :loading="loading">保存</hos-button>
    </div>
  </div>
</template>

<script>
// 书写业务逻辑
export default {
  props: ['id','status'],
  components: {},
  data() {
    return {
      loading: false,
      staffForm: {
        name:"",
        email:"",
        phone:"",
        gender:"",
        age:"",
        description:""
      },
      rules: {
        name: [{ required: true, message: '请输入内容' }],
        email: [{ required: true, message: '请输入内容' }],
        phone: [{ required: true, message: '请输入内容' }],
        gender: [{ required: true, message: '请输入内容' }],
        age: [{ required: true, message: '请输入内容' }],
      },
    }
  },
  mounted() {
    this.staffForm = {
      name:"",
      email:"",
      phone:"",
      gender:"",
      age:"",
      description:""
    };
    if(this.id){
      this.getById();
    }
  },
  methods: {
    getById(){
      this.$api('${name}.staff.selectById', {id: this.id}).then((response) => {
        if (response.success) {
          this.staffForm = response.data;
        }
      })
    },
    save() {
      /**
       * 保存操作，首先校验表单，通过后发起ajax请求
       */
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$api(
              this.id ? '${name}.staff.updateById' : '${name}.staff.insert',
              this.staffForm
          ).then((response) => {
            this.loading = false
            if (response.success) {
              this.showAddDialog = false
              this.$message.success(response.msg)
              this.$store.commit("CLOSE_DIALOG", {_uid: "staffAddDialog"});
              this.$store.commit("UPDATE_TABLE",{_uid: 'staffTable'});
            } else {
              this.$message.error(response.msg)
            }
          }).catch((err)=>{
            this.loading = false
            console.log(err)
          })
        }
      })
    },
    cancel() {
      this.$store.commit("CLOSE_DIALOG", {
        _uid: "staffAddDialog"
      });
      this.loading = false
    },
  },
}
</script>
<style lang="scss" scoped>
/* 书写组件样式，仅在组件内部生效 */
</style>
<style lang="scss">
/* 书写组件样式，全局生效 */
</style>
`;
    fileSave(path.join(__dirname,decompressPath,moduleName,'views/staff/staff-add.vue'))
        .write(staffAddContent,"utf8");

    let routerContent =
`
/**
 * 静态普通路由
 * @type {{}}
 */
export const eventConstantRouterMap = [
    {
        path: '/staff',
        name: 'Staff',
        component: () =>
            import(/* webpackChunkName: "index" */ '@core/${moduleName}/views/staff/index'),
    }
]


/**
 * 静态菜单路由
 * @type { *[] }
 */
export const eventConstantMenuMap = [

]
`
    fileSave(path.join(__dirname,decompressPath,moduleName,'router/index.js'))
        .write(routerContent,"utf8")

    console.log("-------generate test view success-----")
}




unzip().then(()=>{
    // 重命名文件夹为 'hos-app-*'
    fs.renameSync(path.join(__dirname,decompressPath,'template'), path.join(__dirname,decompressPath,moduleName))

    // 0. 添加到 axios/moduleConfig.js
    generateModuleConfig();

    // 1. 生成i18n文件
    generateI18n();

    // 2. 生成router文件
    generateRouter();

    // 3. 生成store文件
    generateStore();

    // 4. 生成测试页面
    generateTestView();


})




