export const queryDetailApi = (id) => {
    return {
      url: 'search/es/dict-item/detail/' + id,
      method: 'GET',
    }
}
export const addApi = (data) => {
    return {
      url: 'search/es/dict-item/insert',
      method: 'POST',
      data
    }
}
export const deleteBatchApi = (data) => {
  return {
    url: 'search/es/dict-item/deletion',
    method: 'POST',
    data
  }
}
export const updateApi = (data) => {
  return {
    url: 'search/es/dict-item/update',
    method: 'post',
    data
  }
}

export const queryListApi = (params) => {
    return {
        url: "search/es/dict-item/page",
        method: 'GET',
        params
    }
}


