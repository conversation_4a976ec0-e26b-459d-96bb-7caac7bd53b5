
export const getFieldTypeTree = () => {
    return {
        url: 'search/field-type/tree',
        method: 'GET',
    }
}
export const queryFieldTypesList = (params) => {
    return {
        url: 'search/field-type/list',
        method: 'GET'
    }
}

export const queryListByType = (params) => {
    return {
        url: 'search/relative/page',
        method: 'GET',
        params
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'search/relative/detail/' + id,
        method: 'GET'
    }
}

export const addApi = (data) => {
    return {
        url: 'search/relative/insert',
        method: 'POST',
        data
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'search/relative/deletion',
        method: 'POST',
        data
    }
}

export const updateApi = (data) => {
    return {
        url: 'search/relative/update',
        method: 'post',
        data
    }
}