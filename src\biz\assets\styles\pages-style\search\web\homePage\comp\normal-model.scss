.index-container-normal-model {
  width: 100%;
  min-width: 1160px;
  background-color: #e8e8e8;

  .top-container {
    margin: 0 0 10px 0;

    .info-card,
    .condition-card,
    .pat-card,
    .adm-card {
      height: 140px;

      .content {
        height: 48px;
        padding-left: 50px;
        display: flex;
        align-items: center;

        > span {
          align-self: last baseline;
          margin-bottom: 2px;
          font-size: 20px;
        }

        .content-right {
          font-size: 16px;
          margin-left: 10px;
          color: #606266;
        }
      }
    }
  }

  .top-container {
    .header {
      height: 40px;
      margin-bottom: 10px;

      .blockTitle {
        font-size: 14px;
        // font-weight: bolder;
        line-height: 14px;
        // color: #000000;
      }
    }

    .info-icon {
      font-size: 26px;
      padding: 10px;
      border: solid 1px #beddfd;
      border-radius: 50px;
      background-color: #beddfd;
      color: #fff;
    }

    .count-icon {
      font-size: 50px;
      padding: 10px;
      padding-left: 0;
      border: none;
      color: #64befa;
    }

    .count-label {
      font-size: 20px;
      font-weight: bolder;
    }

    .count-label-small {
      font-size: 20px;
      font-weight: bolder;
    }
  }

  .trend-container {
    min-height: 350px;
    margin: 0 0 10px 0;
    padding-bottom: 30px;
    color: #666;

    .header {
      height: 40px;
      margin-bottom: 10px;

      // 格栏标题
      .blockTitle {
        font-size: 14px;
        // font-weight: bolder;
        line-height: 14px;
        // color: #000000;
        color: #666;
      }

      // 线状tab
      .tabLine {
        width: 200px;
        float: right;
        height: 100%;
      }

      // 按钮状tab
      .tabBtn {
        // width: 111px;
        float: right;
      }
    }
  }

  .bottom-chart-container {
    width: 100%;
    height: 300px;
    display: flex;

    .bottom-chart-left {
      padding: 0 10px;
      flex: 1;
      min-width: 300px;
    }

    .bottom-chart-right {
      flex: 3;
      padding-left: 55px;
    }
  }
}

// 可供点击的
.clickable {
  cursor: pointer;
}
