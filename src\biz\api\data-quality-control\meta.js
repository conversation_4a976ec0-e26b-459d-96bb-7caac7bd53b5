export const queryListApi = (params) => {
    return {
        url: 'quality/medical-data-standards-meta/list',
        method: 'get',
        params,
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'quality/medical-data-standards-meta/detail/' + id,
        method: 'get',
    }
}

export const addApi = (data) => {
    return {
        url: 'quality/medical-data-standards-meta/insert',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'quality/medical-data-standards-meta/update',
        method: 'post',
        data,
    }
}

export const deleteApi = (id) => {
    return {
        url: 'quality/medical-data-standards-meta/delete/' + id,
        method: 'delete',
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'quality/medical-data-standards-meta/batch-delete',
        method: 'post',
        data
    }
}

export const getBindQcRules = (id) => {
    return {
        url: 'quality/standard-meta-qc-rules/qs/' + id,
        method: 'get'
    }
}

export const metaBindRules = (data) => {
    return {
        url: 'quality/standard-meta-qc-rules/save-or-update',
        method: 'post',
        data
    }
}

