.mr10 {
  margin-right: 10px;
}

.ml20 {
  margin-left: 20px;
}

.border-right {
  border-right: 1px solid #273b4d;
}

.border-left {
  border-left: 1px solid #273b4d;
}

.el-icon-arrow-down {
  font-size: 10px;
}

.is-active {
  background: #31455d !important;
  color: #bfcbd9 !important;
}

.layout {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;

  .layout-left {
    display: inline-block;
    height: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border: 0px;
    background-color: #263445;

    //工具栏一个元素
    .tools-item {
      display: flex;
      position: relative;
      width: 100%;
      height: 48px;
      align-items: center;
      -webkit-box-align: center;
      padding: 0 6px;
      cursor: pointer;
      font-size: 12px;
      margin-bottom: 1px;

      .tools-item-icon {
        color: #409eff;
        margin-right: 10px;
        width: 53px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        display: block;
        border: 1px solid #3a4659;
        background: #282a30;
      }

      .tools-item-text {}
    }
  }

  .layout-left-fold {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 100%;

    font-size: 12px;
    overflow: hidden;
    background-color: #242a30;
    cursor: pointer;
    padding-top: 26%;

    i {
      font-size: 18px;
      width: 18px;
      height: 23px;
      margin-left: 0px;
      color: #bfcbd9;
    }
  }

  .layout-middle {
    // display: flex;
    position: relative;
    //width: calc(100% - 445px);
    height: 100%;
    background-color: rgb(36, 42, 48);
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border: 1px solid rgb(36, 42, 48);
    align-items: center;
    vertical-align: middle;
    text-align: center;

    .top-button {
      display: flex;
      flex-direction: row;
      height: 40px;
      line-height: 40px;
      margin-left: 9px;

      .btn {
        color: #788994;
        width: 55px;
        text-align: center;
        display: block;
        cursor: pointer;

        .el-icon-arrow-down {
          transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          /* IE 9 */
          -moz-transform: rotate(0deg);
          /* Firefox */
          -webkit-transform: rotate(0deg);
          /* Safari 和 Chrome */
          -o-transform: rotate(0deg);
          /* Opera */
          transition: all 0.4s ease-in-out;
        }

        &:hover {
          background: rgb(25, 29, 34);

          .el-icon-arrow-down {
            transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            /* IE 9 */
            -moz-transform: rotate(180deg);
            /* Firefox */
            -webkit-transform: rotate(180deg);
            /* Safari 和 Chrome */
            -o-transform: rotate(180deg);
            /* Opera */
            transition: all 0.4s ease-in-out;
          }
        }
      }

      .btn-disable {
        opacity: 0.3;
        cursor: no-drop;
      }

      .scale-num {
        color: #788994;
        opacity: 1;
        cursor: pointer;

        &.btn-disable {
          cursor: no-drop;

          &:hover {
            background: #20262c;
          }
        }
      }
    }

    .workbench-container {
      position: relative;
      -webkit-transform-origin: 0 0;
      transform-origin: 0 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      overflow: auto;

      .vueRuler {
        // width: 100%;
        // padding: 18px 0px 0px 18px;
        padding: 0;
      }

      .workbench {
        background-color: #1e1e1e;
        position: relative;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        margin: 0;
        padding: 0;
      }

      .bg-grid {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: 30px 30px, 30px 30px;
        background-image: linear-gradient(hsla(0, 0%, 100%, 0.1) 1px,
            transparent 0),
          linear-gradient(90deg, hsla(0, 0%, 100%, 0.1) 1px, transparent 0);
        // z-index: 2;
      }
    }
  }

  .layout-right {
    display: inline-block;
    height: 100%;
    &>.el-tabs{
      height:100%;
    }
  }

  ::v-deep .el-tabs--border-card {
    border: 0;

    .el-tabs__header {
      .el-tabs__nav {
        .el-tabs__item {
          background-color: #242f3b;
          border: 0px;
        }

        .el-tabs__item.is-active {
          background-color: #31455d;
        }
      }
    }

    &>.el-tabs__content {
      background-color: #242a30;
      height: calc(100% - 39px);
      overflow-x: hidden;
      overflow-y: auto;

      .el-tab-pane {
        color: #bfcbd9;
        height: 100%;
        overflow: auto;
        .chart-type{
          height: 100%;
          .el-tabs{
            height: 100%;
            .el-tab-pane{
              height: 100%;
              overflow: auto;
              overflow-x: hidden;
            }
          }
        }
      }

      &::-webkit-scrollbar {
        width: 5px;
        height: 14px;
      }

      &::-webkit-scrollbar-track,
      &::-webkit-scrollbar-thumb {
        border-radius: 1px;
        border: 0 solid transparent;
      }

      &::-webkit-scrollbar-track-piece {
        /*修改滚动条的背景和圆角*/
        background: #29405c;
      }

      &::-webkit-scrollbar-track {
        box-shadow: 1px 1px 5px rgba(116, 148, 170, 0.5) inset;
      }

      &::-webkit-scrollbar-thumb {
        min-height: 20px;
        background-clip: content-box;
        box-shadow: 0 0 0 5px rgba(116, 148, 170, 0.5) inset;
      }

      &::-webkit-scrollbar-corner {
        background: transparent;
      }

      /*修改垂直滚动条的样式*/
      &::-webkit-scrollbar-thumb:vertical {
        background-color: #00113a;
        // -webkit-border-radius: 7px;
      }

      /*修改水平滚动条的样式*/
      &::-webkit-scrollbar-thumb:horizontal {
        background-color: #00113a;
        // -webkit-border-radius: 7px;
      }
    }
  }
}

ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav {
  width: 40px;
  padding: 0;
  list-style: none;
  /* overflow: hidden; */
}

.nav {
  zoom: 1;
}

.nav:before,
.nav:after {
  content: "";
  display: table;
}

.nav:after {
  clear: both;
}

.nav>li {
  width: 55px;
  text-align: left;
  position: relative;
}

.nav>li a {
  float: left;
  padding: 12px 30px;
  color: #999;
  font: bold 12px;
  text-decoration: none;
}

.nav>li:hover {
  color: #788994;
}

.nav>li ul {
  visibility: hidden;
  position: absolute;
  z-index: 1000;
  list-style: none;
  left: 0;
  padding: 0;
  background-color: rgb(36, 42, 48);
  opacity: 0;
  _margin: 0;
  width: 120px;
  transition: all 0.2s ease-in-out;
}

.nav>li:hover>ul {
  opacity: 1;
  visibility: visible;
  margin: 0;

  li:hover {
    background-color: rgb(25, 29, 34);
  }
}

.nav ul li {
  float: left;
  display: block;
  border: 0;
  width: 100%;
  font-size: 12px;
}

.nav ul a {
  padding: 10px;
  width: 100%;
  display: block;
  float: none;
  height: 120px;
  border: 1px solid #30445c;
  background-color: rgb(25, 29, 34);
  transition: all 0.2s ease-in-out;
}

.nav ul a:hover {
  border: 1px solid #3c5e88;
}

.nav ul li:first-child>a:hover:before {
  border-bottom-color: #04acec;
}

.nav ul ul {
  top: 0;
  left: 120px;
  width: 400px;
  height: 300px;
  overflow: auto;
  padding: 10px;
  _margin: 0;
}

.nav ul ul li {
  width: 120px;
  height: 120px;
  margin-right: 3px;
  display: block;
  float: left;
}

.nav .item {
  padding: 5px;
}

::v-deep .vue-ruler-h {
  opacity: 0.3;
}

::v-deep .vue-ruler-v {
  opacity: 0.3;
}

.layout-left {
  width: 200px;
  background: #242a30;
  overflow: hidden;

  .chart-type {
    display: flex;
    flex-direction: row;
    overflow: hidden;
    ::v-deep .el-tabs__nav-prev,::v-deep .el-tabs__nav-next{
      left: 0 !important;
      color: #fff;
    }

    .type-left {
      width: 100%;
      text-align: center;

      ::v-deep .el-tabs__header {
        width: 30%;
        margin-right: 0;

        .el-tabs__nav-wrap {
          &::after {
            background: transparent;
          }

          .el-tabs__item {
            text-align: center;
            width: 100% !important;
            color: #fff;
            padding: 0;
            font-size: 12px !important;
          }
        }
      }

      ::v-deep .el-tabs__content {
        width: 70%;
        height: 100%;
      }
    }
  }

  //工具栏一个元素
  .tools-item {
    display: flex;
    position: relative;
    width: 100%;
    height: 48px;
    align-items: center;
    -webkit-box-align: center;
    padding: 0 6px;
    cursor: pointer;
    font-size: 12px;
    margin-bottom: 1px;

    .tools-item-icon {
      color: #409eff;
      margin-right: 10px;
      width: 53px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      display: block;
      border: 1px solid #3a4659;
      background: #282a30;
    }

    .tools-item-text {
      font-size: 12px !important;
    }
  }

  ::v-deep .el-tabs__content {
    padding: 0;
  }
}

/* 设置滚动条的样式 */

::-webkit-scrollbar {
  width: 0;
  height: 10px;
}