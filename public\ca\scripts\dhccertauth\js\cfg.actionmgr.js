﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_actionstatus";
//页面选中数据
var selectIndex = "";
var editIndex = "";
var modifyBeforeRow = {};
var modifyAfterRow = {};

$(function() {
    initBTN();
    initSignAction();
    initSignModel();

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        initActionGrid(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        initActionGrid(selOrgID);
    }
});

//初始化按钮功能
function initBTN() {
    $("#btnQuery").click(function(){queryCheck();});
    $("#btnModify").click(function(){modifyDataGrid();});
    $("#btnCancelModify").click(function(){cancelModifyDataGrid();});
    $("#btnSave").click(function(){saveDataGrid();});
}

//初始化签名操作列表
function initSignAction() {
    var data = {
        action: "GET_SIGNACTIONLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("signAction",json.data,false,true,"signActionCode","signActionDesc");
    } else {
        $.messager.alert("提示", "获取签名方式数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//初始化签名产品组
function initSignModel() {
    var data = {
        action: "GET_SIGNMODELLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        initCombobox("signModel",json.data,false,true,"signModelCode","signModelDesc");
    } else {
        $.messager.alert("提示", "获取厂商数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//查询按钮触发
function queryCheck() {
    if (editIndex !== "") {
        $.messager.confirm("提示", "编辑还未完成，未保存的修改将会丢失，是否继续查询？", function (r) {
            if (r) {
                $("#dgAction").datagrid("cancelEdit",editIndex);
                queryData();
            }
        });
    } else {
        queryData();
    }
}

//查询
function queryData() {
    var actionCode = $("#signAction").combobox("getValue");
    var modelCode = $("#signModel").combobox("getValue");

    var queryParams = {
        action: "GET_SIGNACTIONCONFIGS",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            actionCode: actionCode,
            modelCode: modelCode
        }
    };
    $("#dgAction").datagrid("load",queryParams);
}

//修改数据
function modifyDataGrid() {
    if (selectIndex === "") return;
    $("#dgAction").datagrid("beginEdit",selectIndex);
    modifyBeforeRow = $.extend({},$("#dgAction").datagrid("getRows")[selectIndex]);
    editIndex = selectIndex;
}

//取消修改
function cancelModifyDataGrid() {
    if (editIndex === "") return;
    $("#dgAction").datagrid("cancelEdit",editIndex);
    editIndex = "";
}

//保存数据
function saveDataGrid() {
    if (editIndex === "") return;
    $("#dgAction").datagrid("endEdit",editIndex);
    var modifyAfterRow = $("#dgAction").datagrid("getRows")[editIndex];
    editIndex = "";

    var modifyAfterStr = JSON.stringify(modifyAfterRow);
    var modifyBeforeStr = JSON.stringify(modifyBeforeRow);
    if (modifyAfterStr != modifyBeforeStr) {
        saveModify(modifyAfterRow);
        modifyAfterRow = {};
        modifyBeforeRow = {};
    }
}

function saveModify(dataObj) {
    var data = {
        action: "SAVE_SIGNACTIONCONFIG",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            signActionData: {
                actionCode: dataObj.actionCode,
                isCareCAOn: dataObj.isCareCAOn == "是" ? "1":"0",
                isPatCAOn: dataObj.isPatCAOn == "是" ? "1":"0",
                isOpenForce: dataObj.isOpenForce == "是" ? "1":"0",
                isSignleLogon: dataObj.isSignleLogon == "是" ? "1":"0",
                isCheckSignUser: dataObj.isCheckSignUser == "是" ? "1":"0"
            }
        }
    };

    ajaxPOSTCommon(data,function (json) {
        if (json.code == 200) {
            if (json.data.success) {
                $.messager.popover({msg: "签名操作配置修改成功",type: "success",timeout: 3000,showType: "show",style: {top: 200,left:(window.screen.width/2)-220}});
            } else {
                $.messager.alert("提示", "签名操作配置修改失败！", "error")
            }
        } else {
            $.messager.alert("提示", "修改签名操作配置失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
        }
    });
}

//加载所有签名操作信息
function initActionGrid(selOrgID) {
    var param = {
        action: "GET_SIGNACTIONCONFIGS",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            actionCode: "",
            modelCode: ""
        }
    };
    $("#dgAction").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbAction",
        url: ca_common_tools.getAppPath("GET_SIGNACTIONCONFIGS"),
        queryParams: param,
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:20,
        pageList:[20,40,100],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"modelDesc",title:"签名模块描述",align:"left"},
            {field:"actionCode",title:"签名操作代码",align:"left"},
            {field:"actionDesc",title:"签名操作描述",align:"left"},
            {field:"isCareCAOn",title:"是否开启医护签名",align:"center",
                formatter: function(value,row,index)
                {
                    var color = value === "是" ? "green" : "red";
                    return "<span style='color:"+ color +"'>"+ value +"</span>";
                },
                editor:{
                    type:"switchbox",options:{onClass:"primary",offClass:"gray",onText:"是",offText:"否"}
                }
            },
            //{field:"isCareCAOn",title:"是否开启医护签名",align:"center",editor:{type:"checkbox",options:{on:"1",off:"0"}}},
            {field:"isPatCAOn",title:"是否开启患者签名",align:"center",
                formatter: function(value,row,index)
                {
                    var color = value === "是" ? "green" : "red";
                    return "<span style='color:"+ color +"'>"+ value +"</span>";
                },
                editor:{
                    type:"switchbox",options:{onClass:"primary",offClass:"gray",onText:"是",offText:"否"}
                }
            },
            {field:"isOpenForce",title:"是否每次签名都认证",align:"center",
                formatter: function(value,row,index)
                {
                    var color = value === "是" ? "green" : "red";
                    return "<span style='color:"+ color +"'>"+ value +"</span>";
                },
                editor:{
                    type:"switchbox",options:{onClass:"primary",offClass:"gray",onText:"是",offText:"否"}
                }
            },
            {field:"isSignleLogon",title:"是否单一认证方式进行认证",align:"center",
                formatter: function(value,row,index)
                {
                    var color = value === "是" ? "green" : "red";
                    return "<span style='color:"+ color +"'>"+ value +"</span>";
                },
                editor:{
                    type:"switchbox",options:{onClass:"primary",offClass:"gray",onText:"是",offText:"否"}
                }
            },
            {field:"isCheckSignUser",title:"是否校验签名用户与登录用户一致",align:"center",
                formatter: function(value,row,index)
                {
                    var color = value === "是" ? "green" : "red";
                    return "<span style='color:"+ color +"'>"+ value +"</span>";
                },
                editor:{
                    type:"switchbox",options:{onClass:"primary",offClass:"gray",onText:"是",offText:"否"}
                }
            }
        ]],
        onDblClickRow:function(rowIndex,row){
        },
        onSelect:function(rowIndex,row){
            selectIndex = rowIndex;
        },
        onLoadSuccess:function(data){
            $("#dgAction").datagrid("unselectAll");
            editIndex = "";
            selectIndex = "";
        },
        onLoadError:function() {
            $.messager.alert("提示","签名操作列表加载失败");
        },
        onBeforeSelect:function(index, row){
            if ((editIndex !== "")&&(editIndex !== index)) {
                $.messager.alert("提示","请先保存或者取消当前修改的数据，再选择其他数据！");
                $("#dgAction").datagrid("selectRow",editIndex);
                return false;
            }
        }
    });
}