<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <style>
    #login-loading {
      position: absolute;
      max-height: 90vh;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  </style>
  <% if (htmlWebpackPlugin.options.mode !== 'his') { %>
  <!-- 表单加粗样式，需要的项目可以开启下面一行css样式的引用 -->
  <!-- <link rel="stylesheet" href="<%= BASE_URL %>crf-border.css"/> -->
  <link rel="stylesheet" href="<%= BASE_URL %>case-view-maker/css/Maker.css"/>
  <link rel="stylesheet" href="<%= BASE_URL %>workbench-maker/WorkbenchMaker.css"/>
  <script type="text/javascript">
    try {
      window.i18nLang = JSON.parse(sessionStorage.getItem('pro__language')).value
    } catch (error) {
      window.i18nLang = 'zh'
    }
    // 国际化全局变量
    // workbench-maker 国际化变量
    window.makerWnI18nObj = {};
    window.makerWnI18n = {
      t: function (key) {
        // console.log('workbench-maker-i18n-', key)
        if(!window.i18nLang) return key
        return (window.makerWnI18nObj[window.i18nLang] && window.makerWnI18nObj[window.i18nLang][key]) || key;
      },
    };
    // case-view-maker 国际化变量
    window.makerCvI18nObj = {};
    window.makerCvI18n = {
      t: function (key) {
        // console.log('case-view-maker-i18n-', key)
        if(!window.i18nLang) return key
        return (window.makerCvI18nObj[window.i18nLang] && window.makerCvI18nObj[window.i18nLang][key]) || key;
      },
    };
    // form-maker 国际化变量
    window.makerFmI18nObj = {};
    window.makerFmI18n = {
      t:function(key){
        // console.log('form-maker-i18n-', key)
        if(!window.i18nLang) return key
        return (window.makerFmI18nObj[window.i18nLang] && window.makerFmI18nObj[window.i18nLang][key]) || key;
      }
    }
  </script>
  <!-- 实现菜单管图标可用部门内部标准图标库,更新时使用Symbol访问在线链接复制js代码到csm-iconfont.js文件即可 -->
  <script type="text/javascript" src="<%= BASE_URL %>iconfont/csm-iconfont.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>iconfont/iconfont.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>environment.js"></script>
  <script src="<%= BASE_URL %>ca/scripts/dhccertauth/js/common.func.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>hos_head.js" id="hos_head"></script>
  <script type="text/javascript" src="<%= BASE_URL %>config.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>schema.js"></script>
  <script type="text/javascript" src="<%= BASE_URL %>case-view-maker/render-i18n.js"></script>
  <% } %>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <% if (htmlWebpackPlugin.options.mode !== 'his') { %>
  <img id="login-loading" src="<%= BASE_URL %>static/loading.png" alt="" />
  <% } %>
  <div id="app"></div>
  <!-- built files will be auto injected -->
  <!-- <script src="<%= BASE_URL %>static/languagePark.js"></script> -->
  <% if (htmlWebpackPlugin.options.mode !== 'his') { %>
  <script type="text/javascript" src="<%= BASE_URL %>hos_body.js" id="hos_body"></script>
  <script src="<%= BASE_URL %>case-view-maker/lib/ace/ace.js"></script>
  <script src="<%= BASE_URL %>case-view-maker/js/Maker.umd.js"></script>
  <script src="<%= BASE_URL %>workbench-maker/WorkbenchMaker.umd.js"></script>
  <% } %>
</body>

</html>