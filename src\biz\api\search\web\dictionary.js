

// 查询字段ES建议词
export const getESSuggest = (params) => {
  return {
    url: "search/elastic-api/select-suggest",
    method: "GET",
    params
  }
}

export const getSubDictItem = (data) => {
  return {
    url: 'search/es/dict-item/list',
    method: 'post',
    data
  }
}

export const getSuggestionByKey = (params) => {
  return {
    url: 'search/es/dict-item/select-by-id-suggest',
    method: 'GET',
    params
  }
}

export const getFilterFieldDicts = (params) => {
  return {
    url: 'search/export-filter-item/dict/select-by-code',
    method: 'GET',
    params
  }
}

