﻿var indexDefOrgID;

//所有页签数据
var tabs = [
    {id:"servmgr",title:"系统配置",src:"dhc.certauth.cfg.servmgr.html"},            //全部参数配置，拆分为下面两个菜单
    {id:"sysmgr",title:"系统配置",src:"dhc.certauth.cfg.servmgr.html?action=sysmgr"},
    {id:"statusmgr",title:"签名开关配置",src:"dhc.certauth.cfg.servmgr.html?action=statusmgr"},
    {id:"servreg",title:"签名服务管理",src:"dhc.certauth.cfg.servreg.html"},
    {id:"deptmgr",title:"签名科室管理",src:"dhc.certauth.cfg.deptmgr.html"},
    {id:"rolemgr",title:"签名角色管理",src:"dhc.certauth.cfg.rolemgr.html"},
    {id:"actionmgr",title:"签名操作管理",src:"dhc.certauth.cfg.actionmgr.html"},
    {id:"certmgr",title:"签名证书管理",src:"dhc.certauth.cfg.certmgr.html"},
    {id:"certreg",title:"签名证书关联",src:"dhc.certauth.cfg.certreg.html"},
    //{id:"caidmgr",title:"CA标识管理",src:"dhc.certauth.cfg.caidmgr.html"},        //不展示，后续考虑去掉
    {id:"usermgr",title:"签名用户管理",src:"dhc.certauth.cfg.usermgr.html"},    //用户管理，默认不展示
    {id:"test",title:"测试服务用",src:"dhc.certauth.test.html"}
]

//获取菜单配置信息
var urlParams = ca_common_tools.getParams();
var globalInfo = {
    Tabs: (typeof(urlParams.TabOptions) == "undefined" || urlParams.TabOptions == "") ? "sysmgr|statusmgr|servreg|deptmgr|rolemgr|actionmgr|certmgr|certreg|usermgr|test":urlParams.TabOptions
}

//url转换
function formatSrc(src) {
    if (src.indexOf("?") > -1) {
        src = src + "&MWToken=" + ca_common_tools.getMWToken();
    } else {
        src = src + "?MWToken=" + ca_common_tools.getMWToken();
    }
    return src;
}

//初始化页签
function initTabs() {
    var langID = ca_common_tools.getSession().LangID;
    var data = {
        action: "GET_DEFAULTORGID",
        params: {
            langID: langID
        }
    };
	var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
		indexDefOrgID = json.data.defaultOrgID || "";
	}

    for(i=0;i<tabs.length;i++ ) {
        if (globalInfo.Tabs.indexOf(tabs[i].id) == "-1")
            continue;

        var src = formatSrc(tabs[i].src);
        var content = "<iframe id='fram" + tabs[i].id + "' frameborder='0' src='"+ src +"' style='width:100%; height:99%;scrolling:no;margin:0px;'></iframe>"
        $("#mainTab").tabs("add",{
            id:       "tab"+tabs[i].id ,
            title:    ca_common_tools.trans(tabs[i].title),
            content:  content,
            closable: false,
            selected: false
       });
    }
    $("#mainTab").tabs("select",0);
}

$(function() {
    initTabs();
});