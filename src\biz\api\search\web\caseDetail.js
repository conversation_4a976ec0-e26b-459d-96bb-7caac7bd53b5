export function getConfig(params) {
  return {
    // url: "search/case-detail/config/select-by-indexid-configtype/" + params.indexId + '/' + params.configType,
    url:'search/case-detail/config/select-by-indexId-configType',
    method: "get",
    params
  }
}

export function getCaseDetailValue({url,data}){
  return {
    url,
    method: 'post',
    data
  }
}

export function requestHttp(data){
  return {
    url: '/search/struct/request-http',
    method: 'post',
    data
  }
}

export function requestContentHttp(data){
  return {
    url: '/search/struct/request-http-content',
    method: 'post',
    data
  }
}

export function getDynamicLink(url){
  return {
    url,
    method: 'get',
  }
}
