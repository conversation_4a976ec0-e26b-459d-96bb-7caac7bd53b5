﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//获取页面传参信息
var urlParams = ca_common_tools.getParams();
var globalInfo = {
    VenderCode: urlParams.VenderCode || "",
    SignTypeCode: urlParams.SignTypeCode || "",
    OrgID: urlParams.OrgID || ""
}

//页面数据
var toSignData = "测试待签数据123ABCabc";
var hisUserName = "";
var hisUserCode = "";
var hisUserID = "";
var hashData = "";
var hashType = "";
var signedData = "";
var signCert = "";
var certNo = "";
var userCertCode = "";
var signImage = "";

if ((globalInfo.VenderCode != "")&&(globalInfo.SignTypeCode != "")) {
    document.write("<script src='../scripts/dhccertauth/ics/ca_common_"+ globalInfo.VenderCode.toLowerCase() +"_"+globalInfo.SignTypeCode.toLowerCase()+".js'><\/script>");
}

$(function() {
    initBTN();
    $("#keys").combobox({
        valueField: "id",
        textField: "text",
        editable: false,
        panelHeight: "auto",
		onSelect:function() {
			var certContainer = $("#keys").combobox("getValue");
            console.time("获取证书是否已登录耗时");
            var callBackFunc = function(isLoginResult) {
                console.timeEnd("获取证书是否已登录耗时");
                if ("0" !== isLoginResult.code) {
                    displayError("获取证书是否已登录失败:"+isLoginResult.msg);
                    return;
                } 
                var isLogin = isLoginResult.data.isLogin;
                if (isLogin) {
                    $("#password").hide();
                } else {
                    $("#password").show();
                }
            }
            ca_key.isLogin({certContainer:certContainer,callBackFunc:callBackFunc});
		}
    });

    //页面加载完成后，自动加载UKey列表
    GetCAUserList();
});

function initBTN() {
    $("#btnSubmit").click(function(){caSign();});
    $("#btnLoadCert").click(function(){GetCAUserList();});
    $("#brnReloadPage").click(function(){reload();});
}

//开始签名流程
function caSign() {
    var certContainer = $("#keys").combobox("getValue");
    if ("" == certContainer) {
        displayError("请选择UKey");
        return;
    }
    var password = $("#txtPassword").val();
    if (("" == password)&&(!$('#password').is(':hidden'))) {
        displayError("请输入密码");
        return;
    }

    console.log("开始签名流程:"+getCurrentTime());//输出到控制台

    console.time("证书登录耗时");
    var callBackFunc = function(loginResult) {
        console.timeEnd("证书登录耗时");
        if ("0" !== loginResult.code) {
            displayError("证书登录失败:"+loginResult.msg);
            return;
        }
        
        console.log("登录成功:"+getCurrentTime());//输出到控制台
        hisUserName = loginResult.data.userName;
        hisUserCode = loginResult.data.userCode
        hisUserID = loginResult.data.userID;
        signImage = loginResult.data.signImage;

        //产品组无需单独调用hash接口，测试无误后暂不开放相关接口
        /*
        console.time("Hash耗时");
        var hashResult = ca_key.hashData({tohashData:toSignData});
        console.timeEnd("Hash耗时");
        if ("0" !== hashResult.code) {
            displayError("Hash接口调用失败:"+hashResult.msg);
            return;
        }
        console.log("Hash数据成功:"+getCurrentTime());//输出到控制台
        */

        console.time("签名耗时");
        var callBackFunc = function(signedDataResult) {
            console.timeEnd("签名耗时");
            if ("0" !== signedDataResult.code) {
                displayError("数据签名失败:"+signedDataResult.msg);
                return;
            }
            console.log("签名数据成功："+getCurrentTime());//输出到控制台
            
            hashData = signedDataResult.data.hashData;
            hashType = signedDataResult.data.hashType;
            signedData = signedDataResult.data.signedData;
            signCert = signedDataResult.data.signCert;
            certNo = signedDataResult.data.certNo;
            userCertCode = signedDataResult.data.userCertCode;
            var digitalSignID = signedDataResult.data.digitalSignID;
            var timeStampData = signedDataResult.data.timeStampData;
            var signDataTime = signedDataResult.data.signDateTime;
            //签名成功，展示相关数据
            ShowSucessInfo(timeStampData,signDataTime,digitalSignID);
        }
        ca_key.hashAndSignedData({
            toSignData:toSignData,
            hashType:"SM3Hex",
            certContainer:certContainer,
            organizationID:globalInfo.OrgID,
            actionCode:"Test",
            episodeID:"",
            businessID:"",
            getSignDetail:true,
            callBackFunc:callBackFunc
        });
    }
    ca_key.login({
        certContainer:certContainer,
        password:password,
        organizationID: globalInfo.OrgID,
        imageType:"Original",
        callBackFunc:callBackFunc
    });
}

//签名成功后展示相关数据
function ShowSucessInfo(timeStampData,signDateTime,digitalSignID) {
    if (digitalSignID == "") {
        displayError("签名数据存储失败");
    } else {
        var signInfo = "【Hash前签名数据】：" + "\n&#09;" + toSignData;
        signInfo = signInfo + "\n"+ "【Hash算法】：" + "\n&#09;" + hashType;
        signInfo = signInfo + "\n"+ "【Hash算法生成值】：" + "\n&#09;" + hashData + "\n";
        signInfo = signInfo + "\n"+ "【CA用户签名证书】：" + "\n&#09;" + signCert;
        signInfo = signInfo + "\n"+ "【CA证书唯一标识】：" + "\n&#09;" + certNo;
        signInfo = signInfo + "\n"+ "【CA用户唯一标识】：" + "\n&#09;" + userCertCode + "\n";
        signInfo = signInfo + "\n"+ "【签名数据存储表ID】：" + "\n&#09;" + digitalSignID;
        signInfo = signInfo + "\n"+ "【CA签名值】：" + "\n&#09;" + signedData.signValue;
        signInfo = signInfo + "\n"+ "【CA签名格式】：" + "\n&#09;" + signedData.pkcsType;
        signInfo = signInfo + "\n"+ "【CA时间戳】：" + "\n&#09;" + timeStampData;
        signInfo = signInfo + "\n"+ "【时间戳解析时间】：" + "\n&#09;" + signDateTime + "\n";
        signInfo = signInfo + "\n"+ "【签名图片base64】：" + "\n&#09;" + signImage;
        signInfo = signInfo + "\n"+ "【关联HIS用户ID】：" + "\n&#09;" + hisUserID;
        signInfo = signInfo + "\n"+ "【关联HIS用户工号】：" + "\n&#09;" + hisUserCode;
        signInfo = signInfo + "\n"+ "【关联HIS用户名】：" + "\n&#09;" + hisUserName;
        signInfo = signInfo.replace(/\n/g, "<br>");
        signInfo = signInfo.replace(/&#09;/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
        displayOK(signInfo);

        if ((signImage !="")&&(signImage != null))
            document.getElementById("image").src ="data:image/jpg;base64,"+signImage;
    }
}

///获取并填充证书列表
function GetCAUserList() {
    console.time("获取证书列表耗时");
    var callBackFunc = function(certListResult) {
        console.timeEnd("获取证书列表耗时");
        console.log("获取证书列表成功："+getCurrentTime());//输出到控制台

        if ("0" !== certListResult.code) {
            displayError("获取证书列表失败:"+certListResult.msg);
            return;
        }

        var lst = certListResult.data.list;
        if ("" == lst || 0 == lst.length) {
            displayError("未检测到证书");
            return;
        }

        var data = new Array();
        for (var i = 0; i < lst.length; i++) {
            data.push({"id":lst[i].certContainer,"text":lst[i].name});
        }
        $("#keys").combobox("loadData", data);
    }
    ca_key.getUserList(callBackFunc);
}

//展示成功信息
function displayOK(info) {
    $("#caTip").hide();
    $("#signInfo").show();
    document.getElementById("caTip").innerHTML = "";
    document.getElementById("signInfo").innerHTML = ca_common_tools.trans(info);
}

//展示错误信息
function displayError(errorInfo) {
    $("#caTip").show();
    $("#signInfo").hide();
    document.getElementById("caTip").innerHTML = ca_common_tools.trans(errorInfo);
    document.getElementById("signInfo").innerHTML = "";
}

//重置页面
function reload() {
    hisUserName = "";
    hisUserID = "";
    hashData = "";
    hashType = "";
    signedData = "";
    signCert = "";
    certNo = "";
    userCertCode = "";
    signImage = "";
    $("#txtPassword").val("");
    $("#keys").combobox("clear");
    $("#image").src = "";
    document.getElementById("image").src ="";

    GetCAUserList();
    $("#caTip").hide();
    $("#signInfo").hide();
    document.getElementById("caTip").innerHTML = "";
    document.getElementById("signInfo").innerHTML = "";
}