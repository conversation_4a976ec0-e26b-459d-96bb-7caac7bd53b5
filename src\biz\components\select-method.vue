<template>
  <hos-card style="width: 273px;margin-right:10px;flex:none;" shadow="never">
    <div slot="header" v-if="title">{{ title }}</div>
    <div class="method-box">
      <template v-for="(item, index) in methodList">
        <div v-if="!item.children" :key="index" class="method-item" :class="{ active: activeMethod.label === item.label }"
          @click="handleNodeClick(item)">{{ item.label }}</div>
        <div v-else :key="index" class="method-group">
          <div class="method-group-name">{{ item.label }}</div>
          <div class="method-box">
            <div v-for="(child, i) in item.children" :key="i" class="method-item"
              :class="{ active: activeMethod.label === child.label }" @click="handleNodeClick(child)">{{ child.label }}</div>
          </div>
        </div>
      </template>
    </div>
  </hos-card>
</template>

<script>
import { _debounce } from "@/utils/throttle.js"
export default {
  props: {
    title: {
      type: String
    },
    methodList: {
      type: Array,
      default() {
        return []
      }
    },
    activeMethod: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.init()
  },
  methods: {
    init() {
    },
    handleNodeClick: _debounce(function(node) {
      this.$emit("selectNode", node)
    }, 200),
  },
}
</script>
<style lang="scss" scoped>
.method-box {
  display: flex;
  flex-wrap: wrap;
  .method-item {
    display: inline-block;
    padding: 10px 5px;
    margin: 5px;
    width: calc(50% - 22px);
    border: 1px solid #ccc;
    text-align: center;
    font-size: 14px;
    color: #555;
    border-radius: 3px;
    cursor: pointer;

    &:hover,
    &.active {
      background-color: #2899ff;
      color: #fff;
      border: 1px solid #2899ff;
    }
  }

  .method-group {
    width: 100%;
    border-top: 1px solid #ccc;

    &:first-child {
      margin-bottom: 10px;
      border-top: 0;
      .method-group-name{
        margin-top: 0;
      }
    }
  }

  .method-group-name {
    border-bottom: 1px solid #ccc;
    color: #555;
    text-align: center;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 5px;

  }
}</style>
