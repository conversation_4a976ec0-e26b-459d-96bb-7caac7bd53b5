
export const queryListApi = (params) => {
    return {
      url: '/wx/mp/api/v1/user/list',
      method: 'get',
      params,
    }
  }

export const addApi = (data) => {
  return {
    url: `/wx/mp/api/v1/user/add`,
    method: 'post',
    data
  }
}

export const detailApi = (id) => {
  return {
    url: `/wx/mp/api/v1/user/info/${id}`,
    method: 'get'
  }
}

export const checkIdCardOrTel = (data) => {
  return {
    url: `/wx/mp/api/v1/user/judge`,
    method: 'post',
    data
  }
}

export const editApi = (data) => {
  return {
    url: `/wx/mp/api/v1/user/update`,
    method: 'post',
    data
  }
}

export const batchDelete = (data) => {
  return {
    url: `/wx/mp/api/v1/user/deletion`,
    method: 'post',
    data
  }
}
