
export function getAccessUser() {
  return {"configs":{"license-enable":"false","on-search-home-input":"true","show-search-history":"true","hospital-code":"TEST","hospital-addchn":"测试医院","hospital-name":"科研平台","on-synonym":"false","hospital-abb":"TEST","hospital-watermarkcolor":"rgba(82,81,81,0.25)","system-name":"科研平台","on-search-synonym":"false","enable-change-password":"true","watermark-enable":"false","license-organization":"测试医院"},"deptName":"","hospitalInfo":{"abb":"TEST","code":"TEST","name":"科研平台","watermarkColor":"rgba(82,81,81,0.25)","addchn":"测试医院"},"isEdcAdmin":true,"roles":["admin"],"isSuperAdmin":false,"isUumsAdmin":true,"licenseInfo":{"productVersion":"3.3.7","machineCode":"","licenseAuthorizeUnit":"测试医院","licenseAuthorizeModule":"正常","licenseAuthorizeDate":"永久","licenseAuthorizeType":"正式版","licenseAuthorizePurpose":"正式","licenseCode":"","productName":"iMedical BDE"},"accessOrgsList":[{"orgName":"演示医院","outOrgCode":"","outOrgParentCode":"","orgLevel":"","remark":"演示医院创建于1963年，是“七五”期间全国七所重点项目建设的中医院之一，2008年入选国家中医临床研究基地，2017年入选“国家中医药传承创新工程”项目建设单位，2022年入选“国家中医药传承创新中心”项目储备库、国家特色服务出口基地。","updateTime":"2023-06-06 11:36:38","version":48,"enabled":1,"deleteFlag":0,"orgParentName":"","orgType":"医院","createBy":"admin","createTime":"2022-07-17 17:01:51","updateBy":"admin","orgCode":"HNZYFY","id":"1","orgParentCode":""}],"enableChangePassword":true,"userId":"1","authorities":["home:view","datadetail:view","datadetail:query","search:view","search:analyzer","search:joinproject","quicksearch:search","advanced:search","advanced:update","advanced:delete","searchTemp:query","searchTemp:insert","searchTemp:update","searchTemp:delete","exportTemp:query","exportTemp:insert","exportTemp:update","exportTemp:delete","dslSearch:view","dslSearch:query","dslSearch:insert","dslSearch:update","dslSearch:delete","exportRecord:view","exportRecord:query","exportRecord:delete","exportRecord:apply","elasticmanage:view","elasticmanage:query","elasticmanage:insert","elasticmanage:update","elasticmanage:delete","approveRecord:view","approveRecord:query","approveRecord:update","approveRecord:delete","admin:view","esop:view","esdataview:view","elasticdslquery:view","elasticdslquery:query","elasticdslquery:insert","modelIndex:view","esIndexManage:view","esIndexManage:query","esIndexManage:update","esIndexManage:insert","esIndexManage:delete","esIndexManage:download","esIndexManage:upload","esThemeManage:view","esThemeManage:query","esThemeManage:insert","esThemeManage:update","esThemeManage:delete","esPropertyManage:view","esPropertyManage:query","esPropertyManage:insert","esPropertyManage:update","esPropertyManage:delete","relativeDatatype:view","relativeDatatype:query","relativeDatatype:insert","relativeDatatype:update","relativeDatatype:delete","exportconfig:view","observePeriod:query","observePeriod:insert","observePeriod:update","observePeriod:delete","observeField:query","observeField:insert","observeField:update","observeField:delete","exportitem:query","exportitem:insert","exportitem:update","exportitem:delete","exportSpec:query","exportSpec:insert","exportSpec:update","exportSpec:delete","exportProperty:query","exportProperty:insert","exportProperty:update","exportProperty:delete","exportModel:view","exportModel:query","exportModel:insert","exportModel:update","exportModel:delete","permission:view","themePermission:query","themePermission:insert","themePermission:update","themePermission:delete","propertyPermission:query","propertyPermission:insert","propertyPermission:update","propertyPermission:delete","roleData:query","roleData:insert","roleData:update","roleData:delete","sysConfigManage:view","sysConfigManage:query","sysConfigManage:insert","sysConfigManage:update","sysConfigManage:delete","esitemdic:view","esitemdic:query","esitemdic:update","esitemdic:insert","esitemdic:delete","tagconfig:view","tagconfig:query","tagconfig:insert","tagconfig:update","tagconfig:delete","termset:view","apiterm:query","cacheclear:view","file:operate","casedetailconfig:insert","casedetailconfig:update","casedetailconfig:delete","casedetailconfig:query","casedetailconfig:view","logs:view","logs:query"],"token":"","foreignId":"","realName":"系统管理员","password":"","appList":[{"iconName":"","appName":"首页","appApiUrl":"","remark":"","appStatus":0,"appManageMainPageUrl":"","isCurrentApp":false,"iconBase":"","appWebsiteMainPageUrl":"http://114.242.246.248:8081/uums/","appType":1,"isDefaultApp":0,"sortNum":0,"id":"0","appTag":"uums"},{"iconName":"","appName":"数据探索","appApiUrl":"http://114.242.246.248:8081/csm-search/login","remark":"","appStatus":1,"appManageMainPageUrl":"http://114.242.246.248:8081/csm-search/login","isCurrentApp":true,"iconBase":"","appWebsiteMainPageUrl":"http://114.242.246.248:8081/csm-search/login","appType":1,"isDefaultApp":1,"sortNum":0,"id":"14","appTag":"MoX2E6r9"},{"iconName":"d52717765b3f4e92a362ddb7ea4c71a6.png","appName":"我的项目","appApiUrl":"http://114.242.246.248:8081/boot-admin-hos/sso/oauth2/login","remark":"","appStatus":1,"appManageMainPageUrl":"http://114.242.246.248:8081/boot-admin-hos/sso/oauth2/login","isCurrentApp":false,"iconBase":"","appWebsiteMainPageUrl":"http://114.242.246.248:8081/boot-admin-hos/sso/oauth2/login","appType":1,"isDefaultApp":0,"sortNum":0,"id":"15","appTag":"qoP9hFgA"},{"iconName":"d9ab6369df5442fabc21ff0182a6c30b.png","appName":"心内科专病库","appApiUrl":"http://114.242.246.248:8081/csm-search/login?modelCode=02","remark":"","appStatus":1,"appManageMainPageUrl":"http://114.242.246.248:8081/csm-search/login?modelCode=02","isCurrentApp":false,"iconBase":"","appWebsiteMainPageUrl":"http://114.242.246.248:8081/csm-search/login?modelCode=02","appType":1,"isDefaultApp":0,"sortNum":0,"id":"17","appTag":"ptfqI13e"},{"iconName":"c3893b641b0e4950acb4af37bb62a3d6.png","appName":"数据治理","appApiUrl":"http://114.242.246.248:8081/datagover/login","remark":"","appStatus":1,"appManageMainPageUrl":"http://114.242.246.248:8081/datagover/login","isCurrentApp":false,"iconBase":"","appWebsiteMainPageUrl":"http://114.242.246.248:8081/datagover/login","appType":1,"isDefaultApp":0,"sortNum":0,"id":"18","appTag":"b9R0IaQ8"},{"iconName":"","appName":"在线分析","appApiUrl":"http://106.63.4.5:8085/#/auth?signature=${signature}&target=_blank","remark":"","appStatus":1,"appManageMainPageUrl":"http://106.63.4.5:8085/#/auth?signature=${signature}&target=_blank","isCurrentApp":false,"iconBase":"","appWebsiteMainPageUrl":"http://106.63.4.5:8085/#/auth?signature=${signature}&target=_blank","appType":1,"isDefaultApp":0,"sortNum":0,"id":"25","appTag":"3H9uJuNH"}],"loginName":"admin","orgs":[]}
}
