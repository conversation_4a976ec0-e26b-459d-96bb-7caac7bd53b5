// 纳排查询
export const getQueryBySubjectId = (data) => {
    return {
        url: `edc/es-search/search-for-patient`,
        method: 'POST',
        params:{
            subjectId:data.subjectId
        },
        data
    }
}

// 根据插排条件导出查询结果
export const exportForSearchResult = (data) => {
    return {
        url: `edc/es-export/export-for-search?subjectId=${data.subjectId}&exportTypeCode=${data.exportTypeCode}&exportFileAddress=${data.exportFileAddress}`,
        method: 'POST',
        data
    }
}

// 保存查询结果到数据分析
export const saveSearchResult = (data) => {
    return {
        url: `proj/subProject/save/search/result`,
        method: 'POST',
        data
    }
}

// 获取患者属性
export const getAttributes = (params) => {
    return {
        url: `patient/attribute/attributes`,
        method: 'GET',
        params
    }
}

// 获取子项目随访计划信息
export const getFollowupPlanBySubjectId = (params) => {
    return {
        url: `edc/followup-plan/select-model-by-subject/${params.subjectId}`,
        method: 'GET'
    }
}

// 生成数据集excel文件
export const generateDataSet = (data) => {
    return {
        url: `edc/data-set/insert`,
        method: 'POST',
        data
    }
}

// 分页查询历史
export const getSearchHistory = (params) => {
    return {
        url: `edc/search-history/page`,
        method: 'GET',
        params
    }
}

// 保存查询历史
export const saveSearchHistory = (data) => {
    return {
        url:  `edc/search-history/insert`,
        method: 'POST',
        data
    }
}

// 删除查询历史
export const deleteSearchHistory = (data) => {
    return {
        url: `edc/search-history/deletion`,
        method: 'POST',
        data
    }
}

// 更新项目用户信息
export const updateProjUser = (params) => {
    return {
        url: `proj/user`,
        method: 'POST',
        params
    }
}

export const getFormCateItems = (params) => {
    return {
        url: `edc/subject-form/tree`,
        method: 'GET',
        params
    }
}

export const getDeFormCateItems = (params) => {
    return {
        url: `edc/subject/deform/connect`,
        method: 'GET',
        params
    }
}