export const queryListApi = (params) => {
  return {
      url: 'edc/report-interface-config/page',
      method: 'get',
      params,
  }
}

export const getAll = (params) => {
  return {
      url: 'edc/report-interface-config/list',
      method: 'get',
      params,
  }
}

export const addApi = (data) => {
  return {
      url: 'edc/report-interface-config/insert',
      method: 'post',
      data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/report-interface-config/update',
    method: 'post',
    data
  }
}

export const deleteBatchApi = (data) => {
  return {
      url: 'edc/report-interface-config/deletion',
      method: 'POST',
      data
  }
}

export const detailApi = (id) => {
  return {
      url: 'edc/report-interface-config/detail/' + id,
      method: 'GET'
  }
}

export const runInterfaceApi = (config) => {
  return {
      ...config
  }
}