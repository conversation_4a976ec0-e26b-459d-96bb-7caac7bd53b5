.app-wrapper{
  height: 100%;
  background: #fff;
  position: relative;
  // 左侧需要留出树的空间的布局
  &.app-wrapper-left {
    .app-container-left {
      width: 300px !important;
      border-right: 1px solid #ddd;
      height: 100%;
    }

    >.app-container {
      width: calc(100% - 300px - 30px) !important;
      left: 310px;
      position: absolute;
      top: 0;
    }
  }
}

.app-dialog {
  .hos-dialog__body {
    padding: 10px 20px 0;
  }
}

.app-dialog {
  .dialog-container {
    width: 100%;
    min-height: 485px;
    display: flex;

    .left-user-list {
      width: 50%;
      border-right: 1px solid rgb(220, 223, 230);

      .left-top {
        height: 40px;
        padding-top: 10px;

        .input-item {
          width: calc(100% - 80px);
        }
      }

      .left-bottom {

        .hos-tabs__header,
        .hos-tabs__content {
          box-shadow: none;
        }

        #scroll-area {
          max-height: 400px;
          overflow-y: scroll;
          padding: 4px 0;
        }
      }
    }

    .user-item {
      height: 30px;
      line-height: 30px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;

      &:hover {
        background: #f3f3f7;
        cursor: pointer;
      }

      .name-span {
        margin-left: 10px;
        display: flex;
        width: calc(100% - 50px);

        .circle {
          flex: none;
          display: inline-block;
          width: 30px;
          height: 30px;
          overflow: hidden;
          text-align: center;
          border-radius: 50%;
          color: #fff;
          background: #e98f87;
          margin-right: 10px;
        }

        .username-span {
          color: black;
          margin-right: 10px;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .username-span-en {
          font-size: 12px;
          flex-shrink: 0;
          flex-basis: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #999;
        }
      }

      .check-span {
        i {
          margin-right: 10px;
          font-size: 30px;
          color: #67c23a;
        }
      }
    }

    .user-item.is-selected {
      background: #f0f9eb;
      color: #67c23a;
    }

    .right-select {
      width: 50%;
      padding: 15px 15px 0;

      .user-chosen {
        margin-top: 20px;
        height: 415px;
        overflow: scroll;

        .name-span {
          background: #f0f9eb;
          display: flex;
          position: relative;
          line-height: 30px;
          margin-bottom: 5px;

          .circle {
            flex: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            margin-left: 5px;
            text-align: center;
            border-radius: 50%;
            color: #fff;
            background: #e98f87;
            margin-right: 10px;
          }

          .username-span {
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            height: 30px;
            color: black;
          }

          .delete-span {
            position: absolute;
            right: 5px;

            &:hover {
              cursor: pointer;
            }

            i {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: center;
    display: inline-block;
    width: 100%
  }

  // 权限弹出框和固定高度的弹出框
  &.auth-dialog,
  &.fix-height-dialog {
    .hos-dialog {
      height: 80vh;

      .hos-dialog__body {
        height: calc(100% - 56px - 48px);
        overflow-y: auto;
      }
    }
  }

  .wide-content {
    padding-bottom: 20px;

    .wide-title {
      margin: 10px 0 5px;

      .wide {
        color: #666;
        font-weight: 500;
        margin-right: 0;
      }
    }

    .wide-box {
      padding: 5px 10px 10px 0;
      color: #999;

      .wide {
        color: #666;
        font-weight: 500;
        margin-right: 0;
      }
    }
  }

}


.popover-content {
  padding: 10px;
}


// 修复hos ui 单选框控件错位问题， 项目中需要用一个div包起来，然后添加该class
.form-hos-radio {
  display: flex;
  align-items: center;
  line-height: 30px;
  height: 30px;
}

// 修复hos ui  日期范围选择框中间字符偏下的问题
.hos-date-editor>.hos-range-separator {
  text-align: center !important;
  display: contents !important;
}

// 统一用户首页
.overview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;

  .top-container {
    width: 100%;
    height: 36px;
    position: relative;
    display: flex;
    width: 100%;
    background: linear-gradient(to right, #0979de, #47a8ff);

    .admin-title {
      height: 36px;
      flex-basis: 200px;
      flex-shrink: 0;
      flex-grow: 0;
      text-align: center;
      width: 100%;
      z-index: 10;
      font-size: 16px;
      line-height: 36px;
      color: #fff;
    }

    .overview-nav {
      height: 36px;
      width: 100%;

      .hos-menu {
        height: 36px !important;
        line-height: 36px;
        border-radius: 0px;
        background: unset;

        // 隐藏
        .hamburger-container {
          display: none !important;
        }
      }
    }
  }

  .bottom-main-content {
    flex-grow: 1;
    background-color: #ececec;
    padding: 10px;
    padding-right: 0;
    overflow-y: scroll;

    // 圆角公共样式
    .radius-5 {
      border-radius: 5px;
    }

    .top-row {
      width: 100%;
      height: 100%;
      min-height: 400px;
      display: flex;

      .left {
        flex-grow: 1;
        margin-right: 10px;

        .simple-paper {
          min-width: 600px;
          height: 86px;
          width: 100%;
          padding: 10px;
          background: white;
          margin-bottom: 10px;
          display: flex;
          flex-direction: column;

          .title {
            font-size: 14px;

            .time-range {
              margin-left: 15px;
              font-size: 12px;
              color: #aeaeae;
            }

            .titleTime {
              color: grey;
              font-size: 12px;
              margin-left: 20px;
            }
          }

          .total-overview {
            flex-grow: 1;
            display: flex;
            align-items: center;

            // padding-left: 20px;
            .total-item {
              flex: 1;
              height: 100%;
              display: flex;
              flex-direction: column;
              // align-items: center;
              justify-content: center;

              .item-num {
                font-size: 25px;
                font-weight: 600;
                text-align: center;
                margin-bottom: 5px;
              }

              .item-describe {
                font-size: 14px;
                text-align: center;

                .zhiyi {
                  color: grey;
                  font-size: 12px;
                  cursor: pointer;
                }
              }
            }
          }
        }

        .three-pie-container {
          height: calc(45% - 36px);
          min-height: 320px;
          background: white;
          display: flex;
          align-items: center;

          .pie-chart {
            width: 33%;
            height: 300px;
          }

          .pie-chart.mini {
            // height: 220px;
          }
        }

        .visit-pat-num {
          height: calc(55% - 70px); // 两者之和加起来要是(100%-86px-20px) 100%-106px
          min-height: 360px;
          background: white;
          margin-top: 10px;

          .title {
            height: 48px;
            box-sizing: border-box;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .content {
            height: calc(100% - 48px);
            display: flex;
            justify-content: center;
            align-items: center;

            .charts-container {
              display: flex;
              width: 100%;
              height: 100%;

              .line-chart {
                width: 100%;
                height: 90%;
                min-height: 300px;
                // height: 300px;
              }
            }
          }
        }
      }

      .right {
        width: 380px;
        min-width: 380px;
        height: 100%;

        .right-top-user {
          height: 86px;
          background: white;
          margin-bottom: 10px;
          padding-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .line-top {
            height: 40px;
            display: flex;
            align-items: center;

            i {
              font-size: 20px;
              margin-right: 10px;
            }

            .user-name {
              font-size: 16px;
              margin-right: 20px;
              text-align: center;
            }

            .dept-name {
              color: #666666;
              font-size: 14px;
            }
          }

          .line-bottom {
            margin-top: 10px;
            font-size: 14px;
            color: #666666;
          }
        }

        .right-bottom-nav {
          padding: 10px;
          background: white;
          height: calc(100% - 96px);
          min-height: 690px;
          overflow-y: auto;

          .item {
            // height: 25%;
            padding-top: 10px;

            .des-and-more {
              height: 20px;
              display: flex;
              justify-content: space-between;

              span {
                font-size: 12px;
              }

              .more {
                cursor: pointer;
              }
            }
          }

          .system-nav {
            width: 100%;
            display: flex;
            padding: 0;
            margin-top: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;

            .system-nav-box {
              width: 33.33%;
              margin-bottom: 10px;

              .img-container {
                // 这个写法能够保证元素高度等于其动态宽度
                width: 96px;
                height: 96px;
                margin: 0 auto 10px;
                background: rgba(64, 158, 255, 0.35);
                color: #589cfc;
                border-radius: 15px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 48px;
                cursor: pointer;

                img {
                  width: 48px;
                }
              }

              .sys-name {
                display: inline-block;
                width: 100%;
                text-align: center;
              }
            }
          }

          .my-project {
            margin-bottom: 20px;

            .sub-item-box {
              .no-data {

                // color: #aeaeae;
                .hos-empty {
                  padding: 20px 0;
                }
              }

              span {
                display: block;
                background: #f2f2f2;
                padding: 10px;
                margin-bottom: 5px;
                font-size: 14px;
              }
            }
          }

          .my-export {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;

            .sub-item-box {
              flex-grow: 1;
              display: flex;
              flex-direction: column;

              .no-data {

                // color: #aeaeae;
                .hos-empty {
                  padding: 20px 0;
                }
              }

              .export-box {
                display: flex;
                justify-content: space-between;
                background: #f2f2f2;
                padding: 10px;
                margin-bottom: 5px;
                font-size: 14px;

                .export-name {
                  width: 200px;
                  color: #02a7f0;
                  padding-left: 0;
                  padding-right: 0;
                  white-space: nowrap;
                  /*内容超宽后禁止换行显示*/
                  overflow: hidden;
                  /*超出部分隐藏*/
                  text-overflow: ellipsis;
                  /*文字超出部分以省略号显示*/
                }
              }
            }
          }

          .my-search {
            padding: 0;
            display: flex;
            flex-direction: column;

            .sub-item-box {
              flex-grow: 1;
              display: flex;
              flex-direction: column;

              .no-data {

                // color: #aeaeae;
                .hos-empty {
                  padding: 20px 0;
                }
              }

              .search-item {
                background: #f2f2f2;
                padding: 10px;
                margin-bottom: 5px;
                font-size: 14px;
                display: flex;
                justify-content: space-between;

                .condition {
                  width: 200px;
                  color: #02a7f0;
                  white-space: nowrap;
                  /*内容超宽后禁止换行显示*/
                  overflow: hidden;
                  /*超出部分隐藏*/
                  text-overflow: ellipsis;
                  /*文字超出部分以省略号显示*/
                }

                .search-time {
                  text-align: right;
                }
              }
            }
          }
        }
      }
    }
  }
}


// 导出记录
.export-record,
.audit-record {
  color: #666;
  // min-height: 100%;
  height: 100%;
  background-color: #fff;

  .main-title {
    height: 50px;
    box-sizing: border-box;
    padding: 15px;
    border-bottom: solid 1px #bcbcbc;
  }

  .record-content {
    height: calc(100% - 80px);
    overflow-y: auto;
    width: 100%;
  }

  .filter-box {
    display: none; // 我的导出页面过滤容器,select屏蔽,创建时间挪位置,将容器隐藏掉
    // width: 100%;
    height: 30px;
    padding: 10px 10px 0;
  }

  .tips {
    height: 25px;
    width: calc(100% - 40px);
    background-color: rgb(236, 236, 236);
    margin: 0 20px;
    box-sizing: border-box;
    padding-left: 10px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 25px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // color: #000;
  }

  .record-item-box {
    .download-progress {
      position: fixed;
      bottom: 65px;
      left: 200px;
      width: calc(100% - 250px);
      height: 30px;
      background-color: #f2f2f2;
      border-radius: 5px;
      display: flex;
      align-items: center;
      margin-left: 15px;
      margin-top: 5px;
      padding: 0 10px;
      font-size: 14px;
    }

    .record-item {
      margin: 10px;
      margin-left: 15px;
      padding-bottom: 10px;
      border-bottom: solid 1px #bcbcbc;
      color: #888;
      font-size: 15px;
      display: flex;

      &.crouser-active {
        cursor: pointer;
      }

      .head-item {
        display: flex;
        justify-content: space-between;
      }

      .item-datetime {

        // display: inline-block;
        // flex-basis: 140px;
        // flex-grow: 0;
        // flex-shrink: 0;
        .item-date,
        .item-time {
          display: inline-block;
          margin: 0 10px 5px 0;
        }

        .item-date {
          color: #409EFF;
        }
      }

      .item-btn-group {
        margin-right: 10px;
      }

      .item-right {
        width: 100%;
      }

      .front-box {
        margin-bottom: 6px;
        display: flex;

        .box-label {
          color: #666;
          flex-basis: 75px;
          flex-shrink: 0;
        }

        .end-box {
          // margin-left: 10px;
          padding-right: 10px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .end-box-green {
          color: rgb(26, 173, 25);
        }

        .end-box-red {
          color: rgb(245, 108, 108);
        }
      }

      .file-name {
        color: #666;
        font-weight: 500;
        margin-bottom: 10px;
      }

      .condition-box,
      .target-box,
      .progress-box {
        // width: 100%;
        padding: 5px 10px;
      }
    }

    .hos-dialog {
      .avatar-uploader .hos-upload {
        margin-bottom: 20px;
        display: block;
        // margin-left:90px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;

        .hos-upload-dragger {
          width: 100%;
        }
      }

      .avatar-uploader .hos-upload:hover {
        border-color: #409EFF;
      }

      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
      }

      .avatar {
        width: 178px;
        height: 178px;
        display: block;
      }
    }
  }

}
.tab-title-line{
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  color:#606266;
  margin-top: 30px;
  margin-bottom: 10px;
  .tab-title-box{
    padding: 10px 10px;
    width: 25%;
    max-width: 280px;
    border-radius: 10px;
    background-color: #e5e5e5;
    margin: 20px 15px;
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    cursor: pointer;
    .tab-tile{
      text-align: center;
      margin-bottom: 20px;
      font-weight: bold;
      font-size: 16px;
    }
    .tab-desc{
      font-size: 14px;
      min-height: 45px;
    }
    .tab-title-icon{
      font-size: 55px;
    }
    &:hover{
      box-shadow: 0 0 5px 0;
      .tab-tile,.tab-title-icon,.tab-desc{
        color:#409EFF;
      }
    }
    &.active{
      .tab-tile,.tab-title-icon,.tab-desc{
        color:#fff;
      }
      background-color: #409EFF;
    }
  }
}
.var-item-box{
  .drag-show{
    display: none;
  }
  &.ghost{
    .drag-show{
      display: inline-block;
    }
    .drag-hide{
      display: none;
    }
  }
}
.select-var {
  .var-box {
    margin-top: 10px;
    overflow-y: auto;
    padding-bottom: 10px;
    .drag-show{
      display: none !important;
    }
    .drag-hide{
      display: flex !important;
    }
    .var-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 26px;
      padding: 0 5px;
      cursor: pointer;
      position: relative;
      font-size: 16px;
      border-radius: 5px;

      .var-item-name {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .var-missing-rate{
        white-space: nowrap;
        font-size: 12px;
        padding: 0 5px;
      }

      &:hover {
        background-color: #eff7ff;
        color: #4781f3;
      }
    }
  }
}

.drop-var{
  border: 1px dashed #ccc;
  border-radius: 5px;
  position: relative;
  .drop-var-list{
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px;
    min-height: 49px;
    position: relative;
    z-index: 1;
  }
  .var-item{
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .var-empty{
    position: absolute;
    z-index: 1;
    text-align: center;
    line-height: 26px;
    padding-top: 10px;
    color:#787979;
    width: calc(100% - 20px);
    user-select:none;
  }
}