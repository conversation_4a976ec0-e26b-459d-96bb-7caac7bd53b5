export const pageListApi = (params) => {
    return {
        url: `edc/export-record/page`,
        method: 'GET',
        params
    }
}

export const queryDetailApi = (params) => {
    return {
        url: `edc/export-record/detail/${params.id}`,
        method: 'GET'
    }
}

export const deleteApi = (data) => {
    return {
        url: `edc/export-record/deletion`,
        method: 'POST',
        data
    }
}

export const downloadData = (params) => {
    return {
        url: `edc/export-record/download`,
        methods: 'GET',
        responseType: 'blob',
        params
    }
}

export const getPassword = (id) => {
    return {
        url: `edc/export-record/get-paw/${id}`,
        method: 'GET'
    }
}