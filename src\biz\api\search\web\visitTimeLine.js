// import { encryptParams } from "@/utils/url"

export function getPatientInfo(params) {
  return {
    url: "search/advanced/patient/select-by-regno",
    method: "GET",
    params
  }
}

// 查询就诊数据
export function getCaseList(params) {
  return {
    url: "search/advanced/adm/list/select-by-regno",
    method: "GET",
    // params:encryptParams(params)
    params
  }
}

// 查询诊断数据
export function getDetailList(params) {
  return {
    url: "search/advanced/case-detail",
    method: "GET",
    // params:encryptParams(params)
    params
  }
}
