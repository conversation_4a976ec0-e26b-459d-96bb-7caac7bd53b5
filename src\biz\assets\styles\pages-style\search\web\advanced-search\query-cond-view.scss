.query-cond-view {
  .search-tips {
    height: 130px;
    line-height: 130px;
    width: 100%;
    font-size: 14px;
    text-align: center;
  }
  .hos-tabs__content {
    min-height: 130px;
  }
  .dialog-title {
    padding: 0px 10px 10px 20px;
    font-size: 21px;
    color: #000;
  }
  .iconStyle {
    cursor: pointer;
    font-size: 22px;
  }
  .selectResultBtn {
    background-color: white;
    cursor: pointer;

    &:hover {
      background-color: #ecf5ff;
    }
  }

  .queryBuilderBox {
    margin-left: 30px;
  }

  &.search-head {
    padding: 40px 10px;
    padding-bottom: 10px;
    // margin-bottom: 20px;
    background-color: #fff;

    .result-tag {
      margin: 0 5px 5px 0;
    }

    .flodBox {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .search-btns {
      position: absolute;
      right: 30px;
      top: 20px;
    }
  }

  .vue-codemirror {
    .CodeMirror {
      height: calc(100vh - 80px);
    }
  }
}
