
export const queryDetailApi = (params) => {
    return {
        code: 200,
        data: {

        }
    }
}

// 分页数据
export const queryListApi = (params) => {
    return {
        url: 'search/export/property/page',
        method: 'GET',
        params
    }
}

// 获取全部字段
export const queryWordListById = (params) => {
    return {
        url: 'search/es-property-metadata/page',
        method: 'GET',
        params
    }
}
// 新增
export const addApi = (data) => {
    return {
        url: 'search/export/property',
        method: 'POST',
        data
    }
}

export const updateApi = (data) => {
    return {
        url: 'search/export/property',
        method: 'PUT',
        data
    }
}

// 删除
export const deleteBatchApi = (data) => {
    return {
        url: 'search/export/property/delete/batch',
        method: 'POST',
        data
    }
}
