<template>
  <hos-dialog class="export-inline SearchItemSelect" :visible.sync="searchItemDialogVisible" :append-to-body="true" width="75%"
    top="7vh" @open="openHandler"
    @close="clickCancelBtn"
    >

    <!-- 顶部搜索框 start -->
    <div style="text-align: center;margin-bottom:10px;">
      <hos-input v-model="keyword" clearable class="inline-input search-item-input" :placeholder="$t('请输入内容')" @input="keywordChange" />
    </div>

    <scroll-menu ref="all_cate" :select-type="type" :all-tag="allTag" type="all_cate" :single-select="singleSelect"
      :height-light="keyword && keyword.trim() ? keyword.trim() : ''"
      :cate-items="searchItemDataLike?searchItemDataLike.cateItems:_searchItemData.cateItems"
      :field-data="searchItemDataLike?searchItemDataLike.fieldData:_searchItemData.fieldData"
      :common-search-items="commonSearchItems"
      :selectItems.sync="selectItems"
      @change-tag="changeTag" @closeSearchDialog="closeSearchDialog" />

    <span slot="footer" class="dialog-footer">
      <template v-if="!singleSelect">
        <hos-button @click="clickCancelBtn">{{ $t('取消') }}</hos-button>
        <hos-button type="primary" @click="closeSearchDialog">{{ $t('确定') }}</hos-button>
      </template>
    </span>
  </hos-dialog>
</template>

<script>
  import { _debounce } from "@/utils/throttle.js"
  import ScrollMenu from "@/components/querybuilder/ScrollMenu"

  export default {
    name:'SearchItemSelect',
    components: {
      ScrollMenu
    },
    props: {
      type: {
        type: String,
        default: "search"
      },
      // 是否单选，默认是
      singleSelect: {
        type: Boolean,
        default() {
          return true
        }
      },
      // 查询项大分类
      cateItems: {
        type: Array,
        default() {
          return []
        }
      },
      // 全部关联字段信息
      fieldData: {
        type: Array,
        default() {
          return []
        }
      },
      // 公共的指标项
      commonSearchItems: {
        type: Array,
        default() {
          return []
        }
      },
      loading: {
        type: Boolean,
        default() {
          return false
        }
      },
      filterTypeId: { // 过滤指定某个表id的字段
        type: [Number,String],
        default() {
          return 0
        }
      },
      // 判断是否默认选中的key
      defaultSelectKey: {
        type: String
      },
      useFor: {
        type: String,
        default: 'export' // export-导出, analysis-在线分析
      },
      // 选中项列表
      // selectItems: {
      //   type: Array,
      //   default: () => []
      // }
    },
    provide() {
      return {
        defaultSelectKey: this.defaultSelectKey
      }
    },
    inject: {
      "specIndexId": {
        from: "specIndexId",
        default: null
      }
    },
    data() {
      return {
        selectItems:[], // 选中项列表
        allTag: [],
        typeId: this.filterTypeId,
        searchItemDialogVisible: false,
        keyword: "",
        isInitActive: false,
        filedCateItems: [],
        esCateFiled: [],
        structCateFiled: [],
        esFields: [],
        convertFields: [],
        searchItemData: { // 全部字段
          cateItems: this.cateItems,
          fieldData: this.fieldData
        },
        searchItemDataLike: null, // 过滤后的字段
      }
    },
    computed: {
      _searchItemData() { // (过滤指定typeId的)
        let cateItems = this.searchItemData.cateItems
        if (this.typeId) {
          cateItems = cateItems.filter(cate => cate.cateId === this.typeId)
        }
        return {
          cateItems: cateItems,
          fieldData: this.searchItemData.fieldData
        }
      }
    },
    watch: {
      searchItemDialogVisible: function(value) {
        if (!value) return
      },
    },
    created() {
    },
    methods: {
      makeStructTree(data, keys) {
        const tmp_arr = []

        if (data && data.length > 0) {
          for (let c_idx = 0; c_idx < data.length; c_idx++) {
            const cate = data[c_idx]
            const tmp_cate = {
              cateId: cate.id || c_idx,
              cateName: cate.label,
              child: []
            }
            if ((cate.children && cate.children.length == 0) || !cate.children) {
              cate.children = [{
                id: cate.id,
                label: cate.label,
                children: []
              }]
            } else {
              if (typeof cate.children[0].id === "number") { // 子分类
              } else {
                const origin_children = cate.children
                cate.children = [{
                  id: cate.id,
                  label: cate.label,
                  children: origin_children
                }]
              }
            }
            const tmp_child = {
              childId: cate.id,
              childName: cate.label,
              item: []
            }
            for (let item_idx = 0; item_idx < cate.children.length; item_idx++) {
              const item = cate.children[item_idx]
              for (const key in keys) {
                keys[key] = item.data[key] ? item.data[key] : keys[key]
              }
              const tmp_item = {
                searchItemName: item.name,
                searchId: item.id,
                ...keys
              }
              tmp_child.item.push(tmp_item)
            }
            tmp_cate.child.push(tmp_child)
            tmp_arr.push(tmp_cate)
          }
        }
        return tmp_arr
      },
      handleNodeClick(item) {
        this.searchItemDialogVisible = false
        const curActiveItem = item ? item.data : null
        this.$emit("close-search-dialog", curActiveItem)
      },
      // 打开弹出框
      openSearchDialog(isInitActive, groupIndex, conditionIndex, subIndex, typeId) {
        // 如果传入true， 则初始化选中
        // 单选始终初始化选中
        this.typeId = typeId || this.filterTypeId

        this.isInitActive = isInitActive
        this.searchItemDialogVisible = true
        if (subIndex !== undefined) {
          // todo 子条件选择同个表中的字段
        }
      },
      // 打开弹框后
      openHandler() {
        this.keyword = ""
        this.searchItemDataLike = null
        this.initData()
        this.getApproveSearchItemLike()
        this.$refs["all_cate"] && (this.$refs["all_cate"].activeIndex = null)
        if (this.isInitActive || this.singleSelect) {
          this.$nextTick(() => {
            this.$refs["all_cate"] && (this.$refs["all_cate"].$emit('update:selectItems', []))

            this.$emit("opened")
          })
        } else {
          this.$emit("opened")
        }
        // 多选时设置了默认选中的选项
        if (!this.singleSelect && this.defaultSelectKey) {
          const defaultSelects = this.searchItemData.fieldData.filter(item => item[this.defaultSelectKey])
          defaultSelects.forEach(item => {
           const tmp = this.getParentByItem(item)
           if (tmp) {
            this.$nextTick(() => {
              this.$refs["all_cate"].clickItem(tmp.item, tmp.sub)
            })
           }
          })
        }
      },
      getParentByItem(item) {
        const cateItems = this.searchItemData.cateItems
         for (let i = 0; i < cateItems.length; i++) {
          const cate = cateItems[i]
          for (let j = 0; j < cate.child.length; j++) {
            const c = cate.child[j]
            for (let k = 0; k < c.item.length; k++) {
              const _item = c.item[k]
              if (_item.searchItemId === item.id) {
                return { item: _item, sub: c }
              }
            }
          }
        }
      },
      // 点击了取消按钮
      clickCancelBtn() {
        const activeItem = this.getSelectItems()
        this.searchItemDialogVisible = false
        if(activeItem){
          activeItem.doNotExport = true // 针对高级检索结果的原表导出，区分关闭弹窗和确定
        }
        this.$emit("close-search-dialog", activeItem)
      },
      // 关闭弹出框
      closeSearchDialog() {
        const activeItem = this.getSelectItems()
        this.searchItemDialogVisible = false
        this.$emit("close-search-dialog", activeItem)
      },
      // 获取全部选中的查询项
      getSelectItems() {
        if (this.singleSelect) {
          return this.$refs["all_cate"].getSelectItems()
        } else {
          const all_cate = this.$refs["all_cate"] ? this.$refs["all_cate"].getSelectItems() : []
          return [...all_cate]
        }
      },
      initData() {
        if (this.cateItems.length > 0) {
          this.searchItemData.cateItems = this.cateItems
        }
        if (this.fieldData.length > 0) {
          this.searchItemData.fieldData = this.fieldData
        }
        if (this.allTag.length === 0) {
          this.$api('biz.search.web.searchitem.searchitem.getAllTag',{ tagType: 1 }).then(res => {
            this.allTag = res.data || []
          })
        }
        if (this.searchItemData.cateItems.length === 0 || (this.$refs.all_cate && this.$refs.all_cate.selectedTags.length > 0)) {
          if (this.$refs.all_cate) {
            this.$refs.all_cate.selectedTags = []
          }
          if (this.type === 'search' || this.type === 'export') {
            this.getItemTreeByRoleCodeAndTag()
          }

        }
      },
      getItemTreeByRoleCodeAndTag(tags = []) {
        const params = {
          key: '',
          indexId: this.specIndexId ?? this.modelData.id
        }
        params.roleCode = this.userInfo.accountCode
        params.tagIds = tags.join(',')
        if (this.useFor === 'analysis') {
          params['isToAnalyzer'] = true
        }
        const promise = this.type === 'search' ? this.$api('biz.search.web.searchitem.searchitem.getSearchItemTreeByRoleCodeAndTag',params) : this.$api('biz.search.web.searchitem.searchitem.getExportItemTreeByRoleCodeAndTag',params)
        promise.then(res => {
          if (res) {
            if (res.success) {
              this.searchItemData = res.data
            } else {
              this.searchItemData = {
                cateItems: [],
                fieldData: []
              }
              this.$message.error(this.$t('获取查询项错误'))
            }
          } else {
            this.$message({ type: 'error', message: this.$t('获取查询项错误') })
          }
        })
      },
      getApproveSearchItemLike() {
        // 改成前端过滤
        if (this.keyword && this.keyword.trim()) {
          const cateItems = this._searchItemData.cateItems.map(cate => {
            return {
              ...cate,
              child: [{
                ...cate.child[0],
                item: cate.child[0].item.filter(it => it.searchItemName.indexOf(this.keyword.trim()) >= 0)
              }]
            }
          }).filter(cate => {
            return cate.child[0].item.length > 0
          })
          const fieldData = this.searchItemData.fieldData
          this.searchItemDataLike = { cateItems, fieldData }
        } else {
          this.searchItemDataLike = null
        }
        const boxDiv = document.getElementsByClassName('search-item-box')
        if (boxDiv && boxDiv.length > 0) {
          for (let i = 0; i < boxDiv.length; i++) {
            const box = boxDiv[i]
            box.scrollTop = 0
          }
        }
      },
      keywordChange: _debounce(function() {
        this.getApproveSearchItemLike()
      }),
      changeTag(selectedTags) {
        this.getItemTreeByRoleCodeAndTag(selectedTags)
      }

    }
  }
</script>
