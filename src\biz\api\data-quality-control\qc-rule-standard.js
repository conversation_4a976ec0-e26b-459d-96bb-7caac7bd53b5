export const queryListApi = (params) => {
    return {
        url: `medical-qc-rules/list/${params.current}/${params.size}`,
        method: 'get',
        params,
    }
}

export const addApi = (data) => {
    return {
        url: 'medical-qc-rules/add',
        method: 'post',
        data,
    }
}

export const editApi = (data) => {
    return {
        url: 'medical-qc-rules/update',
        method: 'post',
        data,
    }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'medical-qc-rules/del',
        method: 'post',
        data
    }
}

export const queryListByMeta = (params) => {
    return {
        url: `medical-qc-rules/query-by-metaId/${params.current}/${params.size}`,
        method: 'get',
        params,
    }
}