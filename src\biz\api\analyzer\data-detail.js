export const pageList = (data) => {
  return {
    url: `/analyzer/detail/data/page`,
    method: 'post',
    data
  }
}

export const list = (subsetId, params) => {
  return {
    url: `/analyzer/detail/data/list/${subsetId}`,
    method: 'get',
    params
  }
}

export const dataView = (data) => {
  return {
    url: `/analyzer/detail/data/view`,
    method: 'post',
    data
  }
}

export const dataSheet = (subsetId) => {
  return {
    url: `/analyzer/data/sheet/${subsetId}`,
    method: 'get',
  }
}

export const getVarListById = (p = {}) => {
  const { varName } = p
  const params = { subsetId: p.subsetId }
  if (varName) {
    params.varName = varName
  }

  return {
    url: `/analyzer/subset/variable/list/id`,
    method: 'get',
    params
  }
}

export const getVarList = (p = {}) => {
  const { varName, varType, outliers, numericOutliers, isSelection, isValueMissing, isNumeric } = p
  // console.log('params', p)
  const params = { subsetId: p.subsetId }
  if (varName) {
    params.varName = varName
  }

  //  1:表示全部定性变量；2:表示全部定量； 如果不传， 差全部类型
  if (varType !== undefined) {
    params.varType = varType
  }

  // true:含缺失值类型的变量；false:不含缺失值类型的变量； 如果不传， 不过滤
  if (isValueMissing) {
    params.missingType = true
  } else if (isValueMissing === false) {
    params.missingType = false
  }

  // true:含异常值类型的变量；false:不含异常值类型的变量； 如果不传， 不过滤
  if (outliers) {
    params.outliersType = true
  } else if (outliers === false) {
    params.outliersType = false
  }

  // true:含数值类型异常值类型的变量；false:不含数值类型异常值类型的变量； 如果不传， 不过滤
  if (numericOutliers) {
    params.numericOutliersType = true
  } else if (numericOutliers === false) {
    params.numericOutliersType = false
  }

  // true:数值类型的变量；false:非数值类型的变量； 如果不传， 不过滤
  if (isNumeric) {
    params.isNumeric = true
  } else if (isNumeric === false) {
    params.isNumeric = false
  }

  // 是否特征筛选 True or False 如果不传， 不过滤
  if (isSelection) {
    params.isSelection = true
  } else if (isSelection === false) {
    params.isSelection = false
  }
  return {
    url: `/analyzer/subset/variable/list`,
    method: 'post',
    data: params
  }
}

export const getSubsetInfo = (params) => {
  return {
    url: `/analyzer/subset/info`,
    method: 'get',
    params
  }
}

// 获取变量列表的详细统计信息
export const getDescribe = (params) => {
  return {
    url: `/analyzer/subset/describe`,
    method: 'get',
    params
  }
}

export const getNumericalDescribe = (params) => {
  return {
    url: `/analyzer/subset/numerical/describe`,
    method: 'get',
    params
  }
}

// 绘制不同特征的之间的相关关系
export const chartHeatmap = (data) => {
  return {
    url: `/analyzer/chart/heatmap`,
    method: 'post',
    data
  }
}

// 获取数值类型异常值
export const getNumericOutliersVarList = (subsetId) => {
  return {
    url: `/analyzer/subset/variable/numeric_outliers/${subsetId}`,
    method: 'get',
  }
}
// 获取指定定性变量的分类 varName:  变量名称， 多个变量用逗号分割
export const getVarCategory = (subsetId, varName) => {
  return {
    url: `/analyzer/subset/variable/category/${subsetId}`,
    method: 'get',
    params: { varName }
  }
}

export const updateVar = (data) => {
  return {
    url: `/analyzer/subset/variable/type/update`,
    method: 'post',
    data
  }
}
export const deleteVar = (data) => {
  return {
    url: `/analyzer/process/delete_col`,
    method: 'post',
    data
  }
}
