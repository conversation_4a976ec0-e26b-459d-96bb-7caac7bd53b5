export const queryListApi = (params) => {
  return {
    url: '/search/case-detail/config/page',
    method: 'get',
    params,
  }
}

export const getAllCaseDetailConfig = (indexId) => {
  return {
    url: `/search/case-detail/config/list-by-indexId/${indexId}`,
    method: 'get'
  }
}

export const getAllCaseDetailCondition = () => {
  return {
    url: `/search/case-detail/config/condition/list`,
    method: 'get'
  }
}

export const queryDetailApi = (id) => {
  return {
    url: '/search/case-detail/config/detail/' + id,
    method: 'get'
  }
}

export const addApi = (data) => {
  return {
    url: '/search/case-detail/config/insert',
    method: 'post',
    data,
  }
}

export const updateApi = (data) => {
  return {
    url: '/search/case-detail/config/update',
    method: 'post',
    data,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: '/search/case-detail/config/deletion',
    method: 'post',
    data,
  }
}

export const getAllIndex = () => {
  return {
    url: '/search/es-index-metadata/list',
    method: 'get'
  }
}
