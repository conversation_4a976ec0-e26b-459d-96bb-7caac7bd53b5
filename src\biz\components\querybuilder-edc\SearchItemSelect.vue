<template>
  <hos-dialog
    class="export-inline app-dialog"
    :visible.sync="searchItemDialogVisible"
    append-to-body
    width="75%"
    top="7vh"
    :title="$t('选择字段')"
    @open="openHandler"
  >
    <div style="text-align: center; margin-bottom: 10px">
      <hos-input
        v-model="keyword"
        clearable
        class="inline-input search-item-input"
        style="width: 350px"
        :placeholder="$t('请输入内容')"
        @input="keywordChange"
      />
    </div>

    <hos-tabs v-model="activeTabName" v-loading="loading">
      <hos-tab-pane :label="$t('患者信息')" name="patient">
        <scroll-menu
          ref="patient"
          type="patient"
          :single-select="singleSelect"
          :cate-items="searchItemDataLike ? searchItemDataLike.crfCateItems.filter(it=>it.cateId == 'patient') : crfCateItems.filter(it=>it.cateId == 'patient')"
          :height-light="keyword && keyword.trim() ? keyword.trim() : ''"
          :field-data="searchItemDataLike ? searchItemDataLike.fieldData : fieldData"
          :common-search-items="commonSearchItems"
          @closeSearchDialog="closeSearchDialog"
        />
      </hos-tab-pane>
      <hos-tab-pane :label="$t('CRF字段')" name="crf" v-if="tabShow.indexOf('crf')>=0">
        <scroll-menu
          ref="crf"
          type="crf"
          :single-select="singleSelect"
          :cate-items="searchItemDataLike ? searchItemDataLike.crfCateItems.filter(it=>it.cateId != 'patient' && it.cateId != 'attachment') : crfCateItems.filter(it=>it.cateId != 'patient' && it.cateId != 'attachment')"
          :height-light="keyword && keyword.trim() ? keyword.trim() : ''"
          :field-data="searchItemDataLike ? searchItemDataLike.fieldData : fieldData"
          :common-search-items="commonSearchItems"
          @closeSearchDialog="closeSearchDialog"
        />
      </hos-tab-pane>
      <hos-tab-pane :label="$t('订阅字段')" name="deform" v-if="tabShow.indexOf('deform')>=0">
        <subscribe-field-select
          ref="deform"
          :single-select="singleSelect"
          :menu-arr="searchItemDataLike ? searchItemDataLike.menuArr : menuArr"
          :height-light="keyword"
          :form-arr="searchItemDataLike ? searchItemDataLike.formArr : formArr"
          @singleSelectDone="singleSelectDone"
        />
      </hos-tab-pane>
      <hos-tab-pane :label="$t('附件')" name="attachment" v-if="tabShow.indexOf('attachment')>=0">
        <scroll-menu
          ref="attachment"
          type="attachment"
          :single-select="singleSelect"
          :cate-items="searchItemDataLike ? searchItemDataLike.crfCateItems.filter(it=>it.cateId == 'attachment') : crfCateItems.filter(it=>it.cateId == 'attachment')"
          :height-light="keyword && keyword.trim() ? keyword.trim() : ''"
          :field-data="searchItemDataLike ? searchItemDataLike.fieldData : fieldData"
          :common-search-items="commonSearchItems"
          @closeSearchDialog="closeSearchDialog"
        />
      </hos-tab-pane>
    </hos-tabs>

    <span slot="footer" class="dialog-footer">
      <hos-button @click="searchItemDialogVisible = false">{{ $t('取消') }}</hos-button>
      <hos-button type="primary" @click="closeSearchDialog">{{ $t('确定') }}</hos-button>
    </span>
  </hos-dialog>
</template>

<script>
import ScrollMenu from './ScrollMenu'
import SubscribeFieldSelect from './SubscribeFieldSelect'
import { debounce } from 'lodash'
import { getFullInputName_edc } from '../../utils/utils'

export default {
  components: {
    ScrollMenu,
    SubscribeFieldSelect
  },
  props: {
    url: {
      type: String,
      default() {
        return '/proj/form/tree'
      }
    },
    type: {
      type: String,
      default() {
        return ''
      }
    },
    // 是否单选，默认是
    singleSelect: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 是否默认选中患者信息，默认否
    patientSelect: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 全部关联字段信息
    fieldData: {
      type: Array,
      default() {
        return []
      }
    },
    // 公共的指标项
    commonSearchItems: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否使用场景是患者批量导入
    isPatientBatchImport: {
      type: Boolean,
      default() {
        return false
      }
    },
    diseaseId: {
      type: String,
      default() {
        return ''
      }
    },
    tabShow:{ // 展示哪些tab
      type:Array,
      default(){
        return ['patient','crf','deform','attachment']
      }
    }
  },
  data() {
    return {
      loading:false,
      searchItemDialogVisible: false,
      // 列表
      keyword: '',
      isInitActive: false,
      // 表单字段
      crfCateItems: [],
      filedCateItems: [],
      esCateFiled: [],
      structCateFiled: [],
      activeTabName: 'patient',
      formArr: [],
      menuArr: [],
      esFields: [],
      convertFields: [],
      searchItemDataLike: null // 过滤后的字段
    }
  },
  computed: {},
  watch: {
    searchItemDialogVisible: function (value) {
      if (!value){
        if(this.$refs.deform){
          this.$refs.deform.closePopover()
        }
        return
      }
      if (this.crfCateItems.length === 0) {
        this.loading = true
        this.getCrfFields().finally(()=>{
          this.loading = false
        })
      }

      // 获取订阅表单以及字段
      if (this.menuArr.length === 0) {
        this.getSubscribeFields()
      }
    }
  },
  methods: {
    async getCrfFields() {
      let orgId = null
      if(this.subjectInfo.loginUserOrgId){
        orgId = this.subjectInfo.loginUserOrgId
      }else{
        if(this.subjectInfo.mainOrg && this.subjectInfo.mainOrg.orgId){
          orgId = this.subjectInfo.mainOrg.orgId
        }
      }

      const params = {
        subjectId: this.subjectId,
        orgId,
      }

      let apiPath = 'biz.edc.project.search.getFormCateItems'

      // 患者批量导入和数据查询在查询crf字段时用的不同的接口
      if (this.isPatientBatchImport) {
        params.accountId = this.userId

        // 全程项目使用新接口
        if (this.subjectInfo.projectType == 3 && this.diseaseId !== '0') {
          params.diseaseId = this.diseaseId
          apiPath = 'biz.edc.disease.getFirstPeriodFormFields'
        } else {
          // 通用项目
          apiPath = 'biz.edc.patient.patientList.getPeriodFormFields'
        }
      }
      
      const { data } = await this.$api(apiPath, params)

      const tmp_arr = []
      if (data && data.length > 0) {
        for (let c_idx = 0; c_idx < data.length; c_idx++) {
          const cate = data[c_idx]
          const tmp_cate = {
            cateId: cate.id,
            cateName: cate.label,
            child: []
          }
          if ((cate.children && cate.children.length === 0) || !cate.children) {
            // 空表单
            cate.children = [
              {
                id: cate.id,
                label: cate.label,
                children: []
              }
            ]
          } else {
            // 判断第二级树节点是子分类还是字段
            if (typeof cate.children[0].id === 'number') {
              // 子分类
            } else {
              // 表单字段 // 添加一个同名的子分类
              const origin_children = cate.children
              cate.children = [
                {
                  id: cate.id,
                  label: cate.label,
                  children: origin_children
                }
              ]
            }
          }
          for (let child_idx = 0; child_idx < cate.children.length; child_idx++) {
            const child = cate.children[child_idx]
            const tmp_child = {
              childId: child.id,
              childName: child.label,
              item: []
            }
            for (let item_idx = 0; item_idx < child.children.length; item_idx++) {
              const item = child.children[item_idx]
              const isCrf = ['patient','attachment'].indexOf(child.id) < 0
              const tmp_item = {
                  searchItemName: '',
                  searchItemId: isCrf ? item.data.key : item.id,
                  fieldType: isCrf ? 'crf' : child.id,
                  fieldName: item.label,
                  fieldKey: isCrf ? item.data.key : item.id,
                  cateName: child.label,
                  cateCode: '',
                  cateId: isCrf ? child.id : '',
                  externalCateId: '',
                  id: isCrf ? child.id : '',
                  prodList:[],
                  inputType: item.data.type, // 这里需要把字段类型传出去
                  isRequired: item.data.isRequired, // 是否必选
              }
              tmp_item.searchItemName = getFullInputName_edc(tmp_item)

              tmp_child.item.push(tmp_item)
            }
            tmp_cate.child.push(tmp_child)
          }
          tmp_arr.push(tmp_cate)
        }
      }
      // 如果需要默认选中患者信息
      console.log(tmp_arr,'tmp_arr')
      if (this.patientSelect) {
        const patientArr = tmp_arr.filter((item) => item.cateId === 'patient')
        const patientItemArr = []
        // 改为默认选中必选字段
        patientArr[0].child[0].item.forEach((element) => {
          if(element.isRequired){
            patientItemArr.push(element)
          }
        })
        console.log('this.$refs.patient',this.$refs.patient)
        if(this.$refs.patient){
          this.$refs.patient.restoreSelectItems(patientItemArr)
        }
      }
      this.crfCateItems = tmp_arr
    },
    // 打开弹出框
    openSearchDialog(isInitActive) {
      // 如果传入true， 则初始化选中
      // 单选始终初始化选中
      this.isInitActive = isInitActive
      this.searchItemDialogVisible = true
      this.keyword = ''
      this.getApproveSearchItemLike()
    },
    // 打开弹框后
    openHandler() {
      if (this.isInitActive || this.singleSelect) {
        this.$nextTick(() => {
          this.$emit('opened')
        })
      } else {
        this.$emit('opened')
      }
    },
    getSubscribeFields() {
      const params = {
        subjectId: this.subjectId
      }
      this.$api('biz.edc.project.search.getDeFormCateItems', params).then(({ data: res }) => {
        res = res || []
        res.map((item) => {
          const obj = JSON.parse(item.metadata)
          item.metadata = obj
          // 这里给所有字段拼接上一个deFormId
          item.metadata.itemProp.forEach((ip) => {
            ip.deformId = item.id
          })
          item.metadata.itemCode.forEach((ic) => {
            ic.deformId = item.id
          })

          // 表级别2或3的字段在选择作为
          if (item.type == 2) {
            // 选中配置的默认选中的那个属性
            const tmp = item.metadata.itemProp.filter(prop=>prop.code == item.checkedProp)
            // 1.查询条件,记录单选的查询属性
            item.conditionQueryAttr = tmp.length>0 ? tmp[0] : item.metadata.itemProp[0]
            // 2.查询字段,记录多选的查询属性(默认全不选,且为必选项)
            item.deformItemInfos = tmp
            item.metadata.itemProp.map((prop) => {
              prop.deformItemInfos = item.deformItemInfos
            })
          }

          // 将表单信息,保存到每个字段上
          if (item.type == 1) {
            item.metadata.itemProp.map((prop) => {
              prop.categoryName = item.categoryName
              prop.categoryGrade = item.categoryGrade
              prop.type = item.type
              prop.categoryId = item.categoryId
              prop.cateId = item.metadata.itemCateCode ? item.metadata.itemCateCode.id : ''
              prop.cateName = item.name
              prop.cateCode = item.metadata.itemCateCode ? item.metadata.itemCateCode.code : ''
              prop.tableId = item.id
            })
          }
          if (item.type == 2) {
            item.metadata.itemCode.map((code) => {
              code.categoryName = item.categoryName
              code.categoryGrade = item.categoryGrade
              code.type = item.type
              code.cateId = item.metadata.itemCateCode ? item.metadata.itemCateCode.id : ''
              code.cateName = item.name
              code.cateCode = item.metadata.itemCateCode ? item.metadata.itemCateCode.code : ''
              code.tableId = item.id
              if (!code.categoryId) {
                code.categoryId = item.categoryId
              }
            })
          }
        })

        // 右侧用于渲染表单字段的数组
        this.formArr = res
        console.log(this.formArr, '表单数组')

        // 有多少个不同的categoryId,一级分类
        const arr = []
        res.map((item) => {
          arr.push(item.categoryId)
        })
        // 去重
        const cateArr = [...new Set(arr)]
        console.log(cateArr, '274')

        // 拼装数据
        const tmp_menuArr = []
        cateArr.map((cateId) => {
          const temArr = res.filter((item) => item.categoryId === cateId)
          if (temArr.length > 1) {
            tmp_menuArr.push({
              categoryId: temArr[0].categoryId,
              categoryName: temArr[0].categoryName,
              categoryGrade: temArr[0].categoryGrade,
              children: temArr
            })
          } else {
            tmp_menuArr.push(temArr[0])
          }
        })
        console.log(tmp_menuArr, 'menu')
        this.menuArr = tmp_menuArr
      })
    },
    // 设置查询条件,点击某个字段后触发
    singleSelectDone(form, field) {
      console.log(form, field, '单击的字段')
      // FIXME:拼装满足格式的数据,仿照注释的数据格式
      const data = {
        searchItemName: '',
        searchItemId: field.id,
        // fieldInfo:{
          fieldType: 'deform',
          fieldName: field.name,
          fieldKey: field.code, // 查询项目编码
          cateName: field.cateName, // 分类名称
          cateCode: field.cateCode, // 分类名称
          cateId: field.cateId,
          externalCateId: field.categoryId, // 分类ID
          id: field.tableId,
          prodList: field.conditionQueryAttr ? [{
            prodId: field.conditionQueryAttr.id, // 查询属性id （可选）
            prodName: field.conditionQueryAttr.name, // 查询属性名称 （可选）
            prodKey: field.conditionQueryAttr.code, // 查询属性编码 （可选）
          }] : [],
        // },
        // fieldInfo: {
        //   id: '',
        //   name: field.name,
        //   code: field.code,
        //   elasticIndexId: 0,
        //   elasticTypeId: 0,
        //   datatypeCode: 'keyword',
        //   dictionaryId: 0,
        //   analyzer: 0,
        //   desensitization: 0,
        //   sequence: 0,
        //   elasticTypeName: this.$t('deform表'),
        //   elasticTypeCode: 'deform',
        //   elasticIndexName: 'csmsearch',
        //   elasticIndexCode: 'csmsearchtest',
        //   dicCateName: null,
        //   businessCode: '',
        //   crf: {
        //     formId: field.deformId,
        //     name: field.name,
        //     key:
        //       field.categoryGrade === 2 || field.categoryGrade === 3
        //         ? form.itemCodeField
        //           ? 'de_' + form.itemCodeField
        //           : 'de_' + field.code
        //         : 'de_' + field.code,
        //     type: 'input',
        //     formGuid: 'deform',
        //     path:
        //       field.categoryGrade === 2 || field.categoryGrade === 3
        //         ? form.itemCodeField
        //           ? 'de_' + form.itemCodeField
        //           : 'de_' + field.code
        //         : 'de_' + field.code,
        //     categoryGrade: field.categoryGrade,
        //     deformFieldCode: field.categoryGrade === 2 || field.categoryGrade === 3 ? field.code : '',
        //     ...(form.itemCatCodeField
        //       ? {
        //           // 这两个属性仅在deformCategoryCode有值的时候拼接，否则查询可能会有问题
        //           deformCategoryCode: form.itemCatCodeField,
        //           categoryKey: form.metadata.itemCateCode.code
        //         }
        //       : {}),
        //     itemKey: form.conditionQueryAttr ? 'de_' + form.conditionQueryAttr.code : ''
        //   }
        // },
        // pFieldInfo: {
        //   id: field.deformId,
        //   name: field.formName,
        //   pname: field.formName + `${form.conditionQueryAttr ? '(' + form.conditionQueryAttr.name + ')' : ''}`
        // }
      }
      data.searchItemName = getFullInputName_edc(data)
      this.searchItemDialogVisible = false
      this.$emit('close-search-dialog', data)
      // 这里是点击单个字段作为查询条件触发的关闭弹窗,随之触发父组件QueryBuilderHuaxi.vue里面的fillInput方法,更新查询条件组
      // this.$emit('close-search-dialog')
    },
    // 关闭弹出框
    closeSearchDialog() {
      let activeItem = this.getSelectItems()

      console.log(activeItem)

      // 拿到选中的订阅字段,注意表级别23的订阅字段必须设置查询属性
      const temArr = this.$refs.deform ? this.$refs.deform.selectQueryFieldArr : []
      let flag = false
      for (let i = 0; i < temArr.length; i++) {
        const field = temArr[i];
        if (field.type == 2) {
          // 只要有一个字段没有设置查询属性,将flag置为true
          if(this.singleSelect){
            if (!field.conditionQueryAttr) {
              flag = true
              break
            }
          }else{
            if (!field.deformItemInfos || field.deformItemInfos.length === 0) {
              flag = true
              // console.log('field...',field)
              break
            }
          }
        }
      }
      if (flag) {
        return this.$message({
          type: 'warning',
          message: this.$t('存在订阅字段没有设置查询属性,请检查!'),
          duration: 2000
        })
      }
      this.searchItemDialogVisible = false
      if(this.singleSelect && activeItem.length>0){
        activeItem = activeItem[0]
      }
      this.$emit('close-search-dialog', activeItem)
    },
    // 获取全部选中的查询项
    getSelectItems() {
      if (this.singleSelect) {
        if(this.$refs[this.activeTabName]){
          return this.$refs[this.activeTabName].getSelectItems()
        }
      } else {
        const res = []
        const types = ['patient','crf','attachment']
        types.forEach(type=>{
          if(this.$refs[type]){
            const items = this.$refs[type].getSelectItems()
            res.push(...items)
          }
        })
        return res
      }
    },
    // 回显全部选中的查询项
    restoreSelectItems(all){
      console.log('restoreSelectItems all',all)
      const types = ['patient','crf','attachment','deform']
      if(all && all.length>0){
        this.activeTabName = all[0] && all[0].fieldType ? all[0].fieldType : 'patient'
      }else{
        this.activeTabName = 'patient'
      }
      types.forEach(type=>{
        if(this.$refs[type]){
          this.$refs[type].restoreSelectItems(all)
        }
      })
    },
    // 清空所有查询项
    clearSearchItems() {
      const types = ['patient','crf','attachment','deform']
      types.forEach(type=>{
        if(this.$refs[type]){
          this.$refs[type].resetSearchItems()
        }
      })
    },
    getApproveSearchItemLike() {
      // 改成前端过滤
      if (this.keyword && this.keyword.trim()) {
        const keyword = this.keyword.trim()
        const crfCateItems = this.crfCateItems
          .map((cate) => {
            return {
              ...cate,
              child: [
                {
                  ...cate.child[0],
                  item: cate.child[0].item.filter((it) => it.searchItemName.indexOf(keyword) >= 0)
                }
              ]
            }
          })
          .filter((cate) => {
            return cate.child[0].item.length > 0
          })
        const formArr = this.formArr
          .map((form) => {
            if (form.type == 1) {
              return {
                ...form,
                metadata: {
                  ...form.metadata,
                  itemProp: form.metadata.itemProp.filter((field) => field.name.indexOf(keyword) >= 0)
                }
              }
            } else if (form.type == 2) {
              return {
                ...form,
                metadata: {
                  ...form.metadata,
                  itemCode: form.metadata.itemCode.filter((field) => field.name.indexOf(keyword) >= 0)
                }
              }
            } else {
              return form
            }
          })
          .filter((form) => {
            if (form.type == 1) {
              return form.metadata.itemProp.length > 0
            } else if (form.type == 2) {
              return form.metadata.itemCode.length > 0
            } else {
              return true
            }
          })
        const filterFormIds = formArr.map((form) => form.id)
        const menuArr = this.menuArr
          .map((item) => {
            if (item.children && item.children.length > 0) {
              return {
                ...item,
                children: item.children.filter((form) => filterFormIds.indexOf(form.id) >= 0)
              }
            } else {
              return item
            }
          })
          .filter((form) => {
            if (form.children) {
              return form.children.length > 0
            } else {
              return filterFormIds.indexOf(form.id) >= 0
            }
          })
        this.searchItemDataLike = { crfCateItems, menuArr, formArr }
      } else {
        this.searchItemDataLike = null
      }
    },
    keywordChange: debounce(function () {
      this.getApproveSearchItemLike()
    }, 500)
  }
}
</script>
