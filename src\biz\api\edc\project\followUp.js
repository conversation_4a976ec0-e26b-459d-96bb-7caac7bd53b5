
// 获取指定项目ID的随访计划信息
export const queryPlanBySubId = (id) => {
  return {
    url: 'edc/followup-plan/select-model-by-subject/' + id,
    method: 'get'
  }
}

// 保存随访计划
export const saveFollowUpPlan = (data) => {
  return {
    url: `edc/followup-plan/save`,
    method: 'post',
    data,
  }
}

// 预览随访计划
export const previewFollowUpPlan = (params) => {
  return {
    url: `edc/followup-plan/preview/${params.referenceDate}`,
    method: 'post',
    data: params.data
  }
}

export const syncHistoryForm = (params) => {
  return {
    url: '/edc/visit-form/sync/history',
    method: 'post',
    params
  }
}

