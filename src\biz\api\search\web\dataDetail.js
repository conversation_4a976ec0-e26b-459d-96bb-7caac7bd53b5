export const queryListApi = (params) => {
    return {
        url: 'search/data-detail/patient',
        method: 'GET',
        params
    }
}

export const getItemTree = (params) => {
    return {

    }
}

export const queryTableByIndexAndTheme = (params) => {
    return {
        url: 'search/data-detail/search/type/page',
        method: 'GET',
        params
    }
}

export const getAdmTypeByAdmno = (params) => {
    return {
        url: '/search/advanced/adm-type',
        method: 'get',
        params
    }
}