// 一般描述
export const general_description = (data) => {
    return {
        url: `/analyzer/analysis/general_description`,
        method: 'post',
        data
    }
}
// 单因素分析
export const single_factor_analysis = (data) => {
  return {
      url: `/analyzer/analysis/single_factor_analysis`,
      method: 'post',
      data
  }
}
// 回归分析
export const regression_analysis = (data) => {
  return {
      url: `/analyzer/analysis/regression_analysis`,
      method: 'post',
      data
  }
}
// 生存分析
export const survival_analysis = (data) => {
  return {
      url: `/analyzer/analysis/survival_analysis`,
      method: 'post',
      data
  }
}
// 生存分析-km曲线
export const survival_analysis_km = (data) => {
  return {
      url: `/analyzer/analysis/km`,
      method: 'post',
      timeout: 60000, // 单独设置km曲线的超时60s
      data
  }
}
// 特征筛选
export const data_selection = (data) => {
  return {
      url: `/analyzer/model/data_selection`,
      method: 'post',
      data
  }
}
// 特征筛选-获取特征筛选的删除特征记录
export const get_data_selection_record = (params) => {
  return {
      url: `/analyzer/model/data_selection_record`,
      method: 'get',
      params
  }
}
// 特征筛选-恢复特征筛选的
export const data_selection_reset = (params) => {
  return {
      url: `/analyzer/model/data_selection_reset`,
      method: 'get',
      params
  }
}
// 模型调参
export const param_tuning = (data) => {
  return {
      url: `/analyzer/model/param_tuning`,
      method: 'post',
      data
  }
}
// 模型调参（异步）
export const param_tuning_async = (data) => {
  return {
      url: `/analyzer/model/param_tuning/async`,
      method: 'post',
      data
  }
}
// 模型调参（异步）
export const param_tuning_async_result = (result_id) => {
  return {
      url: `/analyzer/model/param_tuning/result/${result_id}`,
      method: 'get',
  }
}
// 模型训练
export const model_train = (data) => {
  return {
      url: `/analyzer/model/model_train`,
      method: 'post',
      data
  }
}
// 设置多条ROC曲线（加入对比）
export const set_multi_roc = (data) => {
  return {
      url: `/analyzer/model/set_multi_roc`,
      method: 'post',
      data
  }
}
