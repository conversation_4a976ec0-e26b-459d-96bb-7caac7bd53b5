﻿var ca_key = (function() {
    ///定义厂商签名方式
    var constVenderCode = "FAKE";
    var constignTypeCode = "PHONE";
    var constProduct = "HIS";

    function ca_getLoginQrInfo(params) {
        try {
            var checkParamsResult = ca_common_func.checkPublicParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;
            params.product = constProduct;

            var loginQrInfo = ca_common_func.getLoginQrInfo(params);
            if (loginQrInfo.code != "200")
                throw loginQrInfo;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:loginQrInfo.data  };
        }
        return { code:"0", msg:"", data:loginQrInfo.data };
    }

    function ca_getLoginQrResult(params) {
        try {
            var checkParamsResult = ca_common_func.checkPublicParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            var qrID = params.qrID || "";
            if (qrID == "")
                throw {code:"-1",msg:"查询手机扫码结果时,qrID不允许为空!"};

            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;

            var loginQrResult = ca_common_func.getLoginQrResult(params);
            if (loginQrResult.code != "200")
                throw loginQrResult;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:loginQrResult.data  };
        }
        return { code:"0", msg:"", data:loginQrResult.data };
    }

    function ca_getTokenIsValid(params) {
        try {
            var checkParamsResult = ca_common_func.checkPublicParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            var signToken = params.signToken || "";
            if (signToken == "")
                throw {code:"-1",msg:"查询token是否有效时,signToken不允许为空!"};

            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;
            
            var tokenIsValid = ca_common_func.getTokenIsValid(params);
            if (tokenIsValid.code != "200")
                throw tokenIsValid;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:tokenIsValid.data };
        }
        return { code:"0", msg:"", data:tokenIsValid.data };
    }

    function ca_getPinLoginResult(params) {
        try {
            var checkParamsResult = ca_common_func.checkPublicParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            var userCode = params.userCode || "";
            if (userCode == "")
                throw {code:"-1",msg:"pin码登录时,userCode不允许为空!"};

            var password = params.password || "";
            if (password == "")
                throw {code:"-1",msg:"pin码登录时,password不允许为空!"};

            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;
            
            var pinLoginResult = ca_common_func.getPinLoginResult(params);
            if (pinLoginResult.code != "200")
                throw pinLoginResult;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:pinLoginResult.data };
        }
        return { code:"0", msg:"", data:pinLoginResult.data };
    }

    function ca_login(params) {
        var prompt = "";   //部分厂商登录成功了，但是需要提示用户证书临近过期，放在这里
        try {
            var checkParamsResult = ca_common_func.checkLoginParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;
            params.random = "";
            params.signedData = "";

            var loginResult = ca_common_func.getLoginResult(params);
            if (loginResult.code != "200")
                throw loginResult;
            
            var userID = loginResult.data.userID;
            var userName = loginResult.data.userName;
            var userCode = loginResult.data.userCode;
            var imageType = params.imageType || "";
            var signImage = imageType == "" ? "" : loginResult.data.signImage;
            var certNo = params.certNo || "";
            var userCertCode = params.userCertCode || "";
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:{ loginResult:false } }
        }
        return { code:"0", msg:"", data:{ prompt:prompt,loginResult:true,userID:userID,userName:userName,userCode:userCode,certNo:certNo,userCertCode:userCertCode,signImage:signImage} };   //,signCert:signCert
    }

    function ca_hashData(params) {
        try {
            var hashType = params.hashType || "SM3Hex";
            if ($.inArray(hashType.toUpperCase(), ca_common_func.getHashTypes()) == -1)
                throw {code:"-1",msg:"当前不支持【"+ hashType +"】摘要算法,请联系电子病历组确认!"};

            var tohashData = params.tohashData || "";
            if (tohashData == "")
                throw {code:"-1",msg:"待摘要数据不允许为空"};

            var hashResult = ca_common_func.getHashData(params);
            if (hashResult.code != "200")
                throw hashResult;

            var hashValue = hashResult.data.hashValue;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:{ hashValue:"" } }
        }
        return { code:"0", msg:"", data:{ hashValue:hashValue,hashType:hashType} };
    }

    function ca_signData(params) {
        try {
            var checkParamsResult = ca_common_func.checkSignParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            var signToken = params.signToken || "";
            if (signToken == "")
                throw {code:"-1",msg:"自动签名时,signToken不允许为空!"};
            
            params.authTypeCode = constignTypeCode;
            params.venderCode = constVenderCode;
            params.toSignData = params.hashData;

            var signResult = ca_common_func.getAutoSignResult(params);
            if (signResult.code != "200")
                throw signResult;

            params.signedData = signResult.data.signedData;
            params.certNo = signResult.data.certNo;
            params.signCert = signResult.data.cert;
            params.userCertCode = signResult.data.userCertCode;
            params.timeStamp = signResult.data.timeStamp;

            var saveSignResult = ca_common_func.saveSignData(params);
            if (saveSignResult.code != "200")
                throw saveSignResult;

            var digitalSignID = saveSignResult.data.digitalSignID;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:{ digitalSignID:"" } }
        }

        var data = { digitalSignID:digitalSignID };
        var getDetail = params.getSignDetail;
        if (getDetail) {
            data.hashData = params.hashData;
            data.hashType = params.hashType;
            data.signedData = params.signedData;
            data.certNo = params.certNo;
            data.signCert = params.signCert;
            data.userCertCode = params.userCertCode;
            data.timeStampData = saveSignResult.data.timeStampData;
            data.signDateTime = saveSignResult.data.signDateTime;
        }
        return { code:"0", msg:"", data };
    }

    function ca_hashAndSignData(params) {
        try {
            var toSignData = params.toSignData || "";
            if (toSignData == "")
                throw {code:"-1",msg:"待签数据不允许为空"};

            params.tohashData = toSignData;
            var hashData = ca_hashData(params);
            if (hashData.code != "0")
                throw hashData;

            params.hashData = hashData.data.hashValue;
            params.hashType = hashData.data.hashType;
            
            var saveSignResult = ca_signData(params);
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:{ digitalSignID:"" } }
        }
        return saveSignResult;
    }

    function ca_getCertByContainer(params) {
        try {
            var checkParamsResult = ca_common_func.checkPublicParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            var certContainer = params.certContainer || "";
            if (certContainer == "")
                throw {code:"-1",msg:"查询证书时,certContainer不允许为空!"};
            
            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;
            
            var certInfo = ca_common_func.getCertByContainer(params);
            if (certInfo.code != "200")
                throw certInfo;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:certInfo.data };
        }
        return { code:"0", msg:"", data:certInfo.data };
    }
    
    function ca_getCertInfoByUserCode(params) {
        var usrSignatureInfo = {};
        try {
            var checkParamsResult = ca_common_func.checkPublicParams(params);
            if (checkParamsResult.code != "0")
                throw checkParamsResult;

            var certContainer = params.certContainer || "";
            if (certContainer == "")
                throw {code:"-1",msg:"获取证书信息时,用户标识(工号)不允许为空!"};

            params.userCode = certContainer;
            params.venderCode = constVenderCode;
            params.authTypeCode = constignTypeCode;

            var certInfo = ca_common_func.getCertByUserCode(params);
            if (certInfo.code != "200")
                throw certInfo;
            var certData = certInfo.data;

            var signImage = certData.signSeal;
            if (signImage == "") {
                params.certContainer = certData.certContainer;
                var sealInfo = ca_common_func.getSealInfo(params);
                if (sealInfo.code != "200")
                    throw sealInfo;

                signImage = sealInfo.data.signSeal;
            }
            usrSignatureInfo.identityID = certData.identityID;
            usrSignatureInfo.certificate = certData.cert;
            usrSignatureInfo.certificateNo = certData.certNo;
            usrSignatureInfo.certificateSN = certData.certSn;
            usrSignatureInfo.uKeyNo = certData.certDn;
            usrSignatureInfo.signImage = signImage;
            usrSignatureInfo.usrCertCode = certData.userCertCode;
            usrSignatureInfo.certName = certData.certName;
        } catch(e) {
            return { code:e.code, msg:e.msg || e, data:{}}
        }
        var data = usrSignatureInfo;
        return { code:"0", msg:"", data };
	}

	return {
    	venderCode:constVenderCode,
    	signTypeCode:constignTypeCode,
        ///获取服务配置信息(扫码APP名称、轮询间隔等)
        getConfig: function(params) {
            params.venderCode = constVenderCode;
            params.signTypeCode = constignTypeCode;
            return ca_common_func.getConfigInfo(params);
        },
        ///获取二维码相关信息
        getLoginQrInfo: function(params) {
            return ca_getLoginQrInfo(params);
        },
        ///获取二维码扫描结果
        getLoginQrResult: function(params) {
            return ca_getLoginQrResult(params);
        },
        ///获取pin码校验结果
        pinLogin: function(params) {
            return ca_getPinLoginResult(params);
        },
        ///token是否有效
        getTokenIsValid: function(params) {
            return ca_getTokenIsValid(params);
        },
        ///此接口当前用于业务系统通过certContainer获取最新的证书信息
        getCertInfoByContainer: function(params) {
            return ca_getCertByContainer(params);
        },
        ///证书后台登录(证书延期、验证证书有效性)，返回用户信息及证书信息
        login: function(params) {
            return ca_login(params);
        },
        ///对摘要数据签名，签名后调用后端服务验签，生成时间戳，验证时间戳，返回签名数据存储表Id，根据参数决定是否返回签名数据
        signedData: function(params) {
            return ca_signData(params)
        },
        ///对原数据做摘要，摘要签名，调用后端服务验签，生成时间戳，验证时间戳，返回签名数据存储表Id，摘要算法，摘要值，根据参数决定是否返回签名数据
        hashAndSignedData: function(params) {
            return ca_hashAndSignData(params)
        },
        ///获取证书数据集合，业务产品组正常无需调用，证书关联页面调用
        getCertInfoByUserCode: function(params) {
            return ca_getCertInfoByUserCode(params);
        }
    }
})();