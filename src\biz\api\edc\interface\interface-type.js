// 分页查询
export const pageListApi = (params) => {
    return {
        url: `edc/external-category/page`,
        method: 'GET',
        params
    }
}

// 根据id查询详情
export const queryDetailApi = (id) => {
    return {
        url: `edc/external-category/detail/${id}`,
        method: 'GET',
    }
}

// 新增接口
export const addApi = (data) => {
    return {
        url: 'edc/external-category/insert',
        method: 'POST',
        data
    }
}

// 修改接口
export const editApi = (data) => {
    return {
        url: 'edc/external-category/update',
        method: 'post',
        data
    }
}

// 批量删除
export const deleteApi = (data) => {
    return {
        url: `edc/external-category/deletion`,
        method: 'POST',
        data
    }
}

// 查询ES所有表
export const queryAllEsTable = (id) => {
    return {
        url: `edc/external/setting/get-es-table`,
        method: 'GET',
    }
}

// 同步ES接口类别
export const asyncEsInterTypeData = (data) => {
    return {
        url: `edc/external/setting/async-category`,
        method: 'POST',
        data
    }
}
