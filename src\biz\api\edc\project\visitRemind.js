// 分页获取随访提醒
export const queryListApi = (params) => {
  return {
    url: 'edc/visit-reminder/page',
    method: 'get',
    params,
  }
}

// 今日随访提醒
export const getTodayRemind = (params) => {
  return {
    url: 'edc/visit-reminder/today',
    method: 'get',
    params,
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/visit-reminder/insert',
    method: 'POST',
    data,
  }
}

export const deleteApi = (data) => {
  return {
    url: 'edc/visit-reminder/deletion',
    method: 'POST',
    data,
  }
}

export const finishVisit = (params) => {
  return {
    url: `edc/visit/finish`,
    method: 'POST',
    params
  }
}