// 获取分页数据
export const getPageData = (params) => {
    return {
        url: 'search/approve-record/page',
        method: 'GET',
        params,
    }
}
// 审核通过
export const getPass = (data) => {
    return {
        url: `search/approve-record/pass`,
        method: 'POST',
        data: data
    }
}

// 审核不通过
export const getRefuse = (data) => {
    if (data.approveCause == "") data.approveCause = "重新申请"
    return {
        url: `search/approve-record/disapprove`,
        method: 'POST',
        data: data
    }
}

//  清空数据

export const clearAllData = () => {
    return {
        url: `search/approve-record/deletion-all`,
        method: 'POST',
    }
}

//  删除数据

export const handleDelete = (id) => {
    return {
        url: `search/approve-record/deletion-by-id/${id}`,
        method: 'POST',
    }
}
