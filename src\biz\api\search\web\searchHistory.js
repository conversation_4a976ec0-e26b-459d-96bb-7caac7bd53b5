

// 获取分页
export const getPageData = (params) => {
    return {
      url: 'search/search-history/page',
      method: 'GET',
      params,
    }
  }

  // 修改收藏状态
export const putHandleChange = (data) => {
    return {
      url: 'search/search-history/update',
      method: 'post',
      data,
    }
}

  // 删除记录
  export const deleteData = (id) => {
    return {
      url: `search/search-history/delete/${id}`,
      method: 'post',
    }
}
