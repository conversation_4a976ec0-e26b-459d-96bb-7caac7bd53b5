var fs = require('fs')
var save = require('file-save')
var resolve = require('path').resolve
var basename = require('path').basename
// var localePath = resolve(__dirname, '../../src/locale/lang');
var axios = require('axios')
const { VUE_APP_BASE_URL } = require('../public/environment')
// var fileList = fs.readdirSync(localePath);

// console.log(axios)
let URL = VUE_APP_BASE_URL + '/csm/loginPageData/select-config-page-type'
const token = {"value":"mediway_eyJhbGciOiJIUzI1NiJ9.eyJ0b2tlbi10eXBlIjoicmVmcmVzaF90b2tlbiIsImV4cCI6MTY4OTU2NTcyNSwidXNlciI6IntcImFjY291bnRDb2RlXCI6XCJhZG1pblwiLFwiYWNjb3VudE5hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwicG9zdElkXCI6XCJkZWZhdWx0TWFuYWdlUG9zdDEyMzQ1Njc4OVwiLFwiYWNjb3VudElkXCI6XCIzZTJiZGY4NGY3YjRmOTRiZTJkOGE3MjEzNWY2YzkyNVwiLFwicGhvbmVOdW1iZXJcIjpcIlwiLFwicG9zdE5hbWVcIjpcIui2heeuoeWyl-S9jVwiLFwidGVuYW50SWRcIjpcIlwiLFwiaW5pdExvZ2luVGltZVwiOjE2ODk1NTg1MjU2NTAsXCJncmFudFR5cGVcIjpcInBhc3N3b3JkXCJ9IiwianRpIjoiTjJaak4ySmtNek10WVRVd01DMDBOVGRqTFRnMk9Ea3RNalkxWmpFMU9UTXdNak0wIn0.o6iuoXXwdm71S-Gm3N70XmSVNmobKKD1CyE6iq7YWJg","expire":null}
var getRemoteLocale = function (url, cb) {
  axios
    .request({
      url,
      method: 'GET',
      headers: {
      }
    })
    .then((res) => {
      debugger
      if (res.data) {
        cb(res.data)
      }
    })
}
getRemoteLocale(URL, function(data) {
  let code =
  save(resolve(__dirname, '../public/static', languagePark)).write(data);
})

var transform = function (filename, name, cb) {
  require('babel-core').transformFile(
    resolve(localePath, filename),
    {
      plugins: ['add-module-exports', ['transform-es2015-modules-umd', { loose: true }]],
      moduleId: name
    },
    cb
  )
}

// fileList
//   .filter(function(file) {
//     return /\.js$/.test(file);
//   })
//   .forEach(function(file) {
//     var name = basename(file, '.js');

//     transform(file, name, function(err, result) {
//       if (err) {
//         console.error(err);
//       } else {
//         var code = result.code;

//         code = code
//           .replace('define(\'', 'define(\'element/locale/')
//           .replace('global.', 'global.ELEMENT.lang = global.ELEMENT.lang || {}; \n    global.ELEMENT.lang.');
//         save(resolve(__dirname, '../public/static', languagePark)).write(code);

//         console.log(file);
//       }
//     });
//   });
