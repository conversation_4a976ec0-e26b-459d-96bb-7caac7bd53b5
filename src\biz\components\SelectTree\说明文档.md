# 参数
```json
ultimateTreeOptions: {
    "type": "select",   // 顶部控件类型 input|select
    "selectConf": {     // 选择器配置
        "url": "application/manage/tree",   // options列表请求路径
        "labelName": "label",     // 展示字段名配置
        "valueName": "id",        // 绑定值字段名配置
        "options": []
    },
    "treeConf": {       // 树配置
        "url": "accessAuthority/menuTree",  // 树结构数据请求路径
        "defaultProps": {       // 树结构默认参数
            "children": "children",
            "label": "name",
        },
        "nodeKey": "id",    // 树节点绑定值字段
        "isExpandAll": false,   // 是否展开所有节点
        "title": "所属菜单",    // 树上方标题
        "treeData": [],    // 直接传tree数据，url设置成""
        "isFirstSelect": true,    // 是否默认选中第一条
        "isClickCancel": false,    // 再此点击是否取消选中
    },
    
},
```

# 事件监听
- `selectOption`, 参数：选择值
- `selectNode`, 参数：所点击的树节点

# 方法
- `queryOptions`, 刷新选择器数据并更新树结构数据
- `queryTreeData`, 刷新树结构数据