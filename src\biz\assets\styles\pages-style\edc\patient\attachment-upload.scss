
  .file-manage-tab-box {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;

    .left {
      width: 60%;
      position: relative; // 使用定位是为了强行修改fileList到右侧区域展示
      display: flex;
      justify-content: center;
      align-items: center;

      .upload-demo {
        width: 100%;
        padding-right: 10px;
        text-align: center;

        .hos-upload {
          width: 100%;
          height: calc(100vh - 450px);

          .hos-upload-dragger {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .hos-icon-upload {
              margin-top: 16px;
            }
          }
        }

        .hos-upload__tip {
          text-align: left;

          .tip-title {
            margin-bottom: 10px;
          }
        }

        .hos-upload-list {
          padding-left: 20px;
          width: 66.6%; // left宽60%,right宽40%,为了让file-list定位到右侧且表现为和右侧同宽width:66.6%,right:-66.6%
          text-align: left;
          position: absolute;
          top: 70px;
          right: -66.6%;
        }
      }
    }

    .right {
      width: 40%;
      border-left: 1px solid #E4E7ED;
      padding-left: 20px;

      .btn-container {
        width: 100%;
        height: 32px;
        display: flex;
        justify-content: flex-end;

        .hos-button {}
      }

      .list-title {
        margin-top: 20px;
        color: #606266;
        font-size: 14px;
      }
    }
  }