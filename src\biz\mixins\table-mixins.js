// table-mixins.js

export default {
  data() {
    return {
      tableHeight: 'auto',
      // 是否分页， 默认是
      mixinIsPage: true,
      mixinBottomSize: 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.updateTableHeight)
      this.updateTableHeight()
    })
  },
  methods: {
    updateTableHeight() {
      // 通过原生方法，获取dom节点的高度------获取 table表格header的元素
      // const offsetTop = this.$el.offsetTop
      const clientHeight = this.$el.clientHeight
      const formEl = this.$el.getElementsByClassName('hos-form')
      const tableToolsEl = this.$el.getElementsByClassName('table-tools')
      const headerEl = this.$el.getElementsByClassName('hos-table__header-wrapper')

      const tipEl = this.$el.getElementsByClassName('tip-bar-container')

      const formrH = formEl.length > 0 ? formEl[0].clientHeight : 0
      const tableToolsH = tableToolsEl.length > 0 ? tableToolsEl[0].clientHeight : 0
      const headerH = headerEl.length > 0 ? headerEl[0].clientHeight : 0
      const tipElH = tipEl.length > 0 ? (tipEl[0].clientHeight + 20) : 0

      let pageH = 0
      if (!this.mixinIsPage) {
        pageH = 45
      }
      // const windowHeight = window.innerHeight
      const tableHeight = clientHeight - formrH - tableToolsH - headerH - 25 + pageH - this.mixinBottomSize - tipElH

      if (tableHeight < 300) {
        this.tableHeight = 300 + 'px'
      } else {
        this.tableHeight = tableHeight + 'px'
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateTableHeight)
  }
}