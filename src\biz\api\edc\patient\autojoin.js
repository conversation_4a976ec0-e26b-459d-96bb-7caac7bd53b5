// 导入部分后台重构，暂未迁移接口
export const pageTaskListApi = (params) => {
    return {
        url: `edc/auto-join-task/page`,
        method: 'GET',
        params
    }
}

export const detailApi = (id) => {
    return {
        url: `edc/auto-join-task/detail/` + id,
        method: 'GET'
    }
}

export const deleteApi = (data) => {
    return {
        url: `edc/auto-join-task/deletion`,
        method: 'POST',
        data
    }
}

export const addApi = (data) => {
    return {
        url: `edc/auto-join-task/insert`,
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: `edc/auto-join-task/update`,
        method: 'POST',
        data
    }
}

// 立即执行任务
export const execApi = (id) => {
    return {
        url: `edc/auto-join-task/run/` + id,
        method: 'POST',
    }
}

export const stopApi = (id) => {
    return {
        url: `edc/auto-join-task/stop/` + id,
        method: 'POST',
    }
}

// export const downloadApi = (params) => {
//     return {
//         url: `edc/pat-import-record/download`,
//         method: 'GET',
//         params,
//         responseType: 'blob'
//     }
// }