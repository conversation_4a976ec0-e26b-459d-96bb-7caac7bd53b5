export const addRequest = (data) => {
    return {
        url: 'search/role-permission/insert',
        method: 'POST',
        data
    }
}

export const editRequest = (data) => {
    return {
        url: 'search/role-permission/update',
        method: 'POST',
        data
    }
}

export const listRequest = (data) => {
    return {
        url: `search/role-permission/detail?indexId=${data.indexId}&roleCode=${data.roleCode}`,
        method: 'GET',
    }
}
