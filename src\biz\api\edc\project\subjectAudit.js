export const queryListApi = (params) => {
    return {
        url: 'edc/subject/audit/record/page',
        method: 'get',
        params,
    }
}

export const detailApi = (id) => {
    return {
        url: 'edc/subject/audit/record/detail/' + id,
        method: 'get',
        params,
    }
}

export const deleteApi = (data) => {
    return {
        url: 'edc/subject/audit/record/deletion',
        method: 'post',
        data,
    }
}

// 审核子项目
export const auditSubject = (data) => {
    return {
        url: 'edc/subject/audit/record/update',
        method: 'post',
        data,
    }
}

// 在个人中心审核子项目
export const auditInUserCenter = (data) => {
    return {
        url: '/edc/subject/audit/record/audit',
        method: 'post',
        data,
    }
}