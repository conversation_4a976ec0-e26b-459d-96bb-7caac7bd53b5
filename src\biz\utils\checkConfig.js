import { getAccessUser } from "./auth"

export const personalConfigs = {
  fullTextSearch_onlyInput: 'on-search-home-input',
  fullTextSearch_isHistory: 'show-search-history',
  fullTextSearch_isSynonym: 'on-search-synonym',
  advancedSearch_isSynonym: 'on-synonym',
  system_name: 'system-name',
  watermark_enable: 'watermark-enable',
  system_language: 'system-language'
}

export function checkConfig(key) {
  if (key === undefined || key === null || key.length === 0) {
    return false
  }
  const user = getAccessUser()
  if (user == null) {
    return false
  }
  const configs = user.configs
  if (!configs) {
    return true
  }

  const configValue = configs[key]
  if (configValue === undefined || configValue === null) {
    return true
  } else {
    if (configValue === true || configValue === 'true') {
      return true
    } else if (configValue === false || configValue === 'false') {
      return false
    } else {
      return configValue
    }
  }
}
