
export const queryDetailApi = (params) => {
    return {
        code: 200,
        data: {

        }
    }
}

// 分页数据
export const queryListApi = (params) => {
    return {
        url: 'search/export-model/page',
        method: 'GET',
        params
    }
}

// 新增观测字段配置
export const addApi = (data) => {
    return {
        url: 'search/export-model/insert',
        method: 'POST',
        data
    }
}

// 编辑观测字段配置
export const updateApi = (data) => {
    return {
        url: 'search/export-model/update',
        method: 'POST',
        data
    }
}

// 删除
export const deleteBatchApi = (data) => {
    return {
        url: 'search/export-model/deletion',
        method: 'POST',
        data
    }
}

