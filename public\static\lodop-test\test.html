<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe-lodop</title>
</head>
<body>
    <button onclick="preview()">预览</button>
    <button onclick="printFunc()">打印</button>
    <button onclick="getHtmlStr()">获取html</button>
    <button onclick="getBase64()">获取Base64</button>
    <script>
        window.addEventListener('message', (event) => {
            const {funType, data: {htmlStr}} = event.data
            console.log(htmlStr)
        }, false);
        var paramsData = {
            code: "printparam",
            param: `{
                "name": "李四",
                "sex": "女",
                "goods": [{
                    "name": "洗发水",
                    "price": 8.88
                }, {
                    "name": "牙刷",
                    "price": 555.555
                }, {
                    "name": "香皂",
                    "price": 342
                }, {
                    "name": "毛巾",
                    "price": 432
                }]
            }`,
            elements: [
                {
                    "options": {
                        "left": 192.5,
                        "top": 127.5,
                        "height": 31,
                        "width": 200,
                        "hideTitle": true,
                        "testData": "传入文字",
                        "title": "传入文字",
                        "id": "abc123",
                        "field": "abc123",
                        "hide0": false,
                        "coordinateSync": false,
                        "widthHeightSync": false,
                        "right": 393.74998474121094,
                        "bottom": 158.5,
                        "vCenter": 293.74998474121094,
                        "hCenter": 143
                    },
                    "printElementType": {
                        "title": "文本",
                        "type": "text"
                    }
                }
            ],
            elementsData: {
                abc123: "测试文字"
            }
        }
        function preview() {
            const name = window.parent.name
            window.parent.postMessage({ 
                funcName: 'previewFn',
                lodopName: name,
                params: paramsData
            }, '*')
        }
        function printFunc() {
            const name = window.parent.name
            window.parent.postMessage(
                { 
                funcName: 'printFn',
                lodopName: name,
                params: paramsData
            }, '*')
        }
        function getHtmlStr() {
            const name = window.parent.name
            window.parent.postMessage( { 
                funcName: 'getHtmlStr',
                lodopName: name,
                params: paramsData
            }, '*')
        }
        function getBase64() {
            const name = window.parent.name
            window.parent.postMessage( { 
                funcName: 'getBase64',
                lodopName: name,
                params: paramsData
            }, '*')
        }
    </script>
</body>
</html>