import { LoginLayout, TabLayout, RouteView, BlankLayout } from '@components/layouts'
import i18n from '@base/i18n'
import { returnGlobalValue } from '@base/utils'
import { stringifyParams } from '@/utils/common'
import {
    Encrypt,
    EncryptUrl,
    Decrypt
} from "@/utils/index"
/**
 * 静态普通路由
 * @type {{}}
 */
export const bizConstantRouterMap = [
    /**
     * 直接跳转到项目内的菜单，通过先跳转到中转页，再判断query参数的方式跳转到实际页面（query支持携带其他参数）
     * @example 示例如下：
     * this.$router.push({
            path:'/edc/subject-transfer',
            query:{
                url:'/edc/p/patient/patient-list',
                subjectId:'28d80bcb3b944f1aaba9ecb25be83642',
                // testOther:'otherQuery'
            }
        })
     */
    {
        path: '/edc/subject-transfer',
        name: 'subjectTransfer',
        component: TabLayout,
    },
    {
        path: '/search/adm-time-line',
        name: 'admTimeLine',
        component: TabLayout,
        children: [
            {
                path: 'index',
                name: 'admTimeLineIndex',
                meta: {
                    title: i18n.t('患者就诊列表'),
                    keepAlive: true
                },
                component: () => import('@/views/search/web/adm-time-line/index'),
                beforeEnter: (to, from, next) => {
                    const new_url = returnGlobalValue('VUE_APP_BASE_URL')
                        + '/adm-time-line/index.html?token=${token}&p=' + to.query.p
                    to.meta.url = new_url
                    next();
                }
            },
        ]
    },
    {
        path: '/search/case-detail',
        name: 'caseDetail',
        component: TabLayout,
        children: [
            {
                path: 'index',
                name: 'caseDetailIndex',
                meta: {
                    title: i18n.t('患者详情信息'),
                    keepAlive: true
                },
                component: () => import('@/views/search/web/case-detail/index'),
                beforeEnter: (to, from, next) => {
                    const new_url = returnGlobalValue('VUE_APP_BASE_URL')
                        + '/case-detail/index.html?token=${token}&p=' + to.query.p
                    to.meta.url = new_url
                    const params = JSON.parse(Decrypt(to.query.p))
                    to.meta.title = i18n.t('患者详情信息') + `-${params.regno}`
                    next();
                }
            },
        ]
    },
    {
        path: '/patient-view',
        name: 'patientView',
        component: () =>
            import(/* webpackChunkName: "index" */ '@/views/edc/patient/patient-view/patient-view'),
    },
    {
        path: '/patient-view-disAudit',
        name: 'patientViewDisAudit',
        component: () =>
            import(/* webpackChunkName: "index" */ '@/views/edc/patient/patient-view/patient-view-disAudit'),
    },
    {
        path: '/big-screen-designer-tab',
        component: TabLayout,
        name: 'bigScreenDesignerTab',
        children: [
            {
                path: 'designer',
                name: 'bigScreenDesigner',
                meta: {
                    title: i18n.t('报表设计'),
                    keepAlive: true
                },
                component: () => import('@/views/edc/big-screen-designer/designer'),
                beforeEnter: (to, from, next) => {
                    const name = to.query.name && to.query.name.length > 0 ? `-${to.query.name}` : ''
                    to.meta.title = i18n.t('报表设计') + name
                    next();
                }
            },
            {
                path: 'viewer',
                name: 'bigScreenViewer',
                meta: {
                    title: i18n.t('报表预览'),
                    keepAlive: true
                },
                component: () => import('@/views/edc/big-screen-designer/viewer'),
                beforeEnter: (to, from, next) => {
                    const name = to.query.name && to.query.name.length > 0 ? `-${to.query.name}` : ''
                    to.meta.title = i18n.t('报表设计') + name
                    next();
                }
            }
        ]
    },
    {
        path: '/workbench-designer-tab',
        component: TabLayout,
        name: 'workbenchDesignerTab',
        children: [
            {
                path: 'designer',
                name: 'workbenchDesigner',
                meta: {
                    title: i18n.t('数据概览设计'),
                    keepAlive: true
                },
                component: () => import('@/views/edc/workbench-designer/designer'),
                beforeEnter: (to, from, next) => {
                    const name = to.query.name && to.query.name.length > 0 ? `-${to.query.name}` : ''
                    to.meta.title = i18n.t('数据概览设计') + name
                    next();
                }
            },
            {
                path: 'viewer',
                name: 'workbenchViewer',
                meta: {
                    title: i18n.t('数据概览预览'),
                    keepAlive: true
                },
                component: () => import('@/views/edc/workbench-designer/viewer'),
                beforeEnter: (to, from, next) => {
                    const name = to.query.name && to.query.name.length > 0 ? `-${to.query.name}` : ''
                    to.meta.title = i18n.t('数据概览设计') + name
                    next();
                }
            }
        ]
    },
    {
        path: '/login-middle',
        name: 'loginMiddle',
        component: () =>
            import(/* webpackChunkName: "index" */ '@/views/login-middle/index.vue'),
    },
    // 索引管理 插件
    {
        path: '/index-manage-plugin',
        name: 'indexManagePlugin',
        component: TabLayout,
        children: [
            {
                path: 'head',
                name: 'head',
                meta: {
                    title: i18n.t('Head插件'),
                    // url: returnGlobalValue('VUE_APP_BASE_URL') + '/head/index.html?token=${token}&base_uri=' + returnGlobalValue('VUE_APP_BASE_URL'),
                },
                component: () => import('@base/components/layouts/IframePageView'),
                beforeEnter: (to, from, next) => {

                    to.meta.url = returnGlobalValue('VUE_APP_BASE_URL')
                        + '/head/index.html?token=${token}&base_uri=' + returnGlobalValue('VUE_APP_BASE_URL')
                        + '&sign=' + to.query.sign

                    next();
                  }
            },
            {
                path: 'sense',
                name: 'sense',
                meta: {
                    title: i18n.t('Sense插件'),
                },
                component: () => import('@base/components/layouts/IframePageView'),
                beforeEnter: (to, from, next) => {

                    to.meta.url = returnGlobalValue('VUE_APP_BASE_URL')
                        + '/sense/index.html?token=${token}&base_uri=' + returnGlobalValue('VUE_APP_BASE_URL')
                        + '&sign=' + to.query.sign

                    next();
                  }
            }
        ]
    },
    {
        path: '/search/quality-report/detail',
        name: 'qualityReportDetail',
        component: TabLayout,
        children: [
            {
                path: 'index',
                name: 'qualityReportDetailIndex',
                meta: {
                    title: i18n.t('质量报告明细'),
                    keepAlive: true
                },
                component: () => import('@/views/search/admin/quality-report/detail'),
                beforeEnter: (to, from, next) => {
                    to.meta.title = `${i18n.t('质量报告明细')}(${JSON.parse(Decrypt(to.query.p)).time})`
                    next();
                }
            },
        ]
    },
    {
        path: '/dynamic-page-designer',
        component: TabLayout,
        name: 'dynamicPageDesignerTab',
        children: [
            {
                path: 'designer',
                name: 'dynamicPageDesigner',
                meta: {
                    title: i18n.t('页面设计'),
                    keepAlive: true
                },
                component: () => import('@/views/edc/dynamic-page-designer/designer'),
            }
        ]
    },
    {
        path: '/login-middle',
        name: 'login-middle',
        component: () => import('@/views/login-middle')
    }
]


/**
 * 静态菜单路由
 * @type { *[] }
 */
export const bizConstantMenuMap = [

]
