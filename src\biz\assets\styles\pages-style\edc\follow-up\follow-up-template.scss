// .follow-up-template {
//   .add-btn {
//     font-size: 80px;
//     color: #b3d8ff;
//   }

//   .add-head {
//     text-align: center;
//   }

//   .temp-container {
//     border: 1px solid #EBEEF5;
//     // padding: 0 20px;
//     cursor: pointer;
//     position: relative;
//     border-radius: 10px;
//     -webkit-transition: .3s;
//     transition: .3s;
//     box-shadow: 0 2px 2px 0 rgb(0 0 0 / 10%);
//   }

//   .temp-container:hover {
//     box-shadow: 0 2px 15px 0 rgb(0 0 0 / 10%);

//     .temp-head {
//       background-color: #e2eaf2d3;
//     }
//   }

//   .col-item {
//     margin-bottom: 20px;

//     .temp-head {
//       color: #666;
//       height: 130px;
//       // line-height: 80px;
//       background-color: #e2eaf26e;
//       padding: 20px;
//       border-radius: 10px 10px 0 0;
//       position: relative;

//       .temp-head-name {
//         font-size: 20px;
//         font-weight: bolder;
//         white-space: nowrap;
//         text-overflow: ellipsis;
//         overflow: hidden;
//         // padding-right: 40px;
//         margin: 10px 0 30px 0;
//       }

//       .temp-head-desc {
//         white-space: nowrap;
//         text-overflow: ellipsis;
//         overflow: hidden;
//         font-size: 14px;
//         color: #888;
//       }
//     }

//     .temp-foot {
//       height: 50px;
//       // line-height: 50px;
//       position: relative;
//       padding: 10px 20px;
//     }

//     .temp-foot :nth-child(2),
//     .temp-foot :nth-child(3) {
//       // text-align: center;
//       float: right;
//     }
//   }

// }