.index-box-home {
  width: calc(100% - 5px);
  height: 100%;

  .normal {
    width: 100%;
    height: 100%;
  }

  .special {
    width: 100%;
    height: 100%;
  }
}
.common-model-container {
  .common-card {
    background-color: #fff;
    .blockTitle {
      padding-top: 10px;
      padding-left: 10px;
      display: inline-block;
      color: #666;
    }
  }
}

// 数据概览
@import "./comp/normal-model.scss";
@import "./comp/messageBoxForQ&A.scss";
