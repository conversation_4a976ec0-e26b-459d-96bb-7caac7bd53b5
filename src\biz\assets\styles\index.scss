@import "./components.scss";
@import "./querybuilder/index.scss";

// pages
@import "./pages-style/search/web/index.scss";
@import "./pages-style/edc/index.scss";
@import "./page.scss";

.table-tips{
  color: rgb(144, 147, 153);
  font-size: 14px;
  background: white;
  box-sizing: border-box;
  padding: 5px;
  border-left: 1px solid #e2e2e2;
  border-right: 1px solid #e2e2e2;
}
.logo-title img{
  height: 36px !important;
  box-sizing: border-box;
}
.query-form,.query-form.hos-biz-form{
  height: unset;
}
.hos-container{
  .tree-card.hos-card{
    height: 100%;
    box-sizing: border-box;
    width: 100% !important;
    .hos-card__body{
      padding: 10px !important;
    }
  }
}
// 防止表单设计器上的弹框被hos弹框遮挡，手动固定z-index值
.el-dialog__wrapper{
  z-index: 9999998 !important;
}
.el-drawer__wrapper{
  z-index: 999999 !important;
}
.el-popper{
  z-index: 9999999 !important;
}
.el-tooltip__popper{
  z-index: 99999999 !important;
}

// 打印
.el-checkbox__input,.el-radio__input{
//添加以下三行即可在打印的时候出现相应的样式
  -webkit-print-color-adjust: exact;

  -moz-print-color-adjust: exact;
  color-adjust: exact;
}

 