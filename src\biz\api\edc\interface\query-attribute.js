// 获取外部接口所有的接口类别
export const getAllInterfaceCate = (params) => {
    return {
        url: `edc/external-category/all`,
        method: 'GET',
        params
    }
}

// 获取所有用查询项目做查询的接口类别
export const getAllItemTypeInterfaceCategory = (params) => {
    return {
        url: `edc/external-category/all/item-type`,
        method: 'GET',
        params
    }
}

// 分页查询
export const pageListApi = (params) => {
    return {
        url: `edc/external-item-prop/page`,
        method: 'GET',
        params
    }
}

// 获取指定接口类别下所有
export const getAllByCategoryId = (categoryId) => {
    return {
        url: `edc/external-item-prop/list/${categoryId}`,
        method: 'GET'
    }
}

// 获取全部
export const queryAllApi = (params) => {
    return {
        url: `edc/external-item-prop/all`,
        method: 'GET',
        params
    }
}

// 根据id查询详情
export const queryDetailApi = (id) => {
    return {
        url: `edc/external-item-prop/detail/${id}`,
        method: 'GET',
    }
}

// 新增接口
export const addApi = (data) => {
    return {
        url: 'edc/external-item-prop/insert',
        method: 'POST',
        data
    }
}

// 修改接口
export const editApi = (data) => {
    return {
        url: 'edc/external-item-prop/update',
        method: 'post',
        data
    }
}

// 批量删除
export const deleteApi = (data) => {
    return {
        url: `edc/external-item-prop/deletion`,
        method: 'POST',
        data
    }
}

// 获取所有数据类型
export const getAllDataType = () => {
    return {
        url: `edc/external-data-type/all`,
        method: 'GET'
    }
}

// 获取ES查询属性代码
export const getEsQueryAttrCode = (params) => {
    return {
        url: `edc/external/setting/get-es-item-prop`,
        method: 'get',
        params,
    }
}

// 同步ES查询属性数据
export const asyncEsQueryAttrData = (data) => {
    return {
        url: `edc/external/setting/async-item-prop`,
        method: 'POST',
        data
    }
}