// 公众号配置
export const getDefaultConfig = (subjectId) => {
    return {
        url: '/wx/mp/api/v1/config/default',
        method: 'get',
    }
}

export const setDefaultConfig = (data) => {
    return {
        url: '/wx/mp/api/v1/config/default',
        method: 'POST',
        data
    }
}

export const getConfigBySubjectId = (subjectId) => {
    return {
        url: '/wx/mp/api/v1/config/proj/' + subjectId,
        method: 'get',
    }
}

export const setConfigBySubjectId = (data) => {
    return {
        url: '/wx/mp/api/v1/config/add-or-update',
        method: 'POST',
        data
    }
}

export const saveRealm = (data) => {
    return {
        url: '/wx/mp/api/v1/config/save-realm',
        method: 'POST',
        data
    }
}

/**
 * 生成公众号二维码
 * @param {} configId
 * @returns
 */
export const createQrcode = (configId) => {
    return {
        url: '/wx/mp/api/v1/create-qr/' + configId,
        method: 'get',
        responseType: 'blob'
    }
}

/**
 * 生成公众号用户绑定临时二维码
 * @param {} configId
 * @param {} userId
 * @returns
 */
export const createUserBindTempQrcode = ({configId, userId}) => {
    return {
        url: `/wx/mp/api/v1/create-tmp-qr/${configId}/${userId}`,
        method: 'get',
        responseType: 'blob'
    }
}

/**
 * 生成患者临时二维码
 * @param {} configId
 * @returns
 */
export const createPatientTempQrcode = (patientId) => {
    return {
        url: `/wx/mp/api/v1/create-tmp-patient-qr/${patientId}`,
        method: 'get',
        responseType: 'blob'
    }
}