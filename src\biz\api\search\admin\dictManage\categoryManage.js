
export const addDictCate = (data) => {
  return {
    url: 'search/es/dict-cate/insert',
    method: 'POST',
    data
  }
}

export const addSubDictCate = (data) => {
  return {
    url: 'search/es/dict/insert',
    method: 'POST',
    data
  }
}

export const updateDictCate = (data) => {
  return {
    url: 'search/es/dict-cate/update',
    method: 'post',
    data
  }
}

export const updateSubDictCate = (data) => {
  return {
    url: 'search/es/dict/update',
    method: 'post',
    data
  }
}

export const deleteDictCate = (data) => {
  return {
    url: 'search/es/dict-cate/deletion',
    method: 'POST',
    data
  }
}

export const deleteSubDictCate = (data) => {
  return {
    url: 'search/es/dict/deletion',
    method: 'POST',
    data
  }
}

export const dictPageList = (params) => {
  return {
    url: 'search/es/dict/page',
    method: 'get',
    params
  }
}

