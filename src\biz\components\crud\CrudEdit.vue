<template>
  <div :loading="loading">
    <hos-form ref="dialogForm" :model="dialogForm" label-width="auto" label-position="right">
      <hos-row :gutter="20">
        <!-- 编码 -->
        <template v-for="(item, i) in fields">
          <hos-col v-if="fieldIsHide(item.editHide) == false" :key="i" :span="24 / editColNum" >
            <hos-form-item :label="item.label" :prop="item.prop" :rules="item.rules || []">
              <hos-input
                v-if="item.inputType == 'input'"
                v-model.trim="dialogForm[item.editField]"
                v-bind="item"
                :placeholder="item.placeholder || $t('common.pleaseEnter')"
                :clearable="item.clearable !== false"
                :disabled="fieldIsDisable(item.disabled)"
                @change="(value) => formChange(item.editField, value)"
              />
              <!-- 下拉选择select -->
              <hos-select
                v-else-if="item.inputType == 'select'"
                  v-model="dialogForm[item.editField]" :clearable="item.clearable !== false" :disabled="fieldIsDisable(item.disabled)" :placeholder="item.placeholder || $t('common.pleaseSelect')">
                  <hos-option v-for="o_item in item.SelectOption.localOptions" :key="o_item[item.SelectOption.option]" :label="o_item[item.SelectOption.label]" :value="o_item[item.SelectOption.option]">
                  </hos-option>
              </hos-select>
              <!-- 下拉多选selectMulti -->
              <hos-select
                v-else-if="item.inputType == 'selectMulti'"
                  v-model="dialogForm[item.editField]" multiple :clearable="item.clearable !== false" :disabled="fieldIsDisable(item.disabled)" :placeholder="item.placeholder || $t('common.pleaseSelect')">
                  <hos-option v-for="o_item in item.SelectOption.localOptions" :key="o_item[item.SelectOption.option]" :label="o_item[item.SelectOption.label]" :value="o_item[item.SelectOption.option]">
                  </hos-option>
              </hos-select>
              <!-- 开关 -->
              <hos-switch
                v-else-if="item.inputType == 'switch'"
                v-model.trim="dialogForm[item.editField]"
                v-bind="item"
                :disabled="fieldIsDisable(item.disabled)"
                :active-value="1"
                :inactive-value="0"
                inactive-color="#ccc"
                active-color="#409eff"
                @change="(value) => formChange(item.editField, value)"
              />
              <!-- 数字 -->
              <hos-input-number
                v-else-if="item.inputType == 'input-number'"
                v-model.trim="dialogForm[item.editField]"
                v-bind="item"
                :min="item.inputNumberOption ? item.inputNumberOption.min : 0"
                :max="item.inputNumberOption ? item.inputNumberOption.max : undefined"
                :placeholder="item.placeholder || $t('common.pleaseEnter')"
                :clearable="item.clearable !== false"
                :disabled="fieldIsDisable(item.disabled)"
                @change="(value) => formChange(item.editField, value)"
              />
              <!-- 日期时间框  -->
              <hos-date-picker
                v-else-if="item.inputType.indexOf('date') >= 0"
                v-model="dialogForm[item.editField]"
                v-bind="item"
                style="width: 100%"
                :placeholder="item.placeholder || $t('common.pleaseSelect')"
                :type="item.inputType"
                :format="item.format"
                :value-format="item.valueFormat"
                :disabled="fieldIsDisable(item.disabled)"
                :clearable="item.clearable !== false"
                @change="(value) => formChange(item.editField, value, null)"
              />
              <!-- textarea -->
              <hos-input
                v-else-if="item.inputType == 'textarea'"
                v-model.trim="dialogForm[item.editField]"
                v-bind="item"
                :placeholder="item.placeholder || $t('common.pleaseEnter')"
                :clearable="item.clearable !== false"
                :disabled="fieldIsDisable(item.disabled)"
                type="textarea"
                :rows="2"
                @change="(value) => formChange(item.editField, value, null)"
              />
              <hos-input
                v-else
                v-model.trim="dialogForm[item.editField]"
                v-bind="item"
                :placeholder="item.placeholder || $t('common.pleaseEnter')"
                :clearable="item.clearable !== false"
                :disabled="fieldIsDisable(item.disabled)"
                @change="(value) => formChange(item.editField, value)"
              />
            </hos-form-item>
          </hos-col>
        </template>
      </hos-row>
    </hos-form>
    <div slot="footer" class="dialog-footer tc">
      <hos-button type="primary" @click="cancel">{{
        $t('operation.cancel')
      }}</hos-button>
      <hos-button type="success" @click="save()">{{
        $t('operation.save')
      }}</hos-button>
    </div>
  </div>
</template>
<script>
import { makeWb, makePy } from '@base/utils/character/trans-char-utils.js'
export default {
  props: ['uid', 'id', 'status'],
  components: {},
  inject: {
    CRUD_PROVIDE: {
      default: null
    }
  },
  data() {
    return {
      dialogForm: {},
      loading: false
    }
  },
  computed: {
    editColNum() {
      return this.CRUD_PROVIDE.option.editColNum || 2
    },
    fields() {
      const res = []
      this.CRUD_PROVIDE.option.columns.forEach((col) => {
        if (col.editField) {
          res.push({
            ...col,
            prop: col.editField
          })
        }
      })
      return res
    }
  },
  created() {
    // 为查询框中所有input加上默认值
    this.fields.forEach((item) => {
      // 动态添加属性
      this.$set(this.dialogForm, item.editField, item.defaultValue === undefined ? null : item.defaultValue)
    })
    this.initData()
  },
  mounted() {},
  watch: {},
  methods: {
    handlePyWb(val) {
      this.dialogForm.wbCode = makeWb(val)
      this.dialogForm.pyCode = makePy(val)
    },
    // 初始化数据
    initData() {
      if (this.id) {
        this.getInfoData(this.id)
      }
    },
    // 该行是否显示 true/false/ 'hideOnAdd hideOnEdit'
    fieldIsHide(editHide) {
      if (typeof editHide === 'boolean') {
        return editHide
      }
      if (typeof editHide === 'string') {
        if (this.modelType === 'add') {
          return editHide.indexOf('hideOnAdd') >= 0
        }
        //   if (this.modelType === 'view') {
        //     return editHide.indexOf('hideOnView') >= 0
        //   }
        if (this.modelType === 'edit') {
          return editHide.indexOf('hideOnEdit') >= 0
        }
      }
      return false
    },
    // 该行是否禁用 true/false/ 'disableOnAdd disableOnEdit'
    fieldIsDisable(disable) {
      if (typeof disable === 'boolean') {
        return disable
      }
      if (typeof disable === 'string') {
        if (!this.id) {
          // add
          return disable.indexOf('disableOnAdd') >= 0
        }
        //   if (this.modelType === 'view') {
        //     return disable.indexOf('disableOnView') >= 0
        //   }
        if (this.id) {
          return disable.indexOf('disableOnEdit') >= 0
        }
      }
      return false
    },
    // 表单任何一个变动时，通知外部v-model
    formChange(fieldName, fieldVal) {
      this.$emit('input', this.dialogForm)
      // 表单变动后，回调option中的formChange事件
      if (typeof this.CRUD_PROVIDE.option.formChange === 'function') {
        this.CRUD_PROVIDE.option.formChange(this.dialogForm, fieldName, fieldVal)
      }
    },
    // 修改时获取详情
    getInfoData(id) {
      this.CRUD_PROVIDE.option.buttons.queryByPrimaryKey.api(this.id).then((res) => {
        if (res && res.code == '200') {
          if (res.data.seqNo == null) {
            res.data.seqNo = undefined
          }
          this.dialogForm = res.data
          //   if (
          //       !this.dialogForm.pyCode &&
          //       !this.dialogForm.wbCode &&
          //       this.dialogForm.name
          //   ) {
          //       this.handlePyWb(this.dialogForm.name);
          //   }
        }
      })
    },
    // 保存dialog
    save() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          this.loading = true
          const promise = this.id
            ? this.CRUD_PROVIDE.option.buttons.edit.api(this.dialogForm)
            : this.CRUD_PROVIDE.option.buttons.add.api(this.dialogForm)
          promise.then((res) => {
            this.loading = false
            if (res && res.code == '200') {
              this.$message.success(res.msg)
              this.cancel()
              this.$store.commit('UPDATE_TABLE', {
                _uid: this.CRUD_PROVIDE.uid
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    //取消按钮
    cancel() {
      this.$store.commit('CLOSE_DIALOG', {
        _uid: this.uid
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.margin-right-10 {
  margin-right: 10px;
}
</style>
