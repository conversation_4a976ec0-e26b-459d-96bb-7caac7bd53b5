// 查询当前转入条件配置
export const queryCurConfigApi = (params) => {
  return {
    url: 'edc/disease-convert/cur-config',
    method: 'get',
    params,
  }
}

// 表达式验证
export const validateExpressionApi = (data) => {
  return {
    url: 'edc/disease-convert/verify',
    method: 'post',
    data
  }
}

// 保存转入配置
export const saveConfigApi = (data) => {
  return {
    url: 'edc/disease-convert/save',
    method: 'post',
    data
  }
}

// 更新转入配置
export const updateConfigApi = (data) => {
  return {
    url: 'edc/disease-convert/update',
    method: 'put',
    data
  }
}

// 获取转入配置历史记录
export const queryHistoryApi = (params) => {
  return {
    url: 'edc/disease-convert/page',
    method: 'get',
    params,
  }
}

// 获取全程转入病种记录
export const getTransferExecuteRecordApi = (params) => {
  return {
    url: 'edc/disease-patient/transfer-execute',
    method: 'get',
    params
  }
}

// 转入执行明细
export const getTransferExecuteDetailApi = (params) => {
  return {
    url: 'edc/disease-patient/tansfer-detail',
    method: 'get',
    params
  }
}