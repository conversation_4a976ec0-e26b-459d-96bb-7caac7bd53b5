import {
  mapState
} from 'vuex'
import { deepClone } from "@/utils/index.js"
import { getPermissionValue } from "@base/utils/permission/index.js"
export default {
  data() {
    return {
      businessSystemConfig:null,
    }
  },
  computed: {
    ...mapState( {
      userInfo: state => state.user.userInfo,
      subjectInfo: state => state.subject.subjectInfo,
      subsetInfo: state => state.subset.subsetInfo,
      modelData: state => state.model.modelData,
    } ),
    userId() {
      return this.userInfo && this.userInfo.accountId ? this.userInfo.accountId : null
    },
    subjectId() {
      return this.subjectInfo && this.subjectInfo.id ? this.subjectInfo.id : null
    },
    subsetId() {
      return this.subsetInfo && this.subsetInfo.subsetId ? this.subsetInfo.subsetId : null
    },
    projectId() {
      return this.subjectInfo && this.subjectInfo.projectId ? this.subjectInfo.projectId : null
    },
    isYesList() {
      return [
        { label: this.$t( 'common.yes' ), value: 1 },
        { label: this.$t( 'common.no' ), value: 0 }
      ]
    }
  },
  created() { },
  mounted() { },
  destroyed() { },
  methods: {
    // 获取对象类型
    getObjectType( obj ) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object',
      }
      if ( obj instanceof Element ) {
        return 'element'
      }
      return map[toString.call( obj )]
    },
    isNumber( obj ) {
      return this.getObjectType( obj ) == 'number'
    },
    isString( obj ) {
      return this.getObjectType( obj ) == 'string'
    },
    isArray( obj ) {
      return this.getObjectType( obj ) == 'array'
    },
    hasOwn( obj, key ) {
      return Object.prototype.hasOwnProperty.call( obj, key )
    },

    isNotBlank( val ) {
      return !this.isBlank( val )
    },
    isBlank( val ) {
      if ( this.isNull( val ) ) {
        return true
      }
      if ( typeof val === 'string' ) {
        return val.trim() == ''
      }
      if ( typeof val === 'object' ) {
        for ( var key in val ) {
          return false
        }
        return true
      }
      return false
    },
    isYes(value, h) {
      const isYesMap = this.listToMap(this.isYesList);
      if (h) {
        return h('hos-tag', {
          props: {
            type: value ? 'primary' : 'info',
            size: 'mini'
          }
        }, isYesMap[value] || this.$t('common.no'));
      } else {
        return isYesMap[value] || this.$t('common.no');
      }
    },
    listToMap( list = [], option = {} ) {
      const { label = 'label', value = 'value' } = option
      const res = {}
      list.forEach( item => res[item[value]] = item[label] )
      return res
    },
    isNotNull( val ) {
      return !this.isNull( val )
    },
    isNull( val ) {
      // 特殊判断
      if ( val && parseInt( val ) === 0 ) return false
      const list = ['$parent']
      if ( val instanceof Date || typeof val === 'boolean' || typeof val === 'number' ) return false
      if ( val instanceof Array ) {
        if ( val.length === 0 ) return true
      } else if ( val instanceof Object ) {
        val = this.deepClone( val )
        list.forEach( ( ele ) => {
          delete val[ele]
        } )
        for ( var o in val ) {
          return false
        }
        return true
      } else {
        if ( val === 'null' || val == null || val === 'undefined' || val === undefined || val === '' ) {
          return true
        }
        return false
      }
      return false
    },

    // 对象深拷贝
    deepClone,

    // 查询指定字典编码查询字典项
    async getDictItemByCode( cateCode, dataTargetPropName ) {
      const cacheDictItem = await this.$store.dispatch( 'getDictItem', cateCode )
      // console.log('cacheDictItem',cacheDictItem)
      if ( cacheDictItem ) {
        // 从缓存中获取
        if(dataTargetPropName){
          this[dataTargetPropName] = cacheDictItem
        }
        return cacheDictItem
      }
      const res = await this.$api( 'base.sys_dict.selectByCode', {
        code: cateCode
      } )
      if ( res && res.code == '200' ) {
        res.data.forEach( item => {
          // 对于字典配置的是字符串的数字类型的，自动转为数字类型的数字
          if ( !isNaN( Number( item.value ) ) ) {
            item.value = Number( item.value )
          }
        } )
        if(dataTargetPropName){
          this[dataTargetPropName] = res.data
        }
        this.$store.dispatch( 'setDictItem', { key: cateCode, list: res.data } )
        return res.data
      } else {
        if(dataTargetPropName){
          this[dataTargetPropName] = []
        }
        return []
      }
    },
    /**
     * 获取业务系统配置
     * @param {null|String|Array<String>|Object} code
     * @example 传参code：
     *    1.不传：返回包含所有配置的对象 null => {'csm-hospital-name':'value',...}
     *    2.字符串类型：返回对应key的值 'csm-hospital-name' => 'value'
     *    3.数组：返回传参数组对应的配置项 ['csm-hospital-name'] => ['value']
     *    4.对象：返回传参对象对应的配置项 {hospitalName:'csm-hospital-name'} => {hospitalName:'value'}
     */
    async getBusinessSystemConfig(code){
      if(!this.businessSystemConfig){
        // 没有缓存，从接口查
        const res = await this.$api('biz.config.config-data.busiConfigSelect')
        if (res.data && res.data.length > 0) {
          const obj = {}
          res.data.forEach(item => {
            obj[item.code] = item.value
          })
          this.businessSystemConfig = obj
        }
      }
      if(!this.businessSystemConfig){
        return null
      }
      const config = this.businessSystemConfig
      if(!code){
        // 1.不传：返回包含所有配置的对象 null => {'csm-hospital-name':'value',...}
        return config
      }
      if(typeof code === 'string'){
        // 2.字符串类型：返回对应key的值 'csm-hospital-name' => 'value'
        return config[code] === undefined ? null : config[code]
      }else if(Array.isArray(code)){
        // 3.数组：返回传参数组对应的配置项 ['csm-hospital-name'] => ['value']
        let res = []
        code.forEach(item=>{
          res.push(config[item] === undefined ? null : config[item])
        })
        return res
      }else if(typeof code === 'object'){
        // 4.对象：返回传参对象对应的配置项 {hospitalName:'csm-hospital-name'} => {hospitalName:'value'}
        let res = {}
        Object.keys(code).forEach(key=>{
          let v_key = code[key]
          res[key] = config[v_key] === undefined ? null : config[v_key]
        })
        return res
      }

    },
    $hasPermisson() {
      // 原来代码里面很多地方在展示何内容的地方用了权限判断，这里做了兼容，返回布尔值
      // const PERMISSION_INVISIBLE = "0"; "0"为不可见
      return getPermissionValue(permiKey) !== "0"
    }
  }
}
