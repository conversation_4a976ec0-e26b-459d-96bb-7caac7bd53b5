// 分页查询分类
export const catePageListApi = (params) => {
    return {
        url: `edc/external-item/page/cate`,
        method: 'GET',
        params
    }
}

// 删除，分类与子项共用
export const deleteApi = (data) => {
    return {
        url: `edc/external-item/delete`,
        method: 'POST',
        data
    }
}

// 根据parentID获取子项
export const getChildByPid = (params) => {
    return {
        url: `edc/external-item/page`,
        method: 'GET',
        params
    }
}

// 查询项导入模板下载
export const downloadExcel = () => {
    return {
        url: `edc/external-item/excel/download`,
        method: 'POST',
        responseType: 'blob'
    }
}

// 根据id查询详情
export const queryDetailApi = (id) => {
    return {
        url: `edc/external-item/detail/${id}`,
        method: 'GET',
    }
}

// 新增接口
export const addApi = (data) => {
    return {
        url: 'edc/external-item/insert',
        method: 'POST',
        data
    }
}

// 修改接口
export const editApi = (data) => {
    return {
        url: 'edc/external-item/update',
        method: 'post',
        data
    }
}

// 上传excel
export const uploadExcel = (data) => {
    return {
        url: `edc/external-item/excel/import/${data.categoryId}`,
        method: 'post',
        data: data.formData
    }
}

// 获取指定接口类型下的所有查询项分类和查询项
export const getAllItemsByCateId = (params) => {
    return {
        url: `edc/external-item/list-by-category`,
        method: 'get',
        params,
    }
}

// 获取ES查询项分类编码
export const getEsItemCateCode = (params) => {
    return {
        url: `edc/external/setting/get-es-item-cate-code`,
        method: 'get',
        params,
    }
}

// 获取ES查询项细项编码
export const getEsItemCode = (params) => {
    return {
        url: `edc/external/setting/get-es-item-code`,
        method: 'get',
        params,
    }
}
// 同步查询项分类项和细项
export const asyncEsItemCateCodeData = (data) => {
    return {
        url: `edc/external/setting/async-item-cate-code`,
        method: 'POST',
        data
    }
}

// 同步查询细项
export const asyncEsItemCodeData = (data) => {
    return {
        url: `edc/external/setting/async-item-code`,
        method: 'POST',
        data
    }
}