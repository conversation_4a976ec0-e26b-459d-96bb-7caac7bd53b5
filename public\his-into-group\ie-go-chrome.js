// 从ie跳转到谷歌浏览器相关代码，跳转chrome通过调用goChrome()函数，通过覆盖getUrl方法，指定跳转url
function getUrl(){
  // 示例代码 可在自己页面中覆盖全局的此函数
  // var baseUrl = window.location.protocol + "//" + window.location.host;
  // var pathname = window.location.pathname;
  // var urlParams = window.location.search;
  // var newPath = pathname.replace("browser-detection", "login-middle-page");
  return window.location.href
}
function goChrome(){
  if (isIE()) {
      console.log("IE浏览器");
      if (checkChromePath()) {
          launchChrome();
      } else {
          showDownloadTool();
      }
  }else{
    console.log("不是IE浏览器");
  }
}
function isIE() {
  var userAgent = navigator.userAgent;
   var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
   var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
   
   if(isIE || isIE11){
       return true
   }
       
   return false
}

function checkChromePath() {
   console.log("检测谷歌路径");
   try {
       // 读取自定义注册表项（需通过 ActiveXObject）
       var shell = new ActiveXObject("WScript.Shell");
       // 在 Windows 注册表中，HKCU 是 HKEY_CURRENT_USER 的缩写，表示当前用户的注册表配置单元
       var regPath = ["HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Google Chrome\\InstallLocation",
				"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Google Chrome\\InstallLocation",
				"HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Google Chrome\\InstallLocation",
				"HKEY_CURRENT_USER\\Software\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Google Chrome\\InstallLocation",
				"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths\chrome.exe\\Path",
				"HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\App Paths\\chrome.exe\\Path",
        "HKCU\\Software\\DHCC\\ChromeLauncher\\ChromePath" // 最后一个是提供下载链接或指导用户下载注册的注册表
			];
      // 判断是否安装谷歌浏览器
			for (var i = 0; i < regPath.length; i++) {
				try {
					var x = shell.regRead(regPath[i]);
					if (x) {
						return true;
					}
				} catch (e) {
          continue;
        }
			}
      return false
   } catch (e) {
    alert("启动chrome浏览器失败，请检查确保已安装chrome浏览器，并将"+location.host+'、'+location.hostname+"添加到IE受信任站点\n（IE浏览器右上角设置图标->internet选项->安全->受信任站点->站点->添加），\n确保IE配置安全设置，启用“对未标记为可安全执行脚本的ActiveX控件初始化并执行脚本”\n（IE浏览器右上角设置图标->internet选项->安全->受信任站点->自定义级别->ActiveX控件和插件->对未标记为可安全执行脚本的ActiveX控件初始化并执行脚本->勾选启用）");
      return false;
   }
}

function showDownloadTool() {
   alert("请下载并运行注册工具以打开chrome浏览器。");
   // 提供下载链接或指导用户下载
   var baseUrl = window.location.protocol + "//" + window.location.host + '/csm';
   var targetUrl = baseUrl + "/login-middle-v1/download/chrome_register_tool.exe";

    // 打开新窗口并跳转
    var newWindow = window.open('', '_blank');

    // 判断新窗口是否成功打开
    if (newWindow) {
        newWindow.location.href = targetUrl; // 跳转到指定网址
    } else {
        alert("弹出窗口被阻止，请允许弹出窗口后重试。");
    }
}

function launchChrome(url) {
   var url = encodeURI(getUrl());
   try {
       var shell = new ActiveXObject("WScript.Shell");
       shell.Run('cmd.exe /c start chrome "'+url + '"');
   } catch (e) {
    alert("启动chrome浏览器失败，请检查确保已安装chrome浏览器，并将"+location.host+'、'+location.hostname+"添加到IE受信任站点\n（IE浏览器右上角设置图标->internet选项->安全->受信任站点->站点->添加），\n确保IE配置安全设置，启用“对未标记为可安全执行脚本的ActiveX控件初始化并执行脚本”\n（IE浏览器右上角设置图标->internet选项->安全->受信任站点->自定义级别->ActiveX控件和插件->对未标记为可安全执行脚本的ActiveX控件初始化并执行脚本->勾选启用）");
   }
}