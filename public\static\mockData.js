var resp = {
  id: 'afefdc1f931ffe5bc088373b14b422f2',
  code: 'dengxing-test',
  fileId: '7e4d4db8eb5c7ba19378379d560a8010',
  dataSource: { sourceType: null, className: 'hosPrintGenerateServiceImpl', methodName: 'generatePrintData' },
  param: {
    mainData: [
      {
        dataCode: 'mainData',
        dataName: null,
        dataType: null,
        detailStartRow: null,
        detailEndRow: null,
        dataAttribute: [
          { fieldCode: 'date', fieldName: null, fieldType: '1', fieldImgExt: null, isGroup: false, position: '7,3' },
          {
            fieldCode: 'voucherCode',
            fieldName: null,
            fieldType: '1',
            fieldImgExt: null,
            isGroup: false,
            position: '7,4'
          },
          {
            fieldCode: 'createPerson',
            fieldName: null,
            fieldType: 'img',
            fieldImgExt: {
              pictureType: '6',
              leftUpPost: '3,10',
              rightDownPost: '4,11',
              upMargin: 0,
              downMargin: 0,
              leftMargin: 0,
              rightMargin: 0
            },
            isGroup: false,
            position: '3,10'
          }
        ]
      }
    ],
    listData: [
      {
        dataCode: 'detailData',
        dataName: null,
        dataType: null,
        detailStartRow: 6,
        detailEndRow: 8,
        dataAttribute: [
          { fieldCode: 'zhaiyao', fieldName: null, fieldType: '1', fieldImgExt: null, isGroup: true, position: '2,6' },
          { fieldCode: 'jiefang', fieldName: null, fieldType: '1', fieldImgExt: null, isGroup: false, position: '6,6' },
          { fieldCode: 'daifang', fieldName: null, fieldType: '1', fieldImgExt: null, isGroup: false, position: '7,6' },
          {
            fieldCode: 'tupian',
            fieldName: null,
            fieldType: 'img',
            fieldImgExt: {
              pictureType: '6',
              leftUpPost: '8,6',
              rightDownPost: '9,7',
              upMargin: 2,
              downMargin: -2,
              leftMargin: 2,
              rightMargin: -2
            },
            isGroup: false,
            position: '8,6'
          }
        ]
      }
    ]
  },
  printConfig: {
    isOverPrint: false,
    isClearNull: true,
    isDefPaper: true,
    totalPage: '7,13',
    currPage: '7,12',
    systemPage: ''
  }
}

const importRes = {
  mainData: {
    dataCode: 't_table_main',
    dataName: '主数据测试',
    dataType: '1',
    detailStartRow: null,
    detailEndRow: null,
    dataAttribute: [
      {
        fieldCode: 'mainNameField1',
        fieldName: '主数据字段1',
        fieldType: '1',
        fieldImgExt: null,
        isGroup: null,
        position: null
      },
      {
        fieldCode: 'mainNameField2',
        fieldName: '主数据字段2',
        fieldType: '2',
        fieldImgExt: null,
        isGroup: null,
        position: null
      }
    ]
  },
  listData: [
    {
      dataCode: 't_table_detail',
      dataName: '从数据测试',
      dataType: '2',
      detailStartRow: 3,
      detailEndRow: 3,
      dataAttribute: [
        {
          fieldCode: 'mainNameField1',
          fieldName: '主数据字段1',
          fieldType: '1',
          fieldImgExt: null,
          isGroup: null,
          position: null
        },
        {
          fieldCode: 'mainNameField2',
          fieldName: '主数据字段2',
          fieldType: '2',
          fieldImgExt: null,
          isGroup: null,
          position: null
        }
      ]
    }
  ]
}
