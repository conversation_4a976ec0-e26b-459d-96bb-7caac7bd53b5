﻿var ca_common_func = {
    /*getServerConfig: function() {
        //return "APP_START_MICROSERVICES";     //微服务
        return "APP_START_MONOMER";     //单体
    },*/
    //获取通用请求路径
    getAppPath:function(action) {
        //var serverConfig = this.getServerConfig();
        var appPatch = "";
        var menu = websys_getMenuWin();
        if (typeof menu == "object" && menu) {
            //if (serverConfig == "APP_START_MONOMER") {
                appPatch = menu.baseAppPath + this.getUrl(action);
            /*} else {
                appPatch = "http://localhost:20004/hosca" + this.getUrl(action);        //todo:后期要考虑微服务时读取配置或者其他处理方式
            }*/
        }
        return appPatch;
    },
    getParams: function() {
        //获取url中的请求
        //var localurl = location.href; //获取当前完整的URL地址信息(包含http://，域名，路径，具体文件和全部传递参数)
        var url = location.search; //获取url中"?"符后的字串
        if (url.length > 0) { //判断是否携带参数
            var params = {};
            if (url.indexOf("?") != -1) {
                this.isShwoInput = true;
                var str = url.substr(url.indexOf("?") + 1);
                var strs = str.split("&");
                for (var i = 0; i < strs.length; i++) {
                    params[strs[i].split("=")[0]] = strs[i].split("=")[1];
                }
            }
            return params;
        } else {
            //$.messager.alert("提示", "URL未传递参数", "error");
            return {};
        }
    },
    getMWToken: function() {
        try {
            if (typeof(websys_getMWToken) != "undefined")
                return websys_getMWToken();
            return "";
        } catch(e) {
            return "";
        }
    },
    trans: function(value) {
        if (typeof $g == "function") {
            value = $g(value);
        }
        return value;
    },
    getSession: function() {
        if (websys_getSession()) {
            var session = websys_getSession();
            return {
                UserID: session.userId || "",
                RoleID: session.roleId || "",
                //HospID: session.hospId || "",
                OrgID: session.organizationId || "",
                CTLocID: session.ctLocId || "",
                LangID: session.langId || "",
                UserCode: session.userCode || "",
                PostID: session.ctPostId || ""
            };
        }
        return {};
    },
    ajaxPOSTCommon: function (data, successFunc, async) {
        // 前后台交互POST
        // data 命令数据json对象，例：{"action":"GET_ALLCAREPRVTYPE","params":{"organizationID":"2","langID":"20"}}
        // successFunc 成功后回调函数
        // errorFunc 失败后回调函数 -- 暂不增加，在此方法内处理错误，不再继续向外部传递
        // async 异步true，同步false
        successFunc = async == false ? false : successFunc;
        
        //获取路径
        var baseURL = this.getAppPath(data.action);

        //修改9999的报错提示信息，9999为程序运行bug，不提示用户具体信息
        var callFunction = false;
        if (typeof(successFunc) == "function") {
            var callFunction = function(ret) {
                if (ret.code == "9999")
                    ret.msg = "程序运行异常，请联系系统管理员排查问题。";
                successFunc(ret);
            }
        }

        var errData = "";
        var result = $ipost(baseURL , data, callFunction,function (e) {
            //请求失败
            console.log("post请求后台接口失败，action:" + data.action);
            //$.messager.alert("提示", "post请求后台接口失败，action:" + data.action, "error");
            errData = {"code":e.status, "msg":data.action + ":" + e.statusText};
        });
        if (!async) {
            if (errData != "")
                return errData;

            //修改9999的报错提示信息，9999为程序运行bug，不提示用户具体信息
            if (result.code == "9999")
                result.msg = "程序运行异常，请联系系统管理员排查问题。";
            return result;
        }
    },
    //对照action和后端服务地址
    urls: {
        "CARESIGN_GET_HASHDATA"           :"careSign/getHashData",
        "CARESIGN_GET_SERVERRANDOMDATA"   :"careSign/getServerRandomData",
        "CARESIGN_GET_LOGINRESULT"        :"careSign/getLoginResult",
        "CARESIGN_SAVE_SIGNDATA"          :"careSign/saveSignData",
        "CARESIGN_GET_USERINFO"           :"careSign/getUserInfoByCert",

        "GET_VENDERSIGNTYPEISREG"         :"caConfig/getVenderSignTypeIsReg",

        "CARESIGN_PHONE_GETLOGINQRINFO"   :"careSign/getLoginQrInfo",
        "CARESIGN_PHONE_GETLOGINQRRESULT" :"careSign/getLoginQrResult",
        "CARESIGN_PHONE_GETTOKENISVALID"  :"careSign/getTokenIsValid",
        "CARESIGN_PHONE_GETPINLOGINRESULT":"careSign/getPinLoginResult",
        "CARESIGN_PHONE_GETAUTOSIGNRESULT":"careSign/getAutoSignResult",
        "CARESIGN_PHONE_GETCERTBYCONTAINER":"careSign/getCertByContainer",
        "CARESIGN_PHONE_GETCERTBYUSERCODE":"careSign/getCertByUserCode",
        "CARESIGN_PHONE_GETSEALINFO"      :"careSign/getSealInfo",

        "CARESIGN_GET_CA_STATUS"          :"careSign/getCaStatus",
        "GET_SIGNACTIONCONFIGS"           :"caConfig/getSignActionConfigs",
        "CARESIGN_GET_JS_PATH"            :"careSign/getJsPath",
        "CARESIGN_GET_AUTHTYPELIST"       :"careSign/getAuthTypeList"
    },
    getUrl: function(action) {
        return this.urls[action];
    },
    //在这维护当前支持的摘要算法类型
    getHashTypes: function() {
        return ["SM3HEX","SM3BASE64","SHA256HEX","SHA256BASE64"];
    },
    checkCallBackFunc: function(params) {
        var callBackFunc = params.callBackFunc || "";
        if ("function" !== typeof callBackFunc) {
            if ($ && $.messager && $.messager.alert && typeof $.messager.alert === "function") {
                $.messager.alert("错误", "调用CA接口需传入回调函数,当前未传入！", "error");
            } else {
                alert("调用CA接口需传入回调函数,当前未传入！");
            }
            return false;
        }
        return true;
    },
    checkPublicParams: function(params) {
        var organizationID = params.organizationID || "";
        if (organizationID == "")
            return {code:"-1",msg:"组织机构ID[organizationID]不允许为空!"};

        ///预留语言入参校验
        /*var langID = params.langID || "";
        if (langID == "")
            return {code:"-1",msg:"语言[langID]不允许为空!"};*/
        return {code:"0",msg:""};
    },
    //签名方法校验入参
    checkSignParams: function(params) {
        var hashType = params.hashType || "";
        if (hashType == "")
            return {code:"-1",msg:"对摘要数据签名时,摘要算法[hashType]不允许为空!"};

        if ($.inArray(hashType.toUpperCase(), ca_common_func.getHashTypes()) == -1)
            return {code:"-2",msg:"当前不支持【"+ hashType +"】摘要算法,请联系电子病历组确认!"};

        var hashData = params.hashData || "";
        if (hashData == "")
            return {code:"-3",msg:"对摘要数据签名时,摘要数据[hashData]不允许为空!"};

        var certContainer = params.certContainer || "";
        if (certContainer == "")
            return {code:"-4",msg:"签名时,标识[certContainer]不允许为空!"};

        var organizationID = params.organizationID || "";
        if (organizationID == "")
            return {code:"-5",msg:"签名时,组织机构ID[organizationID]不允许为空!"};

        var actionCode = params.actionCode || "";
        if (actionCode == "")
            return {code:"-6",msg:"签名时,操作代码[actionCode]不允许为空!"};

        var episodeID = params.episodeID || "";
        if ((episodeID == "")&&(actionCode !== "Test"))
            return {code:"-7",msg:"签名时,患者就诊号[episodeID]不允许为空!"};
        
        var businessID = params.businessID || "";
        if ((businessID == "")&&(actionCode !== "Test"))
            return {code:"-8",msg:"签名时,业务唯一标识[businessID]不允许为空!"};

        return {code:"0",msg:""};
    },
    //签名方法校验入参
    checkPureSignParams: function(params) {
        var hashType = params.hashType || "";
        if (hashType == "")
            return {code:"-1",msg:"对摘要数据签名时,摘要算法[hashType]不允许为空!"};

        if ($.inArray(hashType.toUpperCase(), ca_common_func.getHashTypes()) == -1)
            return {code:"-2",msg:"当前不支持【"+ hashType +"】摘要算法,请联系电子病历组确认!"};

        var hashData = params.hashData || "";
        if (hashData == "")
            return {code:"-3",msg:"对摘要数据签名时,摘要数据[hashData]不允许为空!"};

        var certContainer = params.certContainer || "";
        if (certContainer == "")
            return {code:"-4",msg:"签名时,标识[certContainer]不允许为空!"};

        return {code:"0",msg:""};
    },
    //登录方法校验入参
    checkLoginParams: function(params) {
        var certContainer = params.certContainer || "";
        if (certContainer == "")
            return {code:"-1",msg:"CA登录时,CA唯一标识[certContainer]不允许为空!"};

        var organizationID = params.organizationID || "";
        if (organizationID == "")
            return {code:"-2",msg:"CA登录时,组织机构ID[organizationID]不允许为空!"};

        var imageType = params.imageType || "";
        if ((imageType != "")&&(imageType != "Original")&&(imageType != "Compressed"))
            return {code:"-3",msg:"CA登录时,只支持Original或Compressed图片类型获取,不支持【"+ imageType +"】图片类型,请联系电子病历组确认!"};

        return {code:"0",msg:""};
    },
    getHashData: function(params){
        var data = {
            action: "CARESIGN_GET_HASHDATA",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getServerRandomData: function(params){
        var data = {
            action: "CARESIGN_GET_SERVERRANDOMDATA",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getLoginResult: function(params){
        var data = {
            action: "CARESIGN_GET_LOGINRESULT",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    saveSignData: function(params){
        var data = {
            action: "CARESIGN_SAVE_SIGNDATA",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getUserInfo: function(params){
        var data = {
            action: "CARESIGN_GET_USERINFO",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    //获取厂商服务配置内容，比如应用ID，扫码应用名称等等
    getConfigInfo: function(params){
        var data = {
            action: "GET_VENDERSIGNTYPEISREG",
            params: params
        };

        var configInfo = this.ajaxPOSTCommon(data,"",false);
        if (configInfo.code != "200")
            return {code:"-1" ,msg:"获取厂商签名方式注册数据失败，错误码：" + configInfo.code + "，错误描述：" + configInfo.msg};
        
        if (!configInfo.data.isReg)
            return {code:"-2" ,msg:"厂商签名方式未注册，组织机构ID：" + params.organizationID + "，签名方式：" + params.signTypeCode + "，厂商：" + params.venderCode};

        return { code:"0", msg:"", data:configInfo.data.venderService };
	},
    getLoginQrInfo: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETLOGINQRINFO",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getLoginQrResult: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETLOGINQRRESULT",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getTokenIsValid: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETTOKENISVALID",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getPinLoginResult: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETPINLOGINRESULT",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getAutoSignResult: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETAUTOSIGNRESULT",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getCertByContainer: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETCERTBYCONTAINER",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getCertByUserCode: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETCERTBYUSERCODE",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getSealInfo: function(params){
        var data = {
            action: "CARESIGN_PHONE_GETSEALINFO",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
	},
    getSignActionConfig: function(params){
        var data = {
            action: "GET_SIGNACTIONCONFIGS",
            params: params,
            page: 1,
            rows: 10
        };
        return this.ajaxPOSTCommon(data,"",false);
    },
    getCaStatus: function(params){
        var data = {
            action: "CARESIGN_GET_CA_STATUS",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
    },
    getStroageCAInfo: function(params){
        return {code:"200",msg:"",data:{}}
        //从后端获取已登录的CA信息，获取缓存的签名方式、厂商、证书信息（证书唯一标识、用户唯一标识、token、签名方式、签名厂商）
        var data = {
            action: "getStroageCAInfo",
            params: {}
        };
        return this.ajaxPOSTCommon(data,"",false);
    },
    setStroageCAInfo: function(params){
        return {code:"200",msg:"",data:{}}
        //缓存已登录的CA信息，提供接口缓存签名方式、厂商、证书信息（证书唯一标识、用户唯一标识、token、签名方式、签名厂商）
        var data = {
            action: "setStroageCAInfo",
            params: {}
        };
        return this.ajaxPOSTCommon(data,"",false);
    },
    getJsPath: function(params){
        var data = {
            action: "CARESIGN_GET_JS_PATH",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
    },
    getAuthTypeList: function(params){
        var data = {
            action: "CARESIGN_GET_AUTHTYPELIST",
            params: params
        };
        return this.ajaxPOSTCommon(data,"",false);
    },
    /*obj
    {
        callBackFunc: callBackFunc, //回调函数
        actionCode: "EMROPSign",    //动作code
        isHeaderShowWindow: true,   //"是否top页面弹窗"
        signUserCode: "",           //指定用户
        isCheckSignUser: false,     //是否校验签名用户与登录用户一致，不传入时，此参数通过actionCode和organizationID从配置中获取
        isOpenForce: false,         //是否强制弹出，不传入时，此参数通过actionCode和organizationID从配置中获取
        isSignleLogon：false,       //是否只显示单认证方式，不传入时，此参数通过actionCode和organizationID从配置中获取
        signleLogonType:"",         //PHONE,UKEY;isSignleLogon参数传true时，指定签名方式，不传入时使用默认签名方式
    }*/
    getCaCert: function(obj) {   //obj数据参考webSys_getCaCert()方法
        //0.校验参数
        var checkCallBackResult = ca_common_func.checkCallBackFunc(obj);
        if (!checkCallBackResult)
            return;
        var callBackFunc = obj.callBackFunc;

        try {
            //校验必须参数
            if (websys_getSession()) {
                var session = websys_getSession();
            } else {
                throw {code:"-1",msg:"websys_getSession方法不存在,无法获取登录信息!"};
            }

            var actionCode = obj.actionCode || "";
            if ("" == actionCode) {
                throw {code:"-1",msg:"actionCode不允许为空!"};
            }

            var basePath = ca_common_func.getBasePath();
            if ("" == basePath)
                throw {code:"-1",msg:"获取当前页面加载ca程序js路径为空!"};

            //获取CA开关状态
            var caStatus = ca_common_func.getCaStatus({
                organizationID:session.organizationId || "",
                ctLocID:session.ctLocId || "",
                roleID:session.roleId || "",
                userID:session.userId || "",
                langID:session.langId || "",
                postID:session.ctPostId || "",
                actionCode: actionCode
            });
            if (caStatus.code != "200")
                throw caStatus;

            if (!caStatus.data.isCAOn) {
                callBackFunc({ code:"0", msg:"未开启CA功能!", data:{isCaOn:false}});
                return;
            }

            //获取传入的参数
            var signUserCode = obj.signUserCode || "";
            var isHeaderShowWindow = obj.isHeaderShowWindow;
            if (typeof isHeaderShowWindow == "undefined" || isHeaderShowWindow == "")
                isHeaderShowWindow = false;
            var isCheckSignUser = obj.isCheckSignUser;
            if (typeof isCheckSignUser == "undefined")
                isCheckSignUser = "";
            var isOpenForce = obj.isOpenForce;
            if (typeof isOpenForce == "undefined")
                isOpenForce = "";
            var isSignleLogon = obj.isSignleLogon;
            if (typeof isSignleLogon == "undefined")
                isSignleLogon = "";
            var signleLogonType = obj.signleLogonType || "";
            var organizationID = session.organizationId || "";
            //有传入参数为空时，取配置数据
            if ((isCheckSignUser == "")||(isOpenForce == "")||(isSignleLogon == "")) {
                var signActionConfig = ca_common_func.getSignActionConfig({
                    organizationID:organizationID,
                    actionCode:actionCode
                });
                if (signActionConfig.code == "200") {
                    if (signActionConfig.data.total > 0) {
                        var signActionConfigData = signActionConfig.data.records[0];
                        if (isCheckSignUser == "")  isCheckSignUser = (signActionConfigData.isCheckSignUser == "是") ? true : false;
                        if (isOpenForce == "")  isOpenForce = (signActionConfigData.isOpenForce == "是") ? true : false;
                        if (isSignleLogon == "")  isSignleLogon = (signActionConfigData.isSignleLogon == "是") ? true : false;
                    }
                }
            }
            //未配置时给定默认值
            if (isCheckSignUser == "") isCheckSignUser = false;
            if (isCheckSignUser) signUserCode = session.userCode || "";
            if (isOpenForce == "") isOpenForce = false;
            if (isSignleLogon == "") isSignleLogon = false;

            if ((isSignleLogon)&&(signleLogonType == "")) signleLogonType = caStatus.data.defaultAuthTypeCode;
            if (!isSignleLogon) signleLogonType = "";

            //弹出认证页面
            var createModalDialogCallBcak = function(ret,arr) {
                if (ret.code != "0") {
                    callBackFunc({ code:ret.code, msg:ret.msg || e, data:{}});
                } else {
                    var loginSignTypeCode = ret.data.signTypeCode;
                    var loginVenderCode = ret.data.venderCode;
                    var loginUserID = ret.data.userID;
        
                    if (loginUserID == session.userId);
                        ca_common_func.setStroageCAInfo();

                    var appendJsCallBack = function(appendRet) {
                        if (appendRet.code != "0") {
                            callBackFunc({ code:appendRet.code, msg:appendRet.msg || e, data:{}});
                        } else {
                            ret.data.isCaOn = true;
                            callBackFunc({ code:"0", msg:"", data:ret.data});
                        }
                    }
                    //加载js到页面上
                    ca_common_func.appendJs(organizationID, loginSignTypeCode, loginVenderCode, appendJsCallBack, basePath);
                }
            }

            //强制弹认证页面
            if (isOpenForce) {
                ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                return;
            }

            var stroageCAInfo = ca_common_func.getStroageCAInfo();
            if (stroageCAInfo.code != "200")
                throw stroageCAInfo;

            var stroageCertContainer = stroageCAInfo.data.certContainer || "";
            var stroageSignTypeCode = stroageCAInfo.data.signTypeCode || "";
            var stroageVenderCode = stroageCAInfo.data.venderCode || "";
            //缓存信息不为空
            if (stroageCertContainer !== "") {
                var stroageUserCertCode = stroageCAInfo.data.userCertCode || "";
                var stroageCertNo = stroageCAInfo.data.certNo || "";
                var stroageSignToken = stroageCAInfo.data.signToken || "";
                var stroageUkeyPw = stroageCAInfo.data.ukeypw || "";

                var appendJsCallBack = function(ret) {
                    if (ret.code != "0") {
                        ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                        return;
                    } else {
                        //Ukey判断证书是否登录
                        if (stroageSignTypeCode == "UKEY") {
                            var isLoginCallBackFunc = function(isLoginResult) {
                                if ("0" !== isLoginResult.code) {
                                    ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                                    return;
                                } 

                                var isLogin = isLoginResult.data.isLogin;
                                if (!isLogin) {
                                    ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                                    return;
                                }
                                
                                var userInfo = ca_common_func.getUserInfo({
                                    userCertCode:stroageUserCertCode,
                                    certNo:stroageCertNo
                                });
                                if (userInfo.code !== "200") {
                                    ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                                    return;
                                }
                                //UKEY登录有效
                                callBackFunc({ code:"0", msg:"", data:{userCertCode:stroageUserCertCode,certNo:stroageCertNo,certContainer:stroageCertContainer,venderCode:stroageVenderCode,signTypeCode:stroageSignTypeCode,userID:userInfo.data.userID,userCode:userInfo.data.userCode,userName:userInfo.data.userName,password:stroageUkeyPw,isCaOn:true}});
                            }
                            ca_key.isLogin({certContainer:stroageCertContainer,callBackFunc:isLoginCallBackFunc});
                        } else {
                            var isTokenValidCallBackFunc = function(isTokenIsValidResult) {
                                if ("0" !== isTokenIsValidResult.code) {
                                    ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                                    return;
                                } 

                                var isTokenIsValid = isTokenIsValidResult.data;
                                if (!isTokenIsValid) {
                                    ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                                    return;
                                }

                                var userInfo = ca_common_func.getUserInfo({
                                    userCertCode:stroageUserCertCode,
                                    certNo:stroageCertNo
                                });
                                if (userInfo.code !== "200") {
                                    ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                                    return;
                                }
                                //扫码签缓存token有效
                                callBackFunc({ code:"0", msg:"", data:{userCertCode:stroageUserCertCode,certNo:stroageCertNo,certContainer:stroageCertContainer,venderCode:stroageVenderCode,signTypeCode:stroageSignTypeCode,userID:userInfo.data.userID,userCode:userInfo.data.userCode,userName:userInfo.data.userName,signToken:stroageSignToken,isCaOn:true}});
                            }
                            ca_key.getTokenIsValid({certContainer:stroageCertContainer,organizationID:organizationID,signToken:stroageSignToken,callBackFunc:isTokenValidCallBackFunc});
                        }
                    }
                }

                //校验用户时，判断下已登录用户和是否一致
                if (signUserCode != "") {
                    var userInfo = ca_common_func.getUserInfo({
                        userCertCode:stroageUserCertCode,
                        certNo:stroageCertNo
                    });
                    if (userInfo.code !== "200") {
                        ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                        return;
                    } else {
                        if (userInfo.data.userCode != signUserCode) {
                            ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                            return;
                        } else {
                            //加载js到页面上
                            ca_common_func.appendJs(organizationID, stroageSignTypeCode, stroageVenderCode, appendJsCallBack, basePath);
                        }
                    }
                } else {
                    //加载js到页面上
                    ca_common_func.appendJs(organizationID, stroageSignTypeCode, stroageVenderCode, appendJsCallBack, basePath);
                }
            } else {
                //无缓存信息
                ca_common_func.showCaLoginDialog(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, createModalDialogCallBcak, basePath);
                return;
            }
        } catch(e) {
            callBackFunc({ code:e.code, msg:e.msg || e, data:{}});
        }
        
        //0.判断CA开启 (CARESIGN_GET_CA_STATUS,待确定，正常应该是页面加载时，业务产品组判断是否开启CA，如开启是否提供个方法让页面在加载完成时调用，把默认签名方式对应的js提前加载到页面上)

        //1.判断当前页面是否有后台登录签名方式、签名厂商是否已选定
        //1.1动态加载实际的签名js到页面上，异步方式
        //2.判断登录信息是否有效(UKEY的islogin、PHONE的tokenisValid),在此步骤时实现UKEY免密逻辑，使用缓存的token信息(加密后的pin码)进行密码校验
        //3.有效直接返回证书信息
        //4.无效弹出认证页面，需实现多tab签效果
        //5.认证通过后判断用户信息与当前登录用户信息是否一致，一致时调用HOS提供接口缓存信息并返回业务端，不一致不缓存信息直接返回业务端

        //6.HOS提供接口需实现
        //6.1获取缓存的签名方式、厂商、证书信息（证书唯一标识、用户唯一标识、token、签名方式、签名厂商）
        //6.2提供接口缓存签名方式、厂商、证书信息（证书唯一标识、用户唯一标识、token、签名方式、签名厂商）
    },
    stroageCAInfoIsValid: function() {
        //判断缓存证书信息接口是否有效
        //ukey调用islogin，phone调用tokenisvalid
    },
    getBasePath: function() {
        //获取页面加载的CAjs路径，用来确定页面层级
        var tmpScripts = document.getElementsByTagName("script");
        var caScript = "";
        for (var i=tmpScripts.length;i>0;i--) {
            var currentScript = tmpScripts[i-1].getAttribute("src");
            if (currentScript && currentScript.indexOf("ca/scripts/dhccertauth/js/common.func.js") > 0) {
                caScript = currentScript;
                break;
            }
        }
        if (caScript == "") 
            return "";
            //callBackFunc({code:"-1" ,msg:"获取当前页面加载ca程序js路径为空"});
    
        var basePath = (caScript.slice(0,caScript.indexOf('/base/')));
        return basePath;
    },
    appendJs: function(organizationID, signTypeCode, venderCode, callBackFunc, basePath) {
        //缓存的签名方式与页面加载的不一致
        if ((typeof ca_key !== "object")||(ca_key.signTypeCode||"" != signTypeCode && ca_key.venderCode||"" != venderCode)) {
            var jsPath = ca_common_func.getJsPath({
                organizationID:organizationID,
                venderCode:venderCode,
                authTypeCode:signTypeCode
            });
            if (jsPath.code != "200") {
                callBackFunc({code:"-1" ,msg:"获取js路径失败，错误码：" + jsPath.code + "，错误描述：" + jsPath.msg});
                return;
            }
            
            var jsPathValue = jsPath.data;
            if ((jsPathValue == "")||(jsPathValue == null)) {
                callBackFunc({code:"-1" ,msg:"获取js路径为空"});
                return;
            }

            var baseJsPath = basePath + jsPathValue;
            var script = document.createElement("script");
            script.type = "text/javascript";
            script.src = baseJsPath;
            // script.charset = params.charset || "gbk"; // 设置字符集，默认为 gbk
            // 加载完成后执行回调函数
            script.onload = function () {
                callBackFunc({code:"0" ,msg:""});
            };
            document.body.appendChild(script);
        } else {
            callBackFunc({code:"0" ,msg:""});
        }
        //动态加载js，加载成功后调用回调函数
    },
    /// 弹出CA认证页面
    showCaLoginDialog: function(signUserCode, isHeaderShowWindow, signleLogonType, organizationID, callBackFunc, basePath) {
        basePath = basePath + "/base/hos/ca/html/";
        if (isHeaderShowWindow)
            basePath = "/his/base/hos/ca/html/";
        var url = basePath + "dhc.certauth.bsp.calogin.html?SignUserCode="+signUserCode+"&SignleLogonType="+signleLogonType+"&MWToken="+ca_common_func.getMWToken()+"&OrgID="+organizationID;
        var xpwidth=800;
        var xpheight=730;
        if (isHeaderShowWindow) {
            url = url + "&IsHeaderShowWindow=1"
            var content = "<iframe id='caLoginFrame' scrolling='auto' frameborder='0' src='"+url+"' style='width:100%; height:99%;'></iframe>";
            ca_common_func.createTopModalDialog("caLogin","CA登录",xpwidth,xpheight,"caLoginFrame",content,callBackFunc,"",true);
        } else {
            var content = "<iframe id='caLoginFrame' scrolling='auto' frameborder='0' src='"+url+"' style='width:100%; height:99%;'></iframe>";
            ca_common_func.createModalDialog("caLogin","CA登录",xpwidth,xpheight,"caLoginFrame",content,callBackFunc,"",true);
        }
    },
    /// 创建HISUI-Dialog弹窗
    createModalDialog: function(dialogId, dialogTitle, width, height, iframeId, iframeContent,callback,arr,maximi,minimi) {
        $("body").append("<div id='"+dialogId+"'</div>");
        if (isNaN(width)) width = 800;
        if (isNaN(height)) height = 500;
        if (maximi == undefined) maximi = false;
        if (minimi == undefined) minimi = false;
        $HUI.dialog("#"+dialogId,{
            title: dialogTitle,
            width: width,
            height: height,
            cache: false,
            collapsible: false,
            minimizable:minimi,
            maximizable:maximi,
            resizable: false,
            modal: true,
            closed: false,
            closable: true,
            isTopZindex: true,
            content: iframeContent,
            onBeforeClose: function() {
                var tempFrame = $("#"+iframeId)[0].contentWindow;
                if (tempFrame.dialogBeforeClose) {
                    tempFrame.dialogBeforeClose();
                }
                if (tempFrame && tempFrame.returnValue) {
                    returnValue = tempFrame.returnValue;
                    if ((returnValue !== "") &&(typeof(callback) === "function"))
                    {
                        callback(returnValue,arr);
                    }
                }
            },
            onClose: function() {
                //$("#modalIframe").hide();
                $("#"+dialogId).dialog("destroy");
            }
        });
    },
    //关闭dialog,子页面调用
    closeDialog: function(dialogId) {
        $HUI.dialog("#"+dialogId).close();
    },
    /// 创建HISUI-Dialog弹窗
    createTopModalDialog: function(dialogId, dialogTitle, width, height, iframeId, iframeContent,callback,arr,maximi,minimi) {
        top.$("body").append("<div id='"+dialogId+"'</div>");
        if (isNaN(width)) width = 800;
        if (isNaN(height)) height = 500;
        if (maximi == undefined) maximi = false;
        if (minimi == undefined) minimi = false;
        top.$HUI.dialog("#"+dialogId,{
            title: dialogTitle,
            width: width,
            height: height,
            cache: false,
            collapsible: false,
            minimizable:minimi,
            maximizable:maximi,
            resizable: false,
            modal: true,
            closed: false,
            closable: true,
            isTopZindex: true,
            content: iframeContent,
            onBeforeClose: function() {
                var tempFrame = top.$("#"+iframeId)[0].contentWindow;
                if (tempFrame.dialogBeforeClose) {
                    tempFrame.dialogBeforeClose();
                }
                if (tempFrame && tempFrame.returnValue) {
                    returnValue = tempFrame.returnValue;
                    if ((returnValue !== "") &&(typeof(callback) === "function"))
                    {
                        callback(returnValue,arr);
                    }
                }
            },
            onClose: function() {
                //$("#modalIframe").hide();
                top.$("#"+dialogId).dialog("destroy");
            }
        });
    },
    //关闭dialog,子页面调用,关闭页面时直接top.$HUI.dialog("#"+dialogId).close();，不调用closeTopDialog
    closeTopDialog: function(dialogId) {
        top.$HUI.dialog("#"+dialogId).close();
    }
}