<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>科研平台</title>
  <!-- 引入 Bootstrap 样式 -->
  <link rel="stylesheet" href="../his-into-group/lib/bootstrap.min.css" />
  <style>
    .type-select {
      width: 100%;
      text-align: center;
      margin-top: 30px;
    }

    .t-container {
      width: 100%;
      text-align: center;
      position: relative;
    }

    .t-option {
      display: inline-block;
      vertical-align: top;
      width: 30%;
      margin: 0 1.5%;
      text-align: center;
      border-radius: 20px;
      padding: 15px 18px;
      color: #ddd;
      border: 1px solid #ddd;
      /* border-color: transparent; */
      box-shadow: 2px 2px 20px rgba(0, 0, 0, 0.1);
      min-height: 250px;
      cursor: pointer;
      background-color: #fff;
    }

    .t-option:hover {
      border-color: #425efd;
    }

    .t-icon {
      width: 100px;
      height: 100px;
      margin: 0 auto 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      border-radius: 10px; /* 保持原有的圆角样式 */
      background-color: #fff; /* 确保图片背景一致 */
    }

    .t-icon-img {
      max-width: 100%;
      max-height: 100%;
      display: block;
    }

    .t-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #2b2b2b;
    }

    .t-tag {
      display: inline-block;
      border-radius: 2px;
      font-size: 13px;
      color: #feb959;
      border: 1px solid #feb959;
      padding: 0px 6px;
      background: #fcf1dc;
      margin: 0 2px;
      line-height: 18px;
    }

    .t-description {
      font-size: 14px;
      color: #9d9d9d;
      line-height: 22px;
      text-align: left;
      margin: 0 auto;
    }
  </style>
</head>
<body>
  <div class="type-select">
    <div class="t-container" id="options-container">
      <!-- 动态内容将在这里插入 -->
    </div>
  </div>

  <!-- 引入 jQuery 和 Bootstrap JS -->
  <script src="../his-into-group/lib/jquery.min.js"></script>
  <script src="../his-into-group/lib/bootstrap.min.js"></script>
  <script src="../his-into-group/lib/crypto-js-3.1.9-1/crypto-js.js"></script>
  <script src="../his-into-group/lib/util-crypto.js"></script>
  <script src="../his-into-group/common.js"></script>
  <script src="../his-into-group/api.js"></script>
  <script src="../his-into-group/ie-go-chrome.js"></script>
  <script src="./his-into-entry.js"></script>
</body>
</html>
