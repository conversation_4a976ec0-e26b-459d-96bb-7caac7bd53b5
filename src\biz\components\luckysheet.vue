<template>
  <div class="hello">
    <div id="luckysheet" class="luckysheet-content" />
  </div>
</template>

<script>
const luckysheet = window.luckysheet
export default {
  props: {
    // 表头数据
    sheetHeader: { // [{ label: 'aa', prop: 'a' }, { label: 'bb', prop: 'b' }]
      type: Array,
      default() {
        return []
      }
    },
    // 表格记录数据
    sheetData: { // [{ 'a': 1, 'b': 2 }, { 'a': 11, 'b': 22 }]
      type: Array,
      default() {
        return []
      }
    },
    // sheet插件格式表格数据
    cellData: {
      type: Array,
      default() {
        return []
      }
    },
    // 行数
    row: {
      type: Number,
      default() {
        return 0
      }
    },
    // 列数
    column: {
      type: Number,
      default() {
        return 0
      }
    },
    sheetTitle: {
      type: String,
      default() {
        return '表格标题'
      }
    }
  },
  data() {
    return {
      luckysheetOption: {
          container: "luckysheet", // 设定DOM容器的id
          title: this.sheetTitle, // 设定表格名称
          lang: "zh", // 设定表格语言
          // plugins: [{ name: "chart" }],
          data: [
            {
              name: this.sheetTitle, // 工作表名称
              color: "#eee333", // 工作表(工作表名称底部边框线)颜色
              index: 0, // 工作表索引(新增一个工作表时该值是一个随机字符串)
              status: 1, // 激活状态
              order: 0, // 工作表的下标
              hide: 0, // 是否隐藏
              row: this.row ? this.row : this.sheetData.length + 1, // 行数
              column: this.column ? this.column : this.sheetHeader.length, // 列数
              defaultRowHeight: 28, // 自定义行高,单位px
              defaultColWidth: 100, // 自定义列宽,单位px
              celldata: this.cellData && this.cellData.length > 0 ? this.cellData : this.transformToSheetData(), // 初始化使用的单元格数据,r代表行，c代表列，v代表该单元格的值，最后展示的是value1，value2
              // celldata: [{ r: 0, c: 0, v: 1 }], // 初始化使用的单元格数据,r代表行，c代表列，v代表该单元格的值，最后展示的是value1，value2
              config: {
                merge: {}, // 合并单元格
                rowlen: {}, // 表格行高
                columnlen: {}, // 表格列宽
                rowhidden: {}, // 隐藏行
                colhidden: {}, // 隐藏列
                borderInfo: {}, // 边框
                authority: {}, // 工作表保护
              },

              scrollLeft: 0, // 左右滚动条位置
              scrollTop: 0, // 上下滚动条位置
              luckysheet_select_save: [], // 选中的区域
              calcChain: [], // 公式链
              isPivotTable: false, // 是否数据透视表
              pivotTable: {}, // 数据透视表设置
              filter_select: {}, // 筛选范围
              filter: null, // 筛选配置
              luckysheet_alternateformat_save: [], // 交替颜色
              luckysheet_alternateformat_save_modelCustom: [], // 自定义交替颜色
              luckysheet_conditionformat_save: {}, // 条件格式
              frozen: { type: 'row' }, // 冻结行列配置 // 冻结首行
              chart: [], // 图表配置
              zoomRatio: 1, // 缩放比例
              image: [], // 图片
              showGridLines: 1, // 是否显示网格线
              dataVerification: {}, // 数据验证配置
            },
          ],
          showtoolbar: false,
          showtoolbarConfig: {
            undoRedo: false, // 撤销重做，注意撤消重做是两个按钮，由这一个配置决定显示还是隐藏
            paintFormat: false, // 格式刷
            currencyFormat: false, // 货币格式
            percentageFormat: false, // 百分比格式
            numberDecrease: false, // '减少小数位数'
            numberIncrease: false, // '增加小数位数
            moreFormats: false, // '更多格式'
            font: false, // '字体'
            fontSize: false, // '字号大小'
            bold: false, // '粗体 (Ctrl+B)'
            italic: false, // '斜体 (Ctrl+I)'
            strikethrough: false, // '删除线 (Alt+Shift+5)'
            underline: false, // '下划线 (Alt+Shift+6)'
            textColor: false, // '文本颜色'
            fillColor: false, // '单元格颜色'
            border: false, // '边框'
            mergeCell: false, // '合并单元格'
            horizontalAlignMode: false, // '水平对齐方式'
            verticalAlignMode: false, // '垂直对齐方式'
            textWrapMode: false, // '换行方式'
            textRotateMode: false, // '文本旋转方式'
            image: false, // '插入图片'
            link: false, // '插入链接'
            chart: false, // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
            postil: false, // '批注'
            pivotTable: false, // '数据透视表'
            function: false, // '公式'
            frozenMode: false, // '冻结方式'
            sortAndFilter: false, // '排序和筛选'
            conditionalFormat: false, // '条件格式'
            dataVerification: false, // '数据验证'
            splitColumn: false, // '分列'
            screenshot: false, // '截图'
            findAndReplace: false, // '查找替换'
            protection: false, // '工作表保护'
            print: false, // '打印'
          },
          showsheetbar: false, // 是否显示底部sheet页按钮
          showsheetbarConfig: {
            add: false, // 新增sheet
            menu: false, // sheet管理菜单
            sheet: false, // sheet页显示
          },
          showinfobar: false, // 是否显示顶部信息栏
          showstatisticBar: false, // 是否显示底部计数栏
          showstatisticBarConfig: {
            count: false, // 计数栏
            view: false, // 打印视图
            zoom: false, // 缩放
          },
          sheetFormulaBar: false, // 是否显示公式栏
          allowCopy: false, // 是否允许拷贝
          enableAddRow: true, // 允许添加行
        }
    }
  },
  created() {},
  mounted() {
    this.initLuckysheet()
  },
  methods: {
    initLuckysheet() {
      this.$nextTick(() => {
        if (this.sheetHeader.length != 0 && this.sheetData.length != 0) {
          this.luckysheetOption.hook = {
            workbookCreateAfter: () => {
              // this.dataRendSheet(this.sheetHeader, this.sheetData)
            },
            // 单元格更新
            cellUpdated: (r, c, oldValue, newValue) => {
              this.$emit('cellUpdated', { r, c, oldValue, newValue: newValue.v })
            },
            // 每次操作的操作记录
            updated: (operate) => {
              this.$emit('updated', operate)
            }
          }
        }
        luckysheet.create(this.luckysheetOption)
      })
    },
    /** 接口数据回显 */
    dataRendSheet(sheetHeader, sheetData) {
      // 回显表格表头，第一行
      if (sheetHeader.length > 0) {
        sheetHeader.forEach((item1, index1) => {
          luckysheet.setCellValue(0, index1, item1.label)
          // 普通回显数据
          if (sheetData.length > 0) {
            sheetData.forEach((item2, index2) => {
              var row = index2 + 1
              luckysheet.setCellValue(row, index1, item2[item1.prop] || '')
            })
          }
        })
      }
    },
    transformToSheetData() {
      const res = []
      this.sheetHeader.forEach((col, i) => {
        res.push({ r: 0, c: i, ct: { fa: "General", t: "g" }, m: '' + col.label, v: col.label })
      })
      this.sheetData.forEach((rowObj, row_i) => {
        this.sheetHeader.forEach((col, col_i) => {
          const prop = col.prop
          const value = rowObj[prop]
          res.push({ r: row_i + 1, c: col_i, ct: { fa: "General", t: "g" }, m: '' + value, v: value })
        })
      })
      return res
    },
    // 将sheet获取的数据结构还原回传参时的结构
    getSheetData() {
      const data = luckysheet.getSheetData()
      console.log('data', data)
      const curHeader = []
      const curData = []
      data.forEach((row, row_index) => {
        if (row_index === 0) {
          // 首行（标题行）
          row.forEach((col, col_index) => {
            curHeader.push({ label: col.v, prop: col.v })
          })
        } else {
          const rowObj = {}
          row.forEach((col, col_index) => {
            rowObj[curHeader[col_index].prop] = col.v
          })
          curData.push(rowObj)
        }
      })
      return {
        header: curHeader,
        data: curData
      }
    }
  },
}
</script>
<style lang="css" scoped>
.luckysheet-content {
  margin: 0px;
  padding: 0px;
  position: absolute;
  width: 100%;
  height: 500px;
  left: 0px;
  top: 40px;
  bottom: 0px;
}
</style>
