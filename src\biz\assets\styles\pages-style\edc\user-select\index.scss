.outer-container.user-select {
  width: 100%;
  min-height: 485px;
  display: flex;

  .left-uesrlist {
    width: 50%;
    border-right: 1px solid rgb(220, 223, 230);

    .left-top {
      height: 40px;
      padding-top: 10px;

      .input-item {
        width: calc(100% - 100px);
      }
    }

    .left-bottom {

      .hos-tabs__header,
      .hos-tabs__content {
        -webkit-box-shadow: none;
        box-shadow: none;
      }

      #scroll-area {
        height: 400px;
        overflow-y: scroll;
      }

      .user-item {
        height: 30px;
        line-height: 30px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;

        &:hover {
          background: #f3f3f7;
        }

        .name-span {
          margin-left: 10px;
          display: flex;
          width: calc(100% - 50px);

          .circle {
            flex: none;
            display: inline-block;
            width: 30px;
            height: 30px;
            text-align: center;
            border-radius: 50%;
            color: #fff;
            background: #e98f87;
            margin-right: 10px;
          }

          .username-span {
            color: black;
            margin-right: 10px;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .username-span-en {
            font-size: 12px;
            flex-shrink: 0;
            flex-basis: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #999;
          }
        }

        .check-span {
          i {
            margin-right: 10px;
            font-size: 30px;
            color: #67c23a;
          }
        }
      }

      .user-item.is-selected {
        background: #f0f9eb;
        color: #67c23a;
      }
    }
  }

  .right-select {
    width: 50%;
    padding: 15px 15px 0;

    .user-chosen {
      margin-top: 20px;
      height: 415px;
      overflow: scroll;

      .name-span {
        background: #f0f9eb;
        display: flex;
        position: relative;
        line-height: 30px;
        margin-bottom: 5px;

        .circle {
          flex: none;
          display: inline-block;
          width: 30px;
          height: 30px;
          text-align: center;
          border-radius: 50%;
          color: #fff;
          background: #e98f87;
          margin-right: 10px;
        }

        .username-span {
          width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 30px;
          color: black;
        }

        .delete-span {
          position: absolute;
          right: 5px;

          &:hover {
            cursor: pointer;
          }

          i {
            color: #f56c6c;
          }
        }
      }
    }
  }
}