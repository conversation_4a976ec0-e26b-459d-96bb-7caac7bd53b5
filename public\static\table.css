/* 隐藏滚动条 */
/* Webkit (Safari/Chrome) */
/* 注意：Chrome 64及更高版本已不再支持修改滚动条样式 */
body::-webkit-scrollbar {
  width: 0.5em;
}

body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Firefox */
/* 注意：这里使用了 -moz，但是在 Firefox 64 及更新版本中，该样式已被弃用 */
/* 在新版本的 Firefox 中，滚动条样式可能会变回默认样式 */
/* 更多信息请参考 Mozilla 文档 */
body {
  padding: 0 10px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.5) transparent;
}

/* Microsoft Edge 和 Internet Explorer 10+ */
body {
  -ms-overflow-style: none;
}

body::-ms-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
}

.dataTable tr {
  /* margin-top: 20px; */
  height: 40px;
}

.printTable tr {
  display: flex;
  align-items: center;
  height: 40px;
}

.dataTd {
  width: 100px;
  text-align: right;
}

.l-label {
  width: 170px;
  text-align: right;
}

#totalPagesTr,
#currentPagesTr {
  height: 50px;
}

#attributeHead {
  justify-content: flex-end;
}

#tableContent table,
#importDialog table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

#tableContent thead,
#importDialog thead {
  color: #000;
  font-weight: 500;
  height: 45px;
}

#tableContent th,
#importDialog th {
  border: 1px solid #e2e2e2;
  text-align: center;
  background-color: #f8f8f8;
  padding: 0 6px;
}

#tableContent td,
#importDialog td {
  padding: 0 6px;
  text-align: center;
  border: 1px solid #ccc;
}

#importDialog td {
  width: 200px;
}

#tableContent tbody td,
#importDialog tbody td {
  padding: 6px 6px;
}

#tableContent .coordinate {
  width: 220px;
}

#tableContent .coordinate-th {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 按钮样式 */
#config button,
#picSettingDialog button,
#importDialog button,
.picInAttr button {
  padding: 4px 12px;
  font-size: 14px;
  color: #fff;
  background-color: #509de1;
  border: 1px solid #509de1;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
}

/* 按钮悬停时的样式 */
#tableContent button:hover {
  background-color: #95B8E7;
}

.table-header,
.subTable-header {
  padding: 10px 15px;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e2e2e2;
  border-bottom: none;
}

.subTable-header .restrictedInput {
  width: 90px;
}

#tableContent .table-header:first-of-type {
  margin-top: 20px;
}

#tableContent .table-header:not(:first-child) {
  margin-top: 30px;
}

.table-header__left {
  font-size: 16px;
  font-weight: bold;
}

.table-header__right img {
  width: 20px;
  height: 20px;
}

.w200 {
  width: 200px;
}

.button-ml {
  margin-left: 8px;
}

.icon-ml {
  margin-left: 12px;
}

.w194 {
  width: 194px;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.input-mr-10 {
  box-sizing: border-box;
  margin-right: 10px;
}

.align-right {
  text-align: right;
}

.marginLeft40 {
  margin-left: 40px;
}

.marginLeft50 {
  margin-left: 50px;
}

.marginLeft6 {
  margin-left: 6px;
}

.marginRight8 {
  margin-right: 8px;
}

.marginTop10 {
  margin-top: 10px;
}

.red {
  color: red
}

.w90 {
  width: 90px;
}

.w60 {
  width: 60px;
}

.w100 {
  width: 100px;
}

.w120 {
  width: 120px;
}

.w230 {
  width: 230px;
}

.importTable {
  width: 80%;
}

.importTable th {
  height: 50px;
}

.custom-dialog .importTable tbody td {
  padding: 6px 0;
}

.custom-dialog .importTable input {
  width: 80%;
}


/* 底部遮罩样式 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
}

/* 弹窗时禁止滚动底部页面 */
.disable-scroll {
  overflow: hidden;
}