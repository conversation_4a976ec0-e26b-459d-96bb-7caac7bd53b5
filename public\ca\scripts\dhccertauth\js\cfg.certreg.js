﻿//获取session中用户信息
var logonInfo = ca_common_tools.getSession();

//选择组织机构数据
var selOrgID = "";
var tableName = "cf_bsp_ca_usercert";

//页面选中数据
var selectUserCode = "";
var caInfo = {};

$(function() {
    initBTN();
    document.onkeydown = documentOnKeyDown;

    var orgComp = genOrgComp(logonInfo);
    orgComp.options().onSelect = function() {
        selOrgID = orgComp.getValue()
        initUserGrid(selOrgID);
        getCaInfo(selOrgID);
        initCTLoc(selOrgID);
    }
    orgComp.options().onLoadSuccess = function() {
        selOrgID = orgComp.getValue()
        initUserGrid(selOrgID);
        getCaInfo(selOrgID);
        initCTLoc(selOrgID);
    }
})

function initBTN() {
    $("#btnQuery").click(function(){queryCert();});
    $("#btnReset").click(function(){resetCondition();});

    $("#btnKeyCert").click(function(){bindKeyCert();});
    $("#btnPhoneCert").click(function(){bindPhoneCert();});
    $("#btnFaceCert").click(function(){btnFaceCert();});
}

//Desc:初始化科室
function initCTLoc(selOrgID) {
    var data = {
        action: "GET_ALLCTLOC",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        $("#userDept").combobox({
            data:json.data,
            valueField:"ctLocID",
            textField:"ctLocDesc",
            width:167,
            panelwidth:200,
            panelHeight:350,
            filter: function(q, row){
                return ((row["ctLocDesc"].toUpperCase().indexOf(q.toUpperCase()) >= 0)||(row["ctLocAlias"].toUpperCase().indexOf(q.toUpperCase()) >= 0));
            }
        });
    } else {
        $.messager.alert("提示", "获取科室数据失败，错误码：" + json.code + "，错误描述：" + json.msg, "error");
    }
}

//获取对应组织机构配置基础服务
function getCaInfo(selOrgID) {
    var data = {
        action: "GET_SYSOPTION",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID
        }
    };

    var json = ajaxPOSTCommon(data,"",false);
    if (json.code == 200) {
        caInfo = json.data;
        initBindCertBtn();
    } else {
        $.messager.alert("提示","获取通用配置数据错误!");
    }
}

//根据组织机构配置服务信息初始化关联按钮
function initBindCertBtn() {
    $("#btnPhoneCert").show();
    $("#btnKeyCert").show();
    $("#btnFaceCert").show();

    //未上线对应签名方式时隐藏相关关联证书按钮
    if (caInfo.defaultPHONEVenderCode == "") { $("#btnPhoneCert").hide(); }
    if (caInfo.defaultUKEYVenderCode == "") { $("#btnKeyCert").hide(); }
    if (caInfo.defaultFACEVenderCode == "") { $("#btnFaceCert").hide(); }
}

function initUserGrid(selOrgID) {
    var param = {
        action: "GET_USERLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: true,
            ctLocID: "",
            userCode: "",
            userName: "",
            userStatus: "ALL"
        }
    };
    $("#dgUser").datagrid({
        fit:true,
        border:false,
        fitColumns:false,
        toolbar:"#tbUser",
        url: ca_common_tools.getAppPath("GET_USERLIST"),
        queryParams: param,
        idField:"userID",
        singleSelect:true,
        pagination:true,
        rownumbers:true,
        pageSize:50,
        pageList:[10,30,50],
        beforePageText:"第",
        afterPageText:"页, 共{pages}页",
        displayMsg:"显示 {from} 到 {to} ,共 {total} 条记录",
        columns:[[
            {field:"userID",title:"用户ID",hidden:true},
            {field:"userCode",title:"用户工号"},
            {field:"userName",title:"用户姓名"},
            {
                field: "haveCert",
                title: "是否关联证书",
                align: "center",
                width: 260,
                formatter: function(value,row,index) {
                    if (value == "1") {
                        return "<a class='hisui-linkbutton l-btn l-btn-small l-btn-plain' style='width: 190px;' href='javascript:void(0)' onclick='viewCertList(\""+row.userCode+"\")' group=''><span class='l-btn-left l-btn-icon-left'><span class='l-btn-text'>点击查看用户证书列表</span><span class='l-btn-icon icon-green-line-eye'>&nbsp;</span></span></a>";
                    } else {
                        return "<span style='color:red'>未关联证书</span>";
                    }
                }
            }
        ]],
        onLoadError:function() {
            $.messager.alert("提示","用户列表数据加载失败");
        },
        onSelect:function(rowIndex,row){
            selectUserCode = row.userCode;
        },
        onLoadSuccess:function(data){
            $("#dgUser").datagrid("clearSelections");
            selectUserCode = "";
        }
    });
}

function queryCert() {
    var userCode = $("#userCode").val();
    var userName = $("#userName").val();
    var userStatus = $("#userStatus").combobox("getValue");
    var locID = $("#userDept").combobox("getValue");
    if (locID == "undefined") {
        locID = "";
    }

    var param = {
        action: "GET_USERLIST",
        params: {
            organizationID: selOrgID,
            langID: logonInfo.LangID,
            isDefaultLoad: false,
            ctLocID: locID,
            userCode: userCode,
            userName: userName,
            userStatus: userStatus
        }
    };

    $("#dgUser").datagrid("load",param);
}

function resetCondition() {
    $("#UserCode").val("");
    $("#UserName").val("");
    $("#cbxLoc").combobox("setValue","");
    $("#UserStatus").combobox("setValue","ALL");
}

function bindKeyCert() {
    var url = "dhc.certauth.cfg.certreg.bindcert.html?SignTypeCode=UKEY&VenderCode=" +caInfo.defaultUKEYVenderCode+ "&UserCode="+ selectUserCode+ "&OrgID="+ selOrgID +"&MWToken="+ ca_common_tools.getMWToken() +"&IsLimitImageNull="+ caInfo.isLimitImageNull +"&IsLimitImageSize="+ caInfo.isLimitImageSize +"&IsConvertCareImage="+ caInfo.isConvertCareImage;
    var content = '<iframe id="bindKeyCertFrame" scrolling="auto" frameborder="0" src="'+ url +'" style="width:100%; height:99%;"></iframe>';
    createModalDialog("bindKeyCert","关联Ukey证书",880,585,'bindKeyCertFrame',content,'','');
}

function bindPhoneCert() {
    var url = "dhc.certauth.cfg.certreg.bindcert.html?SignTypeCode=PHONE&VenderCode=" +caInfo.defaultPHONEVenderCode+ "&UserCode="+ selectUserCode+ "&OrgID="+ selOrgID +"&MWToken="+ ca_common_tools.getMWToken() +"&IsLimitImageNull="+ caInfo.isLimitImageNull +"&IsLimitImageSize="+ caInfo.isLimitImageSize +"&IsConvertCareImage="+ caInfo.isConvertCareImage;
    var content = '<iframe id="bindPhoneCertFrame" scrolling="auto" frameborder="0" src="'+ url +'" style="width:100%; height:99%;"></iframe>';
    createModalDialog("bindPhoneCert","关联手机证书",880,585,'bindPhoneCertFrame',content,'','');
}

function btnFaceCert() {
    var url = "dhc.certauth.cfg.certreg.bindcert.html?SignTypeCode=FACE&VenderCode=" +caInfo.defaultFACEVenderCode+ "&UserCode="+ selectUserCode+ "&OrgID="+ selOrgID +"&MWToken="+ ca_common_tools.getMWToken() +"&IsLimitImageNull="+ caInfo.isLimitImageNull +"&IsLimitImageSize="+ caInfo.isLimitImageSize +"&IsConvertCareImage="+ caInfo.isConvertCareImage;
    var content = '<iframe id="bindFaceCertFrame" scrolling="auto" frameborder="0" src="'+ url +'" style="width:100%; height:99%;"></iframe>';
    createModalDialog("bindFaceCert","关联手机证书",880,585,'bindFaceCertFrame',content,'','');
}

function viewCertList(userCode) {
    var xpwidth=window.screen.width-200;
    var xpheight=window.screen.height-100;

    var url = "dhc.certauth.cfg.certmgr.html?UserCode="+ userCode +"&MWToken="+ ca_common_tools.getMWToken();
    var content = '<iframe id="viewUserCertListFrame" scrolling="auto" frameborder="0" src="'+ url +'" style="width:99%; height:99%;"></iframe>';
    createModalDialog("viewUserCertList","用户证书列表",xpwidth,xpheight,"viewUserCertListFrame",content,"","",true);
}

function documentOnKeyDown(e) {
    if (window.event) {
        var keyCode=window.event.keyCode;
    } else {
        var keyCode=e.which;
    }

    if (keyCode==13) {
        queryCert();
    }
}