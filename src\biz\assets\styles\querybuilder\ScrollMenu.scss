.scrollMenu{
  .sibarmenu {
    width: 150px;
    height: calc(100vh - 350px);
    overflow-y: auto;
    text-align: right;
    // float: left;
  }

  .search-item-box {
    box-sizing: border-box;
    height: calc(100vh - 350px);
    overflow-y: auto;
    margin-bottom: 20px;
    padding: 10px;
    padding-bottom: calc(100vh - 350px);
  }

  .search-parent-cate {
    display: grid;
  }

  .search-parent-title {
    font-weight: 600;
    margin: 15px 0 0 15px;
  }

  .search-sub-title {
    font-weight: 600;
    margin: 5px 10px;
    ::v-deep .hos-checkbox{
      display: flex;
      align-items: flex-end;
    }
    ::v-deep .hos-checkbox__label{
      font-weight: 600;
    }
  }

  .search-item-content {
    margin: 10px 25px
  }

  .search-item-tag {
    margin: 3px;
    padding: 3px 8px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 14px;
    // color: #606266;
    display: inline-block;
    float: left;
  }

  .search-item-tag:hover {
    color: #fff;
    background-color: #a4c8ec;
  }

  .search-item-tag.active {
    color: #fff;
    background-color: #409eff;
  }

  .hos-menu-item.is-active {
    color: #fff;
    background-color: #409eff;
  }

  .hos-menu-item,
  .hos-menu-item>span {
    height: 45px;
    line-height: 45px;
  }
  .tag-title{
    font-weight: 600;
    padding: 10px;
  }
  .tag-container{
    width: 150px;
    flex-shrink: 0;
    overflow-y: auto;
    height: calc(100vh - 350px);
  }
  .tag-box{
    display: flex;
    flex-wrap: wrap;
    .tag-item{
      margin: 3px;
      padding: 3px 8px;
      border-radius: 2px;
      cursor: pointer;
      font-size: 14px;
      color: #606266;
      display: inline-block;
      &:hover{
        color: #fff;
        background-color: #a4c8ec;
      }
      &.is-active {
        color: #fff;
        background-color: #409eff;
      }
    }
  }
}
