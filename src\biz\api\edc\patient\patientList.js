export const queryListApi = (params) => {
  return {
    url: 'edc/patient/page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/patient/deletion',
    method: 'post',
    data,
  }
}

// isDesensitization 是否脱敏 1脱敏 0 不脱敏
export const queryDetailApi = ({id,isDesensitization}) => {
  return {
    url: 'edc/patient/detail/' + id,
    method: 'get',
    params:{
      isDesensitization
    }
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/patient/update',
    method: 'post',
    data
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/patient/edc-into-patient',
    method: 'post',
    data,
  }
}

// 获取随访第一阶段的表单字段数及患者基本信息字段
export const getPeriodFormFields = (params) => {
  return {
    url: `edc/subject-form/period/tree`,
    method: 'GET',
    params
  }
}

// 获取子项目下全部患者标签
export const getTagList = (params) => {
  return {
    url: `edc/patient-label/list-by-subject`,
    method: 'GET',
    params
  }
}

// 获取子项目下全部患者分组
export const getGroupList = (params) => {
  return {
    url: `edc/patient-group/list-by-subject`,
    method: 'GET',
    params
  }
}

export const updatePatientVisit = (data) => {
  return {
    url: `edc/patient/update/followup/status`,
    method: 'post',
    data
  }
}

// 绑定患者分组
export const bindGroup = (data) => {
  return {
    url: `edc/patient/bind-group`,
    method: 'post',
    data
  }
}

// 绑定患者标签
export const bindLabel = (data) => {
  return {
    url: `edc/patient/bind-label`,
    method: 'post',
    data
  }
}

export const getQueryFieldOption = ({url,subjectId}) =>{
  return {
    url,
    method: 'get',
    params: {subjectId}
  }
}

// 获取项目下可分配的用户范围
export const getAbleUser = (params) => {
  return {
    url: '/edc/distribution/patient/use-able-user',
    method: 'get',
    params
  }
}

// 自动分配患者，不用指定用户范围
export const distributeAuto = (data) => {
  return {
    url: `/edc/distribution/patient/distribute-automatic/3`,
    method: 'post',
    data
  }
}

// 手动指定患者到指定用户，需要指定用户范围
export const distributeMulti = (data) => {
  return {
    url: `/edc/distribution/patient/distribute-multi`,
    method: 'post',
    data
  }
}

// 全程项目待入组列表
export const getWaitIntoList = (params) => {
  return {
    url: '/edc/disease-patient/queue-page',
    method: 'get',
    params
  }
}

// 全程项目待转入列表
export const getWaitTransferList = (params) => {
  return {
    url: '/edc/disease-patient/transfer-page',
    method: 'get',
    params
  }
}

// 全程项目待剔除列表
export const getWaitRemoveList = (params) => {
  return {
    url: '/edc/disease-patient/eliminate-page',
    method: 'get',
    params
  }
}

// 全程项目同意纳入
export const agreeInto = (data) => {
  return {
    url: '/edc/disease-patient/into/agree',
    method: 'post',
    data
  }
}

// 全程项目拒绝纳入
export const rejectInto = (data) => {
  return {
    url: '/edc/disease-patient/into/reject',
    method: 'post',
    data
  }
}

// 全程项目同意转入
export const agreeTransfer = (data) => {
  return {
    url: '/edc/disease-patient/transfer/agree',
    method: 'post',
    data
  }
}

// 全程项目拒绝转入
export const rejectTransfer = (data) => {
  return {
    url: '/edc/disease-patient/transfer/reject',
    method: 'post',
    data
  }
}

// 全程待入组-医嘱命中详情
export const getHitDetail = (params) => {
  return {
    url: '/edc/disease-patient/order-hit',
    method: 'get',
    params
  }
}


