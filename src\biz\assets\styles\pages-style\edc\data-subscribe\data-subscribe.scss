.data-subscribe {
  height: 100%;

  .top {
    height: 100%;
    overflow-y: hidden;
    display: flex;

    .left-cetegory {
      width: 250px;
      min-width: 250px;
      padding: 10px;
      margin-right: 10px;
      box-sizing: border-box;
      border-right: 1px solid #ebeef5;

      font-size: 20px;
      height: 100%;
      overflow-y: auto;

      .cate-list {
        background-color: #f4f4f4;

        .cate-item {
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          font-size: 16px;
        }

        .cate-item.active {
          color: white;
          background-color: #409eff;
        }
      }

      .add-cate {
        background-color: #f4f4f4;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #409eff;
        cursor: pointer;
        font-size: 16px;

        i {
          font-size: 18px;
          margin-right: 5px;
        }
      }
    }
  }
}