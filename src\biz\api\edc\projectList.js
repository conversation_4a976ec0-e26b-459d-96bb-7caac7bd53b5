// 项目列表-表格-获取分页数据
export const queryListApi = (params) => {
  return {
    url: 'edc/project/table-page',
    method: 'get',
    params,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'edc/project/deletion',
    method: 'post',
    data,
  }
}

export const editApi = (data) => {
  return {
    url: 'edc/project/update',
    method: 'post',
    data
  }
}
// 修改项目状态
export const updateStatus = (params) => {
  return {
    url: 'edc/project/update-status',
    method: 'post',
    params
  }
}

export const queryDetailApi = (id) => {
  return {
    url: 'edc/project/table-view/' + id,
    method: 'get',
  }
}

export const addApi = (data) => {
  return {
    url: 'edc/project/insert',
    method: 'post',
    data,
  }
}

// 创建项目时获取设置为研究中心的结构
export const queryCenterList = (params) => {
  return {
    url: 'edc/project-org/sys-org-list',
    method: 'get',
    params,
  }
}

export const queryDeptList = (params) => {
  return {
    url: 'edc/project-dept/sys-dept-list',
    method: 'get',
    params,
  }
}




