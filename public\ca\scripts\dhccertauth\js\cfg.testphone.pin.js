﻿//页面传参信息从父页面获取
var globalInfo = parent.globalInfo;

$(function() {
    initBTN();
});

//初始化轮询次数(默认300)、轮询间隔(默认1秒)
function initBTN() {
    $("#btnSubmit").click(function(){pinLogin();});
}

function pinLogin() {
    parent.displayError("");

    var userCode = $("#txtUserCode").val();
    var password  = $("#txtPassword").val();
    debugger;
    if (password == "") {
        displayError("密码不能为空");
        return;
    }

    password = Base64.encode(password);
    console.time("密码登录耗时");
    var pinLoginResult = parent.ca_key.pinLogin({
        userCode:userCode,
        password:password,
        organizationID:globalInfo.OrgID
    });
    console.timeEnd("密码登录耗时");
    if ("0" !== pinLoginResult.code) {
        displayError("密码登录失败:"+pinLoginResult.msg);
        return;
    }
    console.log("密码登录结果:"+JSON.stringify(pinLoginResult.data)+getCurrentTime());//输出到控制台

    console.time("证书登录耗时");
    var loginResult = parent.ca_key.login({
        certContainer:pinLoginResult.data.certContainer,
        organizationID: globalInfo.OrgID,
        imageType:"Original",
        cert:pinLoginResult.data.cert,
        userCertCode:pinLoginResult.data.userCertCode,
        certNo:pinLoginResult.data.certNo
    });
    console.timeEnd("证书登录耗时");
    if ("0" !== loginResult.code) {
        displayError("证书登录失败:"+loginResult.msg);
        return;
    }
    console.log("证书登录成功:"+getCurrentTime());//输出到控制台
    
    console.time("获取token有效性耗时");
    var tokenIsValid = parent.ca_key.getTokenIsValid({
        organizationID:globalInfo.OrgID,
        certContainer:pinLoginResult.data.certContainer,
        signToken:pinLoginResult.data.signToken
    });
    console.timeEnd("获取token有效性耗时");
    if ("0" !== tokenIsValid.code) {
        displayError("获取token有效性失败:"+tokenIsValid.msg);
        return;
    }
    console.log("获取token有效性成功:"+getCurrentTime());//输出到控制台

    ///测试通过certContainer查询信息接口，此处无实际业务意义，仅为测试接口用
    console.time("通过certContainer查询证书信息接口耗时");
    var certInfo = parent.ca_key.getCertInfoByContainer({
        organizationID:globalInfo.OrgID,
        certContainer:pinLoginResult.data.certContainer
    });
    console.timeEnd("通过certContainer查询证书信息接口耗时");
    if ("0" !== certInfo.code) {
        displayError("通过certContainer查询证书信息失败:"+certInfo.msg);
        return;
    }
    console.log("通过certContainer查询证书信息成功:"+getCurrentTime());//输出到控制台

    //获取用户信息，证书信息返回上层页面做签名
    parent.caSign(pinLoginResult.data,loginResult.data);
}


//展示错误信息
function displayError(errorInfo) {
    parent.displayError(errorInfo);
}