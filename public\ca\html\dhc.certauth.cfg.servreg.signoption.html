﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医护签名拓展配置维护</title>
    <!--引用HISUI-->
    <link rel="stylesheet" type="text/css" href="../../scripts_lib//hisui-0.1.0/dist/css/hisui.lite.min.css">
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.min.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/jquery.hisui.js"></script>
    <script type="text/javascript" src="../../scripts_lib//hisui-0.1.0/dist/js/locale/hisui-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../scripts/websys.jquery.bsp.js"></script>
    <link rel="stylesheet" type="text/css" href="../../css/websys.css">

    <!--公共js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/common.data.js"></script>
    <script type="text/javascript" src="../scripts/dhccertauth/lib/json2.js"></script>

    <!--业务js-->
    <script type="text/javascript" src="../scripts/dhccertauth/js/cfg.servreg.signoption.js"></script>
</head>
<body class="hisui-layout">
    <div data-options="region:'center',title:'',border:false,collapsible:false,split:true,headerCls:'panel-header-gray',iconCls:'icon-paper'" style="padding:15px">
        <table cellpadding="5" cellspacing="10">
            <!--tr>
                <td>二维码数据类型</td>
                <td colspan="3">
                    <select id="qrcodeType" class="hisui-combobox" style="width:100px;">
                        <option value="URL">第三方链接</option>
                        <option value="TEXT">文字</option>
                        <option value="IMAGE">图片</option>
                    </select>
                </td>
                <td style="width:300px;">手机扫码签时，配置第三方返回的二维码数据类型。</td>
            </tr-->
            <tr>
                <td colspan="4"><input class="hisui-checkbox" type="checkbox" data-options="boxPosition:'right'" label="二维码是否需要指定用户工号" id="qrcodeNeedUserCode" style="width: 200px;"/></td>
                <td style="width:300px;">登录二维码是否需要指定用户工号，指定后只有特定用户允许扫码。</td>
            </tr>
            <tr>
                <td colspan="4"><input class="hisui-checkbox" type="checkbox" data-options="boxPosition:'right'" label="是否支持Pin码登录" id="enablePinLogon" style="width: 200px;"/></td>
                <td style="width:300px;">第三方手机扫码签服务，是否提供了通过pin码认证替代扫码认证的功能。</td>
            </tr>
            <tr>
                <td colspan="4"><input class="hisui-checkbox" type="checkbox" data-options="boxPosition:'right'" label="是否支持推送签名" id="enablePushSign" style="width: 200px;"/></td>
                <td style="width:300px;">是否支持推送数据到第三方认证平台，认证后由CA回推数据到HIS的签名方式。</td>
            </tr>
            <tr>
                <td colspan="4"><label>二维码轮询间隔(秒)：</label><input class="hisui-numberbox textbox" data-options="isKeyupChange:true" label="二维码轮询间隔" id="scanQRRollInterval" style="width:80px;float:right;" /></td>
                <td style="width:300px;">配置在前端展示二维码后，间隔多少秒查询一次二维码扫描结果，默认1秒。</td>
            </tr>
            <tr>
                <td colspan="4"><label>二维码轮询次数：</label><input class="hisui-numberbox textbox" data-options="isKeyupChange:true" label="二维码轮询次数" id="scanQRRollMax" style="width:80px;float:right;" /></td>
                <td style="width:300px;">配置在前端展示二维码后，一共查询多少次二维码扫描结果，超过指定次数后停止查询，提示超时，默认100次。</td>
            </tr>
            <tr>
                <td colspan="4"><label>扫码用APP名称：</label><input class="textbox" label="扫码用APP名称" id="appName" style="width:100px;float:right;" /></td>
                <td style="width:300px;">扫码用APP名称，配置后可以在前端展示二维码时，提示用户使用什么app或者方式来扫码。</td>
            </tr>
            <tr>
                <td colspan="4"><a id="btnSaveOption" class="hisui-linkbutton" data-options="iconCls:'icon-ok'" style="height:30px;float:right;margin-right:45px;">确定</a></td>
                <td colspan="4"><a id="btnCancel" class="hisui-linkbutton" data-options="iconCls:'icon-no'" style="margin-left:100px;height:30px;">取消</a></td>
            </tr>
        </table>
    </div>
</body>
</html>