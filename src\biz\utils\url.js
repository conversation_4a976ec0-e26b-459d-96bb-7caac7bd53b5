import common from "./common"
import { aesEncrypt } from '@/utils/aes'
// 根据变量补全字符串
// 例如："http:www.baidu.com?token=${token}" {token:6700980}
export function var2str(str, data = {}) {
  // 解析url，将${}匹配的变量替换为data数据
  const reg = new RegExp(/\$\{(.*?)\}/g)
  if (!str) {
      return ""
  } else if (!reg.test(str)) {
      return str
  } else {
      // 正则匹配到${}动态参数
      return str.replace(reg, (match, prop, index) => {
          if (data && data[prop]) {
              return data[prop]
          } else {
              return ""
          }
      })
  }
}

// 获取url中的参数值
export function getTargetProp(url, propName) {
  const paramArr = url.slice(url.indexOf("?") + 1).split("&")
  const params = {}
  paramArr.map((param) => {
    const [key, val] = param.split("=")
    params[key] = decodeURIComponent(val)
  })
  if (params[propName]) {
    return params[propName]
  }
}

function getAesParams(params) {
  let str = ''
  Object.keys(params).forEach(function (key) {
    if (params[key]) {
      str += key + '=' + encodeURIComponent(params[key]) + '&'
    }
  })
  // 删除最后一个&符号
  str = str.slice(0, -1)
  return str
}

// 通过url与参数封装参数加密请求方法
export function encryptParams(p) {
  const encryptionParameter = getAesParams({ ...p, timestamp: common.getTimestamp(10) })
  return { encryptionParameter: aesEncrypt(encryptionParameter) }
}
