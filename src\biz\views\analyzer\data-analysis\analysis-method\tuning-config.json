{"lr": [{"label": "C", "default": 0.1}, {"label": "max_iter", "default": 10}, {"label": "class_weight", "default": "None", "options": ["None", "Balanced"]}, {"label": "solver", "default": "liblinear", "options": ["liblinear", "lbfgs", "newton-cg", "sag"]}], "xgboost": [{"label": "learning_rate", "default": 0.1}, {"label": "gamma", "default": 0.5}, {"label": "max_depth", "default": 10}, {"label": "min_child_weight", "default": 1}, {"label": "n_estimators", "default": 10}, {"label": "reg_alpha", "default": 0}, {"label": "reg_lambda", "default": 1}, {"label": "subsample", "default": 1}, {"label": "colsample_bytree", "default": 1}], "dt": [{"label": "max_depth", "default": 10}, {"label": "min_samples_leaf", "default": 1}, {"label": "min_impurity_decrease", "default": 0}], "lightgbm": [{"label": "max_depth", "default": 3}, {"label": "learning_rate", "default": 0.1}, {"label": "n_estimators", "default": 50}, {"label": "min_child_weight", "default": 1}], "adaboost": [{"label": "learning_rate", "default": 1}, {"label": "n_estimators", "default": 50}], "gbdt": [{"label": "max_depth", "default": 3}, {"label": "min_samples_split", "default": 2}, {"label": "max_features", "pull": 2, "span": 10}, {"label": "learning_rate", "default": 1, "style": "margin-left:-25%;"}, {"label": "n_estimators", "default": 50}], "rf": [{"label": "criterion", "default": "gini", "options": ["gini", "entropy"]}, {"label": "max_depth", "default": 10}, {"label": "class_weight", "default": "None", "options": ["None", "Balanced"]}, {"label": "min_samples_split", "default": 2}, {"label": "min_samples_leaf", "default": 1}, {"label": "min_impurity_decrease", "default": 1}, {"label": "max_features"}, {"label": "n_estimators", "default": 10, "style": "margin-left:-20%;"}, {"label": "oob_score", "default": "false", "options": ["false", "true"]}]}