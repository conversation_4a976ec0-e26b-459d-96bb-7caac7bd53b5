import { getModelData,setModelData } from "@/utils/model-util";
const model = {
  state: {
    modelData: getModelData() || {
      // "id": "dlxkqpahu7ta3nkwbg8w11bfmmexgibq",
      // "indexPrefix": "csmsearch_new",
      // "indexAlias": "csmsearch_new",
      // "isSplitIndex": 1,
      // "modelCode": "csmsearch_new",
      // "modelName": "全院索引",
      // "isDefaultIndex": 1,
      // "isSpecialDisease": 0,
      // "enableFlag": 1,
      // "sort": 1
    }
  },
  actions: {
    /**
     * @description 设置模型信息
     * @param {Object} state vuex state
     * @param {Object} modelData modelData
     */
    async setModelData({
      state,
      dispatch,
      rootState
    }, modelData) {
      state.modelData = modelData
      // store 赋值
      setModelData(modelData)
      state.modelData = modelData
    },
  }
}

export default model