export const getAllIndex = () => {
    return {
        url: '/search/es-index-metadata/list',
        method: 'get'
    }
}
export const queryListApi = (params) => {
    return {
        url: 'search/es-index-metadata/page',
        method: 'get',
        params,
    }
}

export const queryDetailApi = (id) => {
    return {
        url: 'search/es-index-metadata/detail/'+id,
        method: 'get',
    }
}

export const addApi = (data) => {
    return {
        url: 'search/es-index-metadata/insert',
        method: 'post',
        data,
    }
}

export const deleteApi = (data) => {
  return {
    url: 'search/es-index-metadata/delete/' + data,
    method: 'delete',
  }
}

export const deleteBatchApi = (data) => {
    return {
        url: 'search/es-index-metadata/delete/'+data[0],
        method: 'post'
    }
}

// 获取全部模型
export const getAllModels = () => {
    return {
        url: 'search/es-index-metadata/standard/model',
        method: 'GET',
    }
}
// 同步成功
export const synchronousData = (data) => {
    return request({
        url: `search/es-index-metadata/syncmodel-by-indexid/${data.id}`,
        method: 'GET',

    })
}

// 编辑
export const editModel = (data) => {
    return {
        url: 'search/es-index-metadata/update',
        method: 'post',
        data
    }
}

