export const queryListApi = (params) => {
    return {
      url: '/search/data-acq-config/page',
      method: 'get',
      params,
    }
  }

  export const addApi = (data) => {
    return {
      url: '/search/data-acq-config/insert',
      method: 'post',
      data,
    }
  }

export const editApi = (data) => {
  return {
    url: '/search/data-acq-config/update',
    method: 'post',
    data,
  }
}

export const deleteBatchApi = (data) => {
  return {
    url: 'search/data-acq-config/deletion',
    method: 'post',
    data,
  }
}

export const refreshMetaDataCache = () => {
  return {
    url: '/search/es-index-metadata/clear-metadata-cache',
    method: 'get'
  }
}