const t = {}

t.loading = 'Loading...'

t.brand = {}
t.brand.lg = 'Renren Security'
t.brand.mini = 'RS'

t.add = 'Add'
t.delete = 'Delete'
t.deleteBatch = 'Delete'
t.update = 'Edit'
t.query = 'Query'
t.export = 'Export'
t.handle = 'Action'
t.confirm = 'Confirm'
t.cancel = 'Cancel'
t.logout = 'Sign Out'
t.backupData = 'backup dataBase Data'
t.save = 'Save'
t.close = 'Close'

t.yes = 'Yes'
t.no = 'No'

t.day = 'Day'

t.prompt = {}
t.prompt.title = 'Prompt'
t.prompt.info = 'Confirm to carry out [{handle}] operation?'
t.prompt.success = 'Succeeded'
t.prompt.failed = 'Failed'
t.prompt.deleteBatch = 'Please select delete item'
t.prompt.logout = 'Do you cancel your current account ?'

t.message = {}
t.message.logoutCancel = 'Abandoning logout users'

t.validate = {}
t.validate.required = 'Required field cannot be empty'
t.validate.format = '{attr} format error'

t.upload = {}
t.upload.text = 'Drop files here, or <em>Click Upload</em>'
t.upload.tip = 'Only support {format} format files! '
t.upload.button = 'Click to upload'

t.backup = {}
t.backup.name = 'Backup fileName'
t.backup.size = 'Backup fileLength'
t.backup.date = 'Backup Date'
t.backup.path = 'Backup Path'
t.backup.status = 'Backup Task Status'
t.backup.status0 = 'Backup DataBase Success'
t.backup.status1 = 'Backup DataBase Fail'
t.backup.status2 = 'Backup DataBase Success,But Compress Fail'
t.backup.status3 = 'Other Error, Please Look The Log'

t.datePicker = {}
t.datePicker.range = 'To'
t.datePicker.start = 'Start Date'
t.datePicker.end = 'End Date'

t.fullscreen = {}
t.fullscreen.prompt = 'Your browser does not support this operation'

t.updatePassword = {}
t.updatePassword.title = 'Change Password'
t.updatePassword.username = 'Account'
t.updatePassword.password = 'Original'
t.updatePassword.newPassword = 'New Password'
t.updatePassword.comfirmPassword = 'Confirm'
t.updatePassword.validate = {}
t.updatePassword.validate.comfirmPassword = 'Confirm password is not consistent with new password input'

t.theme = {}
t.theme.d2 = 'D2Admin Classic'
t.theme.violet = 'Violet'
t.theme.line = 'Line'
t.theme.star = 'Star'
t.theme.tomorrowNightBlue = 'Tomorrow Night Blue'
t.theme.list = {}
t.theme.list.button = 'Select'
t.theme.list.buttonActive = 'Activated'

t.layout = {}
t.layout.hello = 'Hello'
t.layout.logout = 'Logout'
t.layout.updatePassword = 'Update Password'
t.layout.tooltip = {}
t.layout.tooltip.fullscreen = 'Fullscreen'
t.layout.tooltip.fullscreenActive = 'Exit Fullscreen'
t.layout.tooltip.theme = 'Theme'
t.layout.tooltip.search = 'Search'
t.layout.search = {}
t.layout.search.placeholder = 'Search Pages'
t.layout.search.tip1 = 'Press'
t.layout.search.tip2 = 'open search panel, and press'
t.layout.search.tip3 = 'close it.'
t.layout.backToIndex = 'To system admin console'
t.layout.about = 'About'

t.layoutTab = {}
t.layoutTab.index = 'Home'
t.layoutTab.noName = 'No Name'

t.contentTabs = {}
t.contentTabs.closeCurrent = 'Close Current Tab'
t.contentTabs.closeOther = 'Close Other Tabs'
t.contentTabs.closeAll = 'Close All Tabs'

/* pages */
t.notFound = {}
t.notFound.desc = 'Sorry! <em>missing</em> on the page you visited...'
t.notFound.back = 'Previous Page'
t.notFound.home = 'Home'

t.login = {}
t.login.language = 'Language'
t.login.motto = 'Time is the most precious treasure of all wealth'
t.login.form = {}
t.login.form.placeholderUsername = 'username'
t.login.form.placeholderPassword = 'password'
t.login.form.placeholderCaptcha = 'Captcha'
t.login.form.textSubmitButton = 'Sign In'
t.login.form.textForget = 'Forget'
t.login.form.textSignUp = 'Sign up'
t.login.footer = {}
t.login.footer.buttonHelp = 'Help'
t.login.footer.buttonPrivacy = 'Privacy'
t.login.footer.buttonClause = 'Clause'
t.login.copyright = {}
t.login.copyright.p1 = 'Copyright'
t.login.copyright.p2 = '2018 D2 Projects Open Source Organizational'
t.login.copyright.p3 = '@FairyEver'
t.login.ruleMessage = {}
t.login.ruleMessage.username = 'User name must be entered'
t.login.ruleMessage.password = 'Password must be entered'
t.login.ruleMessage.captcha = 'Captcha must be entered'

// 登录页许可证相关
t.login.appVersion = 'Version'
t.login.description = 'Description'
t.login.expirationDate = 'Expiration date'
t.login.left = 'Left'
t.login.renewLicense = 'Renew license'
t.login.getLicense = 'Get lisence'
t.login.activateLicense = 'Activate license'
t.login.serviceMachineCode = 'Service machine code'
t.login.enterMachineCode = 'Enter machine code'
t.login.copy = 'Copy'
t.login.installationLicenseSerialNumber = 'Installation license serial number'
t.login.enterSerialNumber = 'Enter serial number'
t.login.activate = 'Activate'

t.home = {}
t.home.sysInfo = {}
t.home.sysInfo.name = 'System Name'
t.home.sysInfo.nameVal = 'renren-security [Enterprise]'
t.home.sysInfo.version = 'Version Information'
t.home.sysInfo.versionVal = process.env.VUE_APP_VERSION
t.home.sysInfo.osName = 'Operating System'
t.home.sysInfo.osVersion = 'System Version'
t.home.sysInfo.osArch = 'System Architecture'
t.home.sysInfo.processors = 'CPU Core Count'
t.home.sysInfo.totalPhysical = 'system Memory'
t.home.sysInfo.freePhysical = 'Remaining Memory'
t.home.sysInfo.memoryRate = 'Memory Usage'
t.home.sysInfo.userLanguage = 'System Language'
t.home.sysInfo.jvmName = 'JVM Information'
t.home.sysInfo.javaVersion = 'JVM Version'
t.home.sysInfo.javaHome = 'JAVA_HOME'
t.home.sysInfo.userDir = 'Working Directory'
t.home.sysInfo.javaTotalMemory = 'JVM Occupies Memory'
t.home.sysInfo.javaFreeMemory = 'JVM Free Memory'
t.home.sysInfo.javaMaxMemory = 'JVM Max Memory'
t.home.sysInfo.userName = 'Current User'
t.home.sysInfo.systemCpuLoad = 'CPU Load'
t.home.sysInfo.userTimezone = 'System Time Zone'

/* modules */
t.model = {}
t.model.name = 'Name'
t.model.key = 'Information'
t.model.version = 'Version'
t.model.createTime = 'Create Time'
t.model.lastUpdateTime = 'Update Time'
t.model.design = 'Online Design'
t.model.deploy = 'Deployment'
t.model.description = 'Description'

t.process = {}
t.process.name = 'name'
t.process.key = 'Identification'
t.process.deployFile = 'Deploy process file'
t.process.id = 'Process ID'
t.process.deploymentId = 'Deployment ID'
t.process.version = 'Version'
t.process.resourceName = 'XML'
t.process.diagramResourceName = 'Image'
t.process.deploymentTime = 'Deployment Time'
t.process.active = 'Activate'
t.process.suspend = 'Hang'
t.process.convertToModel = 'Convert to model'

t.running = {}
t.running.id = 'Instance ID'
t.running.definitionKey = 'Define Key'
t.running.processDefinitionId = 'Define ID'
t.running.processDefinitionName = 'Define the name'
t.running.activityId = 'Current Link'
t.running.suspended = 'Whether Hang'
t.running.suspended0 = 'No'
t.running.suspended1 = 'Yes'

t.news = {}
t.news.title = 'Title'
t.news.pubDate = 'Publish Time'
t.news.createDate = 'Create Time'
t.news.content = 'Content'

t.schedule = {}
t.schedule.beanName = 'Bean Name'
t.schedule.beanNameTips = 'Spring bean name, eg: testTask'
t.schedule.pauseBatch = 'Pause'
t.schedule.resumeBatch = 'Recovery'
t.schedule.runBatch = 'Execution'
t.schedule.log = 'Log List'
t.schedule.params = 'Parameters'
t.schedule.cronExpression = 'Cron Expression'
t.schedule.cronExpressionTips = 'Example: 0 0 12 * * ?'
t.schedule.remark = 'Remarks'
t.schedule.status = 'Status'
t.schedule.status0 = 'Pause'
t.schedule.status1 = 'Normal'
t.schedule.statusLog0 = 'Failed'
t.schedule.statusLog1 = 'Success'
t.schedule.pause = 'Pause'
t.schedule.resume = 'Restore'
t.schedule.run = 'Execute'
t.schedule.jobId = 'Task ID'
t.schedule.times = 'Time-consuming (unit: milliseconds)'
t.schedule.createDate = 'Execution Time'

t.mail = {}
t.mail.name = 'Name'
t.mail.config = 'Mail Configuration'
t.mail.subject = 'Theme'
t.mail.createDate = 'Create Time'
t.mail.send = 'Send Mail'
t.mail.content = 'Content'
t.mail.smtp = 'SMTP'
t.mail.port = 'Port Number'
t.mail.username = 'Email Account'
t.mail.password = 'Mailbox Password'
t.mail.mailTo = 'Recipient'
t.mail.mailCc = 'Cc'
t.mail.params = 'Template Parameter'
t.mail.paramsTips = 'Example: {"code": "123456"}'
t.mail.templateId = 'Template ID'
t.mail.status = 'Status'
t.mail.status0 = 'Send Failed'
t.mail.status1 = 'Successfully Sent'
t.mail.mailFrom = 'Sender'
t.mail.createDate = 'Send Time'

t.sms = {}
t.sms.mobile = 'Mobile Number'
t.sms.status = 'Status'
t.sms.status0 = 'Send Failed'
t.sms.status1 = 'Successfully Sent'
t.sms.config = 'SMS Configuration'
t.sms.send = 'Send SMS'
t.sms.platform = 'platform Type'
t.sms.platform1 = 'Alibaba Cloud'
t.sms.platform2 = 'Tencent Cloud'
t.sms.params = 'Parameters'
t.sms.paramsTips = 'eg: {"code": "123456"}'
t.sms.params1 = 'Parameter 1'
t.sms.params2 = 'Parameter 2'
t.sms.params3 = 'Parameter 3'
t.sms.params4 = 'Parameter 4'
t.sms.createDate = 'Send Time'
t.sms.aliyunAccessKeyId = 'Key'
t.sms.aliyunAccessKeyIdTips = 'Alibaba Cloud AccessKeyId'
t.sms.aliyunAccessKeySecret = 'Secret'
t.sms.aliyunAccessKeySecretTips = 'Alibaba Cloud AccessKeySecret'
t.sms.aliyunSignName = 'SMS Signature'
t.sms.aliyunTemplateCode = 'SMS Template'
t.sms.aliyunTemplateCodeTips = 'SMS Template CODE'
t.sms.qcloudAppId = 'AppId'
t.sms.qcloudAppIdTips = 'Tencent Cloud AppId'
t.sms.qcloudAppKey = 'AppKey'
t.sms.qcloudAppKeyTips = 'Tencent Cloud AppKey'
t.sms.qcloudSignName = 'SMS Signature'
t.sms.qcloudTemplateId = 'SMS Template'
t.sms.qcloudTemplateIdTips = 'SMS template ID'

t.oss = {}
t.oss.config = 'Cloud Storage Configuration'
t.oss.upload = 'Upload File'
t.oss.url = 'URL Address'
t.oss.createDate = 'Create Time'
t.oss.type = 'Type'
t.oss.type1 = 'Seven Cows'
t.oss.type2 = 'Alibaba Cloud'
t.oss.type3 = 'Tencent Cloud'
t.oss.type4 = 'FastDFS'
t.oss.type5 = 'Local Upload'
t.oss.qiniuDomain = 'Domain Name'
t.oss.qiniuDomainTips = 'Seven cattle bound domain name'
t.oss.qiniuPrefix = 'Path Prefix'
t.oss.qiniuPrefixTips = 'Do not set default to empty'
t.oss.qiniuAccessKey = 'AccessKey'
t.oss.qiniuAccessKeyTips = 'Seven cattle AccessKey'
t.oss.qiniuSecretKey = 'SecretKey'
t.oss.qiniuSecretKeyTips = 'Seven Cow SecretKey'
t.oss.qiniuBucketName = 'Space Name'
t.oss.qiniuBucketNameTips = 'Seven cattle storage space name'
t.oss.aliyunDomain = 'Domain Name'
t.oss.aliyunDomainTips = 'Alibaba Cloud bound domain name, such as: http://cdn.renren.io'
t.oss.aliyunPrefix = 'Path Prefix'
t.oss.aliyunPrefixTips = 'Do not set default to empty'
t.oss.aliyunEndPoint = 'EndPoint'
t.oss.aliyunEndPointTips = 'Ali Cloud EndPoint'
t.oss.aliyunAccessKeyId = 'AccessKeyId'
t.oss.aliyunAccessKeyIdTips = 'Alibaba Cloud AccessKeyId'
t.oss.aliyunAccessKeySecret = 'AccessKeySecret'
t.oss.aliyunAccessKeySecretTips = 'Alibaba Cloud AccessKeySecret'
t.oss.aliyunBucketName = 'BucketName'
t.oss.aliyunBucketNameTips = 'Alibaba Cloud BucketName'
t.oss.qcloudDomain = 'Domain Name'
t.oss.qcloudDomainTips = 'Tencent cloud bound domain name'
t.oss.qcloudPrefix = 'Path Prefix'
t.oss.qcloudPrefixTips = 'Do not set default to empty'
t.oss.qcloudAppId = 'AppId'
t.oss.qcloudAppIdTips = 'Tencent Cloud AppId'
t.oss.qcloudSecretId = 'SecretId'
t.oss.qcloudSecretIdTips = 'Tencent Cloud SecretD'
t.oss.qcloudSecretKey = 'SecretKey'
t.oss.qcloudSecretKeyTips = 'Tencent Cloud SecretKey'
t.oss.qcloudBucketName = 'BucketName'
t.oss.qcloudBucketNameTips = 'Tencent Cloud BucketName'
t.oss.qcloudRegion = 'Affiliate'
t.oss.qcloudRegionTips = 'Please Select'
t.oss.qcloudRegionBeijing1 = 'Beijing District 1 (North China)'
t.oss.qcloudRegionBeijing = 'Beijing'
t.oss.qcloudRegionShanghai = 'Shanghai (East China)'
t.oss.qcloudRegionGuangzhou = 'Guangzhou (South China)'
t.oss.qcloudRegionChengdu = 'Chengdu (Southwest)'
t.oss.qcloudRegionChongqing = 'Chongqing'
t.oss.qcloudRegionSingapore = 'Singapore'
t.oss.qcloudRegionHongkong = 'HongKong'
t.oss.qcloudRegionToronto = 'Toronto'
t.oss.qcloudRegionFrankfurt = 'Frankfurt'
t.oss.localDomain = 'Domain Name'
t.oss.localDomainTips = 'Binded domain name, eg http://cdn.renren.io'
t.oss.fastdfsDomain = 'Domain Name'
t.oss.fastdfsDomainTips = 'Binded domain name, eg http://cdn.renren.io'
t.oss.localPrefix = 'Path Prefix'
t.oss.localPrefixTips = 'Do not set default to empty'
t.oss.localPath = 'Storage Directory'
t.oss.localPathTips = 'eg: D:/upload'

t.dept = {}
t.dept.name = 'Name'
t.dept.parentName = 'Superior'
t.dept.id = 'Department Id'
t.dept.sort = 'Sort'
t.dept.parentNameDefault = 'Top Department'

t.dict = {}
t.dict.dictName = 'Name'
t.dict.dictType = 'Type'
t.dict.dictValue = 'Value'
t.dict.sort = 'Sort'
t.dict.remark = 'Remarks'
t.dict.createDate = 'Create Date'

t.logError = {}
t.logError.requestUri = 'Request URI'
t.logError.requestMethod = 'Request Method'
t.logError.requestParams = 'Request Parameters'
t.logError.ip = 'IP'
t.logError.userAgent = 'User Agent'
t.logError.createDate = 'Create Date'
t.logError.errorInfo = 'Exception'

t.logLogin = {}
t.logLogin.creatorName = 'Username'
t.logLogin.status = 'Status'
t.logLogin.status0 = 'Failed'
t.logLogin.status1 = 'Success'
t.logLogin.status2 = 'Locked'
t.logLogin.operation = 'User Action'
t.logLogin.operation0 = 'Login'
t.logLogin.operation1 = 'Exit'
t.logLogin.ip = 'IP'
t.logLogin.userAgent = 'User-Agent'
t.logLogin.createDate = 'Create Date'

t.logOperation = {}
t.logOperation.status = 'Status'
t.logOperation.status0 = 'Failed'
t.logOperation.status1 = 'Success'
t.logOperation.creatorName = 'Username'
t.logOperation.operation = 'User Action'
t.logOperation.requestUri = 'Request URI'
t.logOperation.requestMethod = 'Request Mode'
t.logOperation.requestParams = 'Request Parameters'
t.logOperation.requestTime = 'Request Duration'
t.logOperation.ip = 'IP'
t.logOperation.userAgent = 'User-Agent'
t.logOperation.createDate = 'Create Date'

t.menu = {}
t.menu.name = 'Name'
t.menu.icon = 'Icon'
t.menu.type = 'Type'
t.menu.type0 = 'Menu'
t.menu.type1 = 'Button'
t.menu.sort = 'Sort'
t.menu.url = 'Route'
t.menu.permissions = 'Auth ID'
t.menu.permissionsTips = 'Multiple separated by commas, such as: sys:menu:save,sys:menu:update'
t.menu.parentName = 'Superior'
t.menu.parentNameDefault = 'Top Menu'
t.menu.resource = 'Auth Resources'
t.menu.resourceUrl = 'Resource URL'
t.menu.resourceMethod = 'Request Method'
t.menu.resourceAddItem = 'Add an Item'

t.params = {}
t.params.paramCode = 'Code'
t.params.paramValue = 'Value'
t.params.remark = 'Remarks'

t.role = {}
t.role.name = 'Name'
t.role.remark = 'Remarks'
t.role.createDate = 'Create Date'
t.role.menuList = 'Menu Scope'
t.role.deptList = 'Data Scope'

t.user = {}
t.user.username = 'Username'
t.user.deptName = 'Department'
t.user.email = 'Email'
t.user.mobile = 'Mobile'
t.user.status = 'Status'
t.user.status0 = 'Disable'
t.user.status1 = 'Enable'
t.user.createDate = 'Create Date'
t.user.password = 'Password'
t.user.comfirmPassword = 'Confirm'
t.user.realName = 'Real Name'
t.user.gender = 'Gender'
t.user.gender0 = 'Male'
t.user.gender1 = 'Female'
t.user.gender2 = 'Secure'
t.user.roleIdList = 'Role Config'
t.user.validate = {}
t.user.validate.comfirmPassword = 'Confirm password is not consistent with password input'

t.form = {}
// todo

t.project = {}
t.project.projectName = 'ProjectName'
t.project.description = 'Description'
t.project.projectLevel = 'ProjectLevel'
t.project.startDate = 'StartDate'
t.project.endDate = 'EndDate'
t.project.createTime = 'Createtime'
t.project.createUserId = 'CreateUserId'
t.project.selectProjectType = 'Select a project type'
t.project.singleDisease = 'Single disease'
t.project.multiDisease = 'Multi disease'
t.project.selectCenterType = 'Select research center type'
t.project.singleCenter = 'Single center'
t.project.multiCenter = 'Multi center'
t.project.projectStatus = 'Select project status'
t.project.status = 'Project status'
t.project.status1 = 'Enable'
t.project.status0 = 'New created'
t.project.status2 = 'Deactivated'
t.project.addProject = 'Add project'
t.project.addSubProject = 'Create subproject'
t.project.auditFail = 'Audit failed'
t.project.auditSuccess = 'Audit success'
t.project.singleDiseaseTag = 'SingleDisease'
t.project.multiDiseaseTag = 'MultiDisease'
t.project.singleCenterTag = 'SingleCenter'
t.project.multiCenterTag = 'MultiCenter'
t.project.thereAre = 'There Are'
t.project.auditTips = 'project,the new subject will active after the administrator approves it'
t.project.patientUnit = 'case'
t.project.subProjCountUnit = 'subject total'
t.project.auditSubProject = 'Audit subject'
t.project.auditFailReason = 'Audit failed reason'
t.project.auditing = 'To be Audited'
t.project.changeProject = 'Choose project'
t.project.singleDiseaseProj = 'Single disease project'
t.project.multiDiseaseProj = 'Multi disease project'
t.project.projCategory1 = 'Single center project'
t.project.projCategory2 = 'Multi center project'
t.project.type = 'Project type'
t.project.studyCenter = 'Research center'
t.project.abledDate = 'Effective time'
t.project.centerTips = 'If the associated center selects a hospital, then the project administrator can see all users under the hospital and select them as project members; if a department is selected, then they can see all users under the department'
t.project.cantBeMainAndSubCenter = 'It cannot be set as the main center and sub-center at the same time'
t.project.enableAuditTips = 'Enable the data review process, the reviewer reviews the data entered by the data entry staff'
t.project.otherConfig = 'Other configurations'
t.project.addSuccessfully = 'Created successfully'
t.project.starting = 'Starting'
t.project.startProject = 'Start the project'
t.project.tips = 'Tips'
t.project.tipsOne = '1. The project needs to be started to take effect'
t.project.tipsTwo = '2. You can manage in admin console-》'
t.project.proList = 'Project list'
t.project.viewNewProject = 'View the newly created project in the backend project list and start it'
t.project.configWeChat = 'Config WeChat'
t.project.setAdminFirst = 'Please set admin first'
t.project.selectprojectCneterType = 'Please select center type'
t.project.projCategory1Tips = 'Only one organization can be associated with the organization, and the project members are users in the organization.'
t.project.projCategory2Tips = 'Multiple institutions can be associated, with one institution serving as the primary hub.'
t.project.projectTypeTips = 'It is divided into single disease and multiple disease. A single-disease project does not have sub-projects, and a multi-disease project can create a specified number of projects.'
t.project.editCategory1Project = 'Modify a single-hub project'
t.project.editCategory2Project = 'Modify a polycenter project'
t.project.addCategory1Project = 'Create a single-hub project'
t.project.addCategory2Project = 'Create a polycenter project'
t.project.projectCreating = 'The project is being created'
t.project.exportAuditTips = 'When enabled, project data can only be exported after it is reviewed by the system administrator'
t.project.intoGroupTips = 'When enabled, the enrollee is responsible for case enrollment; Otherwise, the entry clerk is responsible for case enrollment'
t.project.dateDoubtTips = 'Once enabled, the same case data needs to be reviewed separately by two auditors. At the same time, the data that passes the review is locked, otherwise it will be reviewed by the project administrator himself or by a third auditor'
t.project.selectCenterFirst = 'Please select a research center first'
t.project.stepBackPre = 'Go back to the previous step'
t.project.backProjectList = 'Return to the list of items'

t.subjectRole = {}
t.subjectRole.name = 'Role Name'
t.subjectRole.nameRequired = 'The role name is required'
t.subjectRole.remark = 'Role description'
t.subjectRole.createTime = 'Creation time'
t.subjectRole.isProjectAdmin = 'Whether project Manager or not'
t.subjectRole.dataAuth = 'Data permission value'
t.subjectRole.isDesensitization = 'Whether desensitization is needed'

t.member = {}
t.member.deptList = 'List of institutions'
t.member.username = 'username'
t.member.projectRole = 'Project role'

// 患者列表
t.patient = {}
t.patient.empid = 'Patient unique ID'
t.patient.singleImport = 'Single enrollment'
t.patient.multImport = 'Batch import'
t.patient.hisCase = 'Is it a case of this hospital'
t.patient.uploadFileformat = 'Can only upload xls/xlsx files'
t.patient.downLoadTemplate = 'Download import template'
t.patient.uploadDrag = 'Drag the file here, or'
t.patient.fileExtensionError = 'File format does not meet the requirements'
t.patient.fileUploadNetWorkError = 'Please check if the network connection is normal'
t.patient.regno = 'Registration number'
t.patient.sourceDeptName = 'Affiliated center'
t.patient.name = 'Name'
t.patient.initials = 'Name initials'
t.patient.gender = 'Gender'
t.patient.recordId = 'Medical record number'
t.patient.birthday = 'Birthday'
t.patient.mobile = 'Mobile'
t.patient.IdCard = 'IdCard'
t.patient.project = 'Project'
t.patient.joinDate = 'Screening date'
t.patient.intoTime = 'Screening date'
t.patient.pointDate = 'Reference time'
t.patient.joinDoc = 'Enrolled doctor'
t.patient.lastTime = 'Last update'
t.patient.status = 'Total follow-up status'
t.patient.nextTime = 'Next follow-up date'
t.patient.tips = 'Remarks'
t.patient.mark = 'Annotation'
t.patient.business = 'Subproject'
t.patient.fillinUser = 'Filled by'
t.patient.fillinRate = 'Fill rate'
t.patient.import = 'Case import'
t.patient.export = 'Template export'
t.patient.assign = 'Allocation'
t.patient.source = 'Source'
t.patient.folwManage = 'Follow-up process management'
t.patient.setFollowUpPlan = 'Configure follow-up plan'
t.patient.followUpPlan = 'Follow-up process'
t.patient.followUpRange = 'Follow-up stage'
t.patient.detail = 'View'
t.patient.search = 'Search'

t.subProject = {}
t.subProject.name = 'Subproject name'
t.subProject.templateName = 'Template name'
t.subProject.describe = 'Description'

// 个人中心
t.userCenter = {}
t.userCenter.filter = 'Filter'
t.userCenter.add = 'Add'

// 关于弹窗(展示公司信息)
t.about = {}
t.about.aboutTitle = 'Product service support'
t.about.productName = 'Product name'
t.about.productVersion = 'Product version'
t.about.softwareLicenseType = 'Software license authorization type'
t.about.softwareLicensingExpiration = 'Software licensing expiration date'
t.about.softwareLicensingAuthority = 'Software licensing authority'
t.about.softwareLicensePurpose = 'Software license authorization purpose'
t.about.softwareModuleLicensing = 'Software module licensing'
t.about.companyAllRightsReserved = 'Copyright reserved by Donghua Medical Technology Co., Ltd.'
t.about.machineCode = 'Machine code'

// 审核相关
t.auditRecord = {}
t.auditRecord.applyUserName = 'Apply user'
t.auditRecord.applyTime = 'Apply time'
t.auditRecord.applyReason = 'Apply reason'
t.auditRecord.status = 'Apply status'
t.auditRecord.approveMsg = 'Audit message'
t.auditRecord.approveUserName = 'Auditer'
t.auditRecord.approveTime = 'Audit time'
t.auditRecord.status0 = 'To be audited'
t.auditRecord.status1 = 'approved'
t.auditRecord.status2 = 'rejected'
t.auditRecord.status2Reason = 'faild reason'
t.auditRecord.handle = 'Audit'
t.auditRecord.handleLabel = 'Approve or reject'

// 项目概览
t.dashboard = {}
t.dashboard.projInfo = 'Project information'
t.dashboard.projAdmin = 'Admin'
t.dashboard.projStartTime = 'Project start time'
t.dashboard.projEndTime = 'Project end time'
t.dashboard.projAffCenter = 'Affiliated center'
t.dashboard.projMainCenter = 'Main center'
t.dashboard.projSubCenter = 'Sub-center'
t.dashboard.none = 'None'
t.dashboard.description = 'Project description'
t.dashboard.projStatus = 'Project status'
t.dashboard.normal = 'Normal'
t.dashboard.disabled = 'Disabled'
t.dashboard.statusDesc = 'Status description'
t.dashboard.projEnabled = 'Project is enabled'
t.dashboard.projDisabled = 'Project is disabled'
t.dashboard.projCases = 'Number of cases'

t.dashboard.projRole = 'Project role'
t.dashboard.members = 'Project members'
t.dashboard.noMembers = 'No members'
t.dashboard.viewAll = 'View all'
t.dashboard.unprocessed = 'Unhandled messages'
t.dashboard.viewMore = 'View more'
t.dashboard.noUnprocessed = 'No unhandled messages'
t.dashboard.followUpCalendar = 'Follow-up calendar'
t.dashboard.genderDistribution = 'Gender distribution'
t.dashboard.ageDistribution = 'Age distribution'

export default t
