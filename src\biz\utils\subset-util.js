import * as storageUtil from "@base/utils/base/storage-util";

export function getSubsetId() {
  return storageUtil.getLocal('subset-id');
}

export function setSubsetId(subsetId) {
  return storageUtil.setLocal('subset-id', subsetId);
}

export function getSubsetInfo() {
  return storageUtil.getLocal('subset-info');
}

export function setSubsetInfo(info) {
  return storageUtil.setLocal('subset-info', info);
}