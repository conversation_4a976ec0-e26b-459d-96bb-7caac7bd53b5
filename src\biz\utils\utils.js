import Vue from 'vue'
import store from '@base/store'
import {
  parseTime
} from "./index"
import { relationMap2, filterTypeList } from '@/components/querybuilder/mapData.js'

/**
 * 将query转换为描述文本
 * @param {Object} query  需要转换的条件对象
 * @param {Boolean} isText 是否转换为存文字描述， 如果true 则转换为纯文字， 如果false则转换为html格式,默认是
 * @param {Boolean} isSubRule 是否是子条件
 */

// 是否英文环境
const isI18n_EN = (Vue.ls.get('isI18n') || store.state.sys.isI18n) && Vue.ls.get("language") == 'en'
export function queryToTextDesc(query, isText, isSubRule) {
  if (!query) {
    return ""
  }

  if (typeof query === "string" && query.constructor == String) {
    // try {
    //   query = query.length > 0 ? JSON.parse(query) : {}
    // } catch {
    query = {}
    // }
  }

  if (!(query instanceof Object) || JSON.stringify(query) === "{}") {
    return ""
  }

  isText = isText == undefined || isText == null ? true : isText
  let descText = ""
  const curLogical = query.logical
  const curChild = query.children
  let fieldName = ""
  let relativeDesc = ""
  let value = ""
  // 根据中英文环境设置关系词展示中文还是英文
  let curLogicalText
  if (isI18n_EN) {
    curLogicalText = curLogical === "AND" ? "and" : "or"
  } else {
    curLogicalText = curLogical === "AND" ? "并且" : "或者"
  }
  if (isSubRule) {
    curLogicalText = ''
  }
  if (curChild && curChild instanceof Array) {
    curChild.forEach((child) => {
      const curType = child.type
      const curQuery = child.query

      if (curType === "query-builder-group") {
        // 子条件组
        const curSubDescText = queryToTextDesc(curQuery, isText)
        if (descText.length === 0) {
          if (isText) {
            descText = `【${curSubDescText} 】`
          } else {
            descText = `<span>${curSubDescText}</span>`
          }
        } else {
          if (isText) {
            descText += `${curLogicalText} [ ${curSubDescText}] `
          } else {
            // 类名的判断也要兼容英文环境
            descText += `<span class="q-logical${curLogicalText == '或者' || curLogicalText == 'or' ? ' or' : ''}">${curLogicalText}</span><span class="q-child">( ${curSubDescText} )</span>`
          }
        }
      } else {
        // 将query转中文
        fieldName = curQuery.fieldItemInfo.name

        // const operators = curQuery.operators
        const relative = curQuery.relative
        // let valueType = curQuery.inputType
        if (curQuery.inputType == "date") {
          var date = new Date(curQuery.value)
          value = formatDate(date)
        } else if (curQuery.inputType == "datetime") {
          var datetime = new Date(curQuery.value)
          value = formatDateTime(datetime)
        } else {
          value = curQuery.value
        }

        // if (operators && operators instanceof Array) {
        //   operators.forEach((op) => {
        //     if (op.value === relative) {
        //       relativeDesc = op.label
        //     }
        //   })
        // }
        for (const k in relationMap2) {
          if (Object.hasOwnProperty.call(relationMap2, k)) {
            const op = relationMap2[k]
            if (op.code === relative) {
              relativeDesc = op.name
              break
            }
          }
        }
        for (let i_f = 0; i_f < filterTypeList.length; i_f++) {
          const filterTypeItem = filterTypeList[i_f]
          if (filterTypeItem.code === relative) {
            relativeDesc = filterTypeItem.name
            break
          }
        }
        if (!fieldName || !relativeDesc) {
          return true
        }
        if (descText.length === 0) {
          if (isText) {
            descText = `${isSubRule ? '' : '('}${fieldName} ${relativeDesc} ${value}`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
          } else {
            descText = `${isSubRule ? '' : '('}<span class="q-field">${fieldName}</span>
                <span class="q-relative">${relativeDesc}</span><span class="q-value">${value}</span>`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
          }
        } else {
          // ----------------------------------------------
          if (isText) {
            // descText += ` ${curLogicalText} (${fieldName} ${relativeDesc} ${value})`
            descText += isSubRule ? '' : ` ${curLogicalText} (${fieldName} ${relativeDesc} ${value}`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
          } else {
            descText += `<span class="q-logical${curLogicalText == '或者' || curLogicalText == 'or' ? ' or' : ''}">${curLogicalText}</span>${isSubRule ? '' : '('}<span class="q-field">${fieldName}</span>
                <span class="q-relative">${relativeDesc}</span><span class="q-value">${value}</span>`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
            // descText += `<span class="q-logical">${curLogicalText}</span><span class="q-field">${fieldName}</span><span class="q-relative">${relativeDesc}</span><span class="q-value">${value}</span>`
          }
        }
        // ----------------------------------------------
      }
    })
  }
  return descText
}

// 获取字段的全名
export function getFullInputName_edc(fieldItemInfo){
  if(fieldItemInfo.cateName && fieldItemInfo.fieldName){
    let res = fieldItemInfo.cateName + '-' + fieldItemInfo.fieldName
    if(fieldItemInfo.prodList && fieldItemInfo.prodList.length>0){
      const prodName = fieldItemInfo.prodList.map(it=>it.prodName).join(',')
      if(prodName){
        res += '-' + prodName
      }
    }
    return res
  }else{
    return ''
  }
}

/**
 * EDC-将query转换为描述文本
 * @param {Object} query  需要转换的条件对象
 * @param {Boolean} isText 是否转换为存文字描述， 如果true 则转换为纯文字， 如果false则转换为html格式,默认是
 * @param {Boolean} isSubRule 是否是子条件
 */
export function queryToTextDesc_edc(query, isText, isSubRule) {
  if (!query) {
    return ''
  }

  if (typeof query === 'string' && query.constructor === String) {
    try {
      query = query.length > 0 ? JSON.parse(query) : {}
    } catch {
      query = {}
    }
  }

  if (!(query instanceof Object) || JSON.stringify(query) === '{}') {
    return ''
  }

  isText = isText === undefined || isText == null ? true : isText
  let descText = ''
  const curLogical = query.logical
  const curChild = query.children
  let fieldName = ''
  let relativeDesc = ''
  let value = ''
  // 根据中英文环境设置关系词展示中文还是英文
  let curLogicalText
  if (isI18n_EN) {
    curLogicalText = curLogical === "AND" ? "and" : "or"
  } else {
    curLogicalText = curLogical === "AND" ? "并且" : "或者"
  }
  if (isSubRule) {
    curLogicalText = ''
  }
  if (curChild && curChild instanceof Array) {
    curChild.forEach((child) => {
      const curType = child.type
      const curQuery = child.query

      if (curType === 'query-builder-group') {
        // 子条件组
        const curSubDescText = queryToTextDesc_edc(curQuery, isText)
        if (descText.length === 0) {
          if (isText) {
            descText = `[ ${curSubDescText} ]`
          } else {
            descText = `<span>${curSubDescText}</span>`
          }
        } else {
          if (isText) {
            descText += `${curLogicalText} [ ${curSubDescText}] `
          } else {
            descText += `<span class="q-logical${curLogicalText === '或者' ? ' or' : ''}">${curLogicalText}</span><span class="q-child">( ${curSubDescText} )</span>`
          }
        }
      } else {
        // 将query转中文
        fieldName = curQuery.fieldItemInfo.searchItemName

        const relative = curQuery.relative
        // let valueType = curQuery.inputType
        if (curQuery.inputType === 'date') {
          var date = new Date(curQuery.value)
          value = formatDate(date)
        } else if (curQuery.inputType === 'datetime') {
          var datetime = new Date(curQuery.value)
          value = formatDateTime(datetime)
        } else {
          value = curQuery.value
        }

        // if (operators && operators instanceof Array) {
        //   operators.forEach((op) => {
        //     if (op.value === relative) {
        //       relativeDesc = op.label
        //     }
        //   })
        // }
        for (const k in relationMap2) {
          if (Object.hasOwnProperty.call(relationMap2, k)) {
            const op = relationMap2[k]
            if (op.code === relative) {
              relativeDesc = op.name
              break
            }
          }
        }
        for (let i_f = 0; i_f < filterTypeList.length; i_f++) {
          const filterTypeItem = filterTypeList[i_f]
          if (filterTypeItem.code === relative) {
            relativeDesc = filterTypeItem.name
            break
          }
        }
        if (!fieldName || !relativeDesc) {
          return true
        }
        if (descText.length === 0) {
          if (isText) {
            descText = `${isSubRule ? '' : '('}${fieldName} ${relativeDesc} ${value}`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc_edc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
          } else {
            descText = `${isSubRule ? '' : '('}<span class="q-field">${fieldName}</span>
                <span class="q-relative">${relativeDesc}</span><span class="q-value">${value}</span>`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc_edc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
          }
        } else {
          // ----------------------------------------------
          if (isText) {
            // descText += ` ${curLogicalText} (${fieldName} ${relativeDesc} ${value})`
            descText += isSubRule ? '' : ` ${curLogicalText} (${fieldName} ${relativeDesc} ${value}`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc_edc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
          } else {
            descText += `<span class="q-logical${curLogicalText === '或者' ? ' or' : ''}">${curLogicalText}</span>${isSubRule ? '' : '('}<span class="q-field">${fieldName}</span>
                <span class="q-relative">${relativeDesc}</span><span class="q-value">${value}</span>`
            if (child.subRules && child.subRules.length > 0) {
              child.subRules.forEach(it => {
                descText += `、${queryToTextDesc_edc({ children: [it] }, isText, true)}`
              })
            }
            descText += isSubRule ? '' : ')'
            // descText += `<span class="q-logical">${curLogicalText}</span><span class="q-field">${fieldName}</span><span class="q-relative">${relativeDesc}</span><span class="q-value">${value}</span>`
          }
        }
        // ----------------------------------------------
      }
    })
  }
  return descText
}

/**
 * 全文检索转化成中文描述
 * @param {Object} param  需要转换的条件对象
 * @param {string} keyword 搜索关键字
 */
export function getQueryDesc(param, keyword) {
  let ret = ""
  let KEYWORD = isI18n_EN ? 'keyword' : '关键字'
  let INCLUDE = isI18n_EN ? 'include' : 'include'

  keyword && (ret += `<span class="q-value">${KEYWORD }</span> ${INCLUDE} <span class="q-value">${keyword}</span>`)

  if (!param) {
    return ret
  }
  const desc = typeof param === "string" ? JSON.parse(param) : param

  const formatLabel = [{
    title: "就诊类型",
    enTitle: 'Visit Type',
    name: "admLabel"
  }, {
    title: "性别类型",
    enTitle: 'Gender Type',
    name: "genderLabel"
  }, {
    title: "就诊时间",
    enTitle: 'Visit Date',
    name: "admDate"
  }, {
    title: "就诊年龄",
    enTitle: 'Visit Age',
    name: "ageRange",
    child: [{
      title: "开始年龄",
      enTitle: 'Begin Age',
      name: "beginAge"
    }, {
      title: "结束年龄",
      enTitle: 'End Age',
      name: "endAge"
    }]
  }, {
    title: "字段范围",
    enTitle: 'Field Range',
    name: "fieldLabel"
  }]

  formatLabel.forEach(item => {
    ret += getLabel(desc, isI18n_EN ? item.enTitle : item.title, item.name, item.child)
  })
  return ret
}

/**
 * 从条件对象中解析出所有检索词
 * @param {Object} query 需要解析的条件对象
 */
export function getAllSearchWords(query) {
  // 关系词为以下其中之一的条件的检索词
  const relativeList = ['include', 'include2', 'positive_include', 'negative_include']
  if (!query || !query.children) {
    return []
  }
  const searchWords = []
  for (let i = 0; i < query.children.length; i++) {
    const item = query.children[i]
    if (item.type === 'query-builder-rule') {
      addSearchWords(item)
    } else if (item.type === 'query-builder-group') {
      for (let j = 0; j < item.query.children.length; j++) {
        const j_item = item.query.children[j]
        if (j_item.type === 'query-builder-rule') {
          addSearchWords(j_item)
        }
      }
    }
  }
  function addSearchWords(rule) {
    if (rule && rule.query && rule.query.value) {
      if (relativeList.indexOf(rule.query.relative) >= 0) {
        searchWords.push(rule.query)
      }
    }
    if (rule && rule.subRules) {
      for (let i = 0; i < rule.subRules.length; i++) {
        const item = rule.subRules[i]
        if (item.type === "query-builder-rule") {
          addSearchWords(item)
        }
      }
    }
  }
  // searchWords 去重
  const res = []
  const tmp = {}
  searchWords.forEach(item => {
    if (!tmp[item.value]) {
      res.push(item)
      tmp[item.value] = item
    }
  })
  return res
}

function getLabel(param, title, name, child) {
  const ret = ""
    let label = ""
    let sub = ""
    // 写死的汉字翻译
    let EQUEAL = isI18n_EN ? 'equal' : '等于'
    let TO = isI18n_EN ? 'to' : '至'
  if (child) {
    child.forEach(item => {
      label = param[name] && param[name][item.name] || ""
      sub += label
        ? ` ${ret}<span class="q-value">${item.title}</span> ${EQUEAL} <span class="q-value">${label}</span> ` : ""
    })
    return sub
  } else {
    if (name === "admDate") {
      label = param["admDate"] || param["admDateRange"] && param["admDateRange"].toString().split("to").join(` ${TO} `)
    } else {
      label = param[name] || ""
    }

    return label ? ` ${ret}<span class="q-value">${title}</span> ${EQUEAL} <span class="q-value">${label}</span> ` : ""
  }
}

function formatDate(d) {
  if (isI18n_EN) {
    return parseTime(d, "{y}Year{m}Month{d}Day")
  } else {
    return parseTime(d, "{y}年{m}月{d}日")
  }
}

function formatDateTime(d) {
  if (isI18n_EN) {
    return parseTime(d, "{y}Year{m}Month{d}Day {h}:{i}:{s}")
  } else {
    return parseTime(d, "{y}年{m}月{d}日 {h}:{i}:{s}")
  }
}

function buildParam(params) {
  let url = ""
  for (var k in params) {
    const value = params[k] !== undefined ? params[k] : ""
    url += `&${k}=${encodeURIComponent(value)}`
  }
  return url ? url.substring(1) : ""
}

export function buildUrl(url, params) {
  return (url += (url.indexOf("?") < 0 ? "?" : "") + buildParam(params))
}

/**
 *
 * @param {Number} value 要转换的目标数字
 * @param {Boolean} toString 是否转为字符串
 * @returns
 */
export function numberFormat(value, toString = false) {
  if (isNaN(value) || value === undefined) {
    return toString ? "0" : {
      value: 0,
      unit: ""
    }
  }
  const param = {
      value: 0,
      unit: ""
    }
    const k = 10000
    let size
    if (isI18n_EN) {
      size = ["", "Ten Thousand", "Hundred Million"]
    } else {
      size = ["", "万", "亿"]
    }
    let i
  if (value < k) {
    param.value = value
    param.unit = ""
  } else {
    i = Math.floor(Math.log(value) / Math.log(k))

    param.value = (value / Math.pow(k, i)).toFixed(2)
    param.unit = size[i]
  }
  return toString ? `${param.value}${param.unit}` : param
}

/**
 * echarts坐标轴刻度数字过大时处理并添加单位
 * @param {Number} val
 * @returns
 */
export function numberFormat4Option(val) {
  if (typeof val !== 'number') {
    return val
  }
  const num = 10000
  let toFixed = 0
  let sizesValue = ''
  const output = []
  if (val < 1000) {
    // 如果小于1000则直接返回
    sizesValue = ''
    output.push(val)
    return output
  } else if (val >= 10000 && val < 99999999) {
    toFixed = 1
    if (isI18n_EN) {
      sizesValue = 'Ten Thousand'
    } else {
      sizesValue = '万'
    }
  } else if (val >= 100000000) {
    toFixed = 1
    if (isI18n_EN) {
      sizesValue = 'Hundred Million'
    } else {
      sizesValue = '亿'
    }
  }
  const i = Math.floor(Math.log(val) / Math.log(num))
  let sizes = ((val / Math.pow(num, i))).toFixed(toFixed)
  sizes = `${sizes}${sizesValue}`
  output.push(sizes)
  return output
}

/**
 * 处理纳入标准、排除标准为对应字符串
 * @param {String} JSONData  需要被解析的JSON字符串
 */
export function dealWithJSON(JSONData) {
  return queryToTextDesc(JSON.parse(JSONData), false)
  // 弃用
  // var val = JSON.parse(JSONData)
  // var totalWord = "";
  // if(val==null) return null
  // for (let j in val.children) {
  //   if (val.children[j].type == "query-builder-rule") {
  //     var word = val.children[j].query.fieldItemInfo.name
  //     for (let k in val.children[j].query.operators) {
  //       if (val.children[j].query.operators[k].value == val.children[j].query.relative) {
  //         word = word + ` <b>` + val.children[j].query.operators[k].label + `</b> `
  //       }
  //     }
  //     word += val.children[j].query.value
  //     if (j < val.children.length - 1) {
  //       var logicalCN = val.logical == 'AND' ? '并且' : '或者'
  //       totalWord = totalWord + word + ` <span style="background-color:rgb(64,158,255);color:#fff";font-weight:900;padding:2px>` + logicalCN + `</span> `
  //     } else {
  //       totalWord += word
  //     }

  //   } else if (val.children[j].type == "query-builder-sub-group") {
  //     totalWord += "("
  //     var mother = val.children[j].query
  //     for (let p in mother.children) {
  //       var word = mother.children[p].query.fieldItemInfo.name
  //       for (let k in mother.children[p].query.operators) {
  //         if (mother.children[p].query.operators[k].value == mother.children[p].query.relative) {
  //           word = word + ` <b>` + mother.children[p].query.operators[k].label + `</b> `
  //         }
  //       }
  //       word += mother.children[p].query.value
  //       if (p < mother.children.length - 1) {
  //         var logicalCN = mother.logical == 'AND' ? '并且' : '或者'
  //         totalWord = totalWord + word + ` <span style="background-color:rgb(64,158,255);color:#fff";font-weight:900;padding:2px>` + logicalCN + `</span> `
  //       } else {
  //         totalWord += word
  //       }
  //     }
  //     totalWord += ")"
  //   }

  // }
  // return totalWord
}

/**
 * 处理items中的JSON数据
 * @param {String} JSONData  需要被解析的JSON字符串
 * @param {Object} items  返回值中的items为处理后的items
 * @param {String} itemsForPageDone  用于页面渲染的字符串
 */
export function dealWithJSONItem(JSONData) {
  var inputItems = JSON.parse(JSONData)
  // 处理items中的数据(弹层)
  if (inputItems == null) return { items: null, itemsForPageDone: null }
  for (const k in inputItems) {
    var myArray = []
    var theFinalArray = []
    const currentPeriod = inputItems[k]
    currentPeriod.items.forEach(field => {
      // 新增对象
      if (myArray.indexOf(field.esTypeCode) == -1) {
        myArray.push(field.esTypeCode)
        const objForItems = {
          name: field.esTypeName || field.categoryName, // esTypeName是undefined
          list: []
        }
        objForItems.list.push(field.name)
        theFinalArray.push(objForItems)
      } else {
        // 往原有对象中添加数据
        const indexOfOldItem = myArray.indexOf(field.esTypeCode)
        theFinalArray[indexOfOldItem].list.push(field.name)
      }
    })
    currentPeriod.itemsDone = theFinalArray
  }
  // 处理items中的数据(页面渲染)
  let itemsWord = ""
  for (const k in inputItems) {
    itemsWord += "【" + inputItems[k].obPeriod.obPeriodDesc + "】"
    for (const tables of inputItems[k].itemsDone) {
      // itemsWord = itemsWord + inputItems[k].itemsDone[p].name + ": " + inputItems[k].itemsDone[p].list + "; "
      itemsWord += tables.list.join(',') + "; "
    }
  }
  return {
    items: inputItems,
    itemsForPageDone: itemsWord
  }
}

/* 获取地址栏url参数 */
export function getUrlKey(name) {
  // eslint-disable-next-line
  return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])[1].replace(/\+/g, '%20')) || null
}

export function removeURLParameter(url, parameter) {
  var urlparts = url.split('?')
  if (urlparts.length >= 2) {
    // 参数名前缀
    var prefix = encodeURIComponent(parameter) + '='
    var pars = urlparts[1].split(/[&;]/g)

    // 循环查找匹配参数
    for (var i = pars.length; i-- > 0;) {
      if (pars[i].lastIndexOf(prefix, 0) !== -1) {
        // 存在则删除
        pars.splice(i, 1)
      }
    }

    return urlparts[0] + (pars.length > 0 ? '?' + pars.join('&') : '')
  }
  return url
}

// 解决for-in遍历对象时key顺寻错乱的函数
export function objectOrder(obj) { // 排序的函数
  var newkey = Object.keys(obj).sort() // 先用Object内置类的keys方法获取要排序对象的属性名，再利用Array原型上的sort方法对获取的属性名进行排序，newkey是一个数组
  var newObj = {}// 创建一个新的对象，用于存放排好序的键值对
  for (var i = 0; i < newkey.length; i++) { // 遍历newkey数组
    newObj[newkey[i]] = obj[newkey[i]]// 向新创建的对象中按照排好的顺序依次增加键值对
  }
  return newObj// 返回排好序的新对象
}

/**
 * 省略多余字符，用...显示
 * @param {String} value
 * @param {number} len
 */
export function ellipsis(value, len) {
  if (!value) return ''
  if (value.length > len) {
    return value.slice(0, len) + '...'
  }
  return value
}

// 查看详情对话框样式
export function getFieldDiagHtml(itemStr, isText = false) {
  let items = []
  try {
    items = itemStr ? JSON.parse(itemStr) : []
  } catch (e) {
    //
  }
  let itemHtml = ''
  if (isText) {
    return items.map(item => {
      return `${item.searchItemName || item.name}`
    }).join('、')
  }
  for (var item of items) {
    const name = item.searchItemName || item.name
    itemHtml += '<span style="margin:8px 5px;display:inline-block;">' + name + '</span>'
    itemHtml += '&nbsp;'
  }
  itemHtml = itemHtml || (isI18n_EN ? 'No Data' : '无')
  return `<div>${itemHtml}</div>`
}

/**
 *
 * @param {*} EXPORT_TYPE
 * @param {*} queryStr
 * @param {*} isText
 * @returns
 */
export function getFilterHtml(EXPORT_TYPE, queryStr, isText = false) {
  let curQueryObj = {}
  try {
    curQueryObj = queryStr && Function('"use strict";return (' + queryStr + ')')() || {}
  } catch (e) {
    // console.trace(e)
  }

  var reg1 = new RegExp('q-field', 'g')
  var reg2 = new RegExp('q-value', 'g')

  const inLabel = curQueryObj.bringIntoCondJson &&
    queryToTextDesc_edc(curQueryObj.bringIntoCondJson, isText)
  const outLabel = curQueryObj.rulingOutCondJson &&
    queryToTextDesc_edc(curQueryObj.rulingOutCondJson,
      isText)
  let fieldItemInfoHtml = ''

  switch (EXPORT_TYPE) {
    // 传1或者2时适用于纳入标准和排除标准想分开展示的场景
    case 1:
      fieldItemInfoHtml = inLabel
      break
    case 2:
      fieldItemInfoHtml = outLabel
      break
    case 3:
    case 4:
      fieldItemInfoHtml += inLabel ? (isI18n_EN ? ' Inclusion Criteria: ' : ' 纳入标准: ') + inLabel : ''
      fieldItemInfoHtml += outLabel ? (isI18n_EN ? ' Exclusion Criteria: ' : ' 排除标准: ') + outLabel : ''
      break
    case 5:
      fieldItemInfoHtml = curQueryObj.filterString && getQueryDesc(curQueryObj.filterString)
      break

    default:
      break
  }

  fieldItemInfoHtml = fieldItemInfoHtml && fieldItemInfoHtml.replace(reg1, '').replace(reg2, '')

  fieldItemInfoHtml = fieldItemInfoHtml || (isI18n_EN ? 'No Data' : '无')

  return isText ? fieldItemInfoHtml : `<div>${fieldItemInfoHtml}</div>`
}

/**
 * 检测一个字符串是否为正则表达式
 * @param {String} regexp 待检测字符串
 * @returns {Boolean}
 */
export function isRegExp(regexp) {
  const re = /^\/([^\\\/]*(?:\\.[^\\\/]*)*)\/([gimyus]{0,6})$/;
  return re.test(regexp)
}
