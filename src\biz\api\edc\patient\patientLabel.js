// 数据组件管理）

export const pageListApi = (params) => {
    return {
        url: '/edc/patient-label/page',
        method: 'GET',
        params
    }
}

export const detailApi = (id) => {
    return {
        url: '/edc/patient-label/detail/' + id,
        method: 'GET'
    }
}

export const addApi = (data) => {
    return {
        url: '/edc/patient-label/insert',
        method: 'POST',
        data
    }
}

export const editApi = (data) => {
    return {
        url: '/edc/patient-label/update',
        method: 'POST',
        data
    }
}

export const deleteApi = (data) => {
    return {
        url: '/edc/patient-label/deletion',
        method: 'POST',
        data
    }
}